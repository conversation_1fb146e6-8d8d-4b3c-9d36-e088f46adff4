"use strict";
cc._RF.push(module, '0d869kPbP1DcoLTZ3RTKzF5', 'HealOverTimeBuff');
// fight/buff/HealOverTimeBuff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealOverTimeBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var Buff_1 = require("../types/Buff");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
/**
 * 持续治疗Buff
 * 每秒恢复一定生命值的增益效果
 */
var HealOverTimeBuff = /** @class */ (function () {
    function HealOverTimeBuff(caster, target, duration, healPerSecond) {
        this._name = "持续治疗";
        this._description = "每秒恢复生命值";
        this._type = IBuff_1.BuffType.BUFF;
        this._stackCount = 1;
        this._maxStack = 3;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._healInterval = 1.0; // 每秒触发一次
        this._lastHealTime = 0;
        // 视觉效果
        this._iconPath = "icons/buffs/heal_over_time";
        this._effectPrefabPath = "prefabs/effects/HealAura";
        this._id = "heal_over_time_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._healPerSecond = healPerSecond;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u6BCF\u79D2\u6062\u590D" + healPerSecond + "\u70B9\u751F\u547D\u503C\uFF0C\u6301\u7EED" + duration + "\u79D2";
    }
    Object.defineProperty(HealOverTimeBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "periodicEffect", {
        get: function () {
            return {
                interval: this._healInterval,
                effectType: IBuff_1.BuffPeriodicEffectType.HEAL_OVER_TIME,
                value: this._healPerSecond,
                stackable: true
            };
        },
        enumerable: false,
        configurable: true
    });
    /** Buff被添加时触发 */
    HealOverTimeBuff.prototype.onApply = function () {
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        // 播放应用特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /** Buff每帧更新时触发 */
    HealOverTimeBuff.prototype.onTick = function (deltaTime) {
        this._lastHealTime += deltaTime;
        // 检查是否到了治疗时间
        if (this._lastHealTime >= this._healInterval) {
            this.performHeal();
            this._lastHealTime = 0;
        }
    };
    /** Buff被移除时触发 */
    HealOverTimeBuff.prototype.onRemove = function () {
        console.log(this._name + " removed from " + this._target.characterName);
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 执行治疗 */
    HealOverTimeBuff.prototype.performHeal = function () {
        if (this._target && !this._target.isDead) {
            var totalHeal = this._healPerSecond * this._stackCount;
            this._target.heal(totalHeal);
            console.log(this._target.characterName + " healed for " + totalHeal + " HP from " + this._name);
            // 播放治疗特效
            this.playHealEffect();
            // 触发治疗事件
            this._eventManager.emit(FightEvent_1.default.characterHealed, { target: this._target, healer: this._caster, amount: totalHeal, source: this });
        }
    };
    /** 更新Buff状态 */
    HealOverTimeBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    HealOverTimeBuff.prototype.refresh = function () {
        this._remainingTime = this._duration;
        this._lastHealTime = 0;
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    HealOverTimeBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    HealOverTimeBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    HealOverTimeBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.heal_per_second:
                return this._healPerSecond * this._stackCount;
            case Buff_1.EBuffEffectType.total_heal:
                return this._healPerSecond * this._stackCount * this._remainingTime;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    HealOverTimeBuff.prototype.conflictsWith = function (_otherBuff) {
        // 同类型的持续治疗buff不冲突，可以叠加
        return false;
    };
    /** 播放应用特效 */
    HealOverTimeBuff.prototype.playApplyEffect = function () {
        console.log("Playing apply effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效播放逻辑
    };
    /** 播放治疗特效 */
    HealOverTimeBuff.prototype.playHealEffect = function () {
        console.log("Playing heal effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的治疗特效播放逻辑
    };
    /** 停止特效 */
    HealOverTimeBuff.prototype.stopEffect = function () {
        console.log("Stopping effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    HealOverTimeBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            healPerSecond: this._healPerSecond,
            totalHealRemaining: this.getEffectValue(Buff_1.EBuffEffectType.total_heal),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return HealOverTimeBuff;
}());
exports.HealOverTimeBuff = HealOverTimeBuff;

cc._RF.pop();