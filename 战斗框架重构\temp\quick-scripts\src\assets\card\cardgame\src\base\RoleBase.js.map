{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\RoleBase.ts"], "names": [], "mappings": ";;;;AAAA,4CAA4C;AAC5C,0CAA0C;AAC1C,4CAA4C;AAC5C,sDAAsD;AACtD,yCAAyC;AACzC,8CAA8C;AAC9C,8CAA8C;AAC9C,6BAA6B;AAE7B,+CAA+C;AAE/C,4BAA4B;AAC5B,oBAAoB;AACpB,yBAAyB;AACzB,oBAAoB;AACpB,uBAAuB;AACvB,oBAAoB;AACpB,yBAAyB;AACzB,IAAI;AAEJ,mBAAmB;AACnB,sCAAsC;AACtC,6DAA6D;AAC7D,sBAAsB;AACtB,wBAAwB;AACxB,4BAA4B;AAC5B,oBAAoB;AACpB,0BAA0B;AAC1B,oBAAoB;AACpB,2BAA2B;AAC3B,yBAAyB;AACzB,uBAAuB;AAEvB,gCAAgC;AAChC,wBAAwB;AACxB,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,wBAAwB;AACxB,gCAAgC;AAChC,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,cAAc;AACd,wEAAwE;AACxE,2BAA2B;AAE3B,mBAAmB;AACnB,gCAAgC;AAChC,mBAAmB;AACnB,kCAAkC;AAClC,wBAAwB;AACxB,gCAAgC;AAChC,iBAAiB;AACjB,sCAAsC;AAEtC,iBAAiB;AACjB,uBAAuB;AACvB,kBAAkB;AAElB,iBAAiB;AACjB,uBAAuB;AACvB,kBAAkB;AAClB,uBAAuB;AACvB,oBAAoB;AAEpB,iBAAiB;AACjB,uBAAuB;AACvB,wBAAwB;AAExB,iBAAiB;AACjB,uBAAuB;AACvB,2BAA2B;AAE3B,mBAAmB;AACnB,uBAAuB;AACvB,sBAAsB;AAEtB,mBAAmB;AACnB,uBAAuB;AACvB,uBAAuB;AAEvB,kBAAkB;AAClB,uBAAuB;AACvB,qBAAqB;AAErB,mBAAmB;AACnB,uBAAuB;AACvB,mBAAmB;AAEnB,kBAAkB;AAClB,uBAAuB;AACvB,mBAAmB;AAEnB,iEAAiE;AAEjE,0CAA0C;AAC1C,8EAA8E;AAC9E,sCAAsC;AACtC,2BAA2B;AAC3B,2BAA2B;AAC3B,uBAAuB;AACvB,mCAAmC;AACnC,+BAA+B;AAC/B,iCAAiC;AACjC,6BAA6B;AAC7B,uBAAuB;AACvB,2CAA2C;AAC3C,QAAQ;AAER,wCAAwC;AACxC,iCAAiC;AACjC,4BAA4B;AAC5B,4BAA4B;AAC5B,YAAY;AACZ,QAAQ;AAER,uCAAuC;AACvC,+BAA+B;AAC/B,+CAA+C;AAC/C,uBAAuB;AACvB,6CAA6C;AAC7C,+BAA+B;AAC/B,mBAAmB;AACnB,8BAA8B;AAC9B,YAAY;AACZ,QAAQ;AAER,kDAAkD;AAClD,4CAA4C;AAC5C,yGAAyG;AACzG,2FAA2F;AAC3F,YAAY;AACZ,QAAQ;AACR,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import BuffManager from \"../BuffManager\";\r\n// import { ECamp } from \"../CampManager\";\r\n// import CardManager from \"../CardManager\";\r\n// import EquipmentManager from \"../EquipmentManager\";\r\n// import { BRO } from \"../EventManager\";\r\n// import FightManager from \"../FightManager\";\r\n// import SkillManager from \"../SkillManager\";\r\n// import Base from \"./Base\";\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n\r\n// export enum ERoleStatus {\r\n//     /**角色状态-正常 */\r\n//     Normal = `Normal`,\r\n//     /**角色状态-死亡 */\r\n//     Death = `Death`,\r\n//     /**角色状态-冻结 */\r\n//     Frozen = `Frozen`,\r\n// }\r\n\r\n// declare global {\r\n//     type IRoleBaseName = 'RoleBase'\r\n//     export interface IRoleBaseData extends IBaseDataType {\r\n//         hp: number;\r\n//         hpUp: number;\r\n//         roleName: string;\r\n//         /**攻击力 */\r\n//         attack: number;\r\n//         /**防御力 */\r\n//         defence: number;\r\n//         level: number;\r\n//         camp: ECamp;\r\n\r\n//         status?: ERoleStatus;\r\n//         exp?: number;\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 角色控制基类\r\n//  * @description: 游戏中多个角色的控制基类\r\n//  * @Date : 2020-08-12 23:28:52\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 13:57:04\r\n//  * @LastEditors : judu233\r\n//  */\r\n// // @ccclass\r\n// export default class RoleBase extends Base implements IRoleBaseData {\r\n//     data: IRoleBaseData;\r\n\r\n//     /**角色卡片管理 */\r\n//     cardMgr = new CardManager\r\n//     /**角色技能管理 */\r\n//     skillMgr = new SkillManager\r\n//     /**对角色使用buff管理 */\r\n//     buffMgr = new BuffManager\r\n//     /**角色装备 */\r\n//     equipMgr = new EquipmentManager\r\n\r\n//     /**角色id */\r\n//     @Base.ViewLinked\r\n//     id: string;\r\n\r\n//     /**角色血量 */\r\n//     @Base.ViewLinked\r\n//     hp: number;\r\n//     @Base.ViewLinked\r\n//     hpUp: number;\r\n\r\n//     /**角色名字 */\r\n//     @Base.ViewLinked\r\n//     roleName: string;\r\n\r\n//     /**角色状态 */\r\n//     @Base.ViewLinked\r\n//     status: ERoleStatus;\r\n\r\n//     /**角色的攻击力 */\r\n//     @Base.ViewLinked\r\n//     attack: number;\r\n\r\n//     /**角色的防御力 */\r\n//     @Base.ViewLinked\r\n//     defence: number;\r\n\r\n//     /**角色的等级 */\r\n//     @Base.ViewLinked\r\n//     level: number;\r\n\r\n//     /**角色的经验值 */\r\n//     @Base.ViewLinked\r\n//     exp: number;\r\n\r\n//     /**卡牌的阵营 */\r\n//     @Base.ViewLinked\r\n//     camp: ECamp;\r\n\r\n//     get isDeath() { return this.status == ERoleStatus.Death; }\r\n\r\n//     initRole(initData: IRoleBaseData) {\r\n//         let { hp, hpUp, roleName, attack, defence, level, camp } = initData\r\n//         this.data = { ...initData }\r\n//         this.camp = camp\r\n//         this.hpUp = hpUp\r\n//         this.hp = hp\r\n//         this.roleName = roleName\r\n//         this.attack = attack\r\n//         this.defence = defence\r\n//         this.level = level\r\n//         this.exp = 0\r\n//         this.status = ERoleStatus.Normal\r\n//     }\r\n\r\n//     expChangeView(newValue: number) {\r\n//         if (newValue >= 100) {\r\n//             this.exp = 0;\r\n//             this.level++;\r\n//         }\r\n//     }\r\n\r\n//     hpChangeView(newValue: number) {\r\n//         if (newValue <= 0) {\r\n//             this.status = ERoleStatus.Death;\r\n//             return 0\r\n//         } else if (newValue > this.hpUp) {\r\n//             return this.hpUp\r\n//         } else {\r\n//             return newValue\r\n//         }\r\n//     }\r\n\r\n//     statusAfterChangeView(value: ERoleStatus) {\r\n//         if (value == ERoleStatus.Death) {\r\n//             BRO.broadcast(BRO.keys.RefreshUI, { [`${this.camp}${this.className}hp`]: 0 }, this as any)\r\n//             FightManager.campManager.getCampByName(this.camp).roleMgr.deleateRole(this);\r\n//         }\r\n//     }\r\n// }\r\n"]}