{"version": 3, "sources": ["assets\\fight\\buff\\StunBuff.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,kDAA6C;AAC7C,wCAAiD;AAEjD,sEAA0F;AAG1F,qEAAoE;AACpE,sCAAgD;AAEhD;;;GAGG;AACH;IAsBI,kBAAY,MAAkB,EAAE,MAAkB,EAAE,QAAgB;QApB5D,UAAK,GAAW,IAAI,CAAC;QACrB,iBAAY,GAAW,cAAc,CAAC;QACtC,UAAK,GAAa,gBAAQ,CAAC,MAAM,CAAC;QAGlC,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC,CAAC,SAAS;QAGhC,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAyB,EAAE,CAAC;QAEvD,OAAO;QACC,qBAAgB,GAAY,IAAI,CAAC;QACjC,mBAAc,GAAY,IAAI,CAAC;QAC/B,wBAAmB,GAAY,IAAI,CAAC;QAC5C,OAAO;QACC,cAAS,GAAW,kBAAkB,CAAC;QACvC,sBAAiB,GAAW,4BAA4B,CAAC;QAG7D,IAAI,CAAC,GAAG,GAAG,UAAQ,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QAC/E,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,UAAQ,IAAI,CAAC,GAAK,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,GAAG,+CAAU,QAAQ,WAAG,CAAC;QAC1C,oBAAoB;QACpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAGD,sBAAI,wBAAE;QADN,YAAY;aACZ,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,0BAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,iCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,0BAAI;aAAR,cAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,8BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,mCAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAE3D,sBAAI,gCAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACrD,UAAe,MAAc,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;;;;OADb;IAErD,sBAAI,8BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,4BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,4BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,+BAAS;aAAb,cAA2B,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChF,sBAAI,wCAAkB;aAAtB,cAA8D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;;;OAAA;IAChG,sBAAI,8BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,sCAAgB;aAApB,cAAiC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;OAAA;IAEjE,cAAc;IACN,2CAAwB,GAAhC;QACI,eAAe;QACf,IAAM,iBAAiB,GAAG,IAAI,qCAAiB,CACxC,IAAI,CAAC,GAAG,gBAAa,EACxB,QAAQ,EACR,WAAW,EACX,4CAAqB,CAAC,QAAQ,EAC9B,CAAC,EACD,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACnD,CAAC;IAED,iBAAiB;IACjB,0BAAO,GAAP;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,IAAI,CAAC,OAAO,CAAC,aAAa,aAAQ,IAAI,CAAC,SAAS,aAAU,CAAC,CAAC;;YACpG,aAAa;YACb,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACjD;;;;;;;;;QACD,SAAS;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,YAAY;QACZ,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACnI,CAAC;IAED,kBAAkB;IAClB,yBAAM,GAAN,UAAO,SAAiB;;;YACpB,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC9B;;;;;;;;;QACD,YAAY;QACZ,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED,iBAAiB;IACjB,2BAAQ,GAAR;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;;YACxE,UAAU;YACV,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvD;;;;;;;;;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACrF,CAAC;IAED,uBAAuB;IACvB,8BAAW,GAAX,UAAa,KAAa,EAAE,QAAmB;QAC3C,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,qBAAgB,KAAK,CAAC,IAAI,mBAAgB,CAAC,CAAC;YACrF,0BAA0B;YAC1B,OAAO,IAAI,CAAA;SACd;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,aAAa;IACL,0CAAuB,GAA/B;QACI,UAAU;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;SACtC;QACD,mBAAmB;QACnB,sBAAsB;QACtB,OAAO,CAAC,GAAG,CAAC,kCAAgC,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC9E,CAAC;IAED,WAAW;IACH,iCAAc,GAAtB;QACI,kBAAkB;QAClB,mBAAmB;IACvB,CAAC;IAED,eAAe;IACf,yBAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,iBAAiB;IACjB,0BAAO,GAAP;;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;;YACrC,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;aAC3C;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC5E,CAAC;IAED,8BAA8B;IAC9B,2BAAQ,GAAR,UAAS,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QACvB,kBAAkB;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,+BAA0B,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IACrF,CAAC;IAED,sBAAsB;IACtB,8BAAW,GAAX,UAAY,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QAC1B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC5E,CAAC;IAED,mBAAmB;IACnB,iCAAc,GAAd,UAAe,UAAkB;QAC7B,QAAQ,UAAU,EAAE;YAChB,KAAK,sBAAe,CAAC,gBAAgB;gBACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,KAAK,sBAAe,CAAC,cAAc;gBAC/B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,KAAK,sBAAe,CAAC,oBAAoB;gBACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C;gBACI,OAAO,CAAC,CAAC;SAChB;IACL,CAAC;IAED,yBAAyB;IACzB,gCAAa,GAAb,UAAc,SAAgB;QAC1B,qBAAqB;QACrB,IAAI,SAAS,CAAC,IAAI,KAAK,gBAAQ,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IAChB,iCAAc,GAAd;QACI,OAAO,IAAI,CAAC,CAAC,cAAc;IAC/B,CAAC;IAED,aAAa;IACL,kCAAe,GAAvB;QACI,OAAO,CAAC,GAAG,CAAC,6BAA2B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACtF,oBAAoB;QACpB,kBAAkB;IACtB,CAAC;IAED,WAAW;IACH,6BAAU,GAAlB;QACI,OAAO,CAAC,GAAG,CAAC,8BAA4B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACvF,kBAAkB;IACtB,CAAC;IAED,aAAa;IACb,+BAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;YAC5C,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,SAAS,EAAE,IAAI,CAAC,UAAU;SAC7B,CAAC;IACN,CAAC;IACL,eAAC;AAAD,CAnOA,AAmOC,IAAA;AAnOY,4BAAQ", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff, BuffType } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { IAttributeModifier, AttributeModifierType } from \"../types/ICharacterAttributes\";\nimport { ISkill } from \"../types/ISkill\";\nimport { ITimeline } from \"../types/ITimeline\";\nimport { AttributeModifier } from \"../characters/AttributeModifier\";\nimport { EBuffEffectType } from \"../types/Buff\";\n\n/**\n * 眩晕Debuff\n * 使目标无法行动，无法释放技能和移动\n */\nexport class StunBuff implements IBuff {\n    private _id: string;\n    private _name: string = \"眩晕\";\n    private _description: string = \"无法行动、释放技能或移动\";\n    private _type: BuffType = BuffType.DEBUFF;\n    private _duration: number;\n    private _remainingTime: number;\n    private _stackCount: number = 1;\n    private _maxStack: number = 1; // 眩晕不可叠加\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _isExpired: boolean = false;\n    private _attributeModifiers: IAttributeModifier[] = [];\n    private _eventManager: EventManager;\n    // 眩晕配置\n    private _preventMovement: boolean = true;\n    private _preventSkills: boolean = true;\n    private _preventBasicAttack: boolean = true;\n    // 视觉效果\n    private _iconPath: string = \"icons/buffs/stun\";\n    private _effectPrefabPath: string = \"prefabs/effects/StunEffect\";\n\n    constructor(caster: ICharacter, target: ICharacter, duration: number) {\n        this._id = `stun_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        this._caster = caster;\n        this._target = target;\n        this._duration = duration;\n        this._remainingTime = duration;\n        this._eventManager = EventManager.createLocal(`buff_${this._id}`);\n        this._description = `无法行动，持续${duration}秒`;\n        // 创建属性修改器（降低移动速度到0）\n        this.createAttributeModifiers();\n    }\n\n    // 实现IBuff接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get type(): BuffType { return this._type; }\n    get duration(): number { return this._duration; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }\n    get stackCount(): number { return this._stackCount; }\n    set stackCount(_value: number) { this._stackCount = 1; } // 眩晕不可叠加\n    get maxStack(): number { return this._maxStack; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter { return this._target; }\n    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }\n    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }\n    get iconPath(): string { return this._iconPath; }\n    get effectPrefabPath(): string { return this._effectPrefabPath; }\n\n    /** 创建属性修改器 */\n    private createAttributeModifiers(): void {\n        // 移动速度修改器（设为0）\n        const moveSpeedModifier = new AttributeModifier(\n            `${this._id}_move_speed`,\n            \"眩晕移动限制\",\n            \"moveSpeed\",\n            AttributeModifierType.OVERRIDE,\n            0,\n            this._duration\n        );\n        this._attributeModifiers = [moveSpeedModifier];\n    }\n\n    /** Buff被添加时触发 */\n    onApply(): void {\n        console.log(`${this._name} applied to ${this._target.characterName} for ${this._duration} seconds`);\n        // 应用属性修改器到目标\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.addModifier(modifier);\n        }\n        // 播放眩晕特效\n        if (this._effectPrefabPath) {\n            this.playApplyEffect();\n        }\n        // 中断目标当前的行动\n        this.interruptCurrentActions();\n        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });\n        this._eventManager.emit(FightEvent.characterStunned, { target: this._target, caster: this._caster, duration: this._duration });\n    }\n\n    /** Buff每帧更新时触发 */\n    onTick(deltaTime: number): void {\n        for (const modifier of this._attributeModifiers) {\n            modifier.update(deltaTime);\n        }\n        // 持续检查并阻止行动\n        this.preventActions();\n    }\n\n    /** Buff被移除时触发 */\n    onRemove(): void {\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n        // 移除属性修改器\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.removeModifier(modifier.id);\n        }\n        this.stopEffect();\n        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });\n        this._eventManager.emit(FightEvent.characterStunEnded, { target: this._target });\n    }\n\n    /** 释放技能时触发 - 阻止技能释放 */\n    onSkillCast?(skill: ISkill, timeline: ITimeline): ITimeline {\n        if (this._preventSkills) {\n            console.log(`${this._target.characterName} cannot cast ${skill.name} while stunned`);\n            // 返回null或空timeline来阻止技能释放\n            return null\n        }\n        return timeline;\n    }\n\n    /** 中断当前行动 */\n    private interruptCurrentActions(): void {\n        // 中断当前的移动\n        if (this._target.node) {\n            this._target.node.stopAllActions();\n        }\n        // 中断当前的技能释放（如果有的话）\n        // 这里需要与SkillManager配合\n        console.log(`Interrupting all actions for ${this._target.characterName}`);\n    }\n\n    /** 阻止行动 */\n    private preventActions(): void {\n        // 这里可以添加持续的行动阻止逻辑\n        // 比如重置移动输入、阻止技能队列等\n    }\n\n    /** 更新Buff状态 */\n    update(deltaTime: number): boolean {\n        if (this._isExpired) {\n            return true;\n        }\n        this._remainingTime -= deltaTime;\n        this.onTick(deltaTime);\n        if (this._remainingTime <= 0) {\n            this._isExpired = true;\n            return true;\n        }\n        return false;\n    }\n\n    /** 刷新Buff持续时间 */\n    refresh(): void {\n        this._remainingTime = this._duration;\n        for (const modifier of this._attributeModifiers) {\n            modifier.remainingTime = this._duration;\n        }\n        console.log(`${this._name} refreshed on ${this._target.characterName}`);\n    }\n\n    /** 增加叠加层数 - 眩晕不可叠加，但可以刷新时间 */\n    addStack(_count: number = 1): void {\n        // 眩晕不叠加层数，但刷新持续时间\n        this.refresh();\n        console.log(`${this._name} duration refreshed on ${this._target.characterName}`);\n    }\n\n    /** 减少叠加层数 - 眩晕直接移除 */\n    removeStack(_count: number = 1): void {\n        this._stackCount = 0;\n        this._isExpired = true;\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n    }\n\n    /** 获取Buff的当前效果值 */\n    getEffectValue(effectType: string): number {\n        switch (effectType) {\n            case EBuffEffectType.movement_blocked:\n                return this._preventMovement ? 1 : 0;\n            case EBuffEffectType.skills_blocked:\n                return this._preventSkills ? 1 : 0;\n            case EBuffEffectType.basic_attack_blocked:\n                return this._preventBasicAttack ? 1 : 0;\n            default:\n                return 0;\n        }\n    }\n\n    /** 检查Buff是否与另一个Buff冲突 */\n    conflictsWith(otherBuff: IBuff): boolean {\n        // 眩晕与其他控制类debuff可能冲突\n        if (otherBuff.type === BuffType.DEBUFF && otherBuff.name.includes(\"眩晕\")) {\n            return true;\n        }\n        return false;\n    }\n\n    /** 检查是否可以被净化 */\n    canBeDispelled(): boolean {\n        return true; // 眩晕可以被净化技能移除\n    }\n\n    /** 播放应用特效 */\n    private playApplyEffect(): void {\n        console.log(`Playing stun effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的眩晕特效播放逻辑\n        // 比如在目标头顶显示眩晕星星特效\n    }\n\n    /** 停止特效 */\n    private stopEffect(): void {\n        console.log(`Stopping stun effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效停止逻辑\n    }\n\n    /** 获取调试信息 */\n    getDebugInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            type: this._type,\n            duration: this._duration,\n            remainingTime: this._remainingTime,\n            stackCount: this._stackCount,\n            maxStack: this._maxStack,\n            preventMovement: this._preventMovement,\n            preventSkills: this._preventSkills,\n            preventBasicAttack: this._preventBasicAttack,\n            caster: this._caster.characterName,\n            target: this._target.characterName,\n            isExpired: this._isExpired\n        };\n    }\n}\n"]}