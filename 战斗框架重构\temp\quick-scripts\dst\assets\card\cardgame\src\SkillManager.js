
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/SkillManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '087e2LAsmRCjobhhJ95y2R5', 'SkillManager');
// card/cardgame/src/SkillManager.ts

// import Base from "./Base/Base";
// import SkillBase from "./Base/SkillBase";
// const { ccclass, property } = cc._decorator;
// declare global {
//     export interface ISkillManager {
//     }
// }
// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class SkillManager extends Base {
//     /**所有已加载的技能列表 */
//     skillMap: Map<string, ISkillDataType> = new Map();
//     /**加载技能数据表 */
//     loadSkillTableData() {
//     }
//     /**根据id获取技能表数据 */
//     getSkillDataById(id: string) {
//         let skill: SkillBase;
//         try {
//             let skillClassName: { new(): SkillBase; } = eval(id);
//             skill = new skillClassName();
//             //设置数据
//             skill.data = this.skillMap.get(id);
//             skill.initSkillData();
//         } catch (error) {
//             cc.error(`创建技能失败，id：${id},err: ${error}`);
//         }
//         return skill;
//     }
//     /**使用技能 */
//     useSkill(skill: SkillBase) { }
//     /**升级技能 */
//     levelUpSkill(skill: SkillBase) { }
//     /**降级技能 */
//     downgradeSkill(skill: SkillBase) { }
//     /**冻结技能 */
//     frozenSkill(skill: SkillBase) { }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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