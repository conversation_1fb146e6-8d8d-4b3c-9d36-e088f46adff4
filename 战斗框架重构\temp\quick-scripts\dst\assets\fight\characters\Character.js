
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/characters/Character.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e68f8V40WZItaSUzbi9cLvM', 'Character');
// fight/characters/Character.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Character = void 0;
var AttackAction_1 = require("../actions/AttackAction");
var MoveAction_1 = require("../actions/MoveAction");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var BaseCharacter_1 = require("./BaseCharacter");
var CharacterAttributes_1 = require("./CharacterAttributes");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 角色类*/
var Character = /** @class */ (function (_super) {
    __extends(Character, _super);
    function Character() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**角色配置 */
        _this.prefabKey = "";
        _this.roleName = "";
        _this.attackSkillName = "";
        // 动画配置
        _this.idleAnimationName = "idle";
        _this.moveAnimationName = "move";
        _this.attackAnimationName = "attack";
        _this.deathAnimationName = "death";
        // 音效配置
        _this.attackSoundId = "";
        _this.hurtSoundId = "";
        _this.deathSoundId = "";
        return _this;
    }
    /*** 初始化角色*/
    Character.prototype.onCharacterStart = function () {
        this.initializeActions();
        this.initializeAnimationConfig();
        this.initializeSoundConfig();
        this.setupCollision();
    };
    /**  * 更新角色  */
    Character.prototype.onCharacterUpdate = function (deltaTime) {
        // 基类已经处理了技能和Buff的更新， 这里可以添加角色特定的更新逻辑
        this.updateActions(deltaTime);
    };
    /** * 角色死亡处理 */
    Character.prototype.onCharacterDeath = function () {
        var _this = this;
        this.playDeathAnimation();
        this.playDeathSound();
        this.disableCollision();
        // 延迟移除角色
        this.scheduleOnce(function () {
            _this.removeCharacter();
        }, 1.0);
    };
    /** * 执行移动 */
    Character.prototype.performMove = function (direction) {
        if (this._moveAction) {
            var moveVector = direction.clone().multiplyScalar(this.attributes.moveSpeed);
            this._moveAction.moveBy(moveVector);
            this.playMoveAnimation();
        }
    };
    /** * 初始化动作组件 */
    Character.prototype.initializeActions = function () {
        this._attackAction = this.node.getComponent(AttackAction_1.AttackAction) || this.node.addComponent(AttackAction_1.AttackAction);
        this._moveAction = this.node.getComponent(MoveAction_1.MoveAction) || this.node.addComponent(MoveAction_1.MoveAction);
    };
    /** * 初始化动画配置 */
    Character.prototype.initializeAnimationConfig = function () {
        this._animationConfig = {
            idle: this.idleAnimationName,
            move: this.moveAnimationName,
            attack: this.attackAnimationName,
            death: this.deathAnimationName
        };
    };
    /** * 初始化音效配置 */
    Character.prototype.initializeSoundConfig = function () {
        this._soundConfig = {
            attack: this.attackSoundId,
            hurt: this.hurtSoundId,
            death: this.deathSoundId
        };
    };
    /** * 设置碰撞 */
    Character.prototype.setupCollision = function () {
        var collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = true;
        }
    };
    /** * 禁用碰撞 */
    Character.prototype.disableCollision = function () {
        var collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = false;
        }
    };
    /** * 更新动作组件 */
    Character.prototype.updateActions = function (_deltaTime) {
        // 动作组件会自己更新，这里可以添加额外的逻辑
    };
    /*** 播放移动动画*/
    Character.prototype.playMoveAnimation = function () {
        if (this._spine && this._animationConfig.move) {
            this._spine.setAnimation(0, this._animationConfig.move, true);
        }
    };
    /**  * 播放攻击动画  */
    Character.prototype.playAttackAnimation = function (loop) {
        if (loop === void 0) { loop = false; }
        if (this._spine && this._animationConfig.attack) {
            this._spine.setAnimation(0, this._animationConfig.attack, loop);
        }
    };
    /**  * 播放空闲动画  */
    Character.prototype.playIdleAnimation = function () {
        if (this._spine && this._animationConfig.idle) {
            this._spine.setAnimation(0, this._animationConfig.idle, true);
        }
    };
    /** * 播放死亡音效 */
    Character.prototype.playDeathSound = function () {
        if (this._soundConfig.death) {
            // 这里应该调用音效管理器播放音效
            // AudioManager.getInstance().playEffect(this._soundConfig.death);
        }
    };
    /** * 移除角色 */
    Character.prototype.removeCharacter = function () {
        this.node.emit(FightEvent_1.default.characterRemoved, this);
        this.node.destroy();
    };
    /** * 强制攻击目标 */
    Character.prototype.forceAttackTo = function (target) {
        if (!target || target.isDead) {
            console.error('forceAttackTo but target is null or dead');
            return false;
        }
        return this.attack(target);
    };
    /** * 执行攻击动作 */
    Character.prototype.performAttack = function (_target, onHitCallback) {
        var _this = this;
        if (!this._attackAction) {
            return false;
        }
        this.setState(CharacterTypes_1.CharacterState.ATTACKING);
        this.playAttackAnimation();
        // 配置攻击动作
        var attackProps = {
            hurtStartTimeMs: 300,
            hurtEndTimeMs: 600,
            onHurtStart: function () {
                onHitCallback === null || onHitCallback === void 0 ? void 0 : onHitCallback();
                // 这里可以添加伤害计算逻辑
            },
            onHurtEnd: function () {
                _this.setState(CharacterTypes_1.CharacterState.IDLE);
                _this.playIdleAnimation();
            }
        };
        return this._attackAction.doAttackOnce(attackProps);
    };
    /** * 设置角色数据 */
    Character.prototype.setCharacterData = function (data) {
        this._name = data.name;
        this._role = data.role;
        this.roleName = data.name;
        this.prefabKey = typeof data.prefabKey === 'string' ? data.prefabKey : '';
        // 设置初始属性
        if (data.initialAttributes) {
            // 重新创建属性组件以应用初始数据
            this._attributes = new CharacterAttributes_1.CharacterAttributes(data.initialAttributes);
            console.log(this._name + " \u5C5E\u6027\u5DF2\u8BBE\u7F6E:", this._attributes.getAttributeData());
        }
        // 设置位置
        if (data.worldPosition) {
            this.node.position = data.parent ? data.parent.convertToNodeSpaceAR(data.worldPosition) : data.worldPosition;
        }
        // 设置父节点
        if (data.parent) {
            this.node.parent = data.parent;
        }
    };
    /** * 获取开火点世界坐标 */
    Character.prototype.getFireWorldPosition = function () {
        if (this._fireNode) {
            return this._fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        return this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    };
    /** * 获取开火方向 */
    Character.prototype.getFireDirection = function (target) {
        var firePos = this.getFireWorldPosition();
        return target.subtract(firePos).normalize();
    };
    /*** 检查是否在攻击范围内*/
    Character.prototype.isInAttackRange = function (target) {
        if (!target || target.isDead) {
            return false;
        }
        var distance = cc.Vec3.distance(this.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        return distance <= this.attributes.attackRange;
    };
    /** * 面向目标 */
    Character.prototype.faceTarget = function (target) {
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var direction = target.subtract(currentPos);
        if (direction.x < 0) {
            this.node.scaleX = -Math.abs(this.node.scaleX);
        }
        else {
            this.node.scaleX = Math.abs(this.node.scaleX);
        }
    };
    /** * 获取角色信息 */
    Character.prototype.getCharacterInfo = function () {
        return {
            id: this.id,
            name: this.name,
            role: this.role,
            state: this.state,
            isDead: this.isDead,
            attributes: this.attributes.getAttributeData(),
            position: this.node.convertToWorldSpaceAR(cc.Vec3.ZERO),
            skillCount: this.skills.length,
            buffCount: this.buffs.length
        };
    };
    __decorate([
        property(cc.String)
    ], Character.prototype, "prefabKey", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "roleName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackSkillName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "idleAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "moveAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "deathAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackSoundId", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "hurtSoundId", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "deathSoundId", void 0);
    Character = __decorate([
        ccclass
    ], Character);
    return Character;
}(BaseCharacter_1.BaseCharacter));
exports.Character = Character;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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