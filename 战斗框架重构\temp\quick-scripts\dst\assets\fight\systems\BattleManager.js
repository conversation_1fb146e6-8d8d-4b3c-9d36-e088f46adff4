
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/BattleManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '12f73yZztdEmapoujvGAGoA', 'BattleManager');
// fight/systems/BattleManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattleManager = void 0;
var TimelineManager_1 = require("./TimelineManager");
var BulletManager_1 = require("./BulletManager");
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
var CharacterTypes_1 = require("../types/CharacterTypes");
var DamageManager_1 = require("./DamageManager");
/**
 * 战斗管理器 - 统一管理战斗流程
 * 负责协调各个系统的工作
 */
var BattleManager = /** @class */ (function () {
    function BattleManager() {
        this._isInBattle = false;
        this._battleDuration = 0;
        this._participants = new Set();
        this._battleId = "";
        this._timelineManager = new TimelineManager_1.TimelineManager;
        this._bulletManager = new BulletManager_1.BulletManager;
        this._damgeManager = new DamageManager_1.DamageManager;
        this._eventManager = EventManager_1.EventManager.getGlobal();
        this.setupEventListeners();
        console.log("BattleManager using global EventManager:", this._eventManager.instanceId);
    }
    Object.defineProperty(BattleManager, "instance", {
        get: function () {
            if (this._instance == null) {
                this._instance = new BattleManager;
            }
            return this._instance;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "isInBattle", {
        /** 获取战斗状态 */
        get: function () { return this._isInBattle; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "battleDuration", {
        /** 获取战斗时长 */
        get: function () { return this._battleDuration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "participants", {
        /** 获取参战者列表 */
        get: function () { return Array.from(this._participants); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "timelineManager", {
        get: function () { return this._timelineManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "bulletManager", {
        get: function () { return this._bulletManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "damageManager", {
        get: function () { return this._damgeManager; },
        enumerable: false,
        configurable: true
    });
    /** 开始战斗 */
    BattleManager.prototype.startBattle = function (battleId, participants) {
        var e_1, _a;
        if (this._isInBattle) {
            console.warn("Battle is already in progress");
            return;
        }
        this._battleId = battleId;
        this._isInBattle = true;
        this._battleDuration = 0;
        this._participants.clear();
        try {
            // 添加参战者
            for (var participants_1 = __values(participants), participants_1_1 = participants_1.next(); !participants_1_1.done; participants_1_1 = participants_1.next()) {
                var participant = participants_1_1.value;
                this._participants.add(participant);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (participants_1_1 && !participants_1_1.done && (_a = participants_1.return)) _a.call(participants_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 清理之前的Timeline
        this._timelineManager.clearAll();
        console.log("Battle " + battleId + " started with " + participants.length + " participants");
        // 发送全局战斗开始事件
        EventManager_1.EventManager.emit(FightEvent_1.default.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
        // 发送局部事件（用于内部管理）
        this._eventManager.emit(FightEvent_1.default.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
    };
    /** 结束战斗 */
    BattleManager.prototype.endBattle = function (reason) {
        if (reason === void 0) { reason = "normal"; }
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        // 暂停所有Timeline
        this._timelineManager.pauseAll();
        console.log("Battle " + this._battleId + " ended. Reason: " + reason + ", Duration: " + this._battleDuration + "s");
        // 发送全局战斗结束事件
        EventManager_1.EventManager.emit(FightEvent_1.default.battleEnded, {
            battleId: this._battleId,
            reason: reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 发送局部事件
        this._eventManager.emit(FightEvent_1.default.battleEnded, {
            battleId: this._battleId,
            reason: reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 重置状态
        this._isInBattle = false;
        this._battleId = "";
        this._participants.clear();
        // 清理所有Timeline和子弹
        this._timelineManager.clearAll();
        this._bulletManager.clearAllBullets();
    };
    /** 暂停战斗 */
    BattleManager.prototype.pauseBattle = function () {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.pauseAll();
        console.log("Battle " + this._battleId + " paused");
        this._eventManager.emit(FightEvent_1.default.battlePaused, { battleId: this._battleId });
    };
    /** 恢复战斗 */
    BattleManager.prototype.resumeBattle = function () {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.resumeAll();
        console.log("Battle " + this._battleId + " resumed");
        this._eventManager.emit(FightEvent_1.default.battleResumed, { battleId: this._battleId });
    };
    /** 添加参战者 */
    BattleManager.prototype.addParticipant = function (character) {
        this._participants.add(character);
        console.log(character.characterName + " joined the battle");
        this._eventManager.emit(FightEvent_1.default.participantAdded, { battleId: this._battleId, character: character });
    };
    /** 移除参战者 */
    BattleManager.prototype.removeParticipant = function (character) {
        if (this._participants.delete(character)) {
            // 移除该角色相关的所有Timeline
            this._timelineManager.removeTimelinesByCaster(character);
            console.log(character.characterName + " left the battle");
            this._eventManager.emit(FightEvent_1.default.participantRemoved, { battleId: this._battleId, character: character });
            // 检查战斗是否应该结束
            this.checkBattleEnd();
        }
    };
    /** 更新战斗状态 */
    BattleManager.prototype.update = function (deltaTime) {
        var e_2, _a;
        if (!this._isInBattle) {
            return;
        }
        this._battleDuration += deltaTime;
        this._timelineManager.update(deltaTime);
        this._bulletManager.update(deltaTime);
        try {
            // 更新参战者状态
            for (var _b = __values(this._participants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var participant = _c.value;
                if (participant.update) {
                    participant.update(deltaTime);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        this.checkBattleEnd();
    };
    /** 检查战斗是否应该结束 */
    BattleManager.prototype.checkBattleEnd = function () {
        if (!this._isInBattle) {
            return;
        }
        // 检查是否有存活的敌对双方
        var alivePlayers = Array.from(this._participants).filter(function (p) { return !p.isDead && p.role === CharacterTypes_1.CharacterRole.HERO; });
        var aliveEnemies = Array.from(this._participants).filter(function (p) { return !p.isDead && p.role === CharacterTypes_1.CharacterRole.ENEMY; });
        if (alivePlayers.length === 0) {
            this.endBattle("players_defeated");
        }
        else if (aliveEnemies.length === 0) {
            this.endBattle("enemies_defeated");
        }
    };
    /** 设置事件监听器 */
    BattleManager.prototype.setupEventListeners = function () {
        var _this = this;
        // 监听角色死亡事件
        this._eventManager.on(FightEvent_1.default.characterDied, function (event) {
            var character = event.character;
            console.log(character.characterName + " died in battle");
            // 移除死亡角色的Timeline
            _this._timelineManager.removeTimelinesByCaster(character);
            // 检查战斗是否结束
            _this.checkBattleEnd();
        });
        // 监听Timeline事件
        this._eventManager.on(FightEvent_1.default.timelineCompleted, function () {
            console.log("Timeline completed in battle " + _this._battleId);
        });
    };
    /** 获取战斗统计信息 */
    BattleManager.prototype.getBattleStats = function () {
        return {
            battleId: this._battleId,
            isInBattle: this._isInBattle,
            duration: this._battleDuration,
            participantCount: this._participants.size,
            timelineStats: this._timelineManager.getStats(),
            bulletStats: this._bulletManager.getStats(),
            participants: Array.from(this._participants).map(function (p) { return ({
                characterName: p.characterName,
                role: p.role,
                isDead: p.isDead,
                hp: p.attributes.currentHp || 0,
                maxHp: p.attributes.maxHp || 0
            }); })
        };
    };
    /** 打印调试信息 */
    BattleManager.prototype.printDebugInfo = function () {
        console.log("BattleManager Debug Info:", this.getBattleStats());
        this._timelineManager.printDebugInfo();
    };
    Object.defineProperty(BattleManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    BattleManager.prototype.cleanup = function () {
        this.endBattle("cleanup");
        this._eventManager.cleanup();
        this._timelineManager.cleanup();
        this._bulletManager.cleanup();
    };
    return BattleManager;
}());
exports.BattleManager = BattleManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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