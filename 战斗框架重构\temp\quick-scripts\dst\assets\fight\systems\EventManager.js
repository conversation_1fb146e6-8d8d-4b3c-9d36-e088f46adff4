
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/EventManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2265fiHhMdL2af3ZT22JWaV', 'EventManager');
// fight/systems/EventManager.ts

"use strict";
/**
 * 事件管理器
 * 提供事件的注册、触发和管理功能
 */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventManager = void 0;
/**
 * 事件管理器类
 */
var EventManager = /** @class */ (function () {
    function EventManager(instanceId) {
        this._listeners = new Map();
        this._isDestroyed = false;
        this._instanceId = instanceId || "eventManager_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
    }
    /** 获取全局单例实例 */
    EventManager.getGlobal = function () {
        if (!EventManager._globalInstance) {
            EventManager._globalInstance = new EventManager("global");
        }
        return EventManager._globalInstance;
    };
    /** 创建新的局部实例 */
    EventManager.createLocal = function (instanceId) {
        return new EventManager(instanceId);
    };
    /** 全局事件快捷方法 */
    EventManager.on = function (event, callback, context) {
        EventManager.getGlobal().on(event, callback, context);
    };
    EventManager.once = function (event, callback, context) {
        EventManager.getGlobal().once(event, callback, context);
    };
    EventManager.off = function (event, callback, context) {
        EventManager.getGlobal().off(event, callback, context);
    };
    EventManager.emit = function (event, data) {
        EventManager.getGlobal().emit(event, data);
    };
    EventManager.hasListeners = function (event) {
        return EventManager.getGlobal().hasListeners(event);
    };
    EventManager.getListenerCount = function (event) {
        return EventManager.getGlobal().getListenerCount(event);
    };
    EventManager.getEventNames = function () {
        return EventManager.getGlobal().getEventNames();
    };
    EventManager.cleanup = function () {
        if (EventManager._globalInstance) {
            EventManager._globalInstance.cleanup();
            EventManager._globalInstance = null;
        }
    };
    EventManager.getDebugInfo = function () {
        return EventManager.getGlobal().getDebugInfo();
    };
    Object.defineProperty(EventManager.prototype, "instanceId", {
        /** 获取实例ID */
        get: function () { return this._instanceId; },
        enumerable: false,
        configurable: true
    });
    /**
     * 注册事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.on = function (event, callback, context) {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        var listeners = this._listeners.get(event);
        listeners.push({ callback: callback, context: context, once: false });
    };
    /**
     * 注册一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.once = function (event, callback, context) {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        var listeners = this._listeners.get(event);
        listeners.push({
            callback: callback,
            context: context,
            once: true
        });
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.off = function (event, callback, context) {
        if (this._isDestroyed) {
            return;
        }
        var listeners = this._listeners.get(event);
        if (!listeners) {
            return;
        }
        if (!callback) {
            // 如果没有指定回调函数，移除所有监听器
            this._listeners.delete(event);
            return;
        }
        // 移除匹配的监听器
        var filteredListeners = listeners.filter(function (listener) {
            if (listener.callback !== callback) {
                return true;
            }
            if (context !== undefined && listener.context !== context) {
                return true;
            }
            return false;
        });
        if (filteredListeners.length === 0) {
            this._listeners.delete(event);
        }
        else {
            this._listeners.set(event, filteredListeners);
        }
    };
    /**
     * 触发事件
     * @param event 事件名称
     * @param data 事件数据
     */
    EventManager.prototype.emit = function (event, data) {
        var e_1, _a;
        if (this._isDestroyed) {
            return;
        }
        var listeners = this._listeners.get(event);
        if (!listeners || listeners.length === 0) {
            return;
        }
        // 创建监听器副本，避免在回调中修改监听器列表时出现问题
        var listenersCopy = __spread(listeners);
        var onceListeners = [];
        try {
            for (var listenersCopy_1 = __values(listenersCopy), listenersCopy_1_1 = listenersCopy_1.next(); !listenersCopy_1_1.done; listenersCopy_1_1 = listenersCopy_1.next()) {
                var listener = listenersCopy_1_1.value;
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data);
                    }
                    else {
                        listener.callback(data);
                    }
                    // 收集一次性监听器
                    if (listener.once) {
                        onceListeners.push(listener);
                    }
                }
                catch (error) {
                    console.error("Error in event listener for event \"" + event + "\":", error);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (listenersCopy_1_1 && !listenersCopy_1_1.done && (_a = listenersCopy_1.return)) _a.call(listenersCopy_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 移除一次性监听器
        if (onceListeners.length > 0) {
            var remainingListeners = listeners.filter(function (listener) { return !onceListeners.includes(listener); });
            if (remainingListeners.length === 0) {
                this._listeners.delete(event);
            }
            else {
                this._listeners.set(event, remainingListeners);
            }
        }
    };
    /**
     * 检查是否有指定事件的监听器
     * @param event 事件名称
     * @returns 是否有监听器
     */
    EventManager.prototype.hasListeners = function (event) {
        var listeners = this._listeners.get(event);
        return listeners !== undefined && listeners.length > 0;
    };
    /**
     * 获取指定事件的监听器数量
     * @param event 事件名称
     * @returns 监听器数量
     */
    EventManager.prototype.getListenerCount = function (event) {
        var listeners = this._listeners.get(event);
        return listeners ? listeners.length : 0;
    };
    /**
     * 获取所有事件名称
     * @returns 事件名称数组
     */
    EventManager.prototype.getEventNames = function () {
        return Array.from(this._listeners.keys());
    };
    /**
     * 移除所有监听器
     */
    EventManager.prototype.removeAllListeners = function () {
        this._listeners.clear();
    };
    /**
     * 移除指定事件的所有监听器
     * @param event 事件名称
     */
    EventManager.prototype.removeAllListenersForEvent = function (event) {
        this._listeners.delete(event);
    };
    /**
     * 清理事件管理器
     */
    EventManager.prototype.cleanup = function () {
        this.removeAllListeners();
        this._isDestroyed = true;
    };
    /**
     * 获取调试信息
     */
    EventManager.prototype.getDebugInfo = function () {
        var e_2, _a;
        var info = {};
        try {
            for (var _b = __values(this._listeners), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), event = _d[0], listeners = _d[1];
                info[event] = {
                    count: listeners.length,
                    listeners: listeners.map(function (listener) { return ({
                        hasContext: !!listener.context,
                        isOnce: !!listener.once,
                        functionName: listener.callback.name || 'anonymous'
                    }); })
                };
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return info;
    };
    /**
     * 打印调试信息
     */
    EventManager.prototype.printDebugInfo = function () {
        console.log("EventManager Debug Info:", this.getDebugInfo());
    };
    return EventManager;
}());
exports.EventManager = EventManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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