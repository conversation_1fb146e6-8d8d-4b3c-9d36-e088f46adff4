{"version": 3, "sources": ["assets\\fight\\systems\\BattleManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,iDAAgD;AAChD,+CAA8C;AAC9C,kDAA6C;AAE7C,0DAAwD;AACxD,iDAAgD;AAEhD;;;GAGG;AACH;IAiBI;QAXQ,gBAAW,GAAY,KAAK,CAAC;QAC7B,oBAAe,GAAW,CAAC,CAAC;QAC5B,kBAAa,GAAoB,IAAI,GAAG,EAAE,CAAC;QAC3C,cAAS,GAAW,EAAE,CAAC;QAS3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,iCAAe,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,6BAAa,CAAA;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,CAAA;QACtC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,SAAS,EAAE,CAAC;QAC9C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAC3F,CAAC;IAdD,sBAAW,yBAAQ;aAAnB;YACI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;gBACxB,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,CAAA;aACrC;YACD,OAAO,IAAI,CAAC,SAAS,CAAA;QACzB,CAAC;;;OAAA;IAYD,sBAAI,qCAAU;QADd,aAAa;aACb,cAA4B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAEtD,sBAAI,yCAAc;QADlB,aAAa;aACb,cAA+B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;OAAA;IAE7D,sBAAI,uCAAY;QADhB,cAAc;aACd,cAAgD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;;;OAAA;IACxF,sBAAI,0CAAe;aAAnB,cAAyC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;OAAA;IACxE,sBAAI,wCAAa;aAAjB,cAAqC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;;;OAAA;IAClE,sBAAI,wCAAa;aAAjB,cAAsB,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;;;OAAA;IAGjD,WAAW;IACX,mCAAW,GAAX,UAAY,QAAgB,EAAE,YAA0B;;QACpD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO;SACV;QACD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;;YAC3B,QAAQ;YACR,KAA0B,IAAA,iBAAA,SAAA,YAAY,CAAA,0CAAA,oEAAE;gBAAnC,IAAM,WAAW,yBAAA;gBAClB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACvC;;;;;;;;;QACD,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,YAAU,QAAQ,sBAAiB,YAAY,CAAC,MAAM,kBAAe,CAAC,CAAC;QACnF,aAAa;QACb,2BAAY,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;SAC/C,CAAC,CAAC;QACH,iBAAiB;QACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE;YAC9C,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;SAC/C,CAAC,CAAC;IACP,CAAC;IAED,WAAW;IACX,iCAAS,GAAT,UAAU,MAAyB;QAAzB,uBAAA,EAAA,iBAAyB;QAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO;SACV;QACD,eAAe;QACf,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,SAAS,wBAAmB,MAAM,oBAAe,IAAI,CAAC,eAAe,MAAG,CAAC,CAAC;QACrG,aAAa;QACb,2BAAY,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE;YACtC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,MAAM,QAAA;YACN,QAAQ,EAAE,IAAI,CAAC,eAAe;YAC9B,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;SAC/C,CAAC,CAAC;QACH,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE;YAC5C,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,MAAM,QAAA;YACN,QAAQ,EAAE,IAAI,CAAC,eAAe;YAC9B,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;SAC/C,CAAC,CAAC;QACH,OAAO;QACP,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;IAC1C,CAAC;IAED,WAAW;IACX,mCAAW,GAAX;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,SAAS,YAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACnF,CAAC;IAED,WAAW;IACX,oCAAY,GAAZ;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,SAAS,aAAU,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACpF,CAAC;IAED,YAAY;IACZ,sCAAc,GAAd,UAAe,SAAqB;QAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,uBAAoB,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;IAClG,CAAC;IAED,YAAY;IACZ,yCAAiB,GAAjB,UAAkB,SAAqB;QACnC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACtC,qBAAqB;YACrB,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,qBAAkB,CAAC,CAAC;YAC1D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;YAChG,aAAa;YACb,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAED,aAAa;IACb,8BAAM,GAAN,UAAO,SAAiB;;QACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QACD,IAAI,CAAC,eAAe,IAAI,SAAS,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;YACtC,UAAU;YACV,KAA0B,IAAA,KAAA,SAAA,IAAI,CAAC,aAAa,CAAA,gBAAA,4BAAE;gBAAzC,IAAM,WAAW,WAAA;gBAClB,IAAI,WAAW,CAAC,MAAM,EAAE;oBACpB,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;iBACjC;aACJ;;;;;;;;;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED,iBAAiB;IACT,sCAAc,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QACD,eAAe;QACf,IAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,8BAAa,CAAC,IAAI,EAA1C,CAA0C,CAAC,CAAC;QAC5G,IAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,8BAAa,CAAC,KAAK,EAA3C,CAA2C,CAAC,CAAC;QAC7G,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;SACtC;aAAM,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;SACtC;IACL,CAAC;IAED,cAAc;IACN,2CAAmB,GAA3B;QAAA,iBAcC;QAbG,WAAW;QACX,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAU,CAAC,aAAa,EAAE,UAAC,KAAU;YACvD,IAAM,SAAS,GAAG,KAAK,CAAC,SAAuB,CAAC;YAChD,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,oBAAiB,CAAC,CAAC;YACzD,kBAAkB;YAClB,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACzD,WAAW;YACX,KAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAU,CAAC,iBAAiB,EAAE;YAChD,OAAO,CAAC,GAAG,CAAC,kCAAgC,KAAI,CAAC,SAAW,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,eAAe;IACf,sCAAc,GAAd;QACI,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,QAAQ,EAAE,IAAI,CAAC,eAAe;YAC9B,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACzC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAC/C,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;YAC3C,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC;gBACnD,aAAa,EAAE,CAAC,CAAC,aAAa;gBAC9B,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,MAAM,EAAE,CAAC,CAAC,MAAM;gBAChB,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC;gBAC/B,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC;aACjC,CAAC,EANoD,CAMpD,CAAC;SACN,CAAC;IACN,CAAC;IACD,aAAa;IACb,sCAAc,GAAd;QACI,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;IAC3C,CAAC;IAED,sBAAI,uCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,YAAY;IACZ,+BAAO,GAAP;QACI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IACL,oBAAC;AAAD,CA/NA,AA+NC,IAAA;AA/NY,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TimelineManager } from \"./TimelineManager\";\nimport { BulletManager } from \"./BulletManager\";\nimport { EventManager } from \"./EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { CharacterRole } from \"../types/CharacterTypes\";\nimport { DamageManager } from \"./DamageManager\";\n\n/**\n * 战斗管理器 - 统一管理战斗流程\n * 负责协调各个系统的工作\n */\nexport class BattleManager {\n    private static _instance: BattleManager;\n    private _timelineManager: TimelineManager;\n    private _bulletManager: BulletManager;\n    private _damgeManager: DamageManager\n    private _eventManager: EventManager;\n    private _isInBattle: boolean = false;\n    private _battleDuration: number = 0;\n    private _participants: Set<ICharacter> = new Set();\n    private _battleId: string = \"\";\n    static get instance() {\n        if (this._instance == null) {\n            this._instance = new BattleManager\n        }\n        return this._instance\n    }\n\n    constructor() {\n        this._timelineManager = new TimelineManager\n        this._bulletManager = new BulletManager\n        this._damgeManager = new DamageManager\n        this._eventManager = EventManager.getGlobal();\n        this.setupEventListeners();\n        console.log(\"BattleManager using global EventManager:\", this._eventManager.instanceId);\n    }\n\n    /** 获取战斗状态 */\n    get isInBattle(): boolean { return this._isInBattle; }\n    /** 获取战斗时长 */\n    get battleDuration(): number { return this._battleDuration; }\n    /** 获取参战者列表 */\n    get participants(): ReadonlyArray<ICharacter> { return Array.from(this._participants); }\n    get timelineManager(): TimelineManager { return this._timelineManager; }\n    get bulletManager(): BulletManager { return this._bulletManager; }\n    get damageManager() { return this._damgeManager }\n\n\n    /** 开始战斗 */\n    startBattle(battleId: string, participants: ICharacter[]): void {\n        if (this._isInBattle) {\n            console.warn(\"Battle is already in progress\");\n            return;\n        }\n        this._battleId = battleId;\n        this._isInBattle = true;\n        this._battleDuration = 0;\n        this._participants.clear();\n        // 添加参战者\n        for (const participant of participants) {\n            this._participants.add(participant);\n        }\n        // 清理之前的Timeline\n        this._timelineManager.clearAll();\n        console.log(`Battle ${battleId} started with ${participants.length} participants`);\n        // 发送全局战斗开始事件\n        EventManager.emit(FightEvent.battleStarted, {\n            battleId: this._battleId,\n            participants: Array.from(this._participants)\n        });\n        // 发送局部事件（用于内部管理）\n        this._eventManager.emit(FightEvent.battleStarted, {\n            battleId: this._battleId,\n            participants: Array.from(this._participants)\n        });\n    }\n\n    /** 结束战斗 */\n    endBattle(reason: string = \"normal\"): void {\n        if (!this._isInBattle) {\n            console.warn(\"No battle in progress\");\n            return;\n        }\n        // 暂停所有Timeline\n        this._timelineManager.pauseAll();\n        console.log(`Battle ${this._battleId} ended. Reason: ${reason}, Duration: ${this._battleDuration}s`);\n        // 发送全局战斗结束事件\n        EventManager.emit(FightEvent.battleEnded, {\n            battleId: this._battleId,\n            reason,\n            duration: this._battleDuration,\n            participants: Array.from(this._participants)\n        });\n        // 发送局部事件\n        this._eventManager.emit(FightEvent.battleEnded, {\n            battleId: this._battleId,\n            reason,\n            duration: this._battleDuration,\n            participants: Array.from(this._participants)\n        });\n        // 重置状态\n        this._isInBattle = false;\n        this._battleId = \"\";\n        this._participants.clear();\n        // 清理所有Timeline和子弹\n        this._timelineManager.clearAll();\n        this._bulletManager.clearAllBullets();\n    }\n\n    /** 暂停战斗 */\n    pauseBattle(): void {\n        if (!this._isInBattle) {\n            console.warn(\"No battle in progress\");\n            return;\n        }\n        this._timelineManager.pauseAll();\n        console.log(`Battle ${this._battleId} paused`);\n        this._eventManager.emit(FightEvent.battlePaused, { battleId: this._battleId });\n    }\n\n    /** 恢复战斗 */\n    resumeBattle(): void {\n        if (!this._isInBattle) {\n            console.warn(\"No battle in progress\");\n            return;\n        }\n        this._timelineManager.resumeAll();\n        console.log(`Battle ${this._battleId} resumed`);\n        this._eventManager.emit(FightEvent.battleResumed, { battleId: this._battleId });\n    }\n\n    /** 添加参战者 */\n    addParticipant(character: ICharacter): void {\n        this._participants.add(character);\n        console.log(`${character.characterName} joined the battle`);\n        this._eventManager.emit(FightEvent.participantAdded, { battleId: this._battleId, character });\n    }\n\n    /** 移除参战者 */\n    removeParticipant(character: ICharacter): void {\n        if (this._participants.delete(character)) {\n            // 移除该角色相关的所有Timeline\n            this._timelineManager.removeTimelinesByCaster(character);\n            console.log(`${character.characterName} left the battle`);\n            this._eventManager.emit(FightEvent.participantRemoved, { battleId: this._battleId, character });\n            // 检查战斗是否应该结束\n            this.checkBattleEnd();\n        }\n    }\n\n    /** 更新战斗状态 */\n    update(deltaTime: number): void {\n        if (!this._isInBattle) {\n            return;\n        }\n        this._battleDuration += deltaTime;\n        this._timelineManager.update(deltaTime);\n        this._bulletManager.update(deltaTime);\n        // 更新参战者状态\n        for (const participant of this._participants) {\n            if (participant.update) {\n                participant.update(deltaTime);\n            }\n        }\n        this.checkBattleEnd();\n    }\n\n    /** 检查战斗是否应该结束 */\n    private checkBattleEnd(): void {\n        if (!this._isInBattle) {\n            return;\n        }\n        // 检查是否有存活的敌对双方\n        const alivePlayers = Array.from(this._participants).filter(p => !p.isDead && p.role === CharacterRole.HERO);\n        const aliveEnemies = Array.from(this._participants).filter(p => !p.isDead && p.role === CharacterRole.ENEMY);\n        if (alivePlayers.length === 0) {\n            this.endBattle(\"players_defeated\");\n        } else if (aliveEnemies.length === 0) {\n            this.endBattle(\"enemies_defeated\");\n        }\n    }\n\n    /** 设置事件监听器 */\n    private setupEventListeners(): void {\n        // 监听角色死亡事件\n        this._eventManager.on(FightEvent.characterDied, (event: any) => {\n            const character = event.character as ICharacter;\n            console.log(`${character.characterName} died in battle`);\n            // 移除死亡角色的Timeline\n            this._timelineManager.removeTimelinesByCaster(character);\n            // 检查战斗是否结束\n            this.checkBattleEnd();\n        });\n        // 监听Timeline事件\n        this._eventManager.on(FightEvent.timelineCompleted, () => {\n            console.log(`Timeline completed in battle ${this._battleId}`);\n        });\n    }\n\n    /** 获取战斗统计信息 */\n    getBattleStats() {\n        return {\n            battleId: this._battleId,\n            isInBattle: this._isInBattle,\n            duration: this._battleDuration,\n            participantCount: this._participants.size,\n            timelineStats: this._timelineManager.getStats(),\n            bulletStats: this._bulletManager.getStats(),\n            participants: Array.from(this._participants).map(p => ({\n                characterName: p.characterName,\n                role: p.role,\n                isDead: p.isDead,\n                hp: p.attributes.currentHp || 0,\n                maxHp: p.attributes.maxHp || 0\n            }))\n        };\n    }\n    /** 打印调试信息 */\n    printDebugInfo(): void {\n        console.log(\"BattleManager Debug Info:\", this.getBattleStats());\n        this._timelineManager.printDebugInfo();\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n\n    /** 清理管理器 */\n    cleanup(): void {\n        this.endBattle(\"cleanup\");\n        this._eventManager.cleanup();\n        this._timelineManager.cleanup();\n        this._bulletManager.cleanup();\n    }\n}\n"]}