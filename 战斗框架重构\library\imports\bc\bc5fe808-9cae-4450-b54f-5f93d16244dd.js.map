{"version": 3, "sources": ["assets\\fight\\systems\\DamageManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4CAAsE;AAEtE,+CAA8C;AAC9C,kDAA6C;AAE7C;;GAEG;AACH;IAgBI,oBAAY,QAA2B,EAAE,MAAkB,EAAE,UAAkB,EAAE,UAAsB,EAAE,IAAsB;QAAtB,qBAAA,EAAA,SAAsB;QAR/H,eAAU,GAAY,KAAK,CAAC;QAC5B,uBAAkB,GAAW,GAAG,CAAC;QACjC,oBAAe,GAAW,CAAC,CAAC;QAC5B,wBAAmB,GAAW,CAAC,CAAC;QAChC,kBAAa,GAAY,EAAE,CAAC;QAE5B,aAAQ,GAAY,KAAK,CAAC;QAGtB,IAAI,CAAC,EAAE,GAAG,YAAU,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QAChF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,YAAO,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,sBAAI,mCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aACvD,UAAgB,KAAa,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAGvD,yCAAoB,GAApB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,2BAAM,GAAN,UAAO,GAAc;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvB;IACL,CAAC;IAED,2BAAM,GAAN,UAAO,GAAc;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,oCAAe,GAAf,UAAgB,IAAW;QACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IACL,iBAAC;AAAD,CA9CA,AA8CC,IAAA;AAED;;;GAGG;AACH;IAOI;QALQ,sBAAiB,GAAW,CAAC,CAAC;QAC9B,sBAAiB,GAAW,CAAC,CAAC;QAC9B,kBAAa,GAAW,CAAC,CAAC;QAC1B,eAAU,GAAW,CAAC,CAAC;QAG3B,IAAI,CAAC,aAAa,GAAG,IAAI,2BAAY,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG;IACH,kCAAU,GAAV,UAAW,QAAoB,EAAE,MAAkB,EAAE,UAAkB,EAAE,UAA4C,EAAE,IAAsB;QAApE,2BAAA,EAAA,aAAyB,oBAAU,CAAC,QAAQ;QAAE,qBAAA,EAAA,SAAsB;QACzI,SAAS;QACT,IAAM,UAAU,GAAG,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAClF,OAAO;QACP,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC3B,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,4BAAuB,QAAQ,CAAC,aAAe,CAAC,CAAC;YACpF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAC3D,OAAO,UAAU,CAAC;SACrB;QACD,OAAO;QACP,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;YACtC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;YAC7B,IAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,cAAc,IAAI,GAAG,CAAC;YACjE,UAAU,CAAC,WAAW,IAAI,cAAc,CAAC;YACzC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,mBAAiB,QAAQ,CAAC,aAAa,kCAA6B,MAAM,CAAC,aAAe,CAAC,CAAC;SAC3G;QACD,QAAQ;QACR,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QACvF,SAAS;QACT,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACvE,sBAAsB;QACtB,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC3E,qBAAqB;QACrB,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACvE,WAAW;QACX,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QACzE,WAAW;QACX,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC;QAC5E,OAAO;QACP,IAAI,CAAC,iBAAiB,IAAI,UAAU,CAAC,WAAW,CAAC;QACjD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,UAAU;QACV,IAAI,UAAU,CAAC,WAAW,GAAG,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE;YACnD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;gBACtB,IAAI,CAAC,iBAAiB,IAAI,UAAU,CAAC,WAAW,CAAC;aACpD;SACJ;QACD,OAAO,CAAC,GAAG,CAAI,QAAQ,CAAC,aAAa,eAAU,UAAU,CAAC,WAAW,SAAI,UAAU,mBAAc,MAAM,CAAC,aAAe,CAAC,CAAC;QACzH,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3D,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,aAAa;IACL,kCAAU,GAAlB,UAAmB,QAAoB,EAAE,MAAkB;QACvD,IAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,IAAI,GAAG,CAAC;QACpD,IAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,CAAC;QACnD,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC;IACxC,CAAC;IAED,aAAa;IACL,qCAAa,GAArB,UAAsB,QAAoB,EAAE,OAAmB;QAC3D,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,IAAI,GAAG,CAAC;QAC7D,gBAAgB;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC;IACxC,CAAC;IAED,YAAY;IACJ,oCAAY,GAApB,UAAqB,MAAc,EAAE,MAAkB,EAAE,UAAsB;QAC3E,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,QAAQ,UAAU,EAAE;YAChB,KAAK,oBAAU,CAAC,QAAQ;gBACpB,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,oBAAU,CAAC,KAAK;gBACjB,eAAe;gBACf,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;gBAC5E,MAAM;YACV,KAAK,oBAAU,CAAC,IAAI;gBAChB,WAAW;gBACX,OAAO,MAAM,CAAC;SACrB;QAED,0BAA0B;QAC1B,+CAA+C;QAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,aAAa;IACL,4CAAoB,GAA5B,UAA6B,UAAuB,EAAE,OAAmB;QACrE,IAAI,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC;QACpC,kBAAkB;QAClB,gBAAgB;QAChB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,iBAAiB;IACT,8CAAsB,GAA9B,UAA+B,UAAuB,EAAE,QAAoB;;QACxE,IAAI,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC;;YACpC,sBAAsB;YACtB,KAAmB,IAAA,KAAA,SAAA,QAAQ,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA9B,IAAM,IAAI,WAAA;gBACX,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;oBACtE,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;iBACrC;aACJ;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,gBAAgB;IACR,4CAAoB,GAA5B,UAA6B,UAAuB,EAAE,MAAkB;;QACpE,IAAI,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC;;YACpC,qBAAqB;YACrB,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA5B,IAAM,IAAI,WAAA;gBACX,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;oBACxE,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;iBACrC;aACJ;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,aAAa;IACb,gCAAQ,GAAR;QACI,OAAO;YACH,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAC/E,CAAC;IACN,CAAC;IAED,aAAa;IACb,kCAAU,GAAV;QACI,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACxB,CAAC;IAGD,sBAAI,uCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,YAAY;IACZ,+BAAO,GAAP;QACI,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACL,oBAAC;AAAD,CAtKA,AAsKC,IAAA;AAtKY,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ICharacter } from \"../types/ICharacter\";\nimport { DamageType, DamageTag, IDamageInfo } from \"../types/IDamage\";\nimport { IBuff } from \"../types/IBuff\";\nimport { EventManager } from \"./EventManager\";\nimport FightEvent from \"../types/FightEvent\";\n\n/**\n * 简化的伤害信息实现\n */\nclass DamageInfo implements IDamageInfo {\n    readonly id: string;\n    readonly attacker: ICharacter | null;\n    readonly target: ICharacter;\n    baseDamage: number;\n    private _finalDamage: number;\n    readonly damageType: DamageType;\n    readonly tags: DamageTag[];\n    isCritical: boolean = false;\n    criticalMultiplier: number = 1.0;\n    damageReduction: number = 0;\n    damageAmplification: number = 0;\n    attachedBuffs: IBuff[] = [];\n    source?: string;\n    isDodged: boolean = false;\n\n    constructor(attacker: ICharacter | null, target: ICharacter, baseDamage: number, damageType: DamageType, tags: DamageTag[] = []) {\n        this.id = `damage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        this.attacker = attacker;\n        this.target = target;\n        this.baseDamage = baseDamage;\n        this._finalDamage = baseDamage;\n        this.damageType = damageType;\n        this.tags = [...tags];\n    }\n\n    get finalDamage(): number { return this._finalDamage; }\n    set finalDamage(value: number) { this._finalDamage = value; }\n\n    calculateFinalDamage(): number {\n        return this._finalDamage;\n    }\n\n    addTag(tag: DamageTag): void {\n        if (!this.tags.includes(tag)) {\n            this.tags.push(tag);\n        }\n    }\n\n    hasTag(tag: DamageTag): boolean {\n        return this.tags.includes(tag);\n    }\n\n    addAttachedBuff(buff: IBuff): void {\n        this.attachedBuffs.push(buff);\n    }\n}\n\n/**\n * 伤害管理器\n * 负责处理复杂的伤害计算，包括暴击、抗性、伤害修正等\n */\nexport class DamageManager {\n    private _eventManager: EventManager;\n    private _totalDamageDealt: number = 0;\n    private _totalDamageTaken: number = 0;\n    private _criticalHits: number = 0;\n    private _totalHits: number = 0;\n\n    constructor() {\n        this._eventManager = new EventManager();\n    }\n\n    /**\n     * 处理伤害\n     * @param attacker 攻击者\n     * @param target 目标\n     * @param baseDamage 基础伤害\n     * @param damageType 伤害类型\n     * @param tags 伤害标签\n     * @returns 最终伤害信息\n     */\n    dealDamage(attacker: ICharacter, target: ICharacter, baseDamage: number, damageType: DamageType = DamageType.PHYSICAL, tags: DamageTag[] = []): IDamageInfo {\n        // 创建伤害信息\n        const damageInfo = new DamageInfo(attacker, target, baseDamage, damageType, tags);\n        // 检查闪避\n        if (this.checkDodge(attacker, target)) {\n            damageInfo.isDodged = true;\n            damageInfo.finalDamage = 0;\n            console.log(`${target.characterName} dodged attack from ${attacker.characterName}`);\n            this._eventManager.emit(FightEvent.takeDamage, damageInfo);\n            return damageInfo;\n        }\n        // 计算暴击\n        if (this.checkCritical(attacker, target)) {\n            damageInfo.isCritical = true;\n            const criticalDamage = attacker.attributes.criticalDamage || 1.5;\n            damageInfo.finalDamage *= criticalDamage;\n            this._criticalHits++;\n            console.log(`Critical hit! ${attacker.characterName} deals critical damage to ${target.characterName}`);\n        }\n        // 应用防御力\n        damageInfo.finalDamage = this.applyDefense(damageInfo.finalDamage, target, damageType);\n        // 应用伤害减免\n        damageInfo.finalDamage = this.applyDamageReduction(damageInfo, target);\n        // 应用攻击者的伤害修正（来自Buff等）\n        damageInfo.finalDamage = this.applyAttackerModifiers(damageInfo, attacker);\n        // 应用目标的伤害修正（来自Buff等）\n        damageInfo.finalDamage = this.applyTargetModifiers(damageInfo, target);\n        // 确保伤害不为负数\n        damageInfo.finalDamage = Math.max(0, Math.floor(damageInfo.finalDamage));\n        // 计算实际伤害减免\n        damageInfo.damageReduction = damageInfo.baseDamage - damageInfo.finalDamage;\n        // 更新统计\n        this._totalDamageDealt += damageInfo.finalDamage;\n        this._totalHits++;\n        // 应用伤害到目标\n        if (damageInfo.finalDamage > 0 || damageInfo.isDodged) {\n            target.takeDamage(damageInfo);\n            if (!damageInfo.isDodged) {\n                this._totalDamageTaken += damageInfo.finalDamage;\n            }\n        }\n        console.log(`${attacker.characterName} deals ${damageInfo.finalDamage} ${damageType} damage to ${target.characterName}`);\n        // 触发伤害事件\n        this._eventManager.emit(FightEvent.takeDamage, damageInfo);\n        return damageInfo;\n    }\n\n    /** 检查是否闪避 */\n    private checkDodge(attacker: ICharacter, target: ICharacter): boolean {\n        const hitRate = attacker.attributes.accuracy || 1.0;\n        const dodgeRate = target.attributes.evasion || 0.0;\n        const finalHitRate = Math.max(0, hitRate - dodgeRate);\n        return Math.random() > finalHitRate;\n    }\n\n    /** 检查是否暴击 */\n    private checkCritical(attacker: ICharacter, _target: ICharacter): boolean {\n        const criticalRate = attacker.attributes.criticalRate || 0.0;\n        // 这里可以添加目标的暴击抗性\n        return Math.random() < criticalRate;\n    }\n\n    /** 应用防御力 */\n    private applyDefense(damage: number, target: ICharacter, damageType: DamageType): number {\n        let defense = 0;\n        switch (damageType) {\n            case DamageType.PHYSICAL:\n                defense = target.attributes.defense || 0;\n                break;\n            case DamageType.MAGIC:\n                // 魔法防御力（如果有的话）\n                defense = target.attributes.magicDefense || target.attributes.defense * 0.5;\n                break;\n            case DamageType.TRUE:\n                // 真实伤害无视防御\n                return damage;\n        }\n\n        // 简单的防御计算：伤害 = 基础伤害 - 防御力\n        // 可以改为更复杂的公式，如：伤害 = 基础伤害 * (100 / (100 + 防御力))\n        return Math.max(1, damage - defense);\n    }\n\n    /** 应用伤害减免 */\n    private applyDamageReduction(damageInfo: IDamageInfo, _target: ICharacter): number {\n        let damage = damageInfo.finalDamage;\n        // 这里可以添加基于伤害类型的减免\n        // 例如：火焰抗性、冰霜抗性等\n        return damage;\n    }\n\n    /** 应用攻击者的伤害修正 */\n    private applyAttackerModifiers(damageInfo: IDamageInfo, attacker: ICharacter): number {\n        let damage = damageInfo.finalDamage;\n        // 遍历攻击者的所有Buff，应用伤害修正\n        for (const buff of attacker.buffs) {\n            if (buff.onDealDamage) {\n                const modifiedInfo = buff.onDealDamage(damageInfo, damageInfo.target);\n                damage = modifiedInfo.finalDamage;\n            }\n        }\n        return damage;\n    }\n\n    /** 应用目标的伤害修正 */\n    private applyTargetModifiers(damageInfo: IDamageInfo, target: ICharacter): number {\n        let damage = damageInfo.finalDamage;\n        // 遍历目标的所有Buff，应用伤害修正\n        for (const buff of target.buffs) {\n            if (buff.onTakeDamage) {\n                const modifiedInfo = buff.onTakeDamage(damageInfo, damageInfo.attacker);\n                damage = modifiedInfo.finalDamage;\n            }\n        }\n        return damage;\n    }\n\n    /** 获取统计信息 */\n    getStats() {\n        return {\n            totalDamageDealt: this._totalDamageDealt,\n            totalDamageTaken: this._totalDamageTaken,\n            totalHits: this._totalHits,\n            criticalHits: this._criticalHits,\n            criticalRate: this._totalHits > 0 ? this._criticalHits / this._totalHits : 0\n        };\n    }\n\n    /** 重置统计信息 */\n    resetStats(): void {\n        this._totalDamageDealt = 0;\n        this._totalDamageTaken = 0;\n        this._criticalHits = 0;\n        this._totalHits = 0;\n    }\n\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n\n    /** 清理管理器 */\n    cleanup(): void {\n        this.resetStats();\n        this._eventManager.cleanup();\n    }\n}\n"]}