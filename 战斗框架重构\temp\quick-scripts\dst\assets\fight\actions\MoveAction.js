
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/actions/MoveAction.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1dcdeLVGHVOkprcWuIMfVRR', 'MoveAction');
// fight/actions/MoveAction.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoveAction = exports.MoveState = exports.MoveType = void 0;
var FightEvent_1 = require("../types/FightEvent");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 移动类型枚举*/
var MoveType;
(function (MoveType) {
    /** 瞬间移动 */
    MoveType["INSTANT"] = "instant";
    /** 平滑移动 */
    MoveType["SMOOTH"] = "smooth";
    /** 跟随移动 */
    MoveType["FOLLOW"] = "follow";
})(MoveType = exports.MoveType || (exports.MoveType = {}));
/*** 移动状态枚举*/
var MoveState;
(function (MoveState) {
    /** 静止 */
    MoveState["IDLE"] = "idle";
    /** 移动中 */
    MoveState["MOVING"] = "moving";
    /** 跟随中 */
    MoveState["FOLLOWING"] = "following";
})(MoveState = exports.MoveState || (exports.MoveState = {}));
/** * 移动动作类 */
var MoveAction = /** @class */ (function (_super) {
    __extends(MoveAction, _super);
    function MoveAction() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**当前移动状态 */
        _this._currentState = MoveState.IDLE;
        /**移动权限 */
        _this._canMove = true;
        /**当前速度 */
        _this._velocity = cc.Vec3.ZERO;
        /**跟随配置 */
        _this._followConfig = null;
        /**移动队列 */
        _this._moveQueue = [];
        /**当前移动目标和配置 */
        _this._currentTarget = null;
        _this._currentConfig = null;
        _this._moveStartPosition = null;
        _this._moveProgress = 0;
        /**配置属性 */
        _this.defaultSpeed = 100;
        _this.allowQueue = true;
        _this.maxQueueLength = 5;
        return _this;
    }
    Object.defineProperty(MoveAction.prototype, "currentState", {
        /*** 获取当前移动状态*/
        get: function () { return this._currentState; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "isMoving", {
        /**  * 是否正在移动  */
        get: function () { return this._currentState !== MoveState.IDLE; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "canMove", {
        /** * 是否可以移动 */
        get: function () { return this._canMove; },
        /** * 设置移动权限 */
        set: function (value) {
            this._canMove = value;
            if (!value) {
                this.stopMove();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "velocity", {
        /** * 获取当前速度 */
        get: function () { return this._velocity.clone(); },
        enumerable: false,
        configurable: true
    });
    /**
     * 瞬间移动一个偏移量
     * @param offset 移动偏移量
     */
    MoveAction.prototype.moveBy = function (offset) {
        if (!this._canMove) {
            return;
        }
        this._velocity = offset.clone();
    };
    /**
     * 移动到指定位置
     * @param target 目标位置（世界坐标）
     * @param config 移动配置
     */
    MoveAction.prototype.moveTo = function (target, config) {
        if (!this._canMove) {
            return;
        }
        var moveConfig = __assign({ type: MoveType.SMOOTH, speed: this.defaultSpeed, useEasing: false }, config);
        if (this._currentState === MoveState.IDLE) {
            this.startMoveTo(target, moveConfig);
        }
        else if (this.allowQueue && this._moveQueue.length < this.maxQueueLength) {
            this._moveQueue.push({ target: target.clone(), config: moveConfig });
        }
    };
    /**
     * 开始跟随目标
     * @param config 跟随配置
     */
    MoveAction.prototype.startFollow = function (config) {
        if (!this._canMove) {
            return;
        }
        this.stopMove();
        this._followConfig = __assign({ offset: cc.Vec3.ZERO, speed: this.defaultSpeed, minDistance: 10, maxDistance: 1000 }, config);
        this.setState(MoveState.FOLLOWING);
    };
    /** * 停止跟随 */
    MoveAction.prototype.stopFollow = function () {
        if (this._currentState === MoveState.FOLLOWING) {
            this._followConfig = null;
            this.setState(MoveState.IDLE);
        }
    };
    /** * 停止移动 */
    MoveAction.prototype.stopMove = function () {
        this._velocity = cc.Vec3.ZERO;
        this._currentTarget = null;
        this._currentConfig = null;
        this._moveStartPosition = null;
        this._moveProgress = 0;
        this._moveQueue.length = 0;
        if (this._currentState !== MoveState.IDLE) {
            this.setState(MoveState.IDLE);
        }
    };
    /**
     * 设置移动速度
     * @param speed 速度值
     */
    MoveAction.prototype.setSpeed = function (speed) {
        this.defaultSpeed = Math.max(0, speed);
    };
    /**
     * 获取到目标的距离
     * @param target 目标位置
     * @returns 距离
     */
    MoveAction.prototype.getDistanceToTarget = function (target) {
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        return cc.Vec3.distance(currentPos, target);
    };
    /*** 更新移动逻辑*/
    MoveAction.prototype.update = function (deltaTime) {
        if (!this._canMove) {
            return;
        }
        switch (this._currentState) {
            case MoveState.MOVING:
                this.updateMoving(deltaTime);
                break;
            case MoveState.FOLLOWING:
                this.updateFollowing(deltaTime);
                break;
            case MoveState.IDLE:
                this.updateIdle(deltaTime);
                break;
        }
    };
    /** * 组件禁用时停止移动 */
    MoveAction.prototype.onDisable = function () {
        this.stopMove();
        this.stopFollow();
    };
    /** * 开始移动到目标位置 */
    MoveAction.prototype.startMoveTo = function (target, config) {
        var _a;
        this._currentTarget = target.clone();
        this._currentConfig = config;
        this._moveStartPosition = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        this._moveProgress = 0;
        this.setState(MoveState.MOVING);
        // 触发开始回调
        (_a = config.onStart) === null || _a === void 0 ? void 0 : _a.call(config);
    };
    /**  * 更新移动状态  */
    MoveAction.prototype.updateMoving = function (deltaTime) {
        var _a, _b;
        if (!this._currentTarget || !this._currentConfig || !this._moveStartPosition) {
            this.setState(MoveState.IDLE);
            return;
        }
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var targetPos = this._currentTarget;
        var distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否到达目标
        if (distance <= 5) { // 5像素的容差
            this.completeCurrentMove();
            return;
        }
        // 计算移动
        var speed = this._currentConfig.speed || this.defaultSpeed;
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = speed * deltaTime;
        // 应用移动
        var moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
        // 更新进度
        var totalDistance = cc.Vec3.distance(this._moveStartPosition, targetPos);
        var movedDistance = cc.Vec3.distance(this._moveStartPosition, currentPos);
        this._moveProgress = totalDistance > 0 ? Math.min(1, movedDistance / totalDistance) : 1;
        // 触发进度回调
        (_b = (_a = this._currentConfig).onProgress) === null || _b === void 0 ? void 0 : _b.call(_a, this._moveProgress);
    };
    /** * 更新跟随状态 */
    MoveAction.prototype.updateFollowing = function (deltaTime) {
        if (!this._followConfig || !this._followConfig.target || !this._followConfig.target.isValid) {
            this.stopFollow();
            return;
        }
        var config = this._followConfig;
        var targetPos = config.target.convertToWorldSpaceAR(cc.Vec3.ZERO).add(config.offset);
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否需要移动
        if (distance <= config.minDistance) {
            return; // 太近了，不需要移动
        }
        if (distance > config.maxDistance) {
            // 太远了，直接传送
            this.node.position = this.node.parent.convertToNodeSpaceAR(targetPos);
            return;
        }
        // 计算跟随移动
        var speed = config.speed || this.defaultSpeed;
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = speed * deltaTime;
        // 应用移动
        var moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
    };
    /** * 更新空闲状态 */
    MoveAction.prototype.updateIdle = function (deltaTime) {
        // 处理瞬间移动
        if (!this._velocity.equals(cc.Vec3.ZERO)) {
            this.applyMovement(this._velocity);
            this._velocity = cc.Vec3.ZERO;
        }
        // 处理队列中的移动
        if (this._moveQueue.length > 0) {
            var nextMove = this._moveQueue.shift();
            this.startMoveTo(nextMove.target, nextMove.config);
        }
    };
    /**  * 应用移动  */
    MoveAction.prototype.applyMovement = function (moveVector) {
        var currentWorldPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var targetWorldPos = currentWorldPos.add(moveVector);
        if (this.node.parent) {
            var targetLocalPos = this.node.parent.convertToNodeSpaceAR(targetWorldPos);
            this.node.position = targetLocalPos;
        }
        else {
            this.node.position = targetWorldPos;
        }
    };
    /**  * 完成当前移动  */
    MoveAction.prototype.completeCurrentMove = function () {
        var _a, _b;
        if (this._currentConfig) {
            (_b = (_a = this._currentConfig).onComplete) === null || _b === void 0 ? void 0 : _b.call(_a);
        }
        // 精确设置到目标位置
        if (this._currentTarget && this.node.parent) {
            var targetLocalPos = this.node.parent.convertToNodeSpaceAR(this._currentTarget);
            this.node.position = targetLocalPos;
        }
        this.setState(MoveState.IDLE);
    };
    /** * 设置移动状态 */
    MoveAction.prototype.setState = function (newState) {
        if (this._currentState !== newState) {
            var oldState = this._currentState;
            this._currentState = newState;
            this.onStateChanged(oldState, newState);
        }
    };
    /**  * 状态改变处理  */
    MoveAction.prototype.onStateChanged = function (oldState, newState) {
        // 发送状态改变事件
        this.node.emit(FightEvent_1.default.moveStateChanged, { oldState: oldState, newState: newState, component: this });
        // 清理状态特定的数据
        if (newState === MoveState.IDLE) {
            this._currentTarget = null;
            this._currentConfig = null;
            this._moveStartPosition = null;
            this._moveProgress = 0;
        }
    };
    /** * 获取移动信息 */
    MoveAction.prototype.getMoveInfo = function () {
        var _a, _b;
        return {
            currentState: this._currentState,
            isMoving: this.isMoving,
            canMove: this._canMove,
            velocity: this._velocity,
            queueLength: this._moveQueue.length,
            progress: this._moveProgress,
            hasTarget: !!this._currentTarget,
            isFollowing: this._currentState === MoveState.FOLLOWING,
            followTarget: ((_b = (_a = this._followConfig) === null || _a === void 0 ? void 0 : _a.target) === null || _b === void 0 ? void 0 : _b.name) || null
        };
    };
    __decorate([
        property(cc.Float)
    ], MoveAction.prototype, "defaultSpeed", void 0);
    __decorate([
        property(cc.Boolean)
    ], MoveAction.prototype, "allowQueue", void 0);
    __decorate([
        property(cc.Integer)
    ], MoveAction.prototype, "maxQueueLength", void 0);
    MoveAction = __decorate([
        ccclass
    ], MoveAction);
    return MoveAction;
}(cc.Component));
exports.MoveAction = MoveAction;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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