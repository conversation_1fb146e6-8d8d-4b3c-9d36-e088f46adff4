"use strict";
cc._RF.push(module, '1dcdeLVGHVOkprcWuIMfVRR', 'MoveAction');
// fight/actions/MoveAction.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoveAction = exports.MoveState = exports.MoveType = void 0;
var FightEvent_1 = require("../types/FightEvent");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 移动类型枚举*/
var MoveType;
(function (MoveType) {
    /** 瞬间移动 */
    MoveType["INSTANT"] = "instant";
    /** 平滑移动 */
    MoveType["SMOOTH"] = "smooth";
    /** 跟随移动 */
    MoveType["FOLLOW"] = "follow";
})(MoveType = exports.MoveType || (exports.MoveType = {}));
/*** 移动状态枚举*/
var MoveState;
(function (MoveState) {
    /** 静止 */
    MoveState["IDLE"] = "idle";
    /** 移动中 */
    MoveState["MOVING"] = "moving";
    /** 跟随中 */
    MoveState["FOLLOWING"] = "following";
})(MoveState = exports.MoveState || (exports.MoveState = {}));
/** * 移动动作类 */
var MoveAction = /** @class */ (function (_super) {
    __extends(MoveAction, _super);
    function MoveAction() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**当前移动状态 */
        _this._currentState = MoveState.IDLE;
        /**移动权限 */
        _this._canMove = true;
        /**当前速度 */
        _this._velocity = cc.Vec3.ZERO;
        /**跟随配置 */
        _this._followConfig = null;
        /**移动队列 */
        _this._moveQueue = [];
        /**当前移动目标和配置 */
        _this._currentTarget = null;
        _this._currentConfig = null;
        _this._moveStartPosition = null;
        _this._moveProgress = 0;
        /**配置属性 */
        _this.defaultSpeed = 100;
        _this.allowQueue = true;
        _this.maxQueueLength = 5;
        return _this;
    }
    Object.defineProperty(MoveAction.prototype, "currentState", {
        /*** 获取当前移动状态*/
        get: function () { return this._currentState; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "isMoving", {
        /**  * 是否正在移动  */
        get: function () { return this._currentState !== MoveState.IDLE; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "canMove", {
        /** * 是否可以移动 */
        get: function () { return this._canMove; },
        /** * 设置移动权限 */
        set: function (value) {
            this._canMove = value;
            if (!value) {
                this.stopMove();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MoveAction.prototype, "velocity", {
        /** * 获取当前速度 */
        get: function () { return this._velocity.clone(); },
        enumerable: false,
        configurable: true
    });
    /**
     * 瞬间移动一个偏移量
     * @param offset 移动偏移量
     */
    MoveAction.prototype.moveBy = function (offset) {
        if (!this._canMove) {
            return;
        }
        this._velocity = offset.clone();
    };
    /**
     * 移动到指定位置
     * @param target 目标位置（世界坐标）
     * @param config 移动配置
     */
    MoveAction.prototype.moveTo = function (target, config) {
        if (!this._canMove) {
            return;
        }
        var moveConfig = __assign({ type: MoveType.SMOOTH, speed: this.defaultSpeed, useEasing: false }, config);
        if (this._currentState === MoveState.IDLE) {
            this.startMoveTo(target, moveConfig);
        }
        else if (this.allowQueue && this._moveQueue.length < this.maxQueueLength) {
            this._moveQueue.push({ target: target.clone(), config: moveConfig });
        }
    };
    /**
     * 开始跟随目标
     * @param config 跟随配置
     */
    MoveAction.prototype.startFollow = function (config) {
        if (!this._canMove) {
            return;
        }
        this.stopMove();
        this._followConfig = __assign({ offset: cc.Vec3.ZERO, speed: this.defaultSpeed, minDistance: 10, maxDistance: 1000 }, config);
        this.setState(MoveState.FOLLOWING);
    };
    /** * 停止跟随 */
    MoveAction.prototype.stopFollow = function () {
        if (this._currentState === MoveState.FOLLOWING) {
            this._followConfig = null;
            this.setState(MoveState.IDLE);
        }
    };
    /** * 停止移动 */
    MoveAction.prototype.stopMove = function () {
        this._velocity = cc.Vec3.ZERO;
        this._currentTarget = null;
        this._currentConfig = null;
        this._moveStartPosition = null;
        this._moveProgress = 0;
        this._moveQueue.length = 0;
        if (this._currentState !== MoveState.IDLE) {
            this.setState(MoveState.IDLE);
        }
    };
    /**
     * 设置移动速度
     * @param speed 速度值
     */
    MoveAction.prototype.setSpeed = function (speed) {
        this.defaultSpeed = Math.max(0, speed);
    };
    /**
     * 获取到目标的距离
     * @param target 目标位置
     * @returns 距离
     */
    MoveAction.prototype.getDistanceToTarget = function (target) {
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        return cc.Vec3.distance(currentPos, target);
    };
    /*** 更新移动逻辑*/
    MoveAction.prototype.update = function (deltaTime) {
        if (!this._canMove) {
            return;
        }
        switch (this._currentState) {
            case MoveState.MOVING:
                this.updateMoving(deltaTime);
                break;
            case MoveState.FOLLOWING:
                this.updateFollowing(deltaTime);
                break;
            case MoveState.IDLE:
                this.updateIdle(deltaTime);
                break;
        }
    };
    /** * 组件禁用时停止移动 */
    MoveAction.prototype.onDisable = function () {
        this.stopMove();
        this.stopFollow();
    };
    /** * 开始移动到目标位置 */
    MoveAction.prototype.startMoveTo = function (target, config) {
        var _a;
        this._currentTarget = target.clone();
        this._currentConfig = config;
        this._moveStartPosition = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        this._moveProgress = 0;
        this.setState(MoveState.MOVING);
        // 触发开始回调
        (_a = config.onStart) === null || _a === void 0 ? void 0 : _a.call(config);
    };
    /**  * 更新移动状态  */
    MoveAction.prototype.updateMoving = function (deltaTime) {
        var _a, _b;
        if (!this._currentTarget || !this._currentConfig || !this._moveStartPosition) {
            this.setState(MoveState.IDLE);
            return;
        }
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var targetPos = this._currentTarget;
        var distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否到达目标
        if (distance <= 5) { // 5像素的容差
            this.completeCurrentMove();
            return;
        }
        // 计算移动
        var speed = this._currentConfig.speed || this.defaultSpeed;
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = speed * deltaTime;
        // 应用移动
        var moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
        // 更新进度
        var totalDistance = cc.Vec3.distance(this._moveStartPosition, targetPos);
        var movedDistance = cc.Vec3.distance(this._moveStartPosition, currentPos);
        this._moveProgress = totalDistance > 0 ? Math.min(1, movedDistance / totalDistance) : 1;
        // 触发进度回调
        (_b = (_a = this._currentConfig).onProgress) === null || _b === void 0 ? void 0 : _b.call(_a, this._moveProgress);
    };
    /** * 更新跟随状态 */
    MoveAction.prototype.updateFollowing = function (deltaTime) {
        if (!this._followConfig || !this._followConfig.target || !this._followConfig.target.isValid) {
            this.stopFollow();
            return;
        }
        var config = this._followConfig;
        var targetPos = config.target.convertToWorldSpaceAR(cc.Vec3.ZERO).add(config.offset);
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否需要移动
        if (distance <= config.minDistance) {
            return; // 太近了，不需要移动
        }
        if (distance > config.maxDistance) {
            // 太远了，直接传送
            this.node.position = this.node.parent.convertToNodeSpaceAR(targetPos);
            return;
        }
        // 计算跟随移动
        var speed = config.speed || this.defaultSpeed;
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = speed * deltaTime;
        // 应用移动
        var moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
    };
    /** * 更新空闲状态 */
    MoveAction.prototype.updateIdle = function (deltaTime) {
        // 处理瞬间移动
        if (!this._velocity.equals(cc.Vec3.ZERO)) {
            this.applyMovement(this._velocity);
            this._velocity = cc.Vec3.ZERO;
        }
        // 处理队列中的移动
        if (this._moveQueue.length > 0) {
            var nextMove = this._moveQueue.shift();
            this.startMoveTo(nextMove.target, nextMove.config);
        }
    };
    /**  * 应用移动  */
    MoveAction.prototype.applyMovement = function (moveVector) {
        var currentWorldPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var targetWorldPos = currentWorldPos.add(moveVector);
        if (this.node.parent) {
            var targetLocalPos = this.node.parent.convertToNodeSpaceAR(targetWorldPos);
            this.node.position = targetLocalPos;
        }
        else {
            this.node.position = targetWorldPos;
        }
    };
    /**  * 完成当前移动  */
    MoveAction.prototype.completeCurrentMove = function () {
        var _a, _b;
        if (this._currentConfig) {
            (_b = (_a = this._currentConfig).onComplete) === null || _b === void 0 ? void 0 : _b.call(_a);
        }
        // 精确设置到目标位置
        if (this._currentTarget && this.node.parent) {
            var targetLocalPos = this.node.parent.convertToNodeSpaceAR(this._currentTarget);
            this.node.position = targetLocalPos;
        }
        this.setState(MoveState.IDLE);
    };
    /** * 设置移动状态 */
    MoveAction.prototype.setState = function (newState) {
        if (this._currentState !== newState) {
            var oldState = this._currentState;
            this._currentState = newState;
            this.onStateChanged(oldState, newState);
        }
    };
    /**  * 状态改变处理  */
    MoveAction.prototype.onStateChanged = function (oldState, newState) {
        // 发送状态改变事件
        this.node.emit(FightEvent_1.default.moveStateChanged, { oldState: oldState, newState: newState, component: this });
        // 清理状态特定的数据
        if (newState === MoveState.IDLE) {
            this._currentTarget = null;
            this._currentConfig = null;
            this._moveStartPosition = null;
            this._moveProgress = 0;
        }
    };
    /** * 获取移动信息 */
    MoveAction.prototype.getMoveInfo = function () {
        var _a, _b;
        return {
            currentState: this._currentState,
            isMoving: this.isMoving,
            canMove: this._canMove,
            velocity: this._velocity,
            queueLength: this._moveQueue.length,
            progress: this._moveProgress,
            hasTarget: !!this._currentTarget,
            isFollowing: this._currentState === MoveState.FOLLOWING,
            followTarget: ((_b = (_a = this._followConfig) === null || _a === void 0 ? void 0 : _a.target) === null || _b === void 0 ? void 0 : _b.name) || null
        };
    };
    __decorate([
        property(cc.Float)
    ], MoveAction.prototype, "defaultSpeed", void 0);
    __decorate([
        property(cc.Boolean)
    ], MoveAction.prototype, "allowQueue", void 0);
    __decorate([
        property(cc.Integer)
    ], MoveAction.prototype, "maxQueueLength", void 0);
    MoveAction = __decorate([
        ccclass
    ], MoveAction);
    return MoveAction;
}(cc.Component));
exports.MoveAction = MoveAction;

cc._RF.pop();