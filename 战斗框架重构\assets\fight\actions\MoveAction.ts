import FightEvent from "../types/FightEvent";

const { ccclass, property } = cc._decorator;

/*** 移动类型枚举*/
export enum MoveType {
    /** 瞬间移动 */
    INSTANT = "instant",
    /** 平滑移动 */
    SMOOTH = "smooth",
    /** 跟随移动 */
    FOLLOW = "follow"
}
/*** 移动状态枚举*/
export enum MoveState {
    /** 静止 */
    IDLE = "idle",
    /** 移动中 */
    MOVING = "moving",
    /** 跟随中 */
    FOLLOWING = "following"
}
/*** 移动配置接口*/
export interface IMoveConfig {
    /** 移动类型 */
    type: MoveType;
    /** 移动速度（像素/秒） */
    speed?: number;
    /** 是否使用缓动 */
    useEasing?: boolean;
    /** 缓动类型 */
    easingType?: string;
    /** 移动完成回调 */
    onComplete?: () => void;
    /** 移动开始回调 */
    onStart?: () => void;
    /** 移动进度回调 */
    onProgress?: (progress: number) => void;
}
/** * 跟随配置接口 */
export interface IFollowConfig {
    /** 跟随目标 */
    target: cc.Node;
    /** 跟随偏移 */
    offset: cc.Vec3;
    /** 跟随速度 */
    speed?: number;
    /** 最小跟随距离 */
    minDistance?: number;
    /** 最大跟随距离 */
    maxDistance?: number;
}
/** * 移动动作类 */
@ccclass
export class MoveAction extends cc.Component {
    /**当前移动状态 */
    private _currentState: MoveState = MoveState.IDLE;
    /**移动权限 */
    private _canMove: boolean = true;
    /**当前速度 */
    private _velocity: cc.Vec3 = cc.Vec3.ZERO;
    /**跟随配置 */
    private _followConfig: IFollowConfig = null;
    /**移动队列 */
    private _moveQueue: Array<{ target: cc.Vec3; config: IMoveConfig }> = [];
    /**当前移动目标和配置 */
    private _currentTarget: cc.Vec3 = null;
    private _currentConfig: IMoveConfig = null;
    private _moveStartPosition: cc.Vec3 = null;
    private _moveProgress: number = 0;

    /**配置属性 */
    @property(cc.Float)
    defaultSpeed: number = 100;
    @property(cc.Boolean)
    allowQueue: boolean = true;
    @property(cc.Integer)
    maxQueueLength: number = 5;
    /*** 获取当前移动状态*/
    get currentState(): MoveState { return this._currentState; }
    /**  * 是否正在移动  */
    get isMoving(): boolean { return this._currentState !== MoveState.IDLE; }
    /** * 是否可以移动 */
    get canMove(): boolean { return this._canMove; }

    /** * 设置移动权限 */
    set canMove(value: boolean) {
        this._canMove = value;
        if (!value) {
            this.stopMove();
        }
    }

    /** * 获取当前速度 */
    get velocity(): cc.Vec3 { return this._velocity.clone(); }
    /**
     * 瞬间移动一个偏移量
     * @param offset 移动偏移量
     */
    moveBy(offset: cc.Vec3): void {
        if (!this._canMove) {
            return;
        }
        this._velocity = offset.clone();
    }

    /**
     * 移动到指定位置
     * @param target 目标位置（世界坐标）
     * @param config 移动配置
     */
    moveTo(target: cc.Vec3, config?: IMoveConfig): void {
        if (!this._canMove) {
            return;
        }
        const moveConfig: IMoveConfig = {
            type: MoveType.SMOOTH,
            speed: this.defaultSpeed,
            useEasing: false,
            ...config
        };
        if (this._currentState === MoveState.IDLE) {
            this.startMoveTo(target, moveConfig);
        } else if (this.allowQueue && this._moveQueue.length < this.maxQueueLength) {
            this._moveQueue.push({ target: target.clone(), config: moveConfig });
        }
    }

    /**
     * 开始跟随目标
     * @param config 跟随配置
     */
    startFollow(config: IFollowConfig): void {
        if (!this._canMove) {
            return;
        }
        this.stopMove();
        this._followConfig = {
            offset: cc.Vec3.ZERO,
            speed: this.defaultSpeed,
            minDistance: 10,
            maxDistance: 1000,
            ...config
        };
        this.setState(MoveState.FOLLOWING);
    }

    /** * 停止跟随 */
    stopFollow(): void {
        if (this._currentState === MoveState.FOLLOWING) {
            this._followConfig = null;
            this.setState(MoveState.IDLE);
        }
    }
    /** * 停止移动 */
    stopMove(): void {
        this._velocity = cc.Vec3.ZERO;
        this._currentTarget = null;
        this._currentConfig = null;
        this._moveStartPosition = null;
        this._moveProgress = 0;
        this._moveQueue.length = 0;
        if (this._currentState !== MoveState.IDLE) {
            this.setState(MoveState.IDLE);
        }
    }
    /**
     * 设置移动速度
     * @param speed 速度值
     */
    setSpeed(speed: number): void {
        this.defaultSpeed = Math.max(0, speed);
    }
    /**
     * 获取到目标的距离
     * @param target 目标位置
     * @returns 距离
     */
    getDistanceToTarget(target: cc.Vec3): number {
        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        return cc.Vec3.distance(currentPos, target);
    }
    /*** 更新移动逻辑*/
    protected update(deltaTime: number): void {
        if (!this._canMove) {
            return;
        }
        switch (this._currentState) {
            case MoveState.MOVING:
                this.updateMoving(deltaTime);
                break;
            case MoveState.FOLLOWING:
                this.updateFollowing(deltaTime);
                break;
            case MoveState.IDLE:
                this.updateIdle(deltaTime);
                break;
        }
    }
    /** * 组件禁用时停止移动 */
    protected onDisable(): void {
        this.stopMove();
        this.stopFollow();
    }
    /** * 开始移动到目标位置 */
    private startMoveTo(target: cc.Vec3, config: IMoveConfig): void {
        this._currentTarget = target.clone();
        this._currentConfig = config;
        this._moveStartPosition = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        this._moveProgress = 0;
        this.setState(MoveState.MOVING);
        // 触发开始回调
        config.onStart?.();
    }
    /**  * 更新移动状态  */
    private updateMoving(deltaTime: number): void {
        if (!this._currentTarget || !this._currentConfig || !this._moveStartPosition) {
            this.setState(MoveState.IDLE);
            return;
        }
        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const targetPos = this._currentTarget;
        const distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否到达目标
        if (distance <= 5) { // 5像素的容差
            this.completeCurrentMove();
            return;
        }
        // 计算移动
        const speed = this._currentConfig.speed || this.defaultSpeed;
        const direction = targetPos.subtract(currentPos).normalize();
        const moveDistance = speed * deltaTime;
        // 应用移动
        const moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
        // 更新进度
        const totalDistance = cc.Vec3.distance(this._moveStartPosition, targetPos);
        const movedDistance = cc.Vec3.distance(this._moveStartPosition, currentPos);
        this._moveProgress = totalDistance > 0 ? Math.min(1, movedDistance / totalDistance) : 1;
        // 触发进度回调
        this._currentConfig.onProgress?.(this._moveProgress);
    }
    /** * 更新跟随状态 */
    private updateFollowing(deltaTime: number): void {
        if (!this._followConfig || !this._followConfig.target || !this._followConfig.target.isValid) {
            this.stopFollow();
            return;
        }
        const config = this._followConfig;
        const targetPos = config.target.convertToWorldSpaceAR(cc.Vec3.ZERO).add(config.offset);
        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const distance = cc.Vec3.distance(currentPos, targetPos);
        // 检查是否需要移动
        if (distance <= config.minDistance!) {
            return; // 太近了，不需要移动
        }
        if (distance > config.maxDistance!) {
            // 太远了，直接传送
            this.node.position = this.node.parent!.convertToNodeSpaceAR(targetPos);
            return;
        }
        // 计算跟随移动
        const speed = config.speed || this.defaultSpeed;
        const direction = targetPos.subtract(currentPos).normalize();
        const moveDistance = speed * deltaTime;
        // 应用移动
        const moveVector = direction.multiplyScalar(moveDistance);
        this.applyMovement(moveVector);
    }

    /** * 更新空闲状态 */
    private updateIdle(deltaTime: number): void {
        // 处理瞬间移动
        if (!this._velocity.equals(cc.Vec3.ZERO)) {
            this.applyMovement(this._velocity);
            this._velocity = cc.Vec3.ZERO;
        }
        // 处理队列中的移动
        if (this._moveQueue.length > 0) {
            const nextMove = this._moveQueue.shift()!;
            this.startMoveTo(nextMove.target, nextMove.config);
        }
    }
    /**  * 应用移动  */
    private applyMovement(moveVector: cc.Vec3): void {
        const currentWorldPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const targetWorldPos = currentWorldPos.add(moveVector);
        if (this.node.parent) {
            const targetLocalPos = this.node.parent.convertToNodeSpaceAR(targetWorldPos);
            this.node.position = targetLocalPos;
        } else {
            this.node.position = targetWorldPos;
        }
    }
    /**  * 完成当前移动  */
    private completeCurrentMove(): void {
        if (this._currentConfig) {
            this._currentConfig.onComplete?.();
        }
        // 精确设置到目标位置
        if (this._currentTarget && this.node.parent) {
            const targetLocalPos = this.node.parent.convertToNodeSpaceAR(this._currentTarget);
            this.node.position = targetLocalPos;
        }
        this.setState(MoveState.IDLE);
    }
    /** * 设置移动状态 */
    private setState(newState: MoveState): void {
        if (this._currentState !== newState) {
            const oldState = this._currentState;
            this._currentState = newState;
            this.onStateChanged(oldState, newState);
        }
    }
    /**  * 状态改变处理  */
    private onStateChanged(oldState: MoveState, newState: MoveState): void {
        // 发送状态改变事件
        this.node.emit(FightEvent.moveStateChanged, { oldState, newState, component: this });
        // 清理状态特定的数据
        if (newState === MoveState.IDLE) {
            this._currentTarget = null;
            this._currentConfig = null;
            this._moveStartPosition = null;
            this._moveProgress = 0;
        }
    }
    /** * 获取移动信息 */
    getMoveInfo() {
        return {
            currentState: this._currentState,
            isMoving: this.isMoving,
            canMove: this._canMove,
            velocity: this._velocity,
            queueLength: this._moveQueue.length,
            progress: this._moveProgress,
            hasTarget: !!this._currentTarget,
            isFollowing: this._currentState === MoveState.FOLLOWING,
            followTarget: this._followConfig?.target?.name || null
        };
    }
}
