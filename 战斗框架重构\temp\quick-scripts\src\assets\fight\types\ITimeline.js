"use strict";
cc._RF.push(module, 'fd4223f3bNB0I38SwNlmGH6', 'ITimeline');
// fight/types/ITimeline.ts

"use strict";
/**
 * Timeline系统接口定义
 * 基于时间轴的技能效果系统，支持复杂的时序控制
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineEventType = void 0;
/*** Timeline事件类型枚举*/
var TimelineEventType;
(function (TimelineEventType) {
    /** 伤害事件 */
    TimelineEventType["DAMAGE"] = "damage";
    /** 治疗事件 */
    TimelineEventType["HEAL"] = "heal";
    /** 子弹发射事件 */
    TimelineEventType["FIRE_BULLET"] = "fire_bullet";
    /** Buff添加事件 */
    TimelineEventType["ADD_BUFF"] = "add_buff";
    /** Buff移除事件 */
    TimelineEventType["REMOVE_BUFF"] = "remove_buff";
    /** 动画播放事件 */
    TimelineEventType["PLAY_ANIMATION"] = "play_animation";
    /** 音效播放事件 */
    TimelineEventType["PLAY_SOUND"] = "play_sound";
    /** 特效播放事件 */
    TimelineEventType["PLAY_EFFECT"] = "play_effect";
    /** 移动事件 */
    TimelineEventType["MOVE"] = "move";
    /** 传送事件 */
    TimelineEventType["TELEPORT"] = "teleport";
    /** 召唤事件 */
    TimelineEventType["SUMMON"] = "summon";
    /** 自定义事件 */
    TimelineEventType["CUSTOM"] = "custom";
})(TimelineEventType = exports.TimelineEventType || (exports.TimelineEventType = {}));

cc._RF.pop();