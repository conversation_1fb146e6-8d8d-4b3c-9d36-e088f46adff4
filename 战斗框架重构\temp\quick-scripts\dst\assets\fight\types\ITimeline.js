
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/ITimeline.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fd4223f3bNB0I38SwNlmGH6', 'ITimeline');
// fight/types/ITimeline.ts

"use strict";
/**
 * Timeline系统接口定义
 * 基于时间轴的技能效果系统，支持复杂的时序控制
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineEventType = void 0;
/*** Timeline事件类型枚举*/
var TimelineEventType;
(function (TimelineEventType) {
    /** 伤害事件 */
    TimelineEventType["DAMAGE"] = "damage";
    /** 治疗事件 */
    TimelineEventType["HEAL"] = "heal";
    /** 子弹发射事件 */
    TimelineEventType["FIRE_BULLET"] = "fire_bullet";
    /** Buff添加事件 */
    TimelineEventType["ADD_BUFF"] = "add_buff";
    /** Buff移除事件 */
    TimelineEventType["REMOVE_BUFF"] = "remove_buff";
    /** 动画播放事件 */
    TimelineEventType["PLAY_ANIMATION"] = "play_animation";
    /** 音效播放事件 */
    TimelineEventType["PLAY_SOUND"] = "play_sound";
    /** 特效播放事件 */
    TimelineEventType["PLAY_EFFECT"] = "play_effect";
    /** 移动事件 */
    TimelineEventType["MOVE"] = "move";
    /** 传送事件 */
    TimelineEventType["TELEPORT"] = "teleport";
    /** 召唤事件 */
    TimelineEventType["SUMMON"] = "summon";
    /** 自定义事件 */
    TimelineEventType["CUSTOM"] = "custom";
})(TimelineEventType = exports.TimelineEventType || (exports.TimelineEventType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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