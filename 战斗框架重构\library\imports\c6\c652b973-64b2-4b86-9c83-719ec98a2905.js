"use strict";
cc._RF.push(module, 'c652blzZLJLhpyDcZ7JiikF', 'CharacterAttributes');
// fight/characters/CharacterAttributes.ts

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterAttributes = void 0;
var EventManager_1 = require("../systems/EventManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
/*** 角色属性实现*/
var CharacterAttributes = /** @class */ (function () {
    function CharacterAttributes(initialData) {
        /**属性修改器 */
        this._modifiers = new Map();
        this._eventManager = EventManager_1.EventManager.createLocal("CharacterAttributes");
        this.initializeAttributes(initialData);
    }
    Object.defineProperty(CharacterAttributes.prototype, "currentHp", {
        // ICharacterAttributes 接口实现
        get: function () { return this._resourceData.currentHp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxHp", {
        get: function () { return this._currentAttributes.maxHp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attack", {
        get: function () { return this._currentAttributes.attack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "defense", {
        get: function () { return this._currentAttributes.defense; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attackSpeed", {
        get: function () { return this._currentAttributes.attackSpeed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "moveSpeed", {
        get: function () { return this._currentAttributes.moveSpeed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attackRange", {
        get: function () { return this._currentAttributes.attackRange; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "level", {
        get: function () { return this._currentAttributes.level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "currentMp", {
        // ICharacterResource 接口实现
        get: function () { return this._resourceData.currentMp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxMp", {
        get: function () { return this._currentAttributes.maxMp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "currentStamina", {
        get: function () { return this._resourceData.currentStamina; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxStamina", {
        get: function () { return this._resourceData.maxStamina; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "magicAttack", {
        // 额外属性访问器
        get: function () { return this._currentAttributes.attack; } // 暂时使用attack作为magicAttack
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "magicDefense", {
        get: function () { return this._currentAttributes.defense; } // 暂时使用defense作为magicDefense
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "speed", {
        get: function () { return this._currentAttributes.moveSpeed; } // 暂时使用moveSpeed作为speed
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "accuracy", {
        get: function () { return this._currentAttributes.hitRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "evasion", {
        get: function () { return this._currentAttributes.dodgeRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "criticalRate", {
        get: function () { return this._currentAttributes.criticalRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "criticalDamage", {
        get: function () { return this._currentAttributes.criticalDamage; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "hitRate", {
        get: function () { return this._currentAttributes.hitRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "dodgeRate", {
        get: function () { return this._currentAttributes.dodgeRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "experience", {
        get: function () { return this._currentAttributes.experience; },
        enumerable: false,
        configurable: true
    });
    /** * 初始化属性 */
    CharacterAttributes.prototype.initializeAttributes = function (initialData) {
        // 设置默认值
        var defaultAttributes = {
            hp: 100,
            maxHp: 100,
            mp: 50,
            maxMp: 50,
            maxStamina: 100,
            attack: 10,
            defense: 5,
            attackSpeed: 1,
            moveSpeed: 100,
            attackRange: 150,
            criticalRate: 0.05,
            criticalDamage: 1.5,
            hitRate: 0.95,
            dodgeRate: 0.05,
            level: 1,
            experience: 0
        };
        // 合并初始数据
        this._baseAttributes = __assign(__assign({}, defaultAttributes), initialData);
        this._currentAttributes = __assign({}, this._baseAttributes);
        // 初始化资源数据
        this._resourceData = {
            currentHp: this._baseAttributes.hp,
            currentMp: this._baseAttributes.mp,
            currentStamina: this._baseAttributes.maxStamina || 100,
            maxStamina: this._baseAttributes.maxStamina || 100
        };
    };
    /*** 修改生命值*/
    CharacterAttributes.prototype.modifyHp = function (amount) {
        var oldHp = this._resourceData.currentHp;
        this._resourceData.currentHp = Math.max(0, Math.min(this.maxHp, this._resourceData.currentHp + amount));
        if (oldHp !== this._resourceData.currentHp) {
            this._eventManager.emit(FightEvent_1.default.hpChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentHp,
                oldValue: oldHp,
                newValue: this._resourceData.currentHp,
                delta: this._resourceData.currentHp - oldHp
            });
        }
    };
    /** * 修改魔法值 */
    CharacterAttributes.prototype.modifyMp = function (amount) {
        var oldMp = this._resourceData.currentMp;
        this._resourceData.currentMp = Math.max(0, Math.min(this.maxMp, this._resourceData.currentMp + amount));
        if (oldMp !== this._resourceData.currentMp) {
            this._eventManager.emit(FightEvent_1.default.mpChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentMp,
                oldValue: oldMp,
                newValue: this._resourceData.currentMp,
                delta: this._resourceData.currentMp - oldMp
            });
        }
    };
    /** * 修改耐力 */
    CharacterAttributes.prototype.modifyStamina = function (amount) {
        var oldStamina = this._resourceData.currentStamina;
        this._resourceData.currentStamina = Math.max(0, Math.min(this.maxStamina, this._resourceData.currentStamina + amount));
        if (oldStamina !== this._resourceData.currentStamina) {
            this._eventManager.emit(FightEvent_1.default.staminaChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentStamina,
                oldValue: oldStamina,
                newValue: this._resourceData.currentStamina,
                delta: this._resourceData.currentStamina - oldStamina
            });
        }
    };
    /** * 获取生命值百分比 */
    CharacterAttributes.prototype.getHpPercentage = function () {
        return this.maxHp > 0 ? this._resourceData.currentHp / this.maxHp : 0;
    };
    /** * 是否死亡 */
    CharacterAttributes.prototype.isDead = function () {
        return this._resourceData.currentHp <= 0;
    };
    /** * 重置到满血状态 */
    CharacterAttributes.prototype.resetToFull = function () {
        this.modifyHp(this.maxHp - this._resourceData.currentHp);
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    };
    /** * 检查是否有足够的魔法值 */
    CharacterAttributes.prototype.hasEnoughMp = function (amount) {
        return this._resourceData.currentMp >= amount;
    };
    /** * 检查是否有足够的耐力 */
    CharacterAttributes.prototype.hasEnoughStamina = function (amount) {
        return this._resourceData.currentStamina >= amount;
    };
    /** * 获取魔法值百分比 */
    CharacterAttributes.prototype.getMpPercentage = function () {
        return this.maxMp > 0 ? this._resourceData.currentMp / this.maxMp : 0;
    };
    /**  * 获取耐力百分比  */
    CharacterAttributes.prototype.getStaminaPercentage = function () {
        return this.maxStamina > 0 ? this._resourceData.currentStamina / this.maxStamina : 0;
    };
    /** * 重置资源到满值状态 */
    CharacterAttributes.prototype.resetResourcesToFull = function () {
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    };
    /** * 添加属性修改器 */
    CharacterAttributes.prototype.addModifier = function (modifier) {
        this._modifiers.set(modifier.id, modifier);
        modifier.apply(this);
        this.recalculateAttributes();
    };
    /*** 移除属性修改器  */
    CharacterAttributes.prototype.removeModifier = function (modifierId) {
        var modifier = this._modifiers.get(modifierId);
        if (modifier) {
            modifier.remove(this);
            this._modifiers.delete(modifierId);
            this.recalculateAttributes();
        }
    };
    /** * 获取属性修改器 */
    CharacterAttributes.prototype.getModifier = function (modifierId) {
        return this._modifiers.get(modifierId);
    };
    /*** 更新所有修改器*/
    CharacterAttributes.prototype.updateModifiers = function (deltaTime) {
        var e_1, _a;
        var expiredModifiers = [];
        this._modifiers.forEach(function (modifier, id) {
            if (modifier.update(deltaTime)) {
                expiredModifiers.push(id);
            }
        });
        try {
            // 移除过期的修改器
            for (var expiredModifiers_1 = __values(expiredModifiers), expiredModifiers_1_1 = expiredModifiers_1.next(); !expiredModifiers_1_1.done; expiredModifiers_1_1 = expiredModifiers_1.next()) {
                var id = expiredModifiers_1_1.value;
                this.removeModifier(id);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (expiredModifiers_1_1 && !expiredModifiers_1_1.done && (_a = expiredModifiers_1.return)) _a.call(expiredModifiers_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** * 重新计算属性 */
    CharacterAttributes.prototype.recalculateAttributes = function () {
        var _this = this;
        // 重置为基础属性
        this._currentAttributes = __assign({}, this._baseAttributes);
        // 应用所有修改器
        this._modifiers.forEach(function (modifier) {
            modifier.apply(_this);
        });
        // 确保资源不超过最大值
        this._resourceData.currentHp = Math.min(this._resourceData.currentHp, this.maxHp);
        this._resourceData.currentMp = Math.min(this._resourceData.currentMp, this.maxMp);
        this._resourceData.currentStamina = Math.min(this._resourceData.currentStamina, this.maxStamina);
    };
    /** * 直接设置属性值（用于修改器） */
    CharacterAttributes.prototype.setAttributeValue = function (attributeName, value) {
        var key = attributeName;
        if (key in this._currentAttributes) {
            var oldValue = this._currentAttributes[key];
            this._currentAttributes[key] = value;
            this._eventManager.emit(FightEvent_1.default.attributeChanged, { attributeName: attributeName, oldValue: oldValue, newValue: value, delta: value - oldValue });
        }
    };
    /** * 获取基础属性值 */
    CharacterAttributes.prototype.getBaseAttributeValue = function (attributeName) {
        return this._baseAttributes[attributeName];
    };
    /**  * 获取当前属性值  */
    CharacterAttributes.prototype.getCurrentAttributeValue = function (attributeName) {
        var key = attributeName;
        if (key in this._currentAttributes) {
            return this._currentAttributes[key];
        }
        return 0;
    };
    Object.defineProperty(CharacterAttributes.prototype, "eventManager", {
        /** * 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 获取属性数据的副本 */
    CharacterAttributes.prototype.getAttributeData = function () {
        return __assign({}, this._currentAttributes);
    };
    /**  * 获取资源数据的副本  */
    CharacterAttributes.prototype.getResourceData = function () {
        return __assign({}, this._resourceData);
    };
    /** 消耗魔法值 */
    CharacterAttributes.prototype.consumeMp = function (amount) {
        this.modifyMp(-amount);
    };
    /** 消耗耐力 */
    CharacterAttributes.prototype.consumeStamina = function (amount) {
        this.modifyStamina(-amount);
    };
    /** 造成伤害 */
    CharacterAttributes.prototype.takeDamage = function (amount) {
        this.modifyHp(-amount);
    };
    /** * 清理资源 */
    CharacterAttributes.prototype.cleanup = function () {
        this._modifiers.clear();
        this._eventManager.cleanup();
    };
    return CharacterAttributes;
}());
exports.CharacterAttributes = CharacterAttributes;

cc._RF.pop();