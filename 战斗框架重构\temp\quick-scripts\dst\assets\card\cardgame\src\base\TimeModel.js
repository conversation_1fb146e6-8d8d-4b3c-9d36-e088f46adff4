
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/TimeModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1ff16ajd85Dg56Rc5p3oHun', 'TimeModel');
// card/cardgame/src/base/TimeModel.ts

// import { desLoadDataProxy } from "@judu233/cc-vm-core";
// @desLoadDataProxy('TimeModel', 'TimeModel')
// export class TimeModel {
//     /**是否是第一次进入游戏 */
//     static isFirstInGame = true;
//     /*第一次进入的游戏时间 */
//     static firstInDate: Date = null;
//     /**是否是当天第一次进入游戏 */
//     static isTodayFirstInGame = true;
//     /**当天第一次进入的游戏时间 */
//     static todayFirstInDate: Date = null;
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcY2FyZFxcY2FyZGdhbWVcXHNyY1xcYmFzZVxcVGltZU1vZGVsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBEQUEwRDtBQUUxRCw4Q0FBOEM7QUFDOUMsMkJBQTJCO0FBQzNCLHVCQUF1QjtBQUN2QixtQ0FBbUM7QUFDbkMsc0JBQXNCO0FBQ3RCLHVDQUF1QztBQUN2Qyx5QkFBeUI7QUFDekIsd0NBQXdDO0FBQ3hDLHlCQUF5QjtBQUN6Qiw0Q0FBNEM7QUFDNUMsSUFBSSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIi8vIGltcG9ydCB7IGRlc0xvYWREYXRhUHJveHkgfSBmcm9tIFwiQGp1ZHUyMzMvY2Mtdm0tY29yZVwiO1xyXG5cclxuLy8gQGRlc0xvYWREYXRhUHJveHkoJ1RpbWVNb2RlbCcsICdUaW1lTW9kZWwnKVxyXG4vLyBleHBvcnQgY2xhc3MgVGltZU1vZGVsIHtcclxuLy8gICAgIC8qKuaYr+WQpuaYr+esrOS4gOasoei/m+WFpea4uOaIjyAqL1xyXG4vLyAgICAgc3RhdGljIGlzRmlyc3RJbkdhbWUgPSB0cnVlO1xyXG4vLyAgICAgLyrnrKzkuIDmrKHov5vlhaXnmoTmuLjmiI/ml7bpl7QgKi9cclxuLy8gICAgIHN0YXRpYyBmaXJzdEluRGF0ZTogRGF0ZSA9IG51bGw7XHJcbi8vICAgICAvKirmmK/lkKbmmK/lvZPlpKnnrKzkuIDmrKHov5vlhaXmuLjmiI8gKi9cclxuLy8gICAgIHN0YXRpYyBpc1RvZGF5Rmlyc3RJbkdhbWUgPSB0cnVlO1xyXG4vLyAgICAgLyoq5b2T5aSp56ys5LiA5qyh6L+b5YWl55qE5ri45oiP5pe26Ze0ICovXHJcbi8vICAgICBzdGF0aWMgdG9kYXlGaXJzdEluRGF0ZTogRGF0ZSA9IG51bGw7XHJcbi8vIH0iXX0=