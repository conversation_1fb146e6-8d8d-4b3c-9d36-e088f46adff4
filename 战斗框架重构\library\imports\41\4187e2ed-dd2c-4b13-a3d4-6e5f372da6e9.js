"use strict";
cc._RF.push(module, '4187eLt3SxLE6PUbl83Labp', 'ChargeAttackSkill');
// fight/skills/ChargeAttackSkill.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChargeAttackSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var ITimeline_1 = require("../types/ITimeline");
var SkillName_1 = require("../types/SkillName");
var AttackBoostBuff_1 = require("../buff/AttackBoostBuff");
var BattleManager_1 = require("../systems/BattleManager");
/**
 * 冲锋攻击技能
 * 快速冲向目标并造成物理伤害，同时获得短暂的攻击力提升
 */
var ChargeAttackSkill = /** @class */ (function () {
    function ChargeAttackSkill() {
        this._id = SkillName_1.default.charge_attack;
        this._name = "冲锋攻击";
        this._description = "快速冲向敌人并发动强力攻击，攻击后获得攻击力提升";
        this._cooldown = 5.0;
        this._remainingCooldown = 0;
        this._mpCost = 0;
        this._staminaCost = 25;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ENEMY;
        this._range = 300;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_charge",
            soundId: "charge_attack",
            effectPath: "prefabs/effects/ChargeTrail",
            hitEffectPath: "prefabs/effects/ChargeImpact",
            damage: 0,
            damageMultiplier: 1.8,
            chargeSpeed: 800,
            attackBuffDuration: 4.0,
            attackBuffMultiplier: 1.3 // 攻击力提升倍率
        };
    }
    Object.defineProperty(ChargeAttackSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    ChargeAttackSkill.prototype.canCastOn = function (caster, target) {
        if (!target)
            return false;
        if (target.isDead)
            return false;
        if (target.role !== caster.role)
            return true; // 敌对阵营
        return false;
    };
    /** 检查资源消耗 */
    ChargeAttackSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    ChargeAttackSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    ChargeAttackSkill.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " charges at " + (target === null || target === void 0 ? void 0 : target.characterName));
        return true;
    };
    /** 创建技能Timeline */
    ChargeAttackSkill.prototype.createTimeline = function (caster, target, targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 2.5, caster);
        // 0.0s: 播放冲锋准备动画
        var prepareNode = new Timeline_1.TimelineNode(timelineId + "_prepare", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("prepare_animation", "charge_prepare"), false);
        timeline.addNode(prepareNode);
        // 0.3s: 开始冲锋移动
        var chargeStartNode = new Timeline_1.TimelineNode(timelineId + "_charge_start", 0.3, new ChargeMovementEvent("charge_movement", caster, target, this._config.chargeSpeed), false);
        timeline.addNode(chargeStartNode);
        // 0.4s: 播放冲锋音效和拖尾特效
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.4, new TimelineEvents_1.PlaySoundTimelineEvent("charge_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        var trailNode = new Timeline_1.TimelineNode(timelineId + "_trail", 0.4, new TimelineEvents_1.PlayEffectTimelineEvent("charge_trail", this._config.effectPath, false, caster.node.position), false);
        timeline.addNode(trailNode);
        // 1.0s: 冲锋攻击命中
        var attackNode = new Timeline_1.TimelineNode(timelineId + "_attack", 1.0, new ChargeAttackEvent("charge_attack", caster, target, this.calculateDamage(caster)), false);
        timeline.addNode(attackNode);
        // 1.1s: 播放命中特效
        var hitEffectNode = new Timeline_1.TimelineNode(timelineId + "_hit_effect", 1.1, new TimelineEvents_1.PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, (target === null || target === void 0 ? void 0 : target.node.position) || cc.Vec3.ZERO), false);
        timeline.addNode(hitEffectNode);
        // 1.2s: 添加攻击力提升buff
        var buffNode = new Timeline_1.TimelineNode(timelineId + "_buff", 1.2, new AddAttackBuffEvent("attack_buff", caster, this._config.attackBuffDuration, this._config.attackBuffMultiplier), false);
        timeline.addNode(buffNode);
        return timeline;
    };
    /** 计算伤害 */
    ChargeAttackSkill.prototype.calculateDamage = function (caster) {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        var baseAttack = caster.attributes.attack;
        return Math.floor(baseAttack * this._config.damageMultiplier);
    };
    /** 更新技能冷却 */
    ChargeAttackSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    ChargeAttackSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    ChargeAttackSkill.prototype.levelUp = function () {
        this._level++;
        this._staminaCost = Math.max(15, this._staminaCost - 2);
        this._cooldown = Math.max(3.0, this._cooldown - 0.3);
        this._range += 25;
        this._config.damageMultiplier += 0.15;
        this._config.attackBuffMultiplier += 0.05;
        this._config.chargeSpeed += 50;
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    ChargeAttackSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            damageMultiplier: this._config.damageMultiplier,
            chargeSpeed: this._config.chargeSpeed
        };
    };
    return ChargeAttackSkill;
}());
exports.ChargeAttackSkill = ChargeAttackSkill;
/*** 冲锋移动事件*/
var ChargeMovementEvent = /** @class */ (function () {
    function ChargeMovementEvent(id, caster, target, speed) {
        this._type = ITimeline_1.TimelineEventType.MOVE;
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._speed = speed;
    }
    Object.defineProperty(ChargeMovementEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeMovementEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    ChargeMovementEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        // 计算冲锋方向和距离
        var startPos = this._caster.node.position;
        var targetPos = this._target.node.position;
        var direction = targetPos.subtract(startPos).normalize();
        // 冲锋到目标附近（保持一定距离）
        var chargeDistance = cc.Vec3.distance(startPos, targetPos) - 50; // 保持50单位距离
        var endPos = startPos.add(direction.multiplyScalar(chargeDistance));
        console.log(this._caster.characterName + " charges from (" + startPos.x + ", " + startPos.y + ") to (" + endPos.x + ", " + endPos.y + ")");
        // 这里应该实现实际的移动逻辑，比如使用Tween动画
        // cc.tween(this._caster.node)
        //     .to(0.7, { position: endPos })
        //     .start();
    };
    return ChargeMovementEvent;
}());
/*** 冲锋攻击事件*/
var ChargeAttackEvent = /** @class */ (function () {
    function ChargeAttackEvent(id, caster, target, damage) {
        this._type = ITimeline_1.TimelineEventType.DAMAGE;
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._damage = damage;
    }
    Object.defineProperty(ChargeAttackEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    ChargeAttackEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        if (this._target && !this._target.isDead) {
            var damageInfo = {
                amount: this._damage,
                type: IDamage_1.DamageType.PHYSICAL,
                source: this._caster,
                isCritical: Math.random() < 0.2,
                element: "physical"
            };
            this._target.takeDamageSimple(damageInfo.amount, this._caster);
            console.log(this._caster.characterName + " charge attacks " + this._target.characterName + " for " + this._damage + " physical damage");
        }
    };
    return ChargeAttackEvent;
}());
/*** 添加攻击力提升buff事件*/
var AddAttackBuffEvent = /** @class */ (function () {
    function AddAttackBuffEvent(id, caster, duration, multiplier) {
        this._type = ITimeline_1.TimelineEventType.ADD_BUFF;
        this._id = id;
        this._caster = caster;
        this._duration = duration;
        this._multiplier = multiplier;
    }
    Object.defineProperty(AddAttackBuffEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AddAttackBuffEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    AddAttackBuffEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        console.log(this._caster.characterName + " gains attack boost (" + this._multiplier + "x) for " + this._duration + " seconds");
        // 实现AttackBoostBuff
        var attackBuff = new AttackBoostBuff_1.AttackBoostBuff(this._caster, this._caster, this._duration, this._multiplier);
        this._caster.buffManager.addBuff(attackBuff);
    };
    return AddAttackBuffEvent;
}());

cc._RF.pop();