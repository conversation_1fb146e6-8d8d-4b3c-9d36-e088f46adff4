
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/FightEvent.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ffd64V2El5NKJxAXtZVSony', 'FightEvent');
// fight/types/FightEvent.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var FightEvent = /** @class */ (function () {
    function FightEvent() {
    }
    /**攻击状态改变 */
    FightEvent.attackStateChanged = 'attackStateChanged';
    /**Buff被添加时触发事件 */
    FightEvent.buffApplied = 'buffApplied';
    /**Buff被移除时触发 */
    FightEvent.buffRemoved = 'buffRemoved';
    /**刷新Buff持续时间 */
    FightEvent.buffRefreshed = 'buffRefreshed';
    /**增加叠加层数 */
    FightEvent.buffStackChanged = 'buffStackChanged';
    /**执行反击 */
    FightEvent.counterAttack = 'counterAttack';
    /**移动状态改变 */
    FightEvent.moveStateChanged = 'moveStateChanged';
    /**释放技能 */
    FightEvent.skillCast = 'skillCast';
    /**添加Buff */
    FightEvent.buffAdded = 'buffAdded';
    /**受到伤害 */
    FightEvent.takeDamage = 'takeDamage';
    /**治疗 */
    FightEvent.heal = 'heal';
    /**死亡 */
    FightEvent.death = 'death';
    /**设置buff状态 */
    FightEvent.stateChanged = 'stateChanged';
    /**移除角色 */
    FightEvent.characterRemoved = 'characterRemoved';
    /**修改生命值 */
    FightEvent.hpChanged = 'hpChanged';
    /**修改魔法值 */
    FightEvent.mpChanged = 'mpChanged';
    /**修改耐力 */
    FightEvent.staminaChanged = 'staminaChanged';
    /**直接设置属性值（用于修改器） */
    FightEvent.attributeChanged = 'attributeChanged';
    /**buff层级叠加 */
    FightEvent.buffStacked = 'buffStacked';
    /**子弹击中 */
    FightEvent.bulletHit = 'bulletHit';
    /**子弹销毁 */
    FightEvent.bulletDestroyed = 'bulletDestroyed';
    /**子弹射击 */
    FightEvent.bulletFired = 'bulletFired';
    /**添加技能 */
    FightEvent.skillAdded = 'skillAdded';
    /**移除技能 */
    FightEvent.skillRemoved = 'skillRemoved';
    /**重置技能冷却 */
    FightEvent.skillCooldownReset = 'skillCooldownReset';
    /**重置技能冷却（多个） */
    FightEvent.allSkillsCooldownReset = 'allSkillsCooldownReset';
    /**使用技能 */
    FightEvent.skillUsed = 'skillUsed';
    /**添加Timeline */
    FightEvent.timelineAdded = 'timelineAdded';
    /**移除Timeline */
    FightEvent.timelineRemoved = 'timelineRemoved';
    /**暂停所有timeline */
    FightEvent.allTimelinesPaused = 'allTimelinesPaused';
    /**恢复所有timeline */
    FightEvent.allTimelinesResumed = 'allTimelinesResumed';
    /**清除所有Timeline */
    FightEvent.allTimelinesCleared = 'allTimelinesCleared';
    /**暂停指定Timeline */
    FightEvent.timelinePaused = 'timelinePaused';
    /**恢复指定Timeline */
    FightEvent.timelineResumed = 'timelineResumed';
    /**Timeline完成时的回调 */
    FightEvent.timelineCompleted = 'timelineCompleted';
    /**Timeline停止时的回调 */
    FightEvent.timelineStopped = 'timelineStopped';
    /**更新Timeline */
    FightEvent.completedT = 'completedT';
    /**暂停Timeline */
    FightEvent.pausedT = 'pausedT';
    /**恢复Timeline */
    FightEvent.resumedT = 'resumedT';
    /**停止Timeline */
    FightEvent.stoppedT = 'stoppedT';
    /**重置Timeline */
    FightEvent.resetT = 'resetT';
    /***跳转到指定时间点timeline */
    FightEvent.seekedT = 'seekedT';
    // 战斗管理器事件
    /**战斗开始 */
    FightEvent.battleStarted = 'battleStarted';
    /**战斗结束 */
    FightEvent.battleEnded = 'battleEnded';
    /**战斗暂停 */
    FightEvent.battlePaused = 'battlePaused';
    /**战斗恢复 */
    FightEvent.battleResumed = 'battleResumed';
    /**参战者加入 */
    FightEvent.participantAdded = 'participantAdded';
    /**参战者离开 */
    FightEvent.participantRemoved = 'participantRemoved';
    /**角色死亡 */
    FightEvent.characterDied = 'characterDied';
    /**角色被眩晕 */
    FightEvent.characterStunned = 'characterStunned';
    /**角色眩晕结束 */
    FightEvent.characterStunEnded = 'characterStunEnded';
    /**角色中毒 */
    FightEvent.characterPoisoned = 'characterPoisoned';
    /**毒素伤害 */
    FightEvent.poisonDamageDealt = 'poisonDamageDealt';
    /**角色被治疗 */
    FightEvent.characterHealed = 'characterHealed';
    return FightEvent;
}());
exports.default = FightEvent;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXHR5cGVzXFxGaWdodEV2ZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7SUFBQTtJQWdIQSxDQUFDO0lBL0dHLFlBQVk7SUFDTCw2QkFBa0IsR0FBRyxvQkFBb0IsQ0FBQTtJQUNoRCxrQkFBa0I7SUFDWCxzQkFBVyxHQUFHLGFBQWEsQ0FBQTtJQUNsQyxnQkFBZ0I7SUFDVCxzQkFBVyxHQUFHLGFBQWEsQ0FBQTtJQUNsQyxnQkFBZ0I7SUFDVCx3QkFBYSxHQUFHLGVBQWUsQ0FBQTtJQUN0QyxZQUFZO0lBQ0wsMkJBQWdCLEdBQUcsa0JBQWtCLENBQUE7SUFDNUMsVUFBVTtJQUNILHdCQUFhLEdBQUcsZUFBZSxDQUFBO0lBQ3RDLFlBQVk7SUFDTCwyQkFBZ0IsR0FBRyxrQkFBa0IsQ0FBQTtJQUM1QyxVQUFVO0lBQ0gsb0JBQVMsR0FBRyxXQUFXLENBQUE7SUFDOUIsWUFBWTtJQUNMLG9CQUFTLEdBQUcsV0FBVyxDQUFBO0lBQzlCLFVBQVU7SUFDSCxxQkFBVSxHQUFHLFlBQVksQ0FBQTtJQUNoQyxRQUFRO0lBQ0QsZUFBSSxHQUFHLE1BQU0sQ0FBQTtJQUNwQixRQUFRO0lBQ0QsZ0JBQUssR0FBRyxPQUFPLENBQUE7SUFDdEIsY0FBYztJQUNQLHVCQUFZLEdBQUcsY0FBYyxDQUFBO0lBQ3BDLFVBQVU7SUFDSCwyQkFBZ0IsR0FBRyxrQkFBa0IsQ0FBQTtJQUM1QyxXQUFXO0lBQ0osb0JBQVMsR0FBRyxXQUFXLENBQUE7SUFDOUIsV0FBVztJQUNKLG9CQUFTLEdBQUcsV0FBVyxDQUFBO0lBQzlCLFVBQVU7SUFDSCx5QkFBYyxHQUFHLGdCQUFnQixDQUFBO0lBQ3hDLG9CQUFvQjtJQUNiLDJCQUFnQixHQUFHLGtCQUFrQixDQUFBO0lBQzVDLGNBQWM7SUFDUCxzQkFBVyxHQUFHLGFBQWEsQ0FBQTtJQUNsQyxVQUFVO0lBQ0gsb0JBQVMsR0FBRyxXQUFXLENBQUE7SUFDOUIsVUFBVTtJQUNILDBCQUFlLEdBQUcsaUJBQWlCLENBQUE7SUFDMUMsVUFBVTtJQUNILHNCQUFXLEdBQUcsYUFBYSxDQUFBO0lBQ2xDLFVBQVU7SUFDSCxxQkFBVSxHQUFHLFlBQVksQ0FBQTtJQUNoQyxVQUFVO0lBQ0gsdUJBQVksR0FBRyxjQUFjLENBQUE7SUFDcEMsWUFBWTtJQUNMLDZCQUFrQixHQUFHLG9CQUFvQixDQUFBO0lBQ2hELGdCQUFnQjtJQUNULGlDQUFzQixHQUFHLHdCQUF3QixDQUFBO0lBQ3hELFVBQVU7SUFDSCxvQkFBUyxHQUFHLFdBQVcsQ0FBQTtJQUM5QixnQkFBZ0I7SUFDVCx3QkFBYSxHQUFHLGVBQWUsQ0FBQTtJQUN0QyxnQkFBZ0I7SUFDVCwwQkFBZSxHQUFHLGlCQUFpQixDQUFBO0lBQzFDLGtCQUFrQjtJQUNYLDZCQUFrQixHQUFHLG9CQUFvQixDQUFBO0lBQ2hELGtCQUFrQjtJQUNYLDhCQUFtQixHQUFHLHFCQUFxQixDQUFBO0lBQ2xELGtCQUFrQjtJQUNYLDhCQUFtQixHQUFHLHFCQUFxQixDQUFBO0lBQ2xELGtCQUFrQjtJQUNYLHlCQUFjLEdBQUcsZ0JBQWdCLENBQUE7SUFDeEMsa0JBQWtCO0lBQ1gsMEJBQWUsR0FBRyxpQkFBaUIsQ0FBQTtJQUMxQyxvQkFBb0I7SUFDYiw0QkFBaUIsR0FBRyxtQkFBbUIsQ0FBQTtJQUM5QyxvQkFBb0I7SUFDYiwwQkFBZSxHQUFHLGlCQUFpQixDQUFBO0lBQzFDLGdCQUFnQjtJQUNULHFCQUFVLEdBQUcsWUFBWSxDQUFBO0lBQ2hDLGdCQUFnQjtJQUNULGtCQUFPLEdBQUcsU0FBUyxDQUFBO0lBQzFCLGdCQUFnQjtJQUNULG1CQUFRLEdBQUcsVUFBVSxDQUFBO0lBQzVCLGdCQUFnQjtJQUNULG1CQUFRLEdBQUcsVUFBVSxDQUFBO0lBQzVCLGdCQUFnQjtJQUNULGlCQUFNLEdBQUcsUUFBUSxDQUFBO0lBQ3hCLHVCQUF1QjtJQUNoQixrQkFBTyxHQUFHLFNBQVMsQ0FBQTtJQUUxQixVQUFVO0lBQ1YsVUFBVTtJQUNILHdCQUFhLEdBQUcsZUFBZSxDQUFBO0lBQ3RDLFVBQVU7SUFDSCxzQkFBVyxHQUFHLGFBQWEsQ0FBQTtJQUNsQyxVQUFVO0lBQ0gsdUJBQVksR0FBRyxjQUFjLENBQUE7SUFDcEMsVUFBVTtJQUNILHdCQUFhLEdBQUcsZUFBZSxDQUFBO0lBQ3RDLFdBQVc7SUFDSiwyQkFBZ0IsR0FBRyxrQkFBa0IsQ0FBQTtJQUM1QyxXQUFXO0lBQ0osNkJBQWtCLEdBQUcsb0JBQW9CLENBQUE7SUFDaEQsVUFBVTtJQUNILHdCQUFhLEdBQUcsZUFBZSxDQUFBO0lBQ3RDLFdBQVc7SUFDSiwyQkFBZ0IsR0FBRyxrQkFBa0IsQ0FBQTtJQUM1QyxZQUFZO0lBQ0wsNkJBQWtCLEdBQUcsb0JBQW9CLENBQUE7SUFDaEQsVUFBVTtJQUNILDRCQUFpQixHQUFHLG1CQUFtQixDQUFBO0lBQzlDLFVBQVU7SUFDSCw0QkFBaUIsR0FBRyxtQkFBbUIsQ0FBQTtJQUM5QyxXQUFXO0lBQ0osMEJBQWUsR0FBRyxpQkFBaUIsQ0FBQTtJQUU5QyxpQkFBQztDQWhIRCxBQWdIQyxJQUFBO2tCQWhIb0IsVUFBVSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGNsYXNzIEZpZ2h0RXZlbnQge1xyXG4gICAgLyoq5pS75Ye754q25oCB5pS55Y+YICovXHJcbiAgICBzdGF0aWMgYXR0YWNrU3RhdGVDaGFuZ2VkID0gJ2F0dGFja1N0YXRlQ2hhbmdlZCdcclxuICAgIC8qKkJ1Zmbooqvmt7vliqDml7bop6blj5Hkuovku7YgKi9cclxuICAgIHN0YXRpYyBidWZmQXBwbGllZCA9ICdidWZmQXBwbGllZCdcclxuICAgIC8qKkJ1Zmbooqvnp7vpmaTml7bop6blj5EgKi9cclxuICAgIHN0YXRpYyBidWZmUmVtb3ZlZCA9ICdidWZmUmVtb3ZlZCdcclxuICAgIC8qKuWIt+aWsEJ1ZmbmjIHnu63ml7bpl7QgKi9cclxuICAgIHN0YXRpYyBidWZmUmVmcmVzaGVkID0gJ2J1ZmZSZWZyZXNoZWQnXHJcbiAgICAvKirlop7liqDlj6DliqDlsYLmlbAgKi9cclxuICAgIHN0YXRpYyBidWZmU3RhY2tDaGFuZ2VkID0gJ2J1ZmZTdGFja0NoYW5nZWQnXHJcbiAgICAvKirmiafooYzlj43lh7sgKi9cclxuICAgIHN0YXRpYyBjb3VudGVyQXR0YWNrID0gJ2NvdW50ZXJBdHRhY2snXHJcbiAgICAvKirnp7vliqjnirbmgIHmlLnlj5ggKi9cclxuICAgIHN0YXRpYyBtb3ZlU3RhdGVDaGFuZ2VkID0gJ21vdmVTdGF0ZUNoYW5nZWQnXHJcbiAgICAvKirph4rmlL7mioDog70gKi9cclxuICAgIHN0YXRpYyBza2lsbENhc3QgPSAnc2tpbGxDYXN0J1xyXG4gICAgLyoq5re75YqgQnVmZiAqL1xyXG4gICAgc3RhdGljIGJ1ZmZBZGRlZCA9ICdidWZmQWRkZWQnXHJcbiAgICAvKirlj5fliLDkvKTlrrMgKi9cclxuICAgIHN0YXRpYyB0YWtlRGFtYWdlID0gJ3Rha2VEYW1hZ2UnXHJcbiAgICAvKirmsrvnlpcgKi9cclxuICAgIHN0YXRpYyBoZWFsID0gJ2hlYWwnXHJcbiAgICAvKirmrbvkuqEgKi9cclxuICAgIHN0YXRpYyBkZWF0aCA9ICdkZWF0aCdcclxuICAgIC8qKuiuvue9rmJ1ZmbnirbmgIEgKi9cclxuICAgIHN0YXRpYyBzdGF0ZUNoYW5nZWQgPSAnc3RhdGVDaGFuZ2VkJ1xyXG4gICAgLyoq56e76Zmk6KeS6ImyICovXHJcbiAgICBzdGF0aWMgY2hhcmFjdGVyUmVtb3ZlZCA9ICdjaGFyYWN0ZXJSZW1vdmVkJ1xyXG4gICAgLyoq5L+u5pS555Sf5ZG95YC8ICovXHJcbiAgICBzdGF0aWMgaHBDaGFuZ2VkID0gJ2hwQ2hhbmdlZCdcclxuICAgIC8qKuS/ruaUuemtlOazleWAvCAqL1xyXG4gICAgc3RhdGljIG1wQ2hhbmdlZCA9ICdtcENoYW5nZWQnXHJcbiAgICAvKirkv67mlLnogJDlipsgKi9cclxuICAgIHN0YXRpYyBzdGFtaW5hQ2hhbmdlZCA9ICdzdGFtaW5hQ2hhbmdlZCdcclxuICAgIC8qKuebtOaOpeiuvue9ruWxnuaAp+WAvO+8iOeUqOS6juS/ruaUueWZqO+8iSAqL1xyXG4gICAgc3RhdGljIGF0dHJpYnV0ZUNoYW5nZWQgPSAnYXR0cmlidXRlQ2hhbmdlZCdcclxuICAgIC8qKmJ1ZmblsYLnuqflj6DliqAgKi9cclxuICAgIHN0YXRpYyBidWZmU3RhY2tlZCA9ICdidWZmU3RhY2tlZCdcclxuICAgIC8qKuWtkOW8ueWHu+S4rSAqL1xyXG4gICAgc3RhdGljIGJ1bGxldEhpdCA9ICdidWxsZXRIaXQnXHJcbiAgICAvKirlrZDlvLnplIDmr4EgKi9cclxuICAgIHN0YXRpYyBidWxsZXREZXN0cm95ZWQgPSAnYnVsbGV0RGVzdHJveWVkJ1xyXG4gICAgLyoq5a2Q5by55bCE5Ye7ICovXHJcbiAgICBzdGF0aWMgYnVsbGV0RmlyZWQgPSAnYnVsbGV0RmlyZWQnXHJcbiAgICAvKirmt7vliqDmioDog70gKi9cclxuICAgIHN0YXRpYyBza2lsbEFkZGVkID0gJ3NraWxsQWRkZWQnXHJcbiAgICAvKirnp7vpmaTmioDog70gKi9cclxuICAgIHN0YXRpYyBza2lsbFJlbW92ZWQgPSAnc2tpbGxSZW1vdmVkJ1xyXG4gICAgLyoq6YeN572u5oqA6IO95Ya35Y20ICovXHJcbiAgICBzdGF0aWMgc2tpbGxDb29sZG93blJlc2V0ID0gJ3NraWxsQ29vbGRvd25SZXNldCdcclxuICAgIC8qKumHjee9ruaKgOiDveWGt+WNtO+8iOWkmuS4qu+8iSAqL1xyXG4gICAgc3RhdGljIGFsbFNraWxsc0Nvb2xkb3duUmVzZXQgPSAnYWxsU2tpbGxzQ29vbGRvd25SZXNldCdcclxuICAgIC8qKuS9v+eUqOaKgOiDvSAqL1xyXG4gICAgc3RhdGljIHNraWxsVXNlZCA9ICdza2lsbFVzZWQnXHJcbiAgICAvKirmt7vliqBUaW1lbGluZSAqL1xyXG4gICAgc3RhdGljIHRpbWVsaW5lQWRkZWQgPSAndGltZWxpbmVBZGRlZCdcclxuICAgIC8qKuenu+mZpFRpbWVsaW5lICovXHJcbiAgICBzdGF0aWMgdGltZWxpbmVSZW1vdmVkID0gJ3RpbWVsaW5lUmVtb3ZlZCdcclxuICAgIC8qKuaaguWBnOaJgOaciXRpbWVsaW5lICovXHJcbiAgICBzdGF0aWMgYWxsVGltZWxpbmVzUGF1c2VkID0gJ2FsbFRpbWVsaW5lc1BhdXNlZCdcclxuICAgIC8qKuaBouWkjeaJgOaciXRpbWVsaW5lICovXHJcbiAgICBzdGF0aWMgYWxsVGltZWxpbmVzUmVzdW1lZCA9ICdhbGxUaW1lbGluZXNSZXN1bWVkJ1xyXG4gICAgLyoq5riF6Zmk5omA5pyJVGltZWxpbmUgKi9cclxuICAgIHN0YXRpYyBhbGxUaW1lbGluZXNDbGVhcmVkID0gJ2FsbFRpbWVsaW5lc0NsZWFyZWQnXHJcbiAgICAvKirmmoLlgZzmjIflrppUaW1lbGluZSAqL1xyXG4gICAgc3RhdGljIHRpbWVsaW5lUGF1c2VkID0gJ3RpbWVsaW5lUGF1c2VkJ1xyXG4gICAgLyoq5oGi5aSN5oyH5a6aVGltZWxpbmUgKi9cclxuICAgIHN0YXRpYyB0aW1lbGluZVJlc3VtZWQgPSAndGltZWxpbmVSZXN1bWVkJ1xyXG4gICAgLyoqVGltZWxpbmXlrozmiJDml7bnmoTlm57osIMgKi9cclxuICAgIHN0YXRpYyB0aW1lbGluZUNvbXBsZXRlZCA9ICd0aW1lbGluZUNvbXBsZXRlZCdcclxuICAgIC8qKlRpbWVsaW5l5YGc5q2i5pe255qE5Zue6LCDICovXHJcbiAgICBzdGF0aWMgdGltZWxpbmVTdG9wcGVkID0gJ3RpbWVsaW5lU3RvcHBlZCdcclxuICAgIC8qKuabtOaWsFRpbWVsaW5lICovXHJcbiAgICBzdGF0aWMgY29tcGxldGVkVCA9ICdjb21wbGV0ZWRUJ1xyXG4gICAgLyoq5pqC5YGcVGltZWxpbmUgKi9cclxuICAgIHN0YXRpYyBwYXVzZWRUID0gJ3BhdXNlZFQnXHJcbiAgICAvKirmgaLlpI1UaW1lbGluZSAqL1xyXG4gICAgc3RhdGljIHJlc3VtZWRUID0gJ3Jlc3VtZWRUJ1xyXG4gICAgLyoq5YGc5q2iVGltZWxpbmUgKi9cclxuICAgIHN0YXRpYyBzdG9wcGVkVCA9ICdzdG9wcGVkVCdcclxuICAgIC8qKumHjee9rlRpbWVsaW5lICovXHJcbiAgICBzdGF0aWMgcmVzZXRUID0gJ3Jlc2V0VCdcclxuICAgIC8qKirot7PovazliLDmjIflrprml7bpl7Tngrl0aW1lbGluZSAqL1xyXG4gICAgc3RhdGljIHNlZWtlZFQgPSAnc2Vla2VkVCdcclxuXHJcbiAgICAvLyDmiJjmlpfnrqHnkIblmajkuovku7ZcclxuICAgIC8qKuaImOaWl+W8gOWniyAqL1xyXG4gICAgc3RhdGljIGJhdHRsZVN0YXJ0ZWQgPSAnYmF0dGxlU3RhcnRlZCdcclxuICAgIC8qKuaImOaWl+e7k+adnyAqL1xyXG4gICAgc3RhdGljIGJhdHRsZUVuZGVkID0gJ2JhdHRsZUVuZGVkJ1xyXG4gICAgLyoq5oiY5paX5pqC5YGcICovXHJcbiAgICBzdGF0aWMgYmF0dGxlUGF1c2VkID0gJ2JhdHRsZVBhdXNlZCdcclxuICAgIC8qKuaImOaWl+aBouWkjSAqL1xyXG4gICAgc3RhdGljIGJhdHRsZVJlc3VtZWQgPSAnYmF0dGxlUmVzdW1lZCdcclxuICAgIC8qKuWPguaImOiAheWKoOWFpSAqL1xyXG4gICAgc3RhdGljIHBhcnRpY2lwYW50QWRkZWQgPSAncGFydGljaXBhbnRBZGRlZCdcclxuICAgIC8qKuWPguaImOiAheemu+W8gCAqL1xyXG4gICAgc3RhdGljIHBhcnRpY2lwYW50UmVtb3ZlZCA9ICdwYXJ0aWNpcGFudFJlbW92ZWQnXHJcbiAgICAvKirop5LoibLmrbvkuqEgKi9cclxuICAgIHN0YXRpYyBjaGFyYWN0ZXJEaWVkID0gJ2NoYXJhY3RlckRpZWQnXHJcbiAgICAvKirop5LoibLooqvnnKnmmZUgKi9cclxuICAgIHN0YXRpYyBjaGFyYWN0ZXJTdHVubmVkID0gJ2NoYXJhY3RlclN0dW5uZWQnXHJcbiAgICAvKirop5LoibLnnKnmmZXnu5PmnZ8gKi9cclxuICAgIHN0YXRpYyBjaGFyYWN0ZXJTdHVuRW5kZWQgPSAnY2hhcmFjdGVyU3R1bkVuZGVkJ1xyXG4gICAgLyoq6KeS6Imy5Lit5q+SICovXHJcbiAgICBzdGF0aWMgY2hhcmFjdGVyUG9pc29uZWQgPSAnY2hhcmFjdGVyUG9pc29uZWQnXHJcbiAgICAvKirmr5LntKDkvKTlrrMgKi9cclxuICAgIHN0YXRpYyBwb2lzb25EYW1hZ2VEZWFsdCA9ICdwb2lzb25EYW1hZ2VEZWFsdCdcclxuICAgIC8qKuinkuiJsuiiq+ayu+eWlyAqL1xyXG4gICAgc3RhdGljIGNoYXJhY3RlckhlYWxlZCA9ICdjaGFyYWN0ZXJIZWFsZWQnXHJcblxyXG59Il19