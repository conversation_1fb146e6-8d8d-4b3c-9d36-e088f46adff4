"use strict";
cc._RF.push(module, 'abe2awry7lKwa5ERVnLiXT+', 'PoisonBuff');
// fight/buff/PoisonBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoisonBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 毒素Debuff
 * 持续造成毒素伤害并降低目标的生命恢复效果
 */
var PoisonBuff = /** @class */ (function () {
    function PoisonBuff(caster, target, duration, damagePerSecond) {
        this._name = "中毒";
        this._description = "持续受到毒素伤害，生命恢复效果降低";
        this._type = IBuff_1.BuffType.DEBUFF;
        this._stackCount = 1;
        this._maxStack = 10;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._damageInterval = 1.0; // 每秒触发一次
        this._lastDamageTime = 0;
        this._healingReduction = 0.5; // 治疗效果减少50%
        // 视觉效果
        this._iconPath = "icons/buffs/poison";
        this._effectPrefabPath = "prefabs/effects/PoisonAura";
        this._id = "poison_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._damagePerSecond = damagePerSecond;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u6BCF\u79D2\u53D7\u5230" + damagePerSecond + "\u70B9\u6BD2\u7D20\u4F24\u5BB3\uFF0C\u6CBB\u7597\u6548\u679C\u964D\u4F4E" + Math.round(this._healingReduction * 100) + "%\uFF0C\u6301\u7EED" + duration + "\u79D2";
        // 创建属性修改器
        this.createAttributeModifiers();
    }
    Object.defineProperty(PoisonBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) {
            var oldStack = this._stackCount;
            this._stackCount = Math.max(0, Math.min(value, this._maxStack));
            if (oldStack !== this._stackCount) {
                this.updateAttributeModifiers();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "periodicEffect", {
        get: function () {
            return {
                interval: this._damageInterval,
                effectType: IBuff_1.BuffPeriodicEffectType.DAMAGE_OVER_TIME,
                value: this._damagePerSecond,
                stackable: true
            };
        },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    PoisonBuff.prototype.createAttributeModifiers = function () {
        // 生命恢复效果减少修改器
        var healingReductionModifier = new AttributeModifier_1.AttributeModifier(this._id + "_healing_reduction", "毒素治疗减少", "healingReceived", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, -this._healingReduction * this._stackCount, this._duration);
        this._attributeModifiers = [healingReductionModifier];
    };
    /** 更新属性修改器 */
    PoisonBuff.prototype.updateAttributeModifiers = function () {
        var e_1, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                var attributeModifier = modifier;
                if (attributeModifier.attributeName === "healingReceived") {
                    attributeModifier.setValue(-this._healingReduction * this._stackCount);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** Buff被添加时触发 */
    PoisonBuff.prototype.onApply = function () {
        var e_2, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterPoisoned, { target: this._target, caster: this._caster });
    };
    /** Buff每帧更新时触发 */
    PoisonBuff.prototype.onTick = function (deltaTime) {
        var e_3, _a;
        this._lastDamageTime += deltaTime;
        // 检查是否到了造成伤害的时间
        if (this._lastDamageTime >= this._damageInterval) {
            this.dealPoisonDamage();
            this._lastDamageTime = 0;
        }
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /** Buff被移除时触发 */
    PoisonBuff.prototype.onRemove = function () {
        var e_4, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 造成毒素伤害 */
    PoisonBuff.prototype.dealPoisonDamage = function () {
        if (this._target && !this._target.isDead) {
            var totalDamage = this._damagePerSecond * this._stackCount;
            // 直接造成毒素伤害
            this._target.takeDamage(totalDamage, this._caster);
            console.log(this._target.characterName + " takes " + totalDamage + " poison damage from " + this._name + " (Stack: " + this._stackCount + ")");
            this.playDamageEffect();
            // 触发毒素伤害事件
            this._eventManager.emit(FightEvent_1.default.poisonDamageDealt, {
                target: this._target,
                caster: this._caster,
                damage: totalDamage,
                source: this
            });
        }
    };
    /** 更新Buff状态 */
    PoisonBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    PoisonBuff.prototype.refresh = function () {
        var e_5, _a;
        this._remainingTime = this._duration;
        this._lastDamageTime = 0;
        try {
            // 刷新属性修改器时间
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    PoisonBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    PoisonBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    PoisonBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.damage_per_second:
                return this._damagePerSecond * this._stackCount;
            case Buff_1.EBuffEffectType.total_damage_remaining:
                return this._damagePerSecond * this._stackCount * this._remainingTime;
            case Buff_1.EBuffEffectType.healing_reduction:
                return this._healingReduction * this._stackCount;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    PoisonBuff.prototype.conflictsWith = function (_otherBuff) {
        // 毒素buff可以与其他毒素叠加，不冲突
        return false;
    };
    /** 检查是否可以被净化 */
    PoisonBuff.prototype.canBeDispelled = function () {
        return true; // 毒素可以被净化技能移除
    };
    /** 播放应用特效 */
    PoisonBuff.prototype.playApplyEffect = function () {
        console.log("Playing poison effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的中毒特效播放逻辑
    };
    /** 播放伤害特效 */
    PoisonBuff.prototype.playDamageEffect = function () {
        console.log("Playing poison damage effect on " + this._target.characterName);
        // 这里应该实现毒素伤害的视觉特效
    };
    /** 停止特效 */
    PoisonBuff.prototype.stopEffect = function () {
        console.log("Stopping poison effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    PoisonBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            damagePerSecond: this._damagePerSecond,
            totalDamageRemaining: this.getEffectValue(Buff_1.EBuffEffectType.total_damage_remaining),
            healingReduction: this.getEffectValue(Buff_1.EBuffEffectType.healing_reduction),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return PoisonBuff;
}());
exports.PoisonBuff = PoisonBuff;

cc._RF.pop();