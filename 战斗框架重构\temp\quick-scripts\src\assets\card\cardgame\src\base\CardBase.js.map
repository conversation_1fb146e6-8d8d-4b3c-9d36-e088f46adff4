{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\CardBase.ts"], "names": [], "mappings": ";;;;AAAA,0CAA0C;AAC1C,yCAAyC;AACzC,8CAA8C;AAC9C,8CAA8C;AAC9C,6BAA6B;AAC7B,qCAAqC;AACrC,qCAAqC;AAGrC,+CAA+C;AAE/C,0BAA0B;AAC1B,iBAAiB;AACjB,+BAA+B;AAC/B,iBAAiB;AACjB,yBAAyB;AACzB,sBAAsB;AACtB,iBAAiB;AACjB,gBAAgB;AAChB,2BAA2B;AAC3B,gBAAgB;AAChB,+BAA+B;AAC/B,gBAAgB;AAChB,qBAAqB;AACrB,iBAAiB;AACjB,yBAAyB;AACzB,IAAI;AAEJ,cAAc;AACd,qBAAqB;AACrB,kBAAkB;AAClB,qBAAqB;AACrB,kBAAkB;AAClB,qBAAqB;AACrB,kBAAkB;AAClB,uBAAuB;AACvB,kBAAkB;AAClB,yBAAyB;AACzB,IAAI;AACJ,wBAAwB;AACxB,iBAAiB;AACjB,uCAAuC;AACvC,kBAAkB;AAClB,2CAA2C;AAC3C,iBAAiB;AACjB,mCAAmC;AACnC,kBAAkB;AAClB,uCAAuC;AACvC,6BAA6B;AAC7B,uCAAuC;AACvC,IAAI;AAEJ,+BAA+B;AAC/B,mBAAmB;AACnB,iDAAiD;AACjD,gBAAgB;AAChB,6BAA6B;AAC7B,IAAI;AAEJ,0BAA0B;AAC1B,kBAAkB;AAClB,uCAAuC;AACvC,kBAAkB;AAClB,iCAAiC;AACjC,IAAI;AAGJ,mBAAmB;AACnB,sCAAsC;AACtC,uBAAuB;AACvB,6DAA6D;AAC7D,2BAA2B;AAC3B,sBAAsB;AACtB,yBAAyB;AACzB,wBAAwB;AACxB,uBAAuB;AACvB,uBAAuB;AACvB,oBAAoB;AACpB,0BAA0B;AAC1B,sBAAsB;AACtB,gCAAgC;AAEhC,qBAAqB;AACrB,0BAA0B;AAC1B,uBAAuB;AACvB,gCAAgC;AAChC,oBAAoB;AACpB,wBAAwB;AACxB,sBAAsB;AACtB,+BAA+B;AAC/B,+BAA+B;AAC/B,6BAA6B;AAC7B,2BAA2B;AAC3B,gCAAgC;AAChC,iCAAiC;AACjC,2BAA2B;AAC3B,iCAAiC;AACjC,yCAAyC;AACzC,uBAAuB;AACvB,6BAA6B;AAC7B,uBAAuB;AACvB,+BAA+B;AAC/B,yBAAyB;AACzB,+BAA+B;AAC/B,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,0BAA0B;AAC1B,sBAAsB;AACtB,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,cAAc;AACd,wEAAwE;AACxE,oBAAoB;AACpB,0BAA0B;AAC1B,iBAAiB;AACjB,kCAAkC;AAElC,iBAAiB;AACjB,uBAAuB;AACvB,iBAAiB;AAEjB,gBAAgB;AAChB,uBAAuB;AACvB,iBAAiB;AAEjB,qBAAqB;AACrB,mBAAmB;AAEnB,kBAAkB;AAClB,uBAAuB;AACvB,0BAA0B;AAE1B,iBAAiB;AACjB,uBAAuB;AACvB,uBAAuB;AAEvB,kBAAkB;AAClB,kBAAkB;AAElB,kBAAkB;AAClB,uBAAuB;AACvB,qBAAqB;AAErB,sBAAsB;AAEtB,uBAAuB;AACvB,oBAAoB;AAEpB,4BAA4B;AAC5B,0BAA0B;AAE1B,iEAAiE;AAEjE,mBAAmB;AACnB,yCAAyC;AACzC,6CAA6C;AAC7C,sCAAsC;AACtC,qDAAqD;AACrD,YAAY;AACZ,QAAQ;AACR,0BAA0B;AAC1B,wBAAwB;AACxB,gCAAgC;AAChC,kCAAkC;AAClC,wEAAwE;AACxE,+DAA+D;AAC/D,6DAA6D;AAC7D,yFAAyF;AACzF,+DAA+D;AAC/D,gBAAgB;AAChB,oDAAoD;AACpD,YAAY;AACZ,QAAQ;AAER,yCAAyC;AACzC,2BAA2B;AAC3B,2BAA2B;AAC3B,+CAA+C;AAC/C,mBAAmB;AACnB,yDAAyD;AACzD,oCAAoC;AACpC,YAAY;AACZ,QAAQ;AAER,yCAAyC;AACzC,yEAAyE;AACzE,QAAQ;AAGR,gEAAgE;AAChE,UAAU;AACV,gBAAgB;AAChB,4BAA4B;AAC5B,UAAU;AACV,yCAAyC;AACzC,oEAAoE;AACpE,4DAA4D;AAC5D,mBAAmB;AACnB,uDAAuD;AACvD,gEAAgE;AAChE,0DAA0D;AAC1D,qCAAqC;AACrC,4BAA4B;AAC5B,kBAAkB;AAClB,YAAY;AACZ,QAAQ;AAER,UAAU;AACV,iBAAiB;AACjB,4BAA4B;AAC5B,UAAU;AACV,yCAAyC;AACzC,gEAAgE;AAChE,mCAAmC;AACnC,kCAAkC;AAClC,gEAAgE;AAChE,QAAQ;AAER,yCAAyC;AACzC,gEAAgE;AAChE,mCAAmC;AACnC,kCAAkC;AAClC,gEAAgE;AAChE,QAAQ;AAER,UAAU;AACV,wBAAwB;AACxB,kCAAkC;AAClC,iCAAiC;AACjC,8CAA8C;AAC9C,UAAU;AACV,iGAAiG;AACjG,iBAAiB;AACjB,qEAAqE;AACrE,oDAAoD;AACpD,4BAA4B;AAC5B,6CAA6C;AAC7C,oEAAoE;AACpE,kDAAkD;AAClD,kEAAkE;AAClE,YAAY;AACZ,kCAAkC;AAClC,qBAAqB;AACrB,yCAAyC;AACzC,2DAA2D;AAC3D,mBAAmB;AACnB,yBAAyB;AACzB,mCAAmC;AACnC,sEAAsE;AACtE,6CAA6C;AAC7C,uDAAuD;AACvD,yBAAyB;AACzB,6BAA6B;AAC7B,mBAAmB;AACnB,YAAY;AACZ,8BAA8B;AAC9B,QAAQ;AAER,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import { ECamp } from \"../CampManager\";\r\n// import { BRO } from \"../EventManager\";\r\n// import FightManager from \"../FightManager\";\r\n// import SkillManager from \"../SkillManager\";\r\n// import Base from \"./Base\";\r\n// import CampBase from \"./CampBase\";\r\n// import RoleBase from \"./RoleBase\";\r\n\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n\r\n// export enum ECardType {\r\n//     /**装备卡牌 */\r\n//     equipment = 'equipment',\r\n//     /**攻击卡牌 */\r\n//     attact = 'attact',\r\n//     /**血量操作相关的卡牌 */\r\n//     hp = 'hp',\r\n//     /**垃圾牌 */\r\n//     garbage = 'garbage',\r\n//     /**污染卡 */\r\n//     pollution = 'pollution',\r\n//     /**礼物卡 */\r\n//     gift = 'gift',\r\n//     /**修补装备 */\r\n//     repair = 'repair',\r\n// }\r\n\r\n// /**卡片的状态 */\r\n// enum ECardStatus {\r\n//     /**卡牌未使用 */\r\n//     None = `None`,\r\n//     /**卡牌已使用 */\r\n//     Used = `Used`,\r\n//     /**卡牌已死亡 */\r\n//     Death = `Death`,\r\n//     /**卡牌已冻结 */\r\n//     Frozen = `Frozen`,\r\n// }\r\n// export enum EHpType {\r\n//     /**固定恢复 */\r\n//     fixedRecovery = 'fixedRecovery',\r\n//     /**百分比恢复 */\r\n//     percentRecovery = 'percentRecovery',\r\n//     /**扣除固定 */\r\n//     fixedDeduct = 'fixedDeduct',\r\n//     /**扣除百分比 */\r\n//     percentDeduct = 'percentDeduct',\r\n//     /**破碎的血瓶，使用后恢复剩余的血量 */\r\n//     breakRecovery = 'breakRecovery',\r\n// }\r\n\r\n// export enum EPollutionType {\r\n//     /**污染其他卡牌 */\r\n//     pollutionOtherCard = 'pollutionOtherCard',\r\n//     /**自爆卡 */\r\n//     selfBomb = 'selfBomb',\r\n// }\r\n\r\n// export enum EGiftType {\r\n//     /**装备礼物卡 */\r\n//     equipmentGift = 'equipmentGift',\r\n//     /**卷轴礼物卡 */\r\n//     scrollGift = 'scrollGift',\r\n// }\r\n\r\n\r\n// declare global {\r\n//     type ICardBaseName = 'CardBase'\r\n//     /**卡牌或角色的基础数据 */\r\n//     export interface ICardDataType extends IBaseDataType {\r\n//         /**卡牌hp值（耐久值） */\r\n//         hp: number;\r\n//         /**卡牌血量的上限值 */\r\n//         hpUp: number;\r\n//         /**卡牌所属阵营 */\r\n//         camp: ECamp;\r\n//         /**攻击值 */\r\n//         attack: number;\r\n//         /**卡牌的状态 */\r\n//         status?: ECardStatus;\r\n\r\n//         /**卡片类型 */\r\n//         type: ECardType\r\n//         /**卡片图片名字 */\r\n//         cardImageName: string\r\n//         /**卡牌值 */\r\n//         value: number\r\n//         /**卡牌原始值 */\r\n//         originValue?: number\r\n//         /**装备类型 （当卡牌为装备卡时）*/\r\n//         equipType?: number\r\n//         /**装备在场景的图片名字 */\r\n//         equipImgName?: string\r\n//         /**加血回复类型（当卡牌和血量有关时）*/\r\n//         hpType?: EHpType\r\n//         /**污染类型（当卡牌和污染卡片有关时 */\r\n//         pollutionType?: EPollutionType\r\n//         /**是否可以使用 */\r\n//         isCanUse?: boolean\r\n//         /**礼物卡牌类型 */\r\n//         giftType?: EGiftType\r\n//         /**是否持续计时卡片 */\r\n//         isDuration?: boolean\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 卡牌或角色的基类\r\n//  * @description: 说明\r\n//  * @Date : 2020-08-12 23:29:02\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:07:04\r\n//  * @LastEditors : judu233\r\n//  */\r\n// // @ccclass\r\n// export default class CardBase extends Base implements ICardDataType {\r\n//     /**存储卡牌的数据 */\r\n//     data: ICardDataType\r\n//     /**卡牌技能 */\r\n//     skillMgr = new SkillManager\r\n\r\n//     /**卡牌id */\r\n//     @Base.ViewLinked\r\n//     id: string\r\n\r\n//     /**卡牌血量*/\r\n//     @Base.ViewLinked\r\n//     hp: number\r\n\r\n//     /**卡牌血量的上限值 */\r\n//     hpUp: number\r\n\r\n//     /**卡牌的状态 */\r\n//     @Base.ViewLinked\r\n//     status: ECardStatus\r\n\r\n//     /**卡牌名字 */\r\n//     @Base.ViewLinked\r\n//     cardName: string\r\n\r\n//     /**卡牌的阵营 */\r\n//     camp: ECamp\r\n\r\n//     /**卡牌攻击力 */\r\n//     @Base.ViewLinked\r\n//     attack: number\r\n\r\n//     type: ECardType\r\n\r\n//     @Base.ViewLinked\r\n//     value: number\r\n\r\n//     cardImageName: string\r\n//     originValue: number\r\n\r\n//     get isDeath() { return this.status == ECardStatus.Death; }\r\n\r\n//     initCard() {\r\n//         this.status = ECardStatus.None\r\n//         this.data.originValue = this.value\r\n//         if (this.data.isDuration) {\r\n//             this.schedule(this.onScheduleTimer, 1)\r\n//         }\r\n//     }\r\n//     onScheduleTimer() {\r\n//         this.value--;\r\n//         if (this.value < 0) {\r\n//             this.data.value = 0\r\n//             if (this.data.pollutionType == EPollutionType.selfBomb) {\r\n//                 BRO.broadcast(BRO.keys.DiscardCard, this.id)\r\n//                 FightManager.playerCamp.getRole().hp -= 20\r\n//             } else if (this.data.pollutionType == EPollutionType.pollutionOtherCard) {\r\n//                 BRO.broadcast(BRO.keys.PolluteCard, this.id)\r\n//             }\r\n//             this.unschedule(this.onScheduleTimer)\r\n//         }\r\n//     }\r\n\r\n//     hpAfterChangeView(value: number) {\r\n//         if (value < 0) {\r\n//             this.hp = 0;\r\n//             this.status = ECardStatus.Death;\r\n//         } else {\r\n//             if (value >= this.hpUp) value = this.hpUp;\r\n//             this.data.hp = value;\r\n//         }\r\n//     }\r\n\r\n//     broadcast(target, key, newValue) {\r\n//         BRO.broadcast(BRO.keys.RefreshUI, { [key]: newValue }, target)\r\n//     }\r\n\r\n\r\n//     /********************** 关于卡牌的操作 ********************** */\r\n//     /**\r\n//      * 攻击指定阵营\r\n//      * @param camp 要攻击的阵营\r\n//      */\r\n//     async attackCamp(camp: CampBase) {\r\n//         if (FightManager.isAuto || this.camp == ECamp.Computer) {\r\n//             await Promise.all(this.attackCards(camp, 1));\r\n//         } else {\r\n//             await new Promise<void>(waitResolve => {\r\n//                 BRO.once(BRO.keys.PlayerAttack, async () => {\r\n//                     await this.attackCards(camp, 1)[0];\r\n//                     waitResolve();\r\n//                 }, this);\r\n//             });\r\n//         }\r\n//     }\r\n\r\n//     /**\r\n//      * 攻击指定的卡牌\r\n//      * @param card 要攻击的卡牌\r\n//      */\r\n//     async attackCard(card: CardBase) {\r\n//         cc.log(`[${this.cardName}]卡牌开始攻击[${card.cardName}]`);\r\n//         await Promise.wait(1000)\r\n//         card.hp -= this.attack;\r\n//         cc.log(`[${this.cardName}]卡牌攻击完成[${card.cardName}]`);\r\n//     }\r\n\r\n//     async attackRole(role: RoleBase) {\r\n//         cc.log(`[${this.cardName}]卡牌开始攻击[${role.roleName}]`);\r\n//         await Promise.wait(1000)\r\n//         role.hp -= this.attack;\r\n//         cc.log(`[${this.cardName}]卡牌攻击完成[${role.roleName}]`);\r\n//     }\r\n\r\n//     /**\r\n//      * 攻击多个卡牌(同时&&轮流)\r\n//      * @param attackCamp 要攻击的阵营\r\n//      * @param attackCount 攻击数量\r\n//      * @param isSimultaneously 是否同时攻击--默认同时\r\n//      */\r\n//     attackCards(attackCamp: CampBase, attackCount: number, isSimultaneously: boolean = true) {\r\n//         //获取卡牌\r\n//         let cardList = attackCamp.roleMgr.getRoleCard(attackCount)\r\n//         let promiseList: Promise<unknown>[] = [];\r\n//         //电脑攻击模式 -- 同时或轮流\r\n//         if (this.camp == ECamp.Computer) {\r\n//             isSimultaneously = FightManager.isComputerTurnAttack;\r\n//         } else if (this.camp == ECamp.Player) {\r\n//             isSimultaneously = FightManager.isPlayerTurnAttack;\r\n//         }\r\n//         if (isSimultaneously) {\r\n//             //同步攻击\r\n//             for (let card of cardList)\r\n//                 promiseList.push(this.attackCard(card));\r\n//         } else {\r\n//             //异步攻击（轮流）\r\n//             let selfCamp = this;\r\n//             promiseList.push(new Promise<void>(async (resolve) => {\r\n//                 for (let card of cardList)\r\n//                     await selfCamp.attackCard(card);\r\n//                 //完成攻击\r\n//                 resolve();\r\n//             }));\r\n//         }\r\n//         return promiseList;\r\n//     }\r\n\r\n// }\r\n"]}