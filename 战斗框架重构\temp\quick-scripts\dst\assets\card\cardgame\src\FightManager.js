
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/FightManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b8010B43FFEXoXFRK2DNSvj', 'FightManager');
// card/cardgame/src/FightManager.ts

// import CampManager, { ECamp } from "./CampManager";
// import { BRO } from "./EventManager";
// import SkillManager from "./SkillManager";
// /**
//  * @features : 战斗全局类
//  * @description : 战斗全局静态调用类
//  * @Date : 2023-11-10 16:14:27
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:03:58
//  * @LastEditors : judu233
//  */
// export default class FightManager {
//     /**阵营管理 */
//     static campManager: CampManager;
//     /**战斗流程是否是自动的 */
//     static isAuto = false;
//     /**敌人··电脑是否轮流攻击 */
//     static isComputerTurnAttack: boolean = true;
//     /**玩家是否轮流攻击 */
//     static isPlayerTurnAttack: boolean = true;
//     static isCanUseCard = false
//     static isMonsterComing = false
//     static get playerCamp() { return this.campManager.getPlayerCamp(); }
//     static get enemyCamp() { return this.campManager.getEnemyCamp(); }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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