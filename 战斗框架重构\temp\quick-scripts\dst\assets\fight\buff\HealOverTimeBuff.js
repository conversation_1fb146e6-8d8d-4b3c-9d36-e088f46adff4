
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/buff/HealOverTimeBuff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0d869kPbP1DcoLTZ3RTKzF5', 'HealOverTimeBuff');
// fight/buff/HealOverTimeBuff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealOverTimeBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var Buff_1 = require("../types/Buff");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
/**
 * 持续治疗Buff
 * 每秒恢复一定生命值的增益效果
 */
var HealOverTimeBuff = /** @class */ (function () {
    function HealOverTimeBuff(caster, target, duration, healPerSecond) {
        this._name = "持续治疗";
        this._description = "每秒恢复生命值";
        this._type = IBuff_1.BuffType.BUFF;
        this._stackCount = 1;
        this._maxStack = 3;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._healInterval = 1.0; // 每秒触发一次
        this._lastHealTime = 0;
        // 视觉效果
        this._iconPath = "icons/buffs/heal_over_time";
        this._effectPrefabPath = "prefabs/effects/HealAura";
        this._id = "heal_over_time_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._healPerSecond = healPerSecond;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u6BCF\u79D2\u6062\u590D" + healPerSecond + "\u70B9\u751F\u547D\u503C\uFF0C\u6301\u7EED" + duration + "\u79D2";
    }
    Object.defineProperty(HealOverTimeBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealOverTimeBuff.prototype, "periodicEffect", {
        get: function () {
            return {
                interval: this._healInterval,
                effectType: IBuff_1.BuffPeriodicEffectType.HEAL_OVER_TIME,
                value: this._healPerSecond,
                stackable: true
            };
        },
        enumerable: false,
        configurable: true
    });
    /** Buff被添加时触发 */
    HealOverTimeBuff.prototype.onApply = function () {
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        // 播放应用特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /** Buff每帧更新时触发 */
    HealOverTimeBuff.prototype.onTick = function (deltaTime) {
        this._lastHealTime += deltaTime;
        // 检查是否到了治疗时间
        if (this._lastHealTime >= this._healInterval) {
            this.performHeal();
            this._lastHealTime = 0;
        }
    };
    /** Buff被移除时触发 */
    HealOverTimeBuff.prototype.onRemove = function () {
        console.log(this._name + " removed from " + this._target.characterName);
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 执行治疗 */
    HealOverTimeBuff.prototype.performHeal = function () {
        if (this._target && !this._target.isDead) {
            var totalHeal = this._healPerSecond * this._stackCount;
            this._target.heal(totalHeal);
            console.log(this._target.characterName + " healed for " + totalHeal + " HP from " + this._name);
            // 播放治疗特效
            this.playHealEffect();
            // 触发治疗事件
            this._eventManager.emit(FightEvent_1.default.characterHealed, { target: this._target, healer: this._caster, amount: totalHeal, source: this });
        }
    };
    /** 更新Buff状态 */
    HealOverTimeBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    HealOverTimeBuff.prototype.refresh = function () {
        this._remainingTime = this._duration;
        this._lastHealTime = 0;
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    HealOverTimeBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    HealOverTimeBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    HealOverTimeBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.heal_per_second:
                return this._healPerSecond * this._stackCount;
            case Buff_1.EBuffEffectType.total_heal:
                return this._healPerSecond * this._stackCount * this._remainingTime;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    HealOverTimeBuff.prototype.conflictsWith = function (_otherBuff) {
        // 同类型的持续治疗buff不冲突，可以叠加
        return false;
    };
    /** 播放应用特效 */
    HealOverTimeBuff.prototype.playApplyEffect = function () {
        console.log("Playing apply effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效播放逻辑
    };
    /** 播放治疗特效 */
    HealOverTimeBuff.prototype.playHealEffect = function () {
        console.log("Playing heal effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的治疗特效播放逻辑
    };
    /** 停止特效 */
    HealOverTimeBuff.prototype.stopEffect = function () {
        console.log("Stopping effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    HealOverTimeBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            healPerSecond: this._healPerSecond,
            totalHealRemaining: this.getEffectValue(Buff_1.EBuffEffectType.total_heal),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return HealOverTimeBuff;
}());
exports.HealOverTimeBuff = HealOverTimeBuff;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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