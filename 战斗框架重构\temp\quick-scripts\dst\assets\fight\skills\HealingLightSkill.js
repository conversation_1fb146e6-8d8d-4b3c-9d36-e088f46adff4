
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/skills/HealingLightSkill.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '31570J6F7hBKKZhVOpmBzsr', 'HealingLightSkill');
// fight/skills/HealingLightSkill.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealingLightSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var ISkill_1 = require("../types/ISkill");
var SkillName_1 = require("../types/SkillName");
/**
 * 治疗之光技能
 * 单体治疗技能，恢复目标生命值并提供短暂的生命恢复buff
 */
var HealingLightSkill = /** @class */ (function () {
    function HealingLightSkill() {
        this._id = SkillName_1.default.healing_light;
        this._name = "治疗之光";
        this._description = "释放治疗之光，恢复友军生命值并提供持续治疗效果";
        this._cooldown = 3.0;
        this._remainingCooldown = 0;
        this._mpCost = 30;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ALLY;
        this._range = 200;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_heal",
            soundId: "heal_cast",
            effectPath: "prefabs/effects/HealingLight",
            healAmount: 0,
            healMultiplier: 1.5,
            buffDuration: 5.0,
            buffHealPerSecond: 10 // 每秒治疗量
        };
    }
    Object.defineProperty(HealingLightSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    HealingLightSkill.prototype.canCastOn = function (caster, target) {
        if (!target)
            return false;
        if (target.isDead)
            return false;
        if (target.role === caster.role)
            return true; // 同阵营
        return false;
    };
    /** 检查资源消耗 */
    HealingLightSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    HealingLightSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    HealingLightSkill.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " on " + (target === null || target === void 0 ? void 0 : target.characterName));
        return true;
    };
    /** 创建技能Timeline */
    HealingLightSkill.prototype.createTimeline = function (caster, target, targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 2.0, caster);
        // 0.0s: 播放施法动画和音效
        var castNode = new Timeline_1.TimelineNode(timelineId + "_cast", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_animation", this._config.animationName), false);
        timeline.addNode(castNode);
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.1, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        // 0.8s: 执行治疗效果
        var healNode = new Timeline_1.TimelineNode(timelineId + "_heal", 0.8, new HealTimelineEvent("heal_effect", target, caster, this.calculateHealAmount(caster)), false);
        timeline.addNode(healNode);
        // 1.0s: 播放治疗特效
        var effectNode = new Timeline_1.TimelineNode(timelineId + "_effect", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("heal_effect", this._config.effectPath, true, target === null || target === void 0 ? void 0 : target.node.position), false);
        timeline.addNode(effectNode);
        return timeline;
    };
    /** 计算治疗量 */
    HealingLightSkill.prototype.calculateHealAmount = function (caster) {
        if (this._config.healAmount > 0) {
            return this._config.healAmount;
        }
        var baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.healMultiplier);
    };
    /** 更新技能冷却 */
    HealingLightSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    HealingLightSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    HealingLightSkill.prototype.levelUp = function () {
        this._level++;
        this._mpCost = Math.max(15, this._mpCost - 2);
        this._cooldown = Math.max(1.5, this._cooldown - 0.2);
        this._range += 15;
        this._config.healMultiplier += 0.1;
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    HealingLightSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            healMultiplier: this._config.healMultiplier
        };
    };
    return HealingLightSkill;
}());
exports.HealingLightSkill = HealingLightSkill;
/**
 * 治疗Timeline事件
 */
var HealTimelineEvent = /** @class */ (function () {
    function HealTimelineEvent(id, target, caster, healAmount) {
        this._type = ITimeline_1.TimelineEventType.HEAL;
        this._id = id;
        this._target = target;
        this._caster = caster;
        this._healAmount = healAmount;
    }
    Object.defineProperty(HealTimelineEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealTimelineEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    HealTimelineEvent.prototype.execute = function (timeline, nodeIndex, context) {
        if (this._target && !this._target.isDead) {
            // 执行治疗
            this._target.heal(this._healAmount);
            console.log(this._caster.characterName + " heals " + this._target.characterName + " for " + this._healAmount + " HP");
            // 添加持续治疗buff（这里需要实现HealOverTimeBuff）
            // const healBuff = new HealOverTimeBuff(this._caster, this._target, 5.0, 10);
            // this._target.buffManager.addBuff(healBuff);
        }
    };
    return HealTimelineEvent;
}());
// 需要导入TimelineEventType
var ITimeline_1 = require("../types/ITimeline");
var BattleManager_1 = require("../systems/BattleManager");

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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