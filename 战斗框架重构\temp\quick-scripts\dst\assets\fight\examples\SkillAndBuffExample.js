
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/examples/SkillAndBuffExample.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6af80gH3+JN3qm3GDBvWswx', 'SkillAndBuffExample');
// fight/examples/SkillAndBuffExample.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillAndBuffExample = void 0;
var BattleManager_1 = require("../systems/BattleManager");
var Character_1 = require("../characters/Character");
var CharacterTypes_1 = require("../types/CharacterTypes");
// 导入技能
var PlayerSkillFire1_1 = require("../skills/PlayerSkillFire1");
var HealingLightSkill_1 = require("../skills/HealingLightSkill");
var ThunderStormSkill_1 = require("../skills/ThunderStormSkill");
var ChargeAttackSkill_1 = require("../skills/ChargeAttackSkill");
// 导入buff
var AttackBoostBuff_1 = require("../buff/AttackBoostBuff");
var HealOverTimeBuff_1 = require("../buff/HealOverTimeBuff");
var StunBuff_1 = require("../buff/StunBuff");
var PoisonBuff_1 = require("../buff/PoisonBuff");
/**
 * 技能和Buff使用示例
 * 展示如何使用新的技能系统和buff系统
 */
var SkillAndBuffExample = /** @class */ (function () {
    function SkillAndBuffExample() {
        this.battleManager = BattleManager_1.BattleManager.getInstance();
        this.initializeCharacters();
        this.setupSkills();
        this.runExample();
    }
    /** 初始化角色 */
    SkillAndBuffExample.prototype.initializeCharacters = function () {
        // 创建玩家角色（战士）
        this.player = new Character_1.Character();
        this.player.setCharacterData({
            prefabKey: "player_warrior",
            name: "勇敢的战士",
            role: CharacterTypes_1.CharacterRole.HERO,
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                maxStamina: 150,
                attack: 120,
                defense: 80,
                attackSpeed: 1.0,
                moveSpeed: 200,
                attackRange: 150,
                criticalRate: 0.15,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.1,
                level: 1,
                experience: 0
            }
        });
        // 创建敌人角色
        this.enemy = new Character_1.Character();
        this.enemy.setCharacterData({
            prefabKey: "enemy_orc",
            name: "兽人战士",
            role: CharacterTypes_1.CharacterRole.ENEMY,
            initialAttributes: {
                hp: 800,
                maxHp: 800,
                mp: 100,
                maxMp: 100,
                maxStamina: 120,
                attack: 100,
                defense: 60,
                attackSpeed: 1.0,
                moveSpeed: 180,
                attackRange: 120,
                criticalRate: 0.1,
                criticalDamage: 1.3,
                hitRate: 0.9,
                dodgeRate: 0.05,
                level: 1,
                experience: 0
            }
        });
        // 创建治疗师角色
        this.healer = new Character_1.Character();
        this.healer.setCharacterData({
            prefabKey: "healer_priest",
            name: "神圣牧师",
            role: CharacterTypes_1.CharacterRole.HERO,
            initialAttributes: {
                hp: 600,
                maxHp: 600,
                mp: 300,
                maxMp: 300,
                maxStamina: 80,
                attack: 50,
                defense: 40,
                attackSpeed: 1.0,
                moveSpeed: 160,
                attackRange: 200,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.98,
                dodgeRate: 0.15,
                level: 1,
                experience: 0
            }
        });
        console.log("Characters initialized:");
        console.log("Player: " + this.player.characterName + " - HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        console.log("Enemy: " + this.enemy.characterName + " - HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        console.log("Healer: " + this.healer.characterName + " - HP: " + this.healer.attributes.currentHp + "/" + this.healer.attributes.maxHp);
    };
    /** 设置技能 */
    SkillAndBuffExample.prototype.setupSkills = function () {
        // 为玩家添加技能
        var fireballSkill = new PlayerSkillFire1_1.PlayerSkillFire1();
        var chargeSkill = new ChargeAttackSkill_1.ChargeAttackSkill();
        this.player.skillManager.addSkill(fireballSkill);
        this.player.skillManager.addSkill(chargeSkill);
        // 为治疗师添加技能
        var healSkill = new HealingLightSkill_1.HealingLightSkill();
        var thunderSkill = new ThunderStormSkill_1.ThunderStormSkill();
        this.healer.skillManager.addSkill(healSkill);
        this.healer.skillManager.addSkill(thunderSkill);
        console.log("Skills added to characters");
    };
    /** 运行示例 */
    SkillAndBuffExample.prototype.runExample = function () {
        console.log("\n=== 技能和Buff系统演示开始 ===\n");
        // 开始战斗
        this.battleManager.startBattle("skill_demo_battle", [this.player, this.enemy, this.healer]);
        // 演示1: 攻击力提升buff
        this.demonstrateAttackBoostBuff();
        // 演示2: 火球术攻击
        this.demonstrateFireballAttack();
        // 演示3: 毒素debuff
        this.demonstratePoisonDebuff();
        // 演示4: 治疗技能和持续治疗buff
        this.demonstrateHealingSkill();
        // 演示5: 眩晕debuff
        this.demonstrateStunDebuff();
        // 演示6: 冲锋攻击技能
        this.demonstrateChargeAttack();
        // 演示7: 雷暴术范围攻击
        this.demonstrateThunderStorm();
        // 模拟战斗更新
        this.simulateBattleUpdates();
        console.log("\n=== 技能和Buff系统演示结束 ===\n");
    };
    /** 演示攻击力提升buff */
    SkillAndBuffExample.prototype.demonstrateAttackBoostBuff = function () {
        console.log("\n--- 演示攻击力提升Buff ---");
        console.log(this.player.name + " \u5F53\u524D\u653B\u51FB\u529B: " + this.player.attributes.attack);
        var attackBuff = new AttackBoostBuff_1.AttackBoostBuff(this.player, this.player, 10.0, 1.5);
        this.player.buffManager.addBuff(attackBuff);
        console.log(this.player.name + " \u83B7\u5F97\u653B\u51FB\u529B\u63D0\u5347buff\u540E\u653B\u51FB\u529B: " + this.player.attributes.attack);
        console.log("Buff\u4FE1\u606F:", attackBuff.getDebugInfo());
    };
    /** 演示火球术攻击 */
    SkillAndBuffExample.prototype.demonstrateFireballAttack = function () {
        console.log("\n--- 演示火球术攻击 ---");
        var fireballSkill = this.player.skillManager.getSkill("player_skill_fire1");
        if (fireballSkill) {
            console.log(this.player.name + " \u91CA\u653E " + fireballSkill.name);
            console.log("\u654C\u4EBA\u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
            var success = fireballSkill.cast(this.player, this.enemy);
            console.log("\u6280\u80FD\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 演示毒素debuff */
    SkillAndBuffExample.prototype.demonstratePoisonDebuff = function () {
        console.log("\n--- 演示毒素Debuff ---");
        console.log(this.enemy.name + " \u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        var poisonBuff = new PoisonBuff_1.PoisonBuff(this.player, this.enemy, 8.0, 25);
        this.enemy.buffManager.addBuff(poisonBuff);
        console.log(this.enemy.name + " \u4E2D\u6BD2\u4E86\uFF01");
        console.log("\u6BD2\u7D20\u4FE1\u606F:", poisonBuff.getDebugInfo());
    };
    /** 演示治疗技能 */
    SkillAndBuffExample.prototype.demonstrateHealingSkill = function () {
        console.log("\n--- 演示治疗技能 ---");
        // 先让玩家受伤
        this.player.attributes.takeDamage(300);
        console.log(this.player.name + " \u53D7\u4F24\u540EHP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        var healSkill = this.healer.skillManager.getSkill("healing_light");
        if (healSkill) {
            console.log(this.healer.name + " \u5BF9 " + this.player.name + " \u91CA\u653E " + healSkill.name);
            var success = healSkill.cast(this.healer, this.player);
            console.log("\u6CBB\u7597\u6280\u80FD\u91CA\u653E" + (success ? '成功' : '失败'));
        }
        // 添加持续治疗buff
        var healOverTimeBuff = new HealOverTimeBuff_1.HealOverTimeBuff(this.healer, this.player, 6.0, 20);
        this.player.buffManager.addBuff(healOverTimeBuff);
        console.log(this.player.name + " \u83B7\u5F97\u6301\u7EED\u6CBB\u7597\u6548\u679C");
    };
    /** 演示眩晕debuff */
    SkillAndBuffExample.prototype.demonstrateStunDebuff = function () {
        console.log("\n--- 演示眩晕Debuff ---");
        console.log(this.enemy.name + " \u5F53\u524D\u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        var stunBuff = new StunBuff_1.StunBuff(this.player, this.enemy, 3.0);
        this.enemy.buffManager.addBuff(stunBuff);
        console.log(this.enemy.name + " \u88AB\u7729\u6655\u4E86\uFF01");
        console.log(this.enemy.name + " \u7729\u6655\u540E\u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        console.log("\u7729\u6655\u4FE1\u606F:", stunBuff.getDebugInfo());
    };
    /** 演示冲锋攻击技能 */
    SkillAndBuffExample.prototype.demonstrateChargeAttack = function () {
        console.log("\n--- 演示冲锋攻击技能 ---");
        var chargeSkill = this.player.skillManager.getSkill("charge_attack");
        if (chargeSkill) {
            console.log(this.player.name + " \u5BF9 " + this.enemy.name + " \u91CA\u653E " + chargeSkill.name);
            console.log("\u654C\u4EBA\u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
            var success = chargeSkill.cast(this.player, this.enemy);
            console.log("\u51B2\u950B\u653B\u51FB\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 演示雷暴术范围攻击 */
    SkillAndBuffExample.prototype.demonstrateThunderStorm = function () {
        console.log("\n--- 演示雷暴术范围攻击 ---");
        var thunderSkill = this.healer.skillManager.getSkill("thunder_storm");
        if (thunderSkill) {
            var targetPosition = cc.v3(100, 100, 0);
            console.log(this.healer.name + " \u5728\u4F4D\u7F6E (" + targetPosition.x + ", " + targetPosition.y + ") \u91CA\u653E " + thunderSkill.name);
            var success = thunderSkill.cast(this.healer, undefined, undefined, targetPosition);
            console.log("\u96F7\u66B4\u672F\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 模拟战斗更新 */
    SkillAndBuffExample.prototype.simulateBattleUpdates = function () {
        var _this = this;
        console.log("\n--- 模拟战斗更新 (5秒) ---");
        var updateInterval = 0.1; // 100ms更新间隔
        var totalTime = 5.0; // 总共5秒
        var currentTime = 0;
        var updateTimer = setInterval(function () {
            currentTime += updateInterval;
            // 更新所有角色的buff
            _this.player.buffManager.update(updateInterval);
            _this.enemy.buffManager.update(updateInterval);
            _this.healer.buffManager.update(updateInterval);
            // 更新技能冷却
            _this.player.skillManager.update(updateInterval);
            _this.enemy.skillManager.update(updateInterval);
            _this.healer.skillManager.update(updateInterval);
            // 更新战斗管理器
            _this.battleManager.update(updateInterval);
            // 每秒输出一次状态
            if (Math.floor(currentTime) !== Math.floor(currentTime - updateInterval)) {
                console.log("\n\u65F6\u95F4: " + Math.floor(currentTime) + "s");
                console.log(_this.player.name + " HP: " + _this.player.attributes.currentHp + "/" + _this.player.attributes.maxHp + ", Buffs: " + _this.player.buffManager.getAllBuffs().length);
                console.log(_this.enemy.name + " HP: " + _this.enemy.attributes.currentHp + "/" + _this.enemy.attributes.maxHp + ", Buffs: " + _this.enemy.buffManager.getAllBuffs().length);
                console.log(_this.healer.name + " HP: " + _this.healer.attributes.currentHp + "/" + _this.healer.attributes.maxHp + ", Buffs: " + _this.healer.buffManager.getAllBuffs().length);
            }
            if (currentTime >= totalTime) {
                clearInterval(updateTimer);
                _this.printFinalStatus();
            }
        }, updateInterval * 1000);
    };
    /** 打印最终状态 */
    SkillAndBuffExample.prototype.printFinalStatus = function () {
        console.log("\n--- 最终状态 ---");
        console.log(this.player.name + ":");
        console.log("  HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        console.log("  \u653B\u51FB\u529B: " + this.player.attributes.attack);
        console.log("  \u6D3B\u8DC3Buffs: " + this.player.buffManager.getAllBuffs().length);
        console.log(this.enemy.name + ":");
        console.log("  HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        console.log("  \u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        console.log("  \u6D3B\u8DC3Buffs: " + this.enemy.buffManager.getAllBuffs().length);
        console.log(this.healer.name + ":");
        console.log("  HP: " + this.healer.attributes.currentHp + "/" + this.healer.attributes.maxHp);
        console.log("  MP: " + this.healer.attributes.currentMp + "/" + this.healer.attributes.maxMp);
        console.log("  \u6D3B\u8DC3Buffs: " + this.healer.buffManager.getAllBuffs().length);
        // 打印战斗统计
        console.log("\n战斗统计:", this.battleManager.getBattleStats());
    };
    return SkillAndBuffExample;
}());
exports.SkillAndBuffExample = SkillAndBuffExample;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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