
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/actions/AttackAction.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aa8d9i2HDBGRY3h7us0CBBt', 'AttackAction');
// fight/actions/AttackAction.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackAction = void 0;
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 攻击动作类*/
var AttackAction = /** @class */ (function (_super) {
    __extends(AttackAction, _super);
    function AttackAction() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**当前攻击状态 */
        _this.currentState = CharacterTypes_1.AttackState.IDLE;
        /**攻击计时器 */
        _this._attackTimer = 0;
        /**当前攻击属性 */
        _this._currentProps = null;
        /**回调标记 */
        _this._hurtStartTriggered = false;
        _this._hurtEndTriggered = false;
        /**攻击队列 */
        _this._attackQueue = [];
        /**是否允许攻击队列 */
        _this.allowQueue = false;
        /**最大队列长度 */
        _this.maxQueueLength = 3;
        return _this;
    }
    Object.defineProperty(AttackAction.prototype, "isAttacking", {
        /*** 是否正在攻击*/
        get: function () {
            return this.currentState !== CharacterTypes_1.AttackState.IDLE;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackAction.prototype, "canStartNewAttack", {
        /** * 是否可以开始新的攻击 */
        get: function () {
            return this.currentState === CharacterTypes_1.AttackState.IDLE || (this.allowQueue && this._attackQueue.length < this.maxQueueLength);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackAction.prototype, "attackProgress", {
        /** * 获取攻击进度 (0-1) */
        get: function () {
            if (!this._currentProps || this.currentState === CharacterTypes_1.AttackState.IDLE) {
                return 0;
            }
            return Math.min(1, this._attackTimer / this._currentProps.hurtEndTimeMs);
        },
        enumerable: false,
        configurable: true
    });
    /*** 更新攻击状态*/
    AttackAction.prototype.update = function (dt) {
        if (this.currentState === CharacterTypes_1.AttackState.IDLE) {
            return;
        }
        this.updateAttackTimer(dt);
        this.updateAttackState();
        this.triggerCallbacks();
    };
    /** * 组件销毁时清理 */
    AttackAction.prototype.onDestroy = function () {
        this.interruptAttack();
        this.clearQueue();
    };
    /**
     * 执行一次攻击
     * @param props 攻击属性
     * @returns 是否成功开始攻击
     */
    AttackAction.prototype.doAttackOnce = function (props) {
        if (!this.validateAttackProps(props)) {
            console.error("Invalid attack properties");
            return false;
        }
        if (this.currentState === CharacterTypes_1.AttackState.IDLE) {
            this.startAttack(props);
            return true;
        }
        else if (this.allowQueue && this._attackQueue.length < this.maxQueueLength) {
            this._attackQueue.push(props);
            return true;
        }
        return false;
    };
    /*** 中断当前攻击*/
    AttackAction.prototype.interruptAttack = function () {
        if (this.currentState !== CharacterTypes_1.AttackState.IDLE) {
            this.resetAttack();
            this.setState(CharacterTypes_1.AttackState.IDLE);
        }
    };
    /*** 清空攻击队列*/
    AttackAction.prototype.clearQueue = function () {
        this._attackQueue.length = 0;
    };
    /** * 获取队列中的攻击数量 */
    AttackAction.prototype.getQueueLength = function () { return this._attackQueue.length; };
    /**  * 开始攻击  */
    AttackAction.prototype.startAttack = function (props) {
        this._currentProps = props;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
        this.setState(CharacterTypes_1.AttackState.WINDUP);
    };
    /*** 更新攻击计时器*/
    AttackAction.prototype.updateAttackTimer = function (dt) {
        this._attackTimer += dt * 1000; // 转换为毫秒
    };
    /** * 更新攻击状态 */
    AttackAction.prototype.updateAttackState = function () {
        if (!this._currentProps) {
            return;
        }
        var _a = this._currentProps, hurtStartTimeMs = _a.hurtStartTimeMs, hurtEndTimeMs = _a.hurtEndTimeMs;
        if (this._attackTimer >= hurtEndTimeMs) {
            this.setState(CharacterTypes_1.AttackState.IDLE);
            this.processNextAttack();
        }
        else if (this._attackTimer >= hurtStartTimeMs && this.currentState === CharacterTypes_1.AttackState.WINDUP) {
            this.setState(CharacterTypes_1.AttackState.DAMAGE);
        }
    };
    /*** 触发回调*/
    AttackAction.prototype.triggerCallbacks = function () {
        if (!this._currentProps) {
            return;
        }
        var _a = this._currentProps, hurtStartTimeMs = _a.hurtStartTimeMs, hurtEndTimeMs = _a.hurtEndTimeMs, onHurtStart = _a.onHurtStart, onHurtEnd = _a.onHurtEnd, onAttackProgress = _a.onAttackProgress;
        if (!this._hurtStartTriggered && this._attackTimer >= hurtStartTimeMs) {
            this._hurtStartTriggered = true;
            onHurtStart === null || onHurtStart === void 0 ? void 0 : onHurtStart();
        }
        if (onAttackProgress) {
            onAttackProgress(this.attackProgress);
        }
        if (!this._hurtEndTriggered && this._attackTimer >= hurtEndTimeMs) {
            this._hurtEndTriggered = true;
            onHurtEnd === null || onHurtEnd === void 0 ? void 0 : onHurtEnd();
        }
    };
    /*** 设置攻击状态*/
    AttackAction.prototype.setState = function (newState) {
        if (this.currentState !== newState) {
            var oldState = this.currentState;
            this.currentState = newState;
            this.onStateChanged(oldState, newState);
        }
    };
    /** * 状态改变时的处理 */
    AttackAction.prototype.onStateChanged = function (oldState, newState) {
        this.node.emit(FightEvent_1.default.attackStateChanged, { oldState: oldState, newState: newState, component: this });
        switch (newState) {
            case CharacterTypes_1.AttackState.IDLE:
                this.onEnterIdle();
                break;
            case CharacterTypes_1.AttackState.WINDUP:
                this.onEnterWindup();
                break;
            case CharacterTypes_1.AttackState.DAMAGE:
                this.onEnterDamage();
                break;
            case CharacterTypes_1.AttackState.RECOVERY:
                this.onEnterRecovery();
                break;
        }
    };
    /** * 重置攻击状态 */
    AttackAction.prototype.resetAttack = function () {
        this._currentProps = null;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
    };
    /** * 处理下一个攻击 */
    AttackAction.prototype.processNextAttack = function () {
        if (this._attackQueue.length > 0) {
            var nextAttack = this._attackQueue.shift();
            this.startAttack(nextAttack);
        }
    };
    /** * 验证攻击属性 */
    AttackAction.prototype.validateAttackProps = function (props) {
        if (!props) {
            return false;
        }
        if (props.hurtStartTimeMs < 0 || props.hurtEndTimeMs < 0) {
            return false;
        }
        if (props.hurtStartTimeMs > props.hurtEndTimeMs) {
            return false;
        }
        return true;
    };
    /*** 进入空闲状态*/
    AttackAction.prototype.onEnterIdle = function () {
        this.resetAttack();
    };
    /** * 进入前摇状态 */
    AttackAction.prototype.onEnterWindup = function () {
        // 可以在这里添加前摇特效
    };
    /** * 进入伤害状态 */
    AttackAction.prototype.onEnterDamage = function () {
        // 可以在这里添加伤害特效
    };
    /*** 进入后摇状态*/
    AttackAction.prototype.onEnterRecovery = function () {
        // 可以在这里添加后摇特效
    };
    /** * 获取攻击信息 */
    AttackAction.prototype.getAttackInfo = function () {
        return {
            currentState: this.currentState,
            isAttacking: this.isAttacking,
            attackProgress: this.attackProgress,
            queueLength: this._attackQueue.length,
            canStartNewAttack: this.canStartNewAttack,
            currentProps: this._currentProps ? {
                hurtStartTimeMs: this._currentProps.hurtStartTimeMs,
                hurtEndTimeMs: this._currentProps.hurtEndTimeMs
            } : null
        };
    };
    __decorate([
        property(cc.Boolean)
    ], AttackAction.prototype, "allowQueue", void 0);
    __decorate([
        property(cc.Integer)
    ], AttackAction.prototype, "maxQueueLength", void 0);
    AttackAction = __decorate([
        ccclass
    ], AttackAction);
    return AttackAction;
}(cc.Component));
exports.AttackAction = AttackAction;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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