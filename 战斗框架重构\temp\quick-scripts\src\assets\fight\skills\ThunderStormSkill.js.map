{"version": 3, "sources": ["assets\\fight\\skills\\ThunderStormSkill.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA,iDAA8D;AAC9D,6DAAyH;AAGzH,4CAA8C;AAC9C,0CAAqE;AACrE,gDAAkF;AAClF,gDAA2C;AAC3C,6CAA4C;AAC5C,0DAAyD;AAEzD;;;GAGG;AACH;IAAA;QACY,QAAG,GAAW,mBAAS,CAAC,aAAa,CAAC;QACtC,UAAK,GAAW,KAAK,CAAC;QACtB,iBAAY,GAAW,gCAAgC,CAAC;QACxD,cAAS,GAAW,GAAG,CAAC;QACxB,uBAAkB,GAAW,CAAC,CAAC;QAC/B,YAAO,GAAW,EAAE,CAAC;QACrB,iBAAY,GAAW,CAAC,CAAC;QACzB,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAc,kBAAS,CAAC,MAAM,CAAC;QACpC,gBAAW,GAAoB,wBAAe,CAAC,MAAM,CAAC;QACtD,WAAM,GAAW,GAAG,CAAC;QACrB,cAAS,GAAqB,IAAI,CAAC;QACnC,kBAAa,GAAY,EAAE,CAAC;QAEpC,WAAW;QACH,YAAO,GAAG;YACd,aAAa,EAAE,qBAAqB;YACpC,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,8BAA8B;YAC1C,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,CAAC;YACT,gBAAgB,EAAE,GAAG;YACrB,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;YACjB,cAAc,EAAE,CAAC;YACjB,iBAAiB,EAAE,GAAG,CAAC,OAAO;SACjC,CAAC;IA4JN,CAAC;IAzJG,sBAAI,iCAAE;QADN,aAAa;aACb,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,uCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,gDAAiB;aAArB,cAAkC,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;aACnE,UAAsB,KAAa,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAEnE,sBAAI,qCAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7C,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,mCAAI;aAAR,cAAwB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC5C,sBAAI,yCAAU;aAAd,cAAoC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAC9D,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,uCAAQ;aAAZ,cAA4B,OAAO,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC;;;OAAA;IACrD,sBAAI,2CAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAI,qCAAM;aAAV;YACI,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QACxC,CAAC;;;OAAA;IAED,oBAAoB;IACpB,qCAAS,GAAT,UAAU,OAAmB,EAAE,OAAoB;QAC/C,iBAAiB;QACjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,aAAa;IACb,6CAAiB,GAAjB,UAAkB,MAAkB;QAChC,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO;YAC9C,MAAM,CAAC,UAAU,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC;IAC9D,CAAC;IAED,WAAW;IACX,4CAAgB,GAAhB,UAAiB,MAAkB;QAC/B,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,WAAW;IACX,gCAAI,GAAJ,UAAK,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACpF,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxE,6BAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,eAAU,IAAI,CAAC,KAAK,sBAAiB,QAAQ,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,MAAG,CAAC,CAAC;QACtG,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,mBAAmB;IACX,0CAAc,GAAtB,UAAuB,MAAkB,EAAE,OAAoB,EAAE,QAAuB,EAAE,QAAkB;QACxG,IAAM,UAAU,GAAM,IAAI,CAAC,GAAG,SAAI,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QAC9F,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAEnE,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,uBAAY,CAC1B,UAAU,UAAO,EACpB,GAAG,EACH,IAAI,2CAA0B,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAC5E,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3B,eAAe;QACf,IAAM,SAAS,GAAG,IAAI,uBAAY,CAC3B,UAAU,WAAQ,EACrB,GAAG,EACH,IAAI,uCAAsB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAC9D,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5B,eAAe;QACf,IAAM,cAAc,GAAG,IAAI,uBAAY,CAChC,UAAU,iBAAc,EAC3B,GAAG,EACH,IAAI,wCAAuB,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,EACpF,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEjC,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE;YAClD,IAAM,aAAa,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC/D,IAAM,aAAa,GAAG,IAAI,uBAAY,CAC/B,UAAU,mBAAc,CAAG,EAC9B,aAAa,EACb,IAAI,oBAAoB,CAAC,eAAa,CAAG,EAAE,MAAM,EAAE,QAAS,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EACpH,KAAK,CACR,CAAC;YACF,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACnC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,WAAW;IACH,2CAAe,GAAvB,UAAwB,MAAkB;QACtC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SAC9B;QACD,IAAM,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;QACtD,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACvE,CAAC;IAED,aAAa;IACb,kCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC;SACxC;IACL,CAAC;IAED,aAAa;IACb,yCAAa,GAAb;QACI,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,WAAW;IACX,mCAAO,GAAP;QACI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,GAAG,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,uBAAkB,IAAI,CAAC,MAAQ,CAAC,CAAC;IAC9D,CAAC;IAED,aAAa;IACb,wCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YACnC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAC/C,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;SACtC,CAAC;IACN,CAAC;IACL,wBAAC;AAAD,CAvLA,AAuLC,IAAA;AAvLY,8CAAiB;AAyL9B;;GAEG;AACH;IAQI,8BAAY,EAAU,EAAE,MAAkB,EAAE,QAAiB,EAAE,MAAc,EAAE,MAAc;QANrF,UAAK,GAAsB,6BAAiB,CAAC,MAAM,CAAC;QAOxD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,sBAAI,oCAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,sCAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEpD,sCAAO,GAAP,UAAQ,SAAoB,EAAE,UAAkB,EAAE,QAAc;;QAC5D,aAAa;QACb,IAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;;YAC1C,KAAoB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;gBAAxB,IAAM,KAAK,oBAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACf,SAAS;oBACT,IAAM,UAAU,GAAG;wBACf,MAAM,EAAE,IAAI,CAAC,OAAO;wBACpB,IAAI,EAAE,oBAAU,CAAC,KAAK;wBACtB,MAAM,EAAE,IAAI,CAAC,OAAO;wBACpB,UAAU,EAAE,KAAK;wBACjB,OAAO,EAAE,SAAS;qBACrB,CAAC;oBACF,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAClD,OAAO,CAAC,GAAG,CAAC,uBAAqB,KAAK,CAAC,aAAa,aAAQ,IAAI,CAAC,OAAO,oBAAiB,CAAC,CAAC;oBAC3F,SAAS;oBACT,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,EAAE,UAAU;wBACjC,OAAO,CAAC,GAAG,CAAI,KAAK,CAAC,aAAa,8BAA2B,CAAC,CAAC;wBAC/D,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;wBACxD,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;qBACvC;iBACJ;aACJ;;;;;;;;;QACD,YAAY;QACZ,IAAI,CAAC,UAAU,CAAC,iCAAiC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IACvC,CAAC;IAED,eAAe;IACP,iDAAkB,GAA1B;QACI,eAAe;QACf,sCAAsC;QACtC,OAAO,CAAC,GAAG,CAAC,qCAAmC,IAAI,CAAC,OAAO,0BAAqB,IAAI,CAAC,SAAS,CAAC,CAAC,UAAK,IAAI,CAAC,SAAS,CAAC,CAAC,MAAG,CAAC,CAAC;QAC1H,OAAO,EAAE,CAAC;IACd,CAAC;IAED,yCAAU,GAAV,UAAY,QAAgB,EAAE,QAAkB;QAC5C,OAAO,CAAC,GAAG,CAAC,oBAAkB,QAAQ,uBAAiB,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,CAAC,YAAK,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,CAAC,OAAG,CAAC,CAAC;IAC3F,CAAC;IAED,wCAAS,GAAT,UAAW,OAAe;QACtB,OAAO,CAAC,GAAG,CAAC,mBAAiB,OAAS,CAAC,CAAC;IAC5C,CAAC;IACL,2BAAC;AAAD,CA9DA,AA8DC,IAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TimelineManager } from \"../systems/TimelineManager\";\nimport { Timeline, TimelineNode } from \"../timeline/Timeline\";\nimport { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from \"../timeline/TimelineEvents\";\nimport { IBuff } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { DamageType } from \"../types/IDamage\";\nimport { ISkill, SkillType, SkillTargetType } from \"../types/ISkill\";\nimport { ITimeline, ITimelineEvent, TimelineEventType } from \"../types/ITimeline\";\nimport SkillName from \"../types/SkillName\";\nimport { StunBuff } from \"../buff/StunBuff\";\nimport { BattleManager } from \"../systems/BattleManager\";\n\n/**\n * 雷暴术技能\n * 范围攻击技能，在指定区域召唤雷电攻击所有敌人\n */\nexport class ThunderStormSkill implements ISkill {\n    private _id: string = SkillName.thunder_storm;\n    private _name: string = \"雷暴术\";\n    private _description: string = \"在目标区域召唤雷暴，对范围内所有敌人造成雷电伤害并有概率眩晕\";\n    private _cooldown: number = 8.0;\n    private _remainingCooldown: number = 0;\n    private _mpCost: number = 60;\n    private _staminaCost: number = 0;\n    private _level: number = 1;\n    private _type: SkillType = SkillType.ACTIVE;\n    private _targetType: SkillTargetType = SkillTargetType.GROUND;\n    private _range: number = 400;\n    private _timeline: ITimeline | null = null;\n    private _passiveBuffs: IBuff[] = [];\n\n    /** 技能配置 */\n    private _config = {\n        animationName: \"skill_thunder_storm\",\n        soundId: \"thunder_cast\",\n        effectPath: \"prefabs/effects/ThunderStorm\",\n        areaRadius: 150, // 影响范围半径\n        damage: 0, // 0表示使用施法者的魔法攻击力\n        damageMultiplier: 2.0, // 伤害倍率\n        stunChance: 0.3, // 眩晕概率\n        stunDuration: 2.0, // 眩晕持续时间\n        lightningCount: 5, // 闪电数量\n        lightningInterval: 0.3 // 闪电间隔\n    };\n\n    // 实现ISkill接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get cooldown(): number { return this._cooldown; }\n    get remainingCooldown(): number { return this._remainingCooldown; }\n    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }\n    get mpCost(): number { return this._mpCost; }\n    get staminaCost(): number { return this._staminaCost; }\n    get level(): number { return this._level; }\n    get type(): SkillType { return this._type; }\n    get targetType(): SkillTargetType { return this._targetType; }\n    get range(): number { return this._range; }\n    get timeline(): ITimeline { return this._timeline!; }\n    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }\n    get canUse(): boolean {\n        return this._remainingCooldown <= 0;\n    }\n\n    /** 检查是否可以对目标使用技能 */\n    canCastOn(_caster: ICharacter, _target?: ICharacter): boolean {\n        // 地面目标技能，不需要特定目标\n        return true;\n    }\n\n    /** 检查资源消耗 */\n    checkResourceCost(caster: ICharacter): boolean {\n        return caster.attributes.currentMp >= this._mpCost &&\n            caster.attributes.currentStamina >= this._staminaCost;\n    }\n\n    /** 消耗资源 */\n    consumeResources(caster: ICharacter): void {\n        caster.attributes.consumeMp(this._mpCost);\n        caster.attributes.consumeStamina(this._staminaCost);\n    }\n\n    /** 释放技能 */\n    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {\n        if (!position) {\n            console.warn(\"ThunderStormSkill requires a target position\");\n            return false;\n        }\n        if (!this.checkResourceCost(caster)) {\n            return false;\n        }\n        this.consumeResources(caster);\n        this._remainingCooldown = this._cooldown;\n        this._timeline = this.createTimeline(caster, target, targets, position);\n        BattleManager.instance.timelineManager.addTimeline(this._timeline);\n        console.log(`${caster.characterName} casts ${this._name} at position (${position.x}, ${position.y})`);\n        return true;\n    }\n\n    /** 创建技能Timeline */\n    private createTimeline(caster: ICharacter, _target?: ICharacter, _targets?: ICharacter[], position?: cc.Vec3): ITimeline {\n        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        const timeline = new Timeline(timelineId, this._name, 3.0, caster);\n\n        // 0.0s: 播放施法动画\n        const castNode = new TimelineNode(\n            `${timelineId}_cast`,\n            0.0,\n            new PlayAnimationTimelineEvent(\"cast_animation\", this._config.animationName),\n            false\n        );\n        timeline.addNode(castNode);\n\n        // 0.5s: 播放施法音效\n        const soundNode = new TimelineNode(\n            `${timelineId}_sound`,\n            0.5,\n            new PlaySoundTimelineEvent(\"cast_sound\", this._config.soundId),\n            false\n        );\n        timeline.addNode(soundNode);\n\n        // 1.0s: 开始雷暴效果\n        const stormStartNode = new TimelineNode(\n            `${timelineId}_storm_start`,\n            1.0,\n            new PlayEffectTimelineEvent(\"storm_effect\", this._config.effectPath, true, position),\n            false\n        );\n        timeline.addNode(stormStartNode);\n\n        // 1.2s - 2.8s: 连续闪电攻击\n        for (let i = 0; i < this._config.lightningCount; i++) {\n            const lightningTime = 1.2 + i * this._config.lightningInterval;\n            const lightningNode = new TimelineNode(\n                `${timelineId}_lightning_${i}`,\n                lightningTime,\n                new LightningStrikeEvent(`lightning_${i}`, caster, position!, this._config.areaRadius, this.calculateDamage(caster)),\n                false\n            );\n            timeline.addNode(lightningNode);\n        }\n\n        return timeline;\n    }\n\n    /** 计算伤害 */\n    private calculateDamage(caster: ICharacter): number {\n        if (this._config.damage > 0) {\n            return this._config.damage;\n        }\n        const baseMagicAttack = caster.attributes.magicAttack;\n        return Math.floor(baseMagicAttack * this._config.damageMultiplier);\n    }\n\n    /** 更新技能冷却 */\n    update(deltaTime: number): void {\n        if (this._remainingCooldown > 0) {\n            this._remainingCooldown -= deltaTime;\n        }\n    }\n\n    /** 重置冷却时间 */\n    resetCooldown(): void {\n        this._remainingCooldown = 0;\n    }\n\n    /** 升级技能 */\n    levelUp(): void {\n        this._level++;\n        this._mpCost = Math.max(40, this._mpCost - 3);\n        this._cooldown = Math.max(5.0, this._cooldown - 0.5);\n        this._range += 30;\n        this._config.areaRadius += 10;\n        this._config.damageMultiplier += 0.2;\n        this._config.stunChance = Math.min(0.6, this._config.stunChance + 0.05);\n        console.log(`${this._name} leveled up to ${this._level}`);\n    }\n\n    /** 获取技能信息 */\n    getSkillInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            description: this._description,\n            level: this._level,\n            cooldown: this._cooldown,\n            remainingCooldown: this._remainingCooldown,\n            mpCost: this._mpCost,\n            staminaCost: this._staminaCost,\n            range: this._range,\n            canUse: this.canUse,\n            type: this._type,\n            targetType: this._targetType,\n            areaRadius: this._config.areaRadius,\n            damageMultiplier: this._config.damageMultiplier,\n            stunChance: this._config.stunChance\n        };\n    }\n}\n\n/**\n * 闪电攻击Timeline事件\n */\nclass LightningStrikeEvent implements ITimelineEvent {\n    private _id: string;\n    private _type: TimelineEventType = TimelineEventType.DAMAGE;\n    private _caster: ICharacter;\n    private _position: cc.Vec3;\n    private _radius: number;\n    private _damage: number;\n\n    constructor(id: string, caster: ICharacter, position: cc.Vec3, radius: number, damage: number) {\n        this._id = id;\n        this._caster = caster;\n        this._position = position;\n        this._radius = radius;\n        this._damage = damage;\n    }\n\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n\n    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {\n        // 查找范围内的所有敌人\n        const enemies = this.findEnemiesInRange();\n        for (const enemy of enemies) {\n            if (!enemy.isDead) {\n                // 造成雷电伤害\n                const damageInfo = {\n                    amount: this._damage,\n                    type: DamageType.MAGIC,\n                    source: this._caster,\n                    isCritical: false,\n                    element: \"thunder\"\n                };\n                enemy.takeDamage(damageInfo.amount, this._caster);\n                console.log(`Lightning strikes ${enemy.characterName} for ${this._damage} thunder damage`);\n                // 眩晕概率检查\n                if (Math.random() < 0.3) { // 30%概率眩晕\n                    console.log(`${enemy.characterName} is stunned by lightning!`);\n                    const stunBuff = new StunBuff(this._caster, enemy, 2.0);\n                    enemy.buffManager.addBuff(stunBuff);\n                }\n            }\n        }\n        // 播放闪电特效和音效\n        this.playEffect(\"prefabs/effects/LightningStrike\", this._position);\n        this.playSound(\"lightning_strike\");\n    }\n\n    /** 查找范围内的敌人 */\n    private findEnemiesInRange(): ICharacter[] {\n        // 这里需要实现范围查找逻辑\n        // 暂时返回空数组，实际应该通过BattleManager或场景管理器查找\n        console.log(`Searching for enemies in radius ${this._radius} around position (${this._position.x}, ${this._position.y})`);\n        return [];\n    }\n\n    playEffect?(effectId: string, position?: cc.Vec3): void {\n        console.log(`Playing effect ${effectId} at position (${position?.x}, ${position?.y})`);\n    }\n\n    playSound?(soundId: string): void {\n        console.log(`Playing sound ${soundId}`);\n    }\n}\n"]}