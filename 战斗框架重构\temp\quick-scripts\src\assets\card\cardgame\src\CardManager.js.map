{"version": 3, "sources": ["assets\\card\\cardgame\\src\\CardManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,0CAA0C;AAE1C,+CAA+C;AAC/C,mBAAmB;AACnB,uBAAuB;AACvB,wDAAwD;AACxD,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,oBAAoB;AACpB,sBAAsB;AACtB,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,cAAc;AACd,kDAAkD;AAClD,oBAAoB;AACpB,sBAAsB;AAEtB,sBAAsB;AACtB,iCAAiC;AAEjC,kBAAkB;AAClB,uBAAuB;AACvB,uBAAuB;AAEvB,iDAAiD;AAEjD,UAAU;AACV,kBAAkB;AAClB,kCAAkC;AAClC,UAAU;AACV,6CAA6C;AAC7C,uCAAuC;AACvC,2DAA2D;AAC3D,+DAA+D;AAC/D,2CAA2C;AAC3C,wCAAwC;AACxC,+BAA+B;AAC/B,YAAY;AACZ,QAAQ;AAER,qCAAqC;AACrC,sCAAsC;AACtC,+BAA+B;AAC/B,uCAAuC;AACvC,8BAA8B;AAC9B,sBAAsB;AACtB,QAAQ;AAER,kCAAkC;AAClC,0EAA0E;AAC1E,sBAAsB;AACtB,oEAAoE;AACpE,kDAAkD;AAClD,2BAA2B;AAC3B,gBAAgB;AAChB,4CAA4C;AAC5C,2BAA2B;AAC3B,YAAY;AACZ,wBAAwB;AACxB,QAAQ;AAER,yBAAyB;AACzB,wCAAwC;AACxC,iCAAiC;AACjC,QAAQ;AAER,wBAAwB;AACxB,0BAA0B;AAC1B,8CAA8C;AAC9C,qEAAqE;AACrE,2BAA2B;AAC3B,QAAQ;AAER,8DAA8D;AAC9D,UAAU;AACV,0BAA0B;AAC1B,8BAA8B;AAC9B,UAAU;AACV,oCAAoC;AACpC,oEAAoE;AACpE,gCAAgC;AAChC,yEAAyE;AACzE,QAAQ;AAER,UAAU;AACV,yBAAyB;AACzB,6BAA6B;AAC7B,SAAS;AACT,gCAAgC;AAChC,4DAA4D;AAC5D,gCAAgC;AAChC,uEAAuE;AACvE,QAAQ;AAER,uDAAuD;AACvD,0BAA0B;AAC1B,wHAAwH;AACxH,0BAA0B;AAC1B,4GAA4G;AAC5G,0BAA0B;AAC1B,qHAAqH;AACrH,2BAA2B;AAC3B,yGAAyG;AAEzG,aAAa;AACb,wCAAwC;AACxC,mCAAmC;AACnC,aAAa;AACb,8CAA8C;AAC9C,iDAAiD;AACjD,oDAAoD;AACpD,gDAAgD;AAChD,oCAAoC;AACpC,yCAAyC;AACzC,2CAA2C;AAC3C,mEAAmE;AACnE,mCAAmC;AACnC,6CAA6C;AAC7C,qEAAqE;AACrE,mCAAmC;AACnC,iCAAiC;AACjC,6FAA6F;AAC7F,sBAAsB;AACtB,gFAAgF;AAChF,WAAW;AAEX,UAAU;AACV,uCAAuC;AACvC,+BAA+B;AAC/B,UAAU;AACV,mDAAmD;AACnD,qEAAqE;AACrE,2CAA2C;AAC3C,oEAAoE;AACpE,6EAA6E;AAC7E,mEAAmE;AACnE,YAAY;AACZ,6BAA6B;AAC7B,QAAQ;AAER,4BAA4B;AAC5B,0DAA0D;AAC1D,sCAAsC;AACtC,iCAAiC;AACjC,YAAY;AACZ,QAAQ;AAER,uDAAuD;AAGvD,aAAa;AACb,wCAAwC;AACxC,mCAAmC;AACnC,aAAa;AACb,4CAA4C;AAC5C,+CAA+C;AAC/C,oDAAoD;AACpD,kDAAkD;AAClD,qCAAqC;AACrC,sCAAsC;AACtC,yCAAyC;AACzC,2CAA2C;AAC3C,0EAA0E;AAC1E,mCAAmC;AACnC,6CAA6C;AAC7C,4EAA4E;AAC5E,mCAAmC;AACnC,iCAAiC;AACjC,6FAA6F;AAC7F,sBAAsB;AACtB,gFAAgF;AAChF,WAAW;AAEX,UAAU;AACV,sCAAsC;AACtC,8BAA8B;AAC9B,SAAS;AACT,iDAAiD;AACjD,qEAAqE;AACrE,2CAA2C;AAC3C,oEAAoE;AACpE,6EAA6E;AAC7E,mEAAmE;AACnE,YAAY;AACZ,6BAA6B;AAC7B,QAAQ;AACR,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import CardBase from \"./Base/CardBase\";\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n// declare global {\r\n//     /**卡牌或角色的基础数据 */\r\n//     export interface ICardMgr extends IBaseDataType {\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 功能\r\n//  * @description: 说明\r\n//  * @Date : 2020-08-17 10:25:03\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 13:58:24\r\n//  * @LastEditors : judu233\r\n//  */\r\n// // @ccclass\r\n// export default class CardManager extends Base {\r\n//     /**存储卡牌的数据 */\r\n//     data: ICardMgr;\r\n\r\n//     /**当前所有卡牌的信息 */\r\n//     cardList: CardBase[] = [];\r\n\r\n//     /**卡片的上限 */\r\n//     @Base.ViewLinked\r\n//     maxCard: number;\r\n\r\n//     /***************和卡牌相关的接口*************** */\r\n\r\n//     /**\r\n//      * 初始化卡牌列表 \r\n//      * @param initData 卡牌的初始化数据\r\n//      */\r\n//     initCardList(data: ICampMgrDataType) {\r\n//         let initData = data.cardData\r\n//         let cardLis = initData.map(card => new CardBase)\r\n//         for (let [index, card] of Object.entries(cardLis)) {\r\n//             card.data = initData[index];\r\n//             this.cardList.push(card);\r\n//             card.initCard();\r\n//         }\r\n//     }\r\n\r\n//     addCard(card: ICardDataType) {\r\n//         let newCard = new CardBase;\r\n//         newCard.data = card;\r\n//         this.cardList.push(newCard);\r\n//         newCard.initCard();\r\n//         return card\r\n//     }\r\n\r\n//     deletCard(cardId: string) {\r\n//         let card = this.cardList.find(card => card.data.id === cardId);\r\n//         if (card) {\r\n//             this.cardList.splice(this.cardList.indexOf(card), 1);\r\n//             if (this.cardList.includes(card)) {\r\n//                 debugger\r\n//             }\r\n//             card.unscheduleAllCallbacks()\r\n//             return true;\r\n//         }\r\n//         return false;\r\n//     }\r\n\r\n//     /**更新卡牌所有数据的视图  */\r\n//     refreshCardView(card: CardBase) {\r\n//         // card.refreshView();\r\n//     }\r\n\r\n//     /**获取卡牌列表的所有数据 */\r\n//     getCardListData() {\r\n//         let cardData: ICampDataType[] = [];\r\n//         // this.cardList.forEach(card => cardData.push(card.data))\r\n//         return cardData;\r\n//     }\r\n\r\n//     /**************** 所有卡牌列表 相关的接口 ***********************/\r\n//     /**\r\n//      * 根据卡牌名字从存储列表中返回卡牌\r\n//      * @param name 要获取卡牌的名字\r\n//      */\r\n//     getCardByName(name: string) {\r\n//         const card = this.cardList.find(c => c.cardName == name);\r\n//         if (card) return card\r\n//         cc.warn(`[${this.className}]根据名字获取卡牌${name}失败，请检查卡牌名字和卡牌列表！`);\r\n//     }\r\n\r\n//     /**\r\n//     * 根据卡牌id从存储列表中返回卡牌\r\n//     * @param name 要获取卡牌的名字\r\n//     */\r\n//     getCardById(id: string) {\r\n//         const card = this.cardList.find(c => c.id == id);\r\n//         if (card) return card\r\n//         cc.warn(`[${this.className}]根据id获取卡牌${id}失败，请检查卡牌id和卡牌列表！`);\r\n//     }\r\n\r\n//     /*************** 存活卡牌 相关的接口 *******************/\r\n//     /**根据名字从存活列表返回卡牌 */\r\n//     getSurviveCardByName(name: string) { return this.cardList.find(card => card.cardName == name && !card.isDeath); }\r\n//     /**根据id从存活列表返回卡牌 */\r\n//     getSurviveCardById(id: string) { return this.cardList.find(card => card.id == id && !card.isDeath); }\r\n//     /**根据名字返回死亡列表的卡牌 */\r\n//     getDeathCardByName(name: string) { return this.cardList.find(card => card.cardName == name && card.isDeath); }\r\n//     /** 根据id从死亡列表中获取卡牌*/\r\n//     getDeathCardById(id: string) { return this.cardList.find(card => card.id == id && card.isDeath); }\r\n\r\n//     // /**\r\n//     //  * 从存活列表中删除卡牌，并存入死亡卡牌，且改变卡牌的状态\r\n//     //  * @param name 要删除存活卡牌的名字\r\n//     //  */\r\n//     // delSurviveCardByName(name: string) {\r\n//     //     if (this.surviveMap.delete(name)) {\r\n//     //         let card = this.cardMap.get(name);\r\n//     //         this.deathMap.set(name, card);\r\n//     //         card.death = true;\r\n//     //         // switch (this.camp) {\r\n//     //         //     case ECamp.Player:\r\n//     //         //         EM.broadcast(EM.keys.PlayerCardDeath);\r\n//     //         //         break;\r\n//     //         //     case ECamp.Computer:\r\n//     //         //         EM.broadcast(EM.keys.ComputerCardDeath);\r\n//     //         //         break;\r\n//     //         //     default:\r\n//     //         //         cc.warn(`[${this.className}删除存活卡牌，发送事件失败，触发事件阵营：${this.camp}]`);\r\n//     //         // }\r\n//     //     } else cc.warn(`[${this.className}]删除存活卡牌${name}失败，请检查卡牌名字和卡牌列表`);\r\n//     // }\r\n\r\n//     /**\r\n//      * 从存活列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)\r\n//      * @param count 要随机获取的数量\r\n//      */\r\n//     getSurviveCardForRandom(count: number = 1) {\r\n//         let cardList = this.cardList.filter(card => !card.isDeath)\r\n//         let resultList: CardBase[] = [];\r\n//         for (let i = 0; i < count && cardList.length >= 1; i++) {\r\n//             let randomIndex = Math.floor(Math.random() * cardList.length);\r\n//             resultList.push(cardList.splice(randomIndex, 1)[0]);\r\n//         }\r\n//         return resultList;\r\n//     }\r\n\r\n//     getSurviveOneCard() {\r\n//         let cardList = this.getSurviveCardForRandom(1);\r\n//         if (cardList.length != 0) {\r\n//             return cardList[0]\r\n//         }\r\n//     }\r\n\r\n//     /*************** 死亡列表 相关的接口 ***************** */\r\n\r\n\r\n//     // /**\r\n//     //  * 从死亡列表中删除卡牌，并存入存活列表,且改变卡牌的状态\r\n//     //  * @param name 要删除死亡卡牌的名字\r\n//     //  */\r\n//     // delDeathCardByName(name: string) {\r\n//     //     if (this.deathMap.delete(name)) {\r\n//     //         let card = this.cardMap.get(name);\r\n//     //         this.surviveMap.set(name, card);\r\n//     //         card.death = false;\r\n//     //         card.hp = card.hpUp;\r\n//     //         // switch (this.camp) {\r\n//     //         //     case ECamp.Player:\r\n//     //         //         EM.broadcast(EM.keys.PlayerCardResurrection);\r\n//     //         //         break;\r\n//     //         //     case ECamp.Computer:\r\n//     //         //         EM.broadcast(EM.keys.ComputerCardResurrection);\r\n//     //         //         break;\r\n//     //         //     default:\r\n//     //         //         cc.warn(`[${this.className}删除死亡卡牌，发送事件失败，触发事件阵营：${this.camp}]`);\r\n//     //         // }\r\n//     //     } else cc.warn(`[${this.className}]删除死亡卡牌${name}失败，请检查卡牌名字和卡牌列表`);\r\n//     // }\r\n\r\n//     /**\r\n//     * 从死亡列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)\r\n//     * @param count 要随机获取的数量\r\n//     */\r\n//     getDeathCardForRandom(count: number = 1) {\r\n//         let cardList = this.cardList.filter(card => !card.isDeath)\r\n//         let resultList: CardBase[] = [];\r\n//         for (let i = 0; i < count && cardList.length >= 1; i++) {\r\n//             let randomIndex = Math.floor(Math.random() * cardList.length);\r\n//             resultList.push(cardList.splice(randomIndex, 1)[0]);\r\n//         }\r\n//         return resultList;\r\n//     }\r\n// }\r\n"]}