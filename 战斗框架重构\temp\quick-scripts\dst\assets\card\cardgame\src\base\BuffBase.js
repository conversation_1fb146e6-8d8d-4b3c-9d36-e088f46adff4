
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/BuffBase.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fdcb6CRWGtCd6GuCMgCXfsu', 'BuffBase');
// card/cardgame/src/base/BuffBase.ts

// import BuffManager from '../../Controller/BuffManager';
// import SkillBase from './SkillBase';
// import Base from './Base';
// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }
// declare global {
//     /**buff叠加类型 */
//     export interface IBuffTimeType {
//         /**类型 */
//         type: EBuffType;
//         value: number;
//     }
//     /**buff数据接口 */
//     export interface IBuffDataType extends IBaseDataType {
//         /**buff 的类名 */
//         type: string;
//         /**Buff等级 */
//         level: number;
//         /**buff生效时间类型 */
//         buffTime: IBuffTimeType;
//         /**是否在生效中 */
//         isTakeEffect: boolean;
//         /**是否可以叠加 */
//         isCanOverlay: boolean;
//         /**最大叠加几层 */
//         maxFloor: number;
//         /**当前叠加层数*/
//         nowFloor: number;
//     }
// }
// /** 
//  * @features : 游戏技能的buff
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class BuffBase extends Base {
//     /**技能数据 */
//     data: IBuffDataType;
//     /**BUFF作用的目标 */
//     target: any;
//     /**buffManager */
//     buffManage = new BuffManager
//     /**buff的Id */
//     @Base.ViewLinked
//     id: string
//     /**buff的类型 */
//     @Base.ViewLinked
//     type: string
//     /**使用buff ` 子类负责实现具体逻辑*/
//     useBuff() { }
//     /**移除Buff 子类负责实现具体逻辑*/
//     removeBuff() { }
//     /**叠加一层buff 子类负责实现具体逻辑*/
//     overlayBuff() { }
//     /**减少一层buff */
//     reduceBuff() { }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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