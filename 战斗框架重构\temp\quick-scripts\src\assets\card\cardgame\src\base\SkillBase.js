"use strict";
cc._RF.push(module, 'be2a3fmujlJ04XeBAKsSCgN', 'SkillBase');
// card/cardgame/src/base/SkillBase.ts

// import BuffBase from './BuffBase';
// import CardBase from "./CardBase";
// import BuffManager from '../../Controller/BuffManager';
// /**冷却类型 */
// export enum EFrozen {
//     /**回合类型 */
//     round = `round`,
//     /**全局类型 */
//     global = `global`,
//     /**计时 */
//     timeOut = `timeOut`,
//     /**次数 */
//     timeCount = `timeCount`,
// }
// declare global {
//     /**冷却时间接口 */
//     export interface ISkillFrozenType {
//         /**冷却类型 */
//         type: EFrozen;
//         /**次数，计时，回合都是number 全局分为计时和次数 */
//         value: number | ISkillFrozenType;
//     }
//     /**技能数据接口 */
//     export interface ISkillDataType extends IBaseDataType {
//         /**buff  列表 */
//         buffData: IBuffDataType[];
//         /**使用次数 */
//         useCount: number;
//         /**技能等级 */
//         level: number;
//         /**等级上限 */
//         levelHeight: number;
//         /**是否可以使用此技能 */
//         isCanUse: boolean;
//         /**是否可以升级 */
//         isCanLevelUp: boolean;
//         /**是否正在冷却 */
//         isFrozen: boolean;
//         /**冷却信息 */
//         frozen: ISkillFrozenType;
//     }
// }
// /**
//  * @features : 技能控制基类
//  * @description: 对于一个卡牌或角色的基类控制
//  * @Date : 2020-08-12 23:29:09
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:58:43
//  * @LastEditors : judu233
//  */
// export default class SkillBase {
//     /**技能数据 */
//     data: ISkillDataType;
//     /**使用者 */
//     useCard: CardBase;
//     /**被使用者列表 */
//     targetMap: Map<string, CardBase> = new Map();
//     /**buffManager */
//     buffManager = new BuffManager
//     /**初始化技能数据 */
//     initSkillData() {
//         //根据data数据创建buff
//         let buffTypeList = this.data.buffData;
//         try {
//             let buffList: BuffBase[] = [];
//             //创建Buff
//             for (let buffType of buffTypeList) {
//                 let buffClassName: { new(): BuffBase; } = eval(buffType.type);
//                 let buff = new buffClassName();
//                 buffList.push(buff);
//             }
//             //添加到buff管理器中
//             this.buffManager.initBuffManager(buffList);
//         } catch (error) {
//             cc.error(`技能创建buff失败，信息:${error}`);
//         }
//     }
//     /**使用技能，子类负责实现*/
//     useSkill() { }
// }

cc._RF.pop();