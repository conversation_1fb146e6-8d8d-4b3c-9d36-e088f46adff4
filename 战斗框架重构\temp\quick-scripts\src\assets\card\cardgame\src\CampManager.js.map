{"version": 3, "sources": ["assets\\card\\cardgame\\src\\CampManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,0CAA0C;AAC1C,0CAA0C;AAE1C,+CAA+C;AAE/C,aAAa;AACb,sBAAsB;AACtB,eAAe;AACf,6BAA6B;AAC7B,iBAAiB;AACjB,yBAAyB;AACzB,iBAAiB;AACjB,6BAA6B;AAC7B,IAAI;AACJ,mBAAmB;AACnB,iBAAiB;AACjB,0CAA0C;AAC1C,wBAAwB;AACxB,mCAAmC;AACnC,qCAAqC;AACrC,qCAAqC;AACrC,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,sBAAsB;AACtB,uBAAuB;AACvB,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,WAAW;AACX,kDAAkD;AAClD,qBAAqB;AACrB,8BAA8B;AAE9B,iBAAiB;AACjB,iCAAiC;AAEjC,mCAAmC;AACnC,kBAAkB;AAClB,kDAAkD;AAClD,2CAA2C;AAC3C,sCAAsC;AACtC,uCAAuC;AACvC,uCAAuC;AACvC,YAAY;AACZ,QAAQ;AAER,uBAAuB;AACvB,wGAAwG;AACxG,kBAAkB;AAClB,sGAAsG;AACtG,kBAAkB;AAClB,uGAAuG;AACvG,wBAAwB;AACxB,6BAA6B;AAC7B,oFAAoF;AACpF,QAAQ;AAER,4BAA4B;AAC5B,qBAAqB;AACrB,oFAAoF;AACpF,QAAQ;AAER,4BAA4B;AAC5B,sDAAsD;AACtD,QAAQ;AAER,iCAAiC;AACjC,kDAAkD;AAClD,QAAQ;AAER,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import CampBase from \"./Base/CampBase\";\r\n// import RoleBase from \"./Base/RoleBase\";\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n\r\n// /**卡牌阵营 */\r\n// export enum ECamp {\r\n//     /**基类 */\r\n//     CampBase = `CampBase`,\r\n//     /**玩家阵营 */\r\n//     Player = `Player`,\r\n//     /**电脑阵营 */\r\n//     Computer = `Computer`,\r\n// }\r\n// declare global {\r\n//     /**阵营数据 */\r\n//     export interface ICampMgrDataType {\r\n//         /**存储阵营的数据 */\r\n//         campData: ICampDataType,\r\n//         cardData: ICardDataType[],\r\n//         roleData: IRoleBaseData[],\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 阵营管理\r\n//  * @description : 说明\r\n//  * @Date : 2020-08-17 10:24:21\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:08:50\r\n//  * @LastEditors : judu233\r\n//  */\r\n// @ccclass\r\n// export default class CampManager extends Base {\r\n//     /**卡牌阵营管理信息 */\r\n//     data: ICampMgrDataType;\r\n\r\n//     /**阵营列表 */\r\n//     campList: CampBase[] = [];\r\n\r\n//     /******关于阵营的操作*********** */\r\n//     /**初始化阵营 */\r\n//     initAllCamp(dataList: ICampMgrDataType[]) {\r\n//         for (let campData of dataList) {\r\n//             let camp = new CampBase\r\n//             camp.initCamp(campData);\r\n//             this.campList.push(camp)\r\n//         }\r\n//     }\r\n\r\n//     /**根据阵营名字获取阵营 */\r\n//     getCampByName(camp: ECamp) { return this.campList.find(predicate => predicate.campName == camp) }\r\n//     /**获取先手方 */\r\n//     getPlayerCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Player); }\r\n//     /**获取后手方 */\r\n//     getEnemyCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Computer); }\r\n//     /**获取以玩家为首的先手方 */\r\n//     getFirstPlayerCamp() {\r\n//         return { firstCamp: this.getPlayerCamp(), lastCamp: this.getEnemyCamp() }\r\n//     }\r\n    \r\n//     /**检查是否有任何一方已经全部死亡 */\r\n//     checkDeath() {\r\n//         return this.getPlayerCamp().isAllDeath || this.getEnemyCamp().isAllDeath;\r\n//     }\r\n\r\n//     checkMonsterDeath() {\r\n//         return this.getEnemyCamp().curRole.isDeath;\r\n//     }\r\n\r\n//     checkPlayerCampIsDeath() {\r\n//         return this.getPlayerCamp().isAllDeath;\r\n//     }\r\n\r\n// }\r\n"]}