"use strict";
cc._RF.push(module, '2265fiHhMdL2af3ZT22JWaV', 'EventManager');
// fight/systems/EventManager.ts

"use strict";
/**
 * 事件管理器
 * 提供事件的注册、触发和管理功能
 */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventManager = void 0;
/**
 * 事件管理器类
 */
var EventManager = /** @class */ (function () {
    function EventManager(instanceId) {
        this._listeners = new Map();
        this._isDestroyed = false;
        this._instanceId = instanceId || "eventManager_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
    }
    /** 获取全局单例实例 */
    EventManager.getGlobal = function () {
        if (!EventManager._globalInstance) {
            EventManager._globalInstance = new EventManager("global");
        }
        return EventManager._globalInstance;
    };
    /** 创建新的局部实例 */
    EventManager.createLocal = function (instanceId) {
        return new EventManager(instanceId);
    };
    /** 全局事件快捷方法 */
    EventManager.on = function (event, callback, context) {
        EventManager.getGlobal().on(event, callback, context);
    };
    EventManager.once = function (event, callback, context) {
        EventManager.getGlobal().once(event, callback, context);
    };
    EventManager.off = function (event, callback, context) {
        EventManager.getGlobal().off(event, callback, context);
    };
    EventManager.emit = function (event, data) {
        EventManager.getGlobal().emit(event, data);
    };
    EventManager.hasListeners = function (event) {
        return EventManager.getGlobal().hasListeners(event);
    };
    EventManager.getListenerCount = function (event) {
        return EventManager.getGlobal().getListenerCount(event);
    };
    EventManager.getEventNames = function () {
        return EventManager.getGlobal().getEventNames();
    };
    EventManager.cleanup = function () {
        if (EventManager._globalInstance) {
            EventManager._globalInstance.cleanup();
            EventManager._globalInstance = null;
        }
    };
    EventManager.getDebugInfo = function () {
        return EventManager.getGlobal().getDebugInfo();
    };
    Object.defineProperty(EventManager.prototype, "instanceId", {
        /** 获取实例ID */
        get: function () { return this._instanceId; },
        enumerable: false,
        configurable: true
    });
    /**
     * 注册事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.on = function (event, callback, context) {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        var listeners = this._listeners.get(event);
        listeners.push({ callback: callback, context: context, once: false });
    };
    /**
     * 注册一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.once = function (event, callback, context) {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        var listeners = this._listeners.get(event);
        listeners.push({
            callback: callback,
            context: context,
            once: true
        });
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    EventManager.prototype.off = function (event, callback, context) {
        if (this._isDestroyed) {
            return;
        }
        var listeners = this._listeners.get(event);
        if (!listeners) {
            return;
        }
        if (!callback) {
            // 如果没有指定回调函数，移除所有监听器
            this._listeners.delete(event);
            return;
        }
        // 移除匹配的监听器
        var filteredListeners = listeners.filter(function (listener) {
            if (listener.callback !== callback) {
                return true;
            }
            if (context !== undefined && listener.context !== context) {
                return true;
            }
            return false;
        });
        if (filteredListeners.length === 0) {
            this._listeners.delete(event);
        }
        else {
            this._listeners.set(event, filteredListeners);
        }
    };
    /**
     * 触发事件
     * @param event 事件名称
     * @param data 事件数据
     */
    EventManager.prototype.emit = function (event, data) {
        var e_1, _a;
        if (this._isDestroyed) {
            return;
        }
        var listeners = this._listeners.get(event);
        if (!listeners || listeners.length === 0) {
            return;
        }
        // 创建监听器副本，避免在回调中修改监听器列表时出现问题
        var listenersCopy = __spread(listeners);
        var onceListeners = [];
        try {
            for (var listenersCopy_1 = __values(listenersCopy), listenersCopy_1_1 = listenersCopy_1.next(); !listenersCopy_1_1.done; listenersCopy_1_1 = listenersCopy_1.next()) {
                var listener = listenersCopy_1_1.value;
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data);
                    }
                    else {
                        listener.callback(data);
                    }
                    // 收集一次性监听器
                    if (listener.once) {
                        onceListeners.push(listener);
                    }
                }
                catch (error) {
                    console.error("Error in event listener for event \"" + event + "\":", error);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (listenersCopy_1_1 && !listenersCopy_1_1.done && (_a = listenersCopy_1.return)) _a.call(listenersCopy_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 移除一次性监听器
        if (onceListeners.length > 0) {
            var remainingListeners = listeners.filter(function (listener) { return !onceListeners.includes(listener); });
            if (remainingListeners.length === 0) {
                this._listeners.delete(event);
            }
            else {
                this._listeners.set(event, remainingListeners);
            }
        }
    };
    /**
     * 检查是否有指定事件的监听器
     * @param event 事件名称
     * @returns 是否有监听器
     */
    EventManager.prototype.hasListeners = function (event) {
        var listeners = this._listeners.get(event);
        return listeners !== undefined && listeners.length > 0;
    };
    /**
     * 获取指定事件的监听器数量
     * @param event 事件名称
     * @returns 监听器数量
     */
    EventManager.prototype.getListenerCount = function (event) {
        var listeners = this._listeners.get(event);
        return listeners ? listeners.length : 0;
    };
    /**
     * 获取所有事件名称
     * @returns 事件名称数组
     */
    EventManager.prototype.getEventNames = function () {
        return Array.from(this._listeners.keys());
    };
    /**
     * 移除所有监听器
     */
    EventManager.prototype.removeAllListeners = function () {
        this._listeners.clear();
    };
    /**
     * 移除指定事件的所有监听器
     * @param event 事件名称
     */
    EventManager.prototype.removeAllListenersForEvent = function (event) {
        this._listeners.delete(event);
    };
    /**
     * 清理事件管理器
     */
    EventManager.prototype.cleanup = function () {
        this.removeAllListeners();
        this._isDestroyed = true;
    };
    /**
     * 获取调试信息
     */
    EventManager.prototype.getDebugInfo = function () {
        var e_2, _a;
        var info = {};
        try {
            for (var _b = __values(this._listeners), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), event = _d[0], listeners = _d[1];
                info[event] = {
                    count: listeners.length,
                    listeners: listeners.map(function (listener) { return ({
                        hasContext: !!listener.context,
                        isOnce: !!listener.once,
                        functionName: listener.callback.name || 'anonymous'
                    }); })
                };
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return info;
    };
    /**
     * 打印调试信息
     */
    EventManager.prototype.printDebugInfo = function () {
        console.log("EventManager Debug Info:", this.getDebugInfo());
    };
    return EventManager;
}());
exports.EventManager = EventManager;

cc._RF.pop();