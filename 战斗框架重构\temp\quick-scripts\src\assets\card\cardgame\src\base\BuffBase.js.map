{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\BuffBase.ts"], "names": [], "mappings": ";;;;AAAA,0DAA0D;AAC1D,uCAAuC;AACvC,6BAA6B;AAE7B,eAAe;AACf,0BAA0B;AAC1B,qBAAqB;AACrB,IAAI;AAEJ,mBAAmB;AACnB,qBAAqB;AACrB,uCAAuC;AACvC,mBAAmB;AACnB,2BAA2B;AAC3B,yBAAyB;AACzB,QAAQ;AACR,qBAAqB;AACrB,6DAA6D;AAC7D,yBAAyB;AACzB,wBAAwB;AACxB,uBAAuB;AACvB,yBAAyB;AACzB,2BAA2B;AAC3B,mCAAmC;AACnC,uBAAuB;AACvB,iCAAiC;AACjC,uBAAuB;AACvB,iCAAiC;AACjC,uBAAuB;AACvB,4BAA4B;AAC5B,sBAAsB;AACtB,4BAA4B;AAC5B,QAAQ;AACR,IAAI;AAEJ,OAAO;AACP,2BAA2B;AAC3B,mEAAmE;AACnE,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,+CAA+C;AAC/C,iBAAiB;AACjB,2BAA2B;AAC3B,sBAAsB;AACtB,mBAAmB;AACnB,wBAAwB;AACxB,mCAAmC;AAEnC,oBAAoB;AACpB,uBAAuB;AACvB,iBAAiB;AAEjB,oBAAoB;AACpB,uBAAuB;AACvB,mBAAmB;AAEnB,+BAA+B;AAC/B,oBAAoB;AACpB,6BAA6B;AAC7B,uBAAuB;AACvB,+BAA+B;AAC/B,wBAAwB;AACxB,qBAAqB;AACrB,uBAAuB;AACvB,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import BuffManager from '../../Controller/BuffManager';\r\n// import SkillBase from './SkillBase';\r\n// import Base from './Base';\r\n\r\n// /**buff类型 */\r\n// export enum EBuffType {\r\n//     None = `None`,\r\n// }\r\n\r\n// declare global {\r\n//     /**buff叠加类型 */\r\n//     export interface IBuffTimeType {\r\n//         /**类型 */\r\n//         type: EBuffType;\r\n//         value: number;\r\n//     }\r\n//     /**buff数据接口 */\r\n//     export interface IBuffDataType extends IBaseDataType {\r\n//         /**buff 的类名 */\r\n//         type: string;\r\n//         /**Buff等级 */\r\n//         level: number;\r\n//         /**buff生效时间类型 */\r\n//         buffTime: IBuffTimeType;\r\n//         /**是否在生效中 */\r\n//         isTakeEffect: boolean;\r\n//         /**是否可以叠加 */\r\n//         isCanOverlay: boolean;\r\n//         /**最大叠加几层 */\r\n//         maxFloor: number;\r\n//         /**当前叠加层数*/\r\n//         nowFloor: number;\r\n//     }\r\n// }\r\n\r\n// /** \r\n//  * @features : 游戏技能的buff\r\n//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中\r\n//  * @Date : 2020-08-12 23:28:43\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:01:01\r\n//  * @LastEditors : judu233\r\n//  */\r\n// export default class BuffBase extends Base {\r\n//     /**技能数据 */\r\n//     data: IBuffDataType;\r\n//     /**BUFF作用的目标 */\r\n//     target: any;\r\n//     /**buffManager */\r\n//     buffManage = new BuffManager\r\n\r\n//     /**buff的Id */\r\n//     @Base.ViewLinked\r\n//     id: string\r\n\r\n//     /**buff的类型 */\r\n//     @Base.ViewLinked\r\n//     type: string\r\n\r\n//     /**使用buff ` 子类负责实现具体逻辑*/\r\n//     useBuff() { }\r\n//     /**移除Buff 子类负责实现具体逻辑*/\r\n//     removeBuff() { }\r\n//     /**叠加一层buff 子类负责实现具体逻辑*/\r\n//     overlayBuff() { }\r\n//     /**减少一层buff */\r\n//     reduceBuff() { }\r\n// }\r\n"]}