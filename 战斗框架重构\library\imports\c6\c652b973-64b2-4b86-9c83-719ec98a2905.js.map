{"version": 3, "sources": ["assets\\fight\\characters\\CharacterAttributes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,0DAAgH;AAChH,kDAA6C;AAG7C,aAAa;AACb;IAUI,6BAAY,WAA6C;QALzD,WAAW;QACH,eAAU,GAAoC,IAAI,GAAG,EAAE,CAAC;QAK5D,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QACrE,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAGD,sBAAI,0CAAS;QADb,4BAA4B;aAC5B,cAA0B,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAChE,sBAAI,sCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC7D,sBAAI,uCAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC/D,sBAAI,wCAAO;aAAX,cAAwB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,4CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IACzE,sBAAI,0CAAS;aAAb,cAA0B,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACrE,sBAAI,4CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IACzE,sBAAI,sCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAG7D,sBAAI,0CAAS;QADb,0BAA0B;aAC1B,cAA0B,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAChE,sBAAI,sCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC7D,sBAAI,+CAAc;aAAlB,cAA+B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;;;OAAA;IAC1E,sBAAI,2CAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAGlE,sBAAI,4CAAW;QADf,UAAU;aACV,cAA4B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0BAA0B;;;;OAA3B;IACpE,sBAAI,6CAAY;aAAhB,cAA6B,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B;;;;OAA7B;IACtE,sBAAI,sCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,uBAAuB;;;;OAAxB;IACjE,sBAAI,yCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAClE,sBAAI,wCAAO;aAAX,cAAwB,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACnE,sBAAI,6CAAY;aAAhB,cAA6B,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IAC3E,sBAAI,+CAAc;aAAlB,cAA+B,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;;;OAAA;IAC/E,sBAAI,wCAAO;aAAX,cAAwB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,0CAAS;aAAb,cAA0B,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACrE,sBAAI,2CAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAEvE,cAAc;IACN,kDAAoB,GAA5B,UAA6B,WAA6C;QACtE,QAAQ;QACR,IAAM,iBAAiB,GAA2B;YAC9C,EAAE,EAAE,GAAG;YACP,KAAK,EAAE,GAAG;YACV,EAAE,EAAE,EAAE;YACN,KAAK,EAAE,EAAE;YACT,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;YAChB,YAAY,EAAE,IAAI;YAClB,cAAc,EAAE,GAAG;YACnB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;SAChB,CAAC;QACF,SAAS;QACT,IAAI,CAAC,eAAe,yBAAQ,iBAAiB,GAAK,WAAW,CAAE,CAAC;QAChE,IAAI,CAAC,kBAAkB,gBAAQ,IAAI,CAAC,eAAe,CAAE,CAAC;QACtD,UAAU;QACV,IAAI,CAAC,aAAa,GAAG;YACjB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE;YAClC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE;YAClC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,GAAG;YACtD,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,GAAG;SACrD,CAAC;IACN,CAAC;IAED,YAAY;IACZ,sCAAQ,GAAR,UAAS,MAAc;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC;QACxG,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE;gBAC1C,aAAa,EAAE,uCAAsB,CAAC,SAAS;gBAC/C,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS;gBACtC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK;aAC9C,CAAC,CAAC;SACN;IACL,CAAC;IACD,cAAc;IACd,sCAAQ,GAAR,UAAS,MAAc;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC;QACxG,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE;gBAC1C,aAAa,EAAE,uCAAsB,CAAC,SAAS;gBAC/C,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS;gBACtC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK;aAC9C,CAAC,CAAC;SACN;IACL,CAAC;IACD,aAAa;IACb,2CAAa,GAAb,UAAc,MAAc;QACxB,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC;QACvH,IAAI,UAAU,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;YAClD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,cAAc,EAAE;gBAC/C,aAAa,EAAE,uCAAsB,CAAC,cAAc;gBACpD,QAAQ,EAAE,UAAU;gBACpB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;gBAC3C,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,UAAU;aACxD,CAAC,CAAC;SACN;IACL,CAAC;IACD,iBAAiB;IACjB,6CAAe,GAAf;QACI,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IACD,aAAa;IACb,oCAAM,GAAN;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,CAAC,CAAC;IAC7C,CAAC;IACD,gBAAgB;IAChB,yCAAW,GAAX;QACI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC5E,CAAC;IACD,oBAAoB;IACpB,yCAAW,GAAX,UAAY,MAAc;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,MAAM,CAAC;IAClD,CAAC;IACD,mBAAmB;IACnB,8CAAgB,GAAhB,UAAiB,MAAc;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,MAAM,CAAC;IACvD,CAAC;IACD,iBAAiB;IACjB,6CAAe,GAAf;QACI,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IACD,kBAAkB;IAClB,kDAAoB,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IACD,kBAAkB;IAClB,kDAAoB,GAApB;QACI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC5E,CAAC;IACD,gBAAgB;IAChB,yCAAW,GAAX,UAAY,QAA4B;QACpC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC3C,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IACD,gBAAgB;IAChB,4CAAc,GAAd,UAAe,UAAkB;QAC7B,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;IACL,CAAC;IACD,gBAAgB;IAChB,yCAAW,GAAX,UAAY,UAAkB;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IACD,cAAc;IACd,6CAAe,GAAf,UAAgB,SAAiB;;QAC7B,IAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,EAAE;YACjC,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBAC5B,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;;YACH,WAAW;YACX,KAAiB,IAAA,qBAAA,SAAA,gBAAgB,CAAA,kDAAA,gFAAE;gBAA9B,IAAM,EAAE,6BAAA;gBACT,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;;;;;;;;;IACL,CAAC;IACD,eAAe;IACP,mDAAqB,GAA7B;QAAA,iBAWC;QAVG,UAAU;QACV,IAAI,CAAC,kBAAkB,gBAAQ,IAAI,CAAC,eAAe,CAAE,CAAC;QACtD,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC7B,QAAQ,CAAC,KAAK,CAAC,KAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,aAAa;QACb,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACrG,CAAC;IACD,uBAAuB;IACvB,+CAAiB,GAAjB,UAAkB,aAAqB,EAAE,KAAa;QAClD,IAAM,GAAG,GAAG,aAA6C,CAAC;QAC1D,IAAI,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAW,CAAC;YACxD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,aAAa,eAAA,EAAE,QAAQ,UAAA,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,QAAQ,EAAE,CAAC,CAAC;SAC/H;IACL,CAAC;IACD,gBAAgB;IAChB,mDAAqB,GAArB,UAAsB,aAA2C;QAC7D,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAW,CAAC;IACzD,CAAC;IACD,kBAAkB;IAClB,sDAAwB,GAAxB,UAAyB,aAAqB;QAC1C,IAAM,GAAG,GAAG,aAA6C,CAAC;QAC1D,IAAI,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAChC,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAW,CAAC;SACjD;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAED,sBAAI,6CAAY;QADhB,kCAAkC;aAClC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACD,kBAAkB;IAClB,8CAAgB,GAAhB;QACI,oBAAY,IAAI,CAAC,kBAAkB,EAAG;IAC1C,CAAC;IACD,oBAAoB;IACpB,6CAAe,GAAf;QACI,oBAAY,IAAI,CAAC,aAAa,EAAG;IACrC,CAAC;IACD,YAAY;IACZ,uCAAS,GAAT,UAAU,MAAc;QACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IACD,WAAW;IACX,4CAAc,GAAd,UAAe,MAAc;QACzB,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IACD,WAAW;IACX,wCAAU,GAAV,UAAW,MAAc;QACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IACD,aAAa;IACb,qCAAO,GAAP;QACI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACL,0BAAC;AAAD,CArPA,AAqPC,IAAA;AArPY,kDAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport { CharacterAttributeData, CharacterAttributeName, CharacterResourceData } from \"../types/CharacterTypes\";\nimport FightEvent from \"../types/FightEvent\";\nimport { ICharacterAttributes, ICharacterResource, IAttributeModifier } from \"../types/ICharacterAttributes\";\n\n/*** 角色属性实现*/\nexport class CharacterAttributes implements ICharacterAttributes, ICharacterResource {\n    /**基础属性 */\n    private _baseAttributes: CharacterAttributeData;\n    private _currentAttributes: CharacterAttributeData;\n    private _resourceData: CharacterResourceData;\n    /**属性修改器 */\n    private _modifiers: Map<string, IAttributeModifier> = new Map();\n    /**事件管理器 */\n    private _eventManager: EventManager;\n\n    constructor(initialData?: Partial<CharacterAttributeData>) {\n        this._eventManager = EventManager.createLocal(\"CharacterAttributes\");\n        this.initializeAttributes(initialData);\n    }\n\n    // ICharacterAttributes 接口实现\n    get currentHp(): number { return this._resourceData.currentHp; }\n    get maxHp(): number { return this._currentAttributes.maxHp; }\n    get attack(): number { return this._currentAttributes.attack; }\n    get defense(): number { return this._currentAttributes.defense; }\n    get attackSpeed(): number { return this._currentAttributes.attackSpeed; }\n    get moveSpeed(): number { return this._currentAttributes.moveSpeed; }\n    get attackRange(): number { return this._currentAttributes.attackRange; }\n    get level(): number { return this._currentAttributes.level; }\n\n    // ICharacterResource 接口实现\n    get currentMp(): number { return this._resourceData.currentMp; }\n    get maxMp(): number { return this._currentAttributes.maxMp; }\n    get currentStamina(): number { return this._resourceData.currentStamina; }\n    get maxStamina(): number { return this._resourceData.maxStamina; }\n\n    // 额外属性访问器\n    get magicAttack(): number { return this._currentAttributes.attack; } // 暂时使用attack作为magicAttack\n    get magicDefense(): number { return this._currentAttributes.defense; } // 暂时使用defense作为magicDefense\n    get speed(): number { return this._currentAttributes.moveSpeed; } // 暂时使用moveSpeed作为speed\n    get accuracy(): number { return this._currentAttributes.hitRate; }\n    get evasion(): number { return this._currentAttributes.dodgeRate; }\n    get criticalRate(): number { return this._currentAttributes.criticalRate; }\n    get criticalDamage(): number { return this._currentAttributes.criticalDamage; }\n    get hitRate(): number { return this._currentAttributes.hitRate; }\n    get dodgeRate(): number { return this._currentAttributes.dodgeRate; }\n    get experience(): number { return this._currentAttributes.experience; }\n\n    /** * 初始化属性 */\n    private initializeAttributes(initialData?: Partial<CharacterAttributeData>): void {\n        // 设置默认值\n        const defaultAttributes: CharacterAttributeData = {\n            hp: 100,\n            maxHp: 100,\n            mp: 50,\n            maxMp: 50,\n            maxStamina: 100,\n            attack: 10,\n            defense: 5,\n            attackSpeed: 1,\n            moveSpeed: 100,\n            attackRange: 150,\n            criticalRate: 0.05,\n            criticalDamage: 1.5,\n            hitRate: 0.95,\n            dodgeRate: 0.05,\n            level: 1,\n            experience: 0\n        };\n        // 合并初始数据\n        this._baseAttributes = { ...defaultAttributes, ...initialData };\n        this._currentAttributes = { ...this._baseAttributes };\n        // 初始化资源数据\n        this._resourceData = {\n            currentHp: this._baseAttributes.hp,\n            currentMp: this._baseAttributes.mp,\n            currentStamina: this._baseAttributes.maxStamina || 100,\n            maxStamina: this._baseAttributes.maxStamina || 100\n        };\n    }\n\n    /*** 修改生命值*/\n    modifyHp(amount: number): void {\n        const oldHp = this._resourceData.currentHp;\n        this._resourceData.currentHp = Math.max(0, Math.min(this.maxHp, this._resourceData.currentHp + amount));\n        if (oldHp !== this._resourceData.currentHp) {\n            this._eventManager.emit(FightEvent.hpChanged, {\n                attributeName: CharacterAttributeName.currentHp,\n                oldValue: oldHp,\n                newValue: this._resourceData.currentHp,\n                delta: this._resourceData.currentHp - oldHp\n            });\n        }\n    }\n    /** * 修改魔法值 */\n    modifyMp(amount: number): void {\n        const oldMp = this._resourceData.currentMp;\n        this._resourceData.currentMp = Math.max(0, Math.min(this.maxMp, this._resourceData.currentMp + amount));\n        if (oldMp !== this._resourceData.currentMp) {\n            this._eventManager.emit(FightEvent.mpChanged, {\n                attributeName: CharacterAttributeName.currentMp,\n                oldValue: oldMp,\n                newValue: this._resourceData.currentMp,\n                delta: this._resourceData.currentMp - oldMp\n            });\n        }\n    }\n    /** * 修改耐力 */\n    modifyStamina(amount: number): void {\n        const oldStamina = this._resourceData.currentStamina;\n        this._resourceData.currentStamina = Math.max(0, Math.min(this.maxStamina, this._resourceData.currentStamina + amount));\n        if (oldStamina !== this._resourceData.currentStamina) {\n            this._eventManager.emit(FightEvent.staminaChanged, {\n                attributeName: CharacterAttributeName.currentStamina,\n                oldValue: oldStamina,\n                newValue: this._resourceData.currentStamina,\n                delta: this._resourceData.currentStamina - oldStamina\n            });\n        }\n    }\n    /** * 获取生命值百分比 */\n    getHpPercentage(): number {\n        return this.maxHp > 0 ? this._resourceData.currentHp / this.maxHp : 0;\n    }\n    /** * 是否死亡 */\n    isDead(): boolean {\n        return this._resourceData.currentHp <= 0;\n    }\n    /** * 重置到满血状态 */\n    resetToFull(): void {\n        this.modifyHp(this.maxHp - this._resourceData.currentHp);\n        this.modifyMp(this.maxMp - this._resourceData.currentMp);\n        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);\n    }\n    /** * 检查是否有足够的魔法值 */\n    hasEnoughMp(amount: number): boolean {\n        return this._resourceData.currentMp >= amount;\n    }\n    /** * 检查是否有足够的耐力 */\n    hasEnoughStamina(amount: number): boolean {\n        return this._resourceData.currentStamina >= amount;\n    }\n    /** * 获取魔法值百分比 */\n    getMpPercentage(): number {\n        return this.maxMp > 0 ? this._resourceData.currentMp / this.maxMp : 0;\n    }\n    /**  * 获取耐力百分比  */\n    getStaminaPercentage(): number {\n        return this.maxStamina > 0 ? this._resourceData.currentStamina / this.maxStamina : 0;\n    }\n    /** * 重置资源到满值状态 */\n    resetResourcesToFull(): void {\n        this.modifyMp(this.maxMp - this._resourceData.currentMp);\n        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);\n    }\n    /** * 添加属性修改器 */\n    addModifier(modifier: IAttributeModifier): void {\n        this._modifiers.set(modifier.id, modifier);\n        modifier.apply(this);\n        this.recalculateAttributes();\n    }\n    /*** 移除属性修改器  */\n    removeModifier(modifierId: string): void {\n        const modifier = this._modifiers.get(modifierId);\n        if (modifier) {\n            modifier.remove(this);\n            this._modifiers.delete(modifierId);\n            this.recalculateAttributes();\n        }\n    }\n    /** * 获取属性修改器 */\n    getModifier(modifierId: string): IAttributeModifier | undefined {\n        return this._modifiers.get(modifierId);\n    }\n    /*** 更新所有修改器*/\n    updateModifiers(deltaTime: number): void {\n        const expiredModifiers: string[] = [];\n        this._modifiers.forEach((modifier, id) => {\n            if (modifier.update(deltaTime)) {\n                expiredModifiers.push(id);\n            }\n        });\n        // 移除过期的修改器\n        for (const id of expiredModifiers) {\n            this.removeModifier(id);\n        }\n    }\n    /** * 重新计算属性 */\n    private recalculateAttributes(): void {\n        // 重置为基础属性\n        this._currentAttributes = { ...this._baseAttributes };\n        // 应用所有修改器\n        this._modifiers.forEach((modifier) => {\n            modifier.apply(this);\n        });\n        // 确保资源不超过最大值\n        this._resourceData.currentHp = Math.min(this._resourceData.currentHp, this.maxHp);\n        this._resourceData.currentMp = Math.min(this._resourceData.currentMp, this.maxMp);\n        this._resourceData.currentStamina = Math.min(this._resourceData.currentStamina, this.maxStamina);\n    }\n    /** * 直接设置属性值（用于修改器） */\n    setAttributeValue(attributeName: string, value: number): void {\n        const key = attributeName as keyof CharacterAttributeData;\n        if (key in this._currentAttributes) {\n            const oldValue = this._currentAttributes[key] as number;\n            this._currentAttributes[key] = value;\n            this._eventManager.emit(FightEvent.attributeChanged, { attributeName, oldValue, newValue: value, delta: value - oldValue });\n        }\n    }\n    /** * 获取基础属性值 */\n    getBaseAttributeValue(attributeName: keyof CharacterAttributeData): number {\n        return this._baseAttributes[attributeName] as number;\n    }\n    /**  * 获取当前属性值  */\n    getCurrentAttributeValue(attributeName: string): number {\n        const key = attributeName as keyof CharacterAttributeData;\n        if (key in this._currentAttributes) {\n            return this._currentAttributes[key] as number;\n        }\n        return 0;\n    }\n    /** * 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n    /** * 获取属性数据的副本 */\n    getAttributeData(): CharacterAttributeData {\n        return { ...this._currentAttributes };\n    }\n    /**  * 获取资源数据的副本  */\n    getResourceData(): CharacterResourceData {\n        return { ...this._resourceData };\n    }\n    /** 消耗魔法值 */\n    consumeMp(amount: number): void {\n        this.modifyMp(-amount);\n    }\n    /** 消耗耐力 */\n    consumeStamina(amount: number): void {\n        this.modifyStamina(-amount);\n    }\n    /** 造成伤害 */\n    takeDamage(amount: number): void {\n        this.modifyHp(-amount);\n    }\n    /** * 清理资源 */\n    cleanup(): void {\n        this._modifiers.clear();\n        this._eventManager.cleanup();\n    }\n}\n"]}