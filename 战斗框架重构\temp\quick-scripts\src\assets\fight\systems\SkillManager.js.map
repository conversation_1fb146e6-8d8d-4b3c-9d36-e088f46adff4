{"version": 3, "sources": ["assets\\fight\\systems\\SkillManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA6C;AAE7C,0CAAoD;AACpD,+CAA8C;AAE9C;;;GAGG;AACH;IAKI,sBAAY,SAAqB;QAHzB,YAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;QACzC,sBAAiB,GAAkB,IAAI,CAAC;QAG5C,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,kBAAgB,SAAS,CAAC,aAAe,CAAC,CAAC;IAC7F,CAAC;IACD;;;OAGG;IACH,+BAAQ,GAAR,UAAS,KAAa;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;YAC5B,OAAO,CAAC,IAAI,CAAC,WAAS,KAAK,CAAC,EAAE,kCAA+B,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAClC,sBAAsB;QACtB,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAS,CAAC,YAAY,EAAE;YACvC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAClC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,UAAU,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;IAC1F,CAAC;IACD;;;;OAIG;IACH,kCAAW,GAAX,UAAY,OAAe;;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7B,oBAAoB;QACpB,IAAI,OAAA,IAAI,CAAC,iBAAiB,0CAAE,EAAE,MAAK,OAAO,EAAE;YACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,YAAY,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;OAIG;IACH,+BAAQ,GAAR,UAAS,OAAe;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAC7C,CAAC;IACD;;;OAGG;IACH,0CAAmB,GAAnB;QACI,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;;OAGG;IACH,0CAAmB,GAAnB,UAAoB,OAAe;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAClC;IACL,CAAC;IACD;;;OAGG;IACH,mCAAY,GAAZ;QACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IACD;;;;OAIG;IACH,sCAAe,GAAf,UAAgB,IAAe;QAC3B,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,IAAI,KAAK,IAAI,EAAnB,CAAmB,CAAC,CAAC;IACpE,CAAC;IACD;;;OAGG;IACH,yCAAkB,GAAlB;QACI,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,MAAM,EAAZ,CAAY,CAAC,CAAC;IAC7D,CAAC;IACD;;;OAGG;IACH,wCAAiB,GAAjB;QACI,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,CAAC,EAA5C,CAA4C,CAAC,CAAC;IAC7F,CAAC;IACD;;;;OAIG;IACH,+BAAQ,GAAR,UAAS,OAAe;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD;;;;OAIG;IACH,uCAAgB,GAAhB,UAAiB,OAAe;QAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IACxC,CAAC;IACD;;;;OAIG;IACH,gDAAyB,GAAzB,UAA0B,OAAe;QACrC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD;;;OAGG;IACH,oCAAa,GAAb,UAAc,OAAgB;;QAC1B,IAAI,OAAO,EAAE;YACT,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,kBAAkB,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;aACjG;SACJ;aAAM;;gBACH,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;oBAAtC,IAAM,KAAK,WAAA;oBACZ,KAAK,CAAC,aAAa,EAAE,CAAC;iBACzB;;;;;;;;;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IACD;;;;;;OAMG;IACH,+BAAQ,GAAR,UAAS,OAAe,EAAE,MAAmB,EAAE,QAAkB;QAC7D,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,CAAC,IAAI,CAAC,WAAS,OAAO,eAAY,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,uBAAqB,OAAO,eAAY,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;SAChB;QACD,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QAClE,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SAC1G;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IACD;;;OAGG;IACH,6BAAM,GAAN,UAAO,SAAiB;;;YACpB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAtC,IAAM,KAAK,WAAA;gBACZ,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC3B;;;;;;;;;IACL,CAAC;IACD;;;OAGG;IACH,oCAAa,GAAb;;QACI,IAAM,KAAK,GAAG;YACV,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC9B,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,EAAE;SACnB,CAAC;;YACF,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAtC,IAAM,KAAK,WAAA;gBACZ,IAAI,KAAK,CAAC,MAAM,EAAE;oBACd,KAAK,CAAC,eAAe,EAAE,CAAC;iBAC3B;qBAAM;oBACH,KAAK,CAAC,cAAc,EAAE,CAAC;iBAC1B;gBACD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACtC;gBACD,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;aACpC;;;;;;;;;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACD;;;OAGG;IACH,sCAAe,GAAf;;QACI,IAAM,IAAI,GAAG,EAAY,CAAA;;YACzB,KAA0B,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAA,gBAAA,4BAAE;gBAA7B,IAAA,KAAA,mBAAW,EAAV,EAAE,QAAA,EAAE,KAAK,QAAA;gBACjB,IAAI,CAAC,EAAE,CAAC,GAAG;oBACP,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;oBAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;iBACvB,CAAC;aACL;;;;;;;;;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,sBAAI,sCAAY;QAHhB;;WAEG;aACH;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACD,gBAAgB;IAChB,8BAAO,GAAP;QACI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACD,eAAe;IACf,mCAAY,GAAZ;;QACI,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YAC/B,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC7B,gBAAgB,EAAE,OAAA,IAAI,CAAC,iBAAiB,0CAAE,EAAE,KAAI,IAAI;YACpD,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE;YAC9B,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE;SAC9B,CAAC;IACN,CAAC;IACL,mBAAC;AAAD,CA7OA,AA6OC,IAAA;AA7OY,oCAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import FightEvent from \"../types/FightEvent\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { ISkill, SkillType } from \"../types/ISkill\";\nimport { EventManager } from \"./EventManager\";\n\n/**\n * 技能管理器\n * 负责管理角色的技能学习、使用和冷却\n */\nexport class SkillManager {\n    private _character: ICharacter;\n    private _skills: Map<string, ISkill> = new Map();\n    private _basicAttackSkill: ISkill | null = null;\n    private _eventManager: EventManager;\n    constructor(character: ICharacter) {\n        this._character = character;\n        this._eventManager = EventManager.createLocal(`SkillManager_${character.characterName}`);\n    }\n    /**\n     * 添加技能\n     * @param skill 技能实例\n     */\n    addSkill(skill: ISkill): void {\n        if (this._skills.has(skill.id)) {\n            console.warn(`Skill ${skill.id} already exists, replacing...`);\n        }\n        this._skills.set(skill.id, skill);\n        // 如果是基础攻击技能，设置为默认攻击技能\n        if (skill.type === SkillType.BASIC_ATTACK) {\n            this._basicAttackSkill = skill;\n        }\n        this._eventManager.emit(FightEvent.skillAdded, { skill, character: this._character });\n    }\n    /**\n     * 移除技能\n     * @param skillId 技能ID\n     * @returns 是否移除成功\n     */\n    removeSkill(skillId: string): boolean {\n        const skill = this._skills.get(skillId);\n        if (!skill) {\n            return false;\n        }\n        this._skills.delete(skillId);\n        // 如果移除的是基础攻击技能，清空引用\n        if (this._basicAttackSkill?.id === skillId) {\n            this._basicAttackSkill = null;\n        }\n        this._eventManager.emit(FightEvent.skillRemoved, { skill, character: this._character });\n        return true;\n    }\n    /**\n     * 获取技能\n     * @param skillId 技能ID\n     * @returns 技能实例或null\n     */\n    getSkill(skillId: string): ISkill | null {\n        return this._skills.get(skillId) || null;\n    }\n    /**\n     * 获取基础攻击技能\n     * @returns 基础攻击技能或null\n     */\n    getBasicAttackSkill(): ISkill | null {\n        return this._basicAttackSkill;\n    }\n    /**\n     * 设置基础攻击技能\n     * @param skillId 技能ID\n     */\n    setBasicAttackSkill(skillId: string): void {\n        const skill = this._skills.get(skillId);\n        if (skill) {\n            this._basicAttackSkill = skill;\n        }\n    }\n    /**\n     * 获取所有技能\n     * @returns 技能数组\n     */\n    getAllSkills(): ISkill[] {\n        return Array.from(this._skills.values());\n    }\n    /**\n     * 根据类型获取技能\n     * @param type 技能类型\n     * @returns 技能数组\n     */\n    getSkillsByType(type: SkillType): ISkill[] {\n        return this.getAllSkills().filter(skill => skill.type === type);\n    }\n    /**\n     * 获取可用的技能\n     * @returns 可用技能数组\n     */\n    getAvailableSkills(): ISkill[] {\n        return this.getAllSkills().filter(skill => skill.canUse);\n    }\n    /**\n     * 获取冷却中的技能\n     * @returns 冷却中技能数组\n     */\n    getCooldownSkills(): ISkill[] {\n        return this.getAllSkills().filter(skill => !skill.canUse && skill.remainingCooldown > 0);\n    }\n    /**\n     * 检查是否拥有技能\n     * @param skillId 技能ID\n     * @returns 是否拥有\n     */\n    hasSkill(skillId: string): boolean {\n        return this._skills.has(skillId);\n    }\n    /**\n     * 检查技能是否可用\n     * @param skillId 技能ID\n     * @returns 是否可用\n     */\n    isSkillAvailable(skillId: string): boolean {\n        const skill = this._skills.get(skillId);\n        return skill ? skill.canUse : false;\n    }\n    /**\n     * 获取技能冷却剩余时间\n     * @param skillId 技能ID\n     * @returns 剩余冷却时间（秒）\n     */\n    getSkillCooldownRemaining(skillId: string): number {\n        const skill = this._skills.get(skillId);\n        return skill ? skill.remainingCooldown : 0;\n    }\n    /**\n     * 重置技能冷却\n     * @param skillId 技能ID，如果不指定则重置所有技能\n     */\n    resetCooldown(skillId?: string): void {\n        if (skillId) {\n            const skill = this._skills.get(skillId);\n            if (skill) {\n                skill.resetCooldown();\n                this._eventManager.emit(FightEvent.skillCooldownReset, { skill, character: this._character });\n            }\n        } else {\n            for (const skill of this._skills.values()) {\n                skill.resetCooldown();\n            }\n            this._eventManager.emit(FightEvent.allSkillsCooldownReset, { character: this._character });\n        }\n    }\n    /**\n     * 使用技能\n     * @param skillId 技能ID\n     * @param target 目标角色\n     * @param position 目标位置\n     * @returns 是否使用成功\n     */\n    useSkill(skillId: string, target?: ICharacter, position?: cc.Vec3): boolean {\n        const skill = this._skills.get(skillId);\n        if (!skill) {\n            console.warn(`Skill ${skillId} not found`);\n            return false;\n        }\n        if (!skill.canCastOn(this._character, target)) {\n            console.warn(`Cannot cast skill ${skillId} on target`);\n            return false;\n        }\n        const success = skill.cast(this._character, target, [], position);\n        if (success) {\n            this._eventManager.emit(FightEvent.skillUsed, { skill, character: this._character, target, position });\n        }\n        return success;\n    }\n    /**\n     * 更新所有技能状态\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void {\n        for (const skill of this._skills.values()) {\n            skill.update(deltaTime);\n        }\n    }\n    /**\n     * 获取技能统计信息\n     * @returns 统计信息\n     */\n    getSkillStats() {\n        const stats = {\n            totalSkills: this._skills.size,\n            availableSkills: 0,\n            cooldownSkills: 0,\n            skillsByType: {}\n        };\n        for (const skill of this._skills.values()) {\n            if (skill.canUse) {\n                stats.availableSkills++;\n            } else {\n                stats.cooldownSkills++;\n            }\n            if (!stats.skillsByType[skill.type]) {\n                stats.skillsByType[skill.type] = 0;\n            }\n            stats.skillsByType[skill.type]++;\n        }\n        return stats;\n    }\n    /**\n     * 导出技能数据\n     * @returns 技能数据\n     */\n    exportSkillData() {\n        const data = {} as ISkill\n        for (const [id, skill] of this._skills) {\n            data[id] = {\n                id: skill.id,\n                name: skill.name,\n                type: skill.type,\n                level: skill.level,\n                cooldown: skill.cooldown,\n                remainingCooldown: skill.remainingCooldown,\n                canUse: skill.canUse\n            };\n        }\n        return data;\n    }\n    /**\n     * 获取事件管理器（供外部直接使用，避免包装方法冗余）\n     */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n    /** * 清理技能管理器 */\n    cleanup(): void {\n        this._skills.clear();\n        this._basicAttackSkill = null;\n        this._eventManager.cleanup();\n    }\n    /** * 获取调试信息 */\n    getDebugInfo() {\n        return {\n            characterId: this._character.id,\n            skillCount: this._skills.size,\n            basicAttackSkill: this._basicAttackSkill?.id || null,\n            skills: this.exportSkillData(),\n            stats: this.getSkillStats()\n        };\n    }\n}\n"]}