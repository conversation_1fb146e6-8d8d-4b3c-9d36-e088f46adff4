
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/buff/StunBuff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '55fa3ixozBPQI7/pHu4XR6K', 'StunBuff');
// fight/buff/StunBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StunBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 眩晕Debuff
 * 使目标无法行动，无法释放技能和移动
 */
var StunBuff = /** @class */ (function () {
    function StunBuff(caster, target, duration) {
        this._name = "眩晕";
        this._description = "无法行动、释放技能或移动";
        this._type = IBuff_1.BuffType.DEBUFF;
        this._stackCount = 1;
        this._maxStack = 1; // 眩晕不可叠加
        this._isExpired = false;
        this._attributeModifiers = [];
        // 眩晕配置
        this._preventMovement = true;
        this._preventSkills = true;
        this._preventBasicAttack = true;
        // 视觉效果
        this._iconPath = "icons/buffs/stun";
        this._effectPrefabPath = "prefabs/effects/StunEffect";
        this._id = "stun_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u65E0\u6CD5\u884C\u52A8\uFF0C\u6301\u7EED" + duration + "\u79D2";
        // 创建属性修改器（降低移动速度到0）
        this.createAttributeModifiers();
    }
    Object.defineProperty(StunBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (_value) { this._stackCount = 1; } // 眩晕不可叠加
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    StunBuff.prototype.createAttributeModifiers = function () {
        // 移动速度修改器（设为0）
        var moveSpeedModifier = new AttributeModifier_1.AttributeModifier(this._id + "_move_speed", "眩晕移动限制", "moveSpeed", ICharacterAttributes_1.AttributeModifierType.OVERRIDE, 0, this._duration);
        this._attributeModifiers = [moveSpeedModifier];
    };
    /** Buff被添加时触发 */
    StunBuff.prototype.onApply = function () {
        var e_1, _a;
        console.log(this._name + " applied to " + this._target.characterName + " for " + this._duration + " seconds");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放眩晕特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 中断目标当前的行动
        this.interruptCurrentActions();
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterStunned, { target: this._target, caster: this._caster, duration: this._duration });
    };
    /** Buff每帧更新时触发 */
    StunBuff.prototype.onTick = function (deltaTime) {
        var e_2, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 持续检查并阻止行动
        this.preventActions();
    };
    /** Buff被移除时触发 */
    StunBuff.prototype.onRemove = function () {
        var e_3, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            // 移除属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterStunEnded, { target: this._target });
    };
    /** 释放技能时触发 - 阻止技能释放 */
    StunBuff.prototype.onSkillCast = function (skill, timeline) {
        if (this._preventSkills) {
            console.log(this._target.characterName + " cannot cast " + skill.name + " while stunned");
            // 返回null或空timeline来阻止技能释放
            return null;
        }
        return timeline;
    };
    /** 中断当前行动 */
    StunBuff.prototype.interruptCurrentActions = function () {
        // 中断当前的移动
        if (this._target.node) {
            this._target.node.stopAllActions();
        }
        // 中断当前的技能释放（如果有的话）
        // 这里需要与SkillManager配合
        console.log("Interrupting all actions for " + this._target.characterName);
    };
    /** 阻止行动 */
    StunBuff.prototype.preventActions = function () {
        // 这里可以添加持续的行动阻止逻辑
        // 比如重置移动输入、阻止技能队列等
    };
    /** 更新Buff状态 */
    StunBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    StunBuff.prototype.refresh = function () {
        var e_4, _a;
        this._remainingTime = this._duration;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 - 眩晕不可叠加，但可以刷新时间 */
    StunBuff.prototype.addStack = function (_count) {
        if (_count === void 0) { _count = 1; }
        // 眩晕不叠加层数，但刷新持续时间
        this.refresh();
        console.log(this._name + " duration refreshed on " + this._target.characterName);
    };
    /** 减少叠加层数 - 眩晕直接移除 */
    StunBuff.prototype.removeStack = function (_count) {
        if (_count === void 0) { _count = 1; }
        this._stackCount = 0;
        this._isExpired = true;
        console.log(this._name + " removed from " + this._target.characterName);
    };
    /** 获取Buff的当前效果值 */
    StunBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.movement_blocked:
                return this._preventMovement ? 1 : 0;
            case Buff_1.EBuffEffectType.skills_blocked:
                return this._preventSkills ? 1 : 0;
            case Buff_1.EBuffEffectType.basic_attack_blocked:
                return this._preventBasicAttack ? 1 : 0;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    StunBuff.prototype.conflictsWith = function (otherBuff) {
        // 眩晕与其他控制类debuff可能冲突
        if (otherBuff.type === IBuff_1.BuffType.DEBUFF && otherBuff.name.includes("眩晕")) {
            return true;
        }
        return false;
    };
    /** 检查是否可以被净化 */
    StunBuff.prototype.canBeDispelled = function () {
        return true; // 眩晕可以被净化技能移除
    };
    /** 播放应用特效 */
    StunBuff.prototype.playApplyEffect = function () {
        console.log("Playing stun effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的眩晕特效播放逻辑
        // 比如在目标头顶显示眩晕星星特效
    };
    /** 停止特效 */
    StunBuff.prototype.stopEffect = function () {
        console.log("Stopping stun effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    StunBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            preventMovement: this._preventMovement,
            preventSkills: this._preventSkills,
            preventBasicAttack: this._preventBasicAttack,
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return StunBuff;
}());
exports.StunBuff = StunBuff;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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