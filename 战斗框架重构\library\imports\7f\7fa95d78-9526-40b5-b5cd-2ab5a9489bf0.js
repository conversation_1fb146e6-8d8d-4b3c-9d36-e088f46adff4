"use strict";
cc._RF.push(module, '7fa9514lSZAtbXNKrWpSJvw', 'CharacterTypes');
// fight/types/CharacterTypes.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackState = exports.CharacterAttributeName = exports.CharacterSelectTag = exports.CharacterState = exports.CharacterRole = void 0;
/*** 角色阵营枚举*/
var CharacterRole;
(function (CharacterRole) {
    /** 无效/未定义 */
    CharacterRole[CharacterRole["INVALID"] = -1] = "INVALID";
    /** 英雄/玩家 */
    CharacterRole[CharacterRole["HERO"] = 0] = "HERO";
    /** 敌人 */
    CharacterRole[CharacterRole["ENEMY"] = 1] = "ENEMY";
    /** 子弹/投射物 */
    CharacterRole[CharacterRole["BULLET"] = 2] = "BULLET";
    /** 伙伴/宠物 */
    CharacterRole[CharacterRole["PARTNER"] = 3] = "PARTNER";
    /** 中立 */
    CharacterRole[CharacterRole["NEUTRAL"] = 4] = "NEUTRAL";
})(CharacterRole = exports.CharacterRole || (exports.CharacterRole = {}));
/*** 角色状态枚举*/
var CharacterState;
(function (CharacterState) {
    /** 空闲 */
    CharacterState["IDLE"] = "idle";
    /** 移动 */
    CharacterState["MOVING"] = "moving";
    /** 攻击 */
    CharacterState["ATTACKING"] = "attacking";
    /** 释放技能 */
    CharacterState["CASTING"] = "casting";
    /** 受伤 */
    CharacterState["HURT"] = "hurt";
    /** 死亡 */
    CharacterState["DEAD"] = "dead";
    /** 眩晕 */
    CharacterState["STUNNED"] = "stunned";
    /** 沉默 */
    CharacterState["SILENCED"] = "silenced";
    /** 无敌 */
    CharacterState["INVINCIBLE"] = "invincible";
})(CharacterState = exports.CharacterState || (exports.CharacterState = {}));
/*** 角色选择标签枚举*/
var CharacterSelectTag;
(function (CharacterSelectTag) {
    /** 未选中 */
    CharacterSelectTag[CharacterSelectTag["NONE"] = 0] = "NONE";
    /** 玩家选中 */
    CharacterSelectTag[CharacterSelectTag["PLAYER"] = 1] = "PLAYER";
    /** 伙伴选中 */
    CharacterSelectTag[CharacterSelectTag["PARTNER"] = 2] = "PARTNER";
})(CharacterSelectTag = exports.CharacterSelectTag || (exports.CharacterSelectTag = {}));
var CharacterAttributeName = /** @class */ (function () {
    function CharacterAttributeName() {
    }
    /** 生命值 */
    CharacterAttributeName.hp = 'hp';
    /** 最大生命值 */
    CharacterAttributeName.maxHp = 'maxHp';
    /** 魔法值 */
    CharacterAttributeName.mp = 'mp';
    /** 最大魔法值 */
    CharacterAttributeName.maxMp = 'maxMp';
    /** 最大耐力 */
    CharacterAttributeName.maxStamina = 'maxStamina';
    /** 攻击力 */
    CharacterAttributeName.attack = 'attack';
    /** 防御力 */
    CharacterAttributeName.defense = 'defense';
    /** 攻击速度 */
    CharacterAttributeName.attackSpeed = 'attackSpeed';
    /** 移动速度 */
    CharacterAttributeName.moveSpeed = 'moveSpeed';
    /** 攻击范围 */
    CharacterAttributeName.attackRange = 'attackRange';
    /** 暴击率 */
    CharacterAttributeName.criticalRate = 'criticalRate';
    /** 暴击伤害 */
    CharacterAttributeName.criticalDamage = 'criticalDamage';
    /** 命中率 */
    CharacterAttributeName.hitRate = 'hitRate';
    /** 闪避率 */
    CharacterAttributeName.dodgeRate = 'dodgeRate';
    /** 等级 */
    CharacterAttributeName.level = 'level';
    /** 经验值 */
    CharacterAttributeName.experience = 'experience';
    /**当前hp */
    CharacterAttributeName.currentHp = 'currentHp';
    /**当前Mp */
    CharacterAttributeName.currentMp = 'currentMp';
    /**当前耐力*/
    CharacterAttributeName.currentStamina = 'currentStamina';
    return CharacterAttributeName;
}());
exports.CharacterAttributeName = CharacterAttributeName;
/** * 攻击状态枚举 */
var AttackState;
(function (AttackState) {
    /** 空闲状态 */
    AttackState["IDLE"] = "idle";
    /** 前摇阶段 */
    AttackState["WINDUP"] = "windup";
    /** 伤害阶段 */
    AttackState["DAMAGE"] = "damage";
    /** 后摇阶段 */
    AttackState["RECOVERY"] = "recovery";
})(AttackState = exports.AttackState || (exports.AttackState = {}));

cc._RF.pop();