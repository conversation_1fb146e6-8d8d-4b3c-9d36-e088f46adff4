{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\Equipment.ts"], "names": [], "mappings": ";;;;AACA,4CAA4C;AAC5C,0CAA0C;AAC1C,8CAA8C;AAC9C,6BAA6B;AAE7B,eAAe;AACf,0BAA0B;AAC1B,qBAAqB;AACrB,IAAI;AACJ,cAAc;AACd,gCAAgC;AAChC,eAAe;AACf,yBAAyB;AACzB,eAAe;AACf,yBAAyB;AACzB,gBAAgB;AAChB,2BAA2B;AAC3B,iBAAiB;AACjB,yBAAyB;AACzB,IAAI;AAEJ,mBAAmB;AACnB,wCAAwC;AACxC,wCAAwC;AACxC,iCAAiC;AACjC,mBAAmB;AACnB,0BAA0B;AAC1B,uBAAuB;AACvB,0BAA0B;AAC1B,uBAAuB;AACvB,yBAAyB;AACzB,kCAAkC;AAClC,sBAAsB;AACtB,6BAA6B;AAC7B,QAAQ;AACR,iCAAiC;AAEjC,WAAW;AACX,IAAI;AAEJ,OAAO;AACP,sBAAsB;AACtB,mEAAmE;AACnE,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,0EAA0E;AAC1E,iBAAiB;AACjB,4BAA4B;AAE5B,mBAAmB;AACnB,kCAAkC;AAClC,wBAAwB;AACxB,gCAAgC;AAEhC,oBAAoB;AACpB,uBAAuB;AACvB,iBAAiB;AAEjB,oBAAoB;AACpB,uBAAuB;AACvB,mBAAmB;AAEnB,eAAe;AACf,uBAAuB;AACvB,sBAAsB;AAEtB,uBAAuB;AACvB,sBAAsB;AACtB,uBAAuB;AACvB,qBAAqB;AAErB,uBAAuB;AACvB,8BAA8B;AAE9B,uBAAuB;AACvB,4BAA4B;AAE5B,uBAAuB;AACvB,kBAAkB;AAElB,uBAAuB;AACvB,yBAAyB;AAEzB,eAAe;AACf,+CAA+C;AAC/C,QAAQ;AAER,4CAA4C;AAC5C,+BAA+B;AAC/B,mDAAmD;AACnD,YAAY;AACZ,QAAQ;AAER,+BAA+B;AAC/B,oBAAoB;AACpB,6BAA6B;AAC7B,uBAAuB;AACvB,+BAA+B;AAC/B,wBAAwB;AACxB,qBAAqB;AACrB,uBAAuB;AACvB,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n// import BuffManager from \"../BuffManager\";\r\n// import { ECamp } from \"../CampManager\";\r\n// import SkillManager from \"../SkillManager\";\r\n// import Base from \"./Base\";\r\n\r\n// /**buff类型 */\r\n// export enum EBuffType {\r\n//     None = `None`,\r\n// }\r\n// /**卡片的状态 */\r\n// export enum EquipmentStatus {\r\n//     /**正常 */\r\n//     Normal = `Normal`,\r\n//     /**损坏 */\r\n//     Broken = `Broken`,\r\n//     /**已装备 */\r\n//     Equiped = `Equiped`,\r\n//     /**无法使用 */\r\n//     Unable = `Unable`,\r\n// }\r\n\r\n// declare global {\r\n//     type IEquipmentName = 'Equipment'\r\n//     export interface IEquipmentData {\r\n//         equipmentName: string;\r\n//         /**耐久 */\r\n//         durable: number\r\n//         /**提供的防御力 */\r\n//         defence: number\r\n//         /**提供的攻击力 */\r\n//         attack: number\r\n//         status: EquipmentStatus\r\n//         camp: ECamp\r\n//         orginValue: number\r\n//     }\r\n//     // interface IMainUIData {\r\n\r\n//     // }\r\n// }\r\n\r\n// /** \r\n//  * @features : 游戏装备\r\n//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中\r\n//  * @Date : 2020-08-12 23:28:43\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:01:01\r\n//  * @LastEditors : judu233\r\n//  */\r\n// export default class Equipment extends Base implements IEquipmentData {\r\n//     /**技能数据 */\r\n//     data: IEquipmentData;\r\n\r\n//     /**装备技能管理 */\r\n//     skillMgr = new SkillManager\r\n//     /**对装备使用buff管理 */\r\n//     buffMgr = new BuffManager\r\n\r\n//     /**buff的Id */\r\n//     @Base.ViewLinked\r\n//     id: string\r\n\r\n//     /**buff的类型 */\r\n//     @Base.ViewLinked\r\n//     type: string\r\n\r\n//     /**耐久 */\r\n//     @Base.ViewLinked\r\n//     durable: number\r\n\r\n//     @Base.ViewLinked\r\n//     defence: number\r\n//     @Base.ViewLinked\r\n//     attack: number\r\n\r\n//     @Base.ViewLinked\r\n//     status: EquipmentStatus\r\n\r\n//     @Base.ViewLinked\r\n//     equipmentName: string\r\n\r\n//     @Base.ViewLinked\r\n//     camp: ECamp\r\n\r\n//     @Base.ViewLinked\r\n//     orginValue: number\r\n\r\n//     init() {\r\n//         this.status = EquipmentStatus.Normal\r\n//     }\r\n\r\n//     durableChangeView(newValue: number) {\r\n//         if (newValue <= 0) {\r\n//             this.status = EquipmentStatus.Broken\r\n//         }\r\n//     }\r\n\r\n//     /**使用buff ` 子类负责实现具体逻辑*/\r\n//     useBuff() { }\r\n//     /**移除Buff 子类负责实现具体逻辑*/\r\n//     removeBuff() { }\r\n//     /**叠加一层buff 子类负责实现具体逻辑*/\r\n//     overlayBuff() { }\r\n//     /**减少一层buff */\r\n//     reduceBuff() { }\r\n// }\r\n"]}