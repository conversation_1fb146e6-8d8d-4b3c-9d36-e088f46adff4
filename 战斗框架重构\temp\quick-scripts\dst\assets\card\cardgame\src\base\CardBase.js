
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/CardBase.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '488a29eeQZBAZsC9vkt8yeQ', 'CardBase');
// card/cardgame/src/base/CardBase.ts

// import { ECamp } from "../CampManager";
// import { BRO } from "../EventManager";
// import FightManager from "../FightManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";
// import CampBase from "./CampBase";
// import RoleBase from "./RoleBase";
// const { ccclass, property } = cc._decorator;
// export enum ECardType {
//     /**装备卡牌 */
//     equipment = 'equipment',
//     /**攻击卡牌 */
//     attact = 'attact',
//     /**血量操作相关的卡牌 */
//     hp = 'hp',
//     /**垃圾牌 */
//     garbage = 'garbage',
//     /**污染卡 */
//     pollution = 'pollution',
//     /**礼物卡 */
//     gift = 'gift',
//     /**修补装备 */
//     repair = 'repair',
// }
// /**卡片的状态 */
// enum ECardStatus {
//     /**卡牌未使用 */
//     None = `None`,
//     /**卡牌已使用 */
//     Used = `Used`,
//     /**卡牌已死亡 */
//     Death = `Death`,
//     /**卡牌已冻结 */
//     Frozen = `Frozen`,
// }
// export enum EHpType {
//     /**固定恢复 */
//     fixedRecovery = 'fixedRecovery',
//     /**百分比恢复 */
//     percentRecovery = 'percentRecovery',
//     /**扣除固定 */
//     fixedDeduct = 'fixedDeduct',
//     /**扣除百分比 */
//     percentDeduct = 'percentDeduct',
//     /**破碎的血瓶，使用后恢复剩余的血量 */
//     breakRecovery = 'breakRecovery',
// }
// export enum EPollutionType {
//     /**污染其他卡牌 */
//     pollutionOtherCard = 'pollutionOtherCard',
//     /**自爆卡 */
//     selfBomb = 'selfBomb',
// }
// export enum EGiftType {
//     /**装备礼物卡 */
//     equipmentGift = 'equipmentGift',
//     /**卷轴礼物卡 */
//     scrollGift = 'scrollGift',
// }
// declare global {
//     type ICardBaseName = 'CardBase'
//     /**卡牌或角色的基础数据 */
//     export interface ICardDataType extends IBaseDataType {
//         /**卡牌hp值（耐久值） */
//         hp: number;
//         /**卡牌血量的上限值 */
//         hpUp: number;
//         /**卡牌所属阵营 */
//         camp: ECamp;
//         /**攻击值 */
//         attack: number;
//         /**卡牌的状态 */
//         status?: ECardStatus;
//         /**卡片类型 */
//         type: ECardType
//         /**卡片图片名字 */
//         cardImageName: string
//         /**卡牌值 */
//         value: number
//         /**卡牌原始值 */
//         originValue?: number
//         /**装备类型 （当卡牌为装备卡时）*/
//         equipType?: number
//         /**装备在场景的图片名字 */
//         equipImgName?: string
//         /**加血回复类型（当卡牌和血量有关时）*/
//         hpType?: EHpType
//         /**污染类型（当卡牌和污染卡片有关时 */
//         pollutionType?: EPollutionType
//         /**是否可以使用 */
//         isCanUse?: boolean
//         /**礼物卡牌类型 */
//         giftType?: EGiftType
//         /**是否持续计时卡片 */
//         isDuration?: boolean
//     }
// }
// /**
//  * @features : 卡牌或角色的基类
//  * @description: 说明
//  * @Date : 2020-08-12 23:29:02
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:07:04
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class CardBase extends Base implements ICardDataType {
//     /**存储卡牌的数据 */
//     data: ICardDataType
//     /**卡牌技能 */
//     skillMgr = new SkillManager
//     /**卡牌id */
//     @Base.ViewLinked
//     id: string
//     /**卡牌血量*/
//     @Base.ViewLinked
//     hp: number
//     /**卡牌血量的上限值 */
//     hpUp: number
//     /**卡牌的状态 */
//     @Base.ViewLinked
//     status: ECardStatus
//     /**卡牌名字 */
//     @Base.ViewLinked
//     cardName: string
//     /**卡牌的阵营 */
//     camp: ECamp
//     /**卡牌攻击力 */
//     @Base.ViewLinked
//     attack: number
//     type: ECardType
//     @Base.ViewLinked
//     value: number
//     cardImageName: string
//     originValue: number
//     get isDeath() { return this.status == ECardStatus.Death; }
//     initCard() {
//         this.status = ECardStatus.None
//         this.data.originValue = this.value
//         if (this.data.isDuration) {
//             this.schedule(this.onScheduleTimer, 1)
//         }
//     }
//     onScheduleTimer() {
//         this.value--;
//         if (this.value < 0) {
//             this.data.value = 0
//             if (this.data.pollutionType == EPollutionType.selfBomb) {
//                 BRO.broadcast(BRO.keys.DiscardCard, this.id)
//                 FightManager.playerCamp.getRole().hp -= 20
//             } else if (this.data.pollutionType == EPollutionType.pollutionOtherCard) {
//                 BRO.broadcast(BRO.keys.PolluteCard, this.id)
//             }
//             this.unschedule(this.onScheduleTimer)
//         }
//     }
//     hpAfterChangeView(value: number) {
//         if (value < 0) {
//             this.hp = 0;
//             this.status = ECardStatus.Death;
//         } else {
//             if (value >= this.hpUp) value = this.hpUp;
//             this.data.hp = value;
//         }
//     }
//     broadcast(target, key, newValue) {
//         BRO.broadcast(BRO.keys.RefreshUI, { [key]: newValue }, target)
//     }
//     /********************** 关于卡牌的操作 ********************** */
//     /**
//      * 攻击指定阵营
//      * @param camp 要攻击的阵营
//      */
//     async attackCamp(camp: CampBase) {
//         if (FightManager.isAuto || this.camp == ECamp.Computer) {
//             await Promise.all(this.attackCards(camp, 1));
//         } else {
//             await new Promise<void>(waitResolve => {
//                 BRO.once(BRO.keys.PlayerAttack, async () => {
//                     await this.attackCards(camp, 1)[0];
//                     waitResolve();
//                 }, this);
//             });
//         }
//     }
//     /**
//      * 攻击指定的卡牌
//      * @param card 要攻击的卡牌
//      */
//     async attackCard(card: CardBase) {
//         cc.log(`[${this.cardName}]卡牌开始攻击[${card.cardName}]`);
//         await Promise.wait(1000)
//         card.hp -= this.attack;
//         cc.log(`[${this.cardName}]卡牌攻击完成[${card.cardName}]`);
//     }
//     async attackRole(role: RoleBase) {
//         cc.log(`[${this.cardName}]卡牌开始攻击[${role.roleName}]`);
//         await Promise.wait(1000)
//         role.hp -= this.attack;
//         cc.log(`[${this.cardName}]卡牌攻击完成[${role.roleName}]`);
//     }
//     /**
//      * 攻击多个卡牌(同时&&轮流)
//      * @param attackCamp 要攻击的阵营
//      * @param attackCount 攻击数量
//      * @param isSimultaneously 是否同时攻击--默认同时
//      */
//     attackCards(attackCamp: CampBase, attackCount: number, isSimultaneously: boolean = true) {
//         //获取卡牌
//         let cardList = attackCamp.roleMgr.getRoleCard(attackCount)
//         let promiseList: Promise<unknown>[] = [];
//         //电脑攻击模式 -- 同时或轮流
//         if (this.camp == ECamp.Computer) {
//             isSimultaneously = FightManager.isComputerTurnAttack;
//         } else if (this.camp == ECamp.Player) {
//             isSimultaneously = FightManager.isPlayerTurnAttack;
//         }
//         if (isSimultaneously) {
//             //同步攻击
//             for (let card of cardList)
//                 promiseList.push(this.attackCard(card));
//         } else {
//             //异步攻击（轮流）
//             let selfCamp = this;
//             promiseList.push(new Promise<void>(async (resolve) => {
//                 for (let card of cardList)
//                     await selfCamp.attackCard(card);
//                 //完成攻击
//                 resolve();
//             }));
//         }
//         return promiseList;
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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