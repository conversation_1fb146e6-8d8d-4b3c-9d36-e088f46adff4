import { TimelineManager } from "../systems/TimelineManager";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from "../timeline/TimelineEvents";
import { IBuff } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { DamageType } from "../types/IDamage";
import { ISkill, SkillType, SkillTargetType } from "../types/ISkill";
import { ITimeline, ITimelineEvent, TimelineEventType } from "../types/ITimeline";
import SkillName from "../types/SkillName";
import { AttackBoostBuff } from "../buff/AttackBoostBuff";
import { BattleManager } from "../systems/BattleManager";

/**
 * 冲锋攻击技能
 * 快速冲向目标并造成物理伤害，同时获得短暂的攻击力提升
 */
export class ChargeAttackSkill implements ISkill {
    private _id: string = SkillName.charge_attack;
    private _name: string = "冲锋攻击";
    private _description: string = "快速冲向敌人并发动强力攻击，攻击后获得攻击力提升";
    private _cooldown: number = 5.0;
    private _remainingCooldown: number = 0;
    private _mpCost: number = 0;
    private _staminaCost: number = 25;
    private _level: number = 1;
    private _type: SkillType = SkillType.ACTIVE;
    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ENEMY;
    private _range: number = 300;
    private _timeline: ITimeline | null = null;
    private _passiveBuffs: IBuff[] = [];

    /** 技能配置 */
    private _config = {
        animationName: "skill_charge",
        soundId: "charge_attack",
        effectPath: "prefabs/effects/ChargeTrail",
        hitEffectPath: "prefabs/effects/ChargeImpact",
        damage: 0, // 0表示使用施法者的攻击力
        damageMultiplier: 1.8, // 伤害倍率
        chargeSpeed: 800, // 冲锋速度
        attackBuffDuration: 4.0, // 攻击力提升持续时间
        attackBuffMultiplier: 1.3 // 攻击力提升倍率
    };

    // 实现ISkill接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get cooldown(): number { return this._cooldown; }
    get remainingCooldown(): number { return this._remainingCooldown; }
    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }
    get mpCost(): number { return this._mpCost; }
    get staminaCost(): number { return this._staminaCost; }
    get level(): number { return this._level; }
    get type(): SkillType { return this._type; }
    get targetType(): SkillTargetType { return this._targetType; }
    get range(): number { return this._range; }
    get timeline(): ITimeline { return this._timeline!; }
    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }
    get canUse(): boolean {
        return this._remainingCooldown <= 0;
    }

    /** 检查是否可以对目标使用技能 */
    canCastOn(caster: ICharacter, target?: ICharacter): boolean {
        if (!target) return false;
        if (target.isDead) return false;
        if (target.role !== caster.role) return true; // 敌对阵营
        return false;
    }

    /** 检查资源消耗 */
    checkResourceCost(caster: ICharacter): boolean {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    }

    /** 消耗资源 */
    consumeResources(caster: ICharacter): void {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    }

    /** 释放技能 */
    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }

        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(`${caster.characterName} charges at ${target?.characterName}`);
        return true;
    }

    /** 创建技能Timeline */
    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {
        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const timeline = new Timeline(timelineId, this._name, 2.5, caster);

        // 0.0s: 播放冲锋准备动画
        const prepareNode = new TimelineNode(`${timelineId}_prepare`, 0.0, new PlayAnimationTimelineEvent("prepare_animation", "charge_prepare"), false);
        timeline.addNode(prepareNode);
        // 0.3s: 开始冲锋移动
        const chargeStartNode = new TimelineNode(`${timelineId}_charge_start`, 0.3, new ChargeMovementEvent("charge_movement", caster, target!, this._config.chargeSpeed), false);
        timeline.addNode(chargeStartNode);
        // 0.4s: 播放冲锋音效和拖尾特效
        const soundNode = new TimelineNode(`${timelineId}_sound`, 0.4, new PlaySoundTimelineEvent("charge_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        const trailNode = new TimelineNode(`${timelineId}_trail`, 0.4, new PlayEffectTimelineEvent("charge_trail", this._config.effectPath, false, caster.node.position), false);
        timeline.addNode(trailNode);
        // 1.0s: 冲锋攻击命中
        const attackNode = new TimelineNode(`${timelineId}_attack`, 1.0, new ChargeAttackEvent("charge_attack", caster, target!, this.calculateDamage(caster)), false);
        timeline.addNode(attackNode);
        // 1.1s: 播放命中特效
        const hitEffectNode = new TimelineNode(`${timelineId}_hit_effect`, 1.1, new PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, target?.node.position || cc.Vec3.ZERO), false);
        timeline.addNode(hitEffectNode);
        // 1.2s: 添加攻击力提升buff
        const buffNode = new TimelineNode(`${timelineId}_buff`, 1.2, new AddAttackBuffEvent("attack_buff", caster, this._config.attackBuffDuration, this._config.attackBuffMultiplier), false);
        timeline.addNode(buffNode);
        return timeline;
    }

    /** 计算伤害 */
    private calculateDamage(caster: ICharacter): number {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        const baseAttack = caster.attributes.attack;
        return Math.floor(baseAttack * this._config.damageMultiplier);
    }

    /** 更新技能冷却 */
    update(deltaTime: number): void {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    }

    /** 重置冷却时间 */
    resetCooldown(): void {
        this._remainingCooldown = 0;
    }

    /** 升级技能 */
    levelUp(): void {
        this._level++;
        this._staminaCost = Math.max(15, this._staminaCost - 2);
        this._cooldown = Math.max(3.0, this._cooldown - 0.3);
        this._range += 25;
        this._config.damageMultiplier += 0.15;
        this._config.attackBuffMultiplier += 0.05;
        this._config.chargeSpeed += 50;
        console.log(`${this._name} leveled up to ${this._level}`);
    }

    /** 获取技能信息 */
    getSkillInfo() {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            damageMultiplier: this._config.damageMultiplier,
            chargeSpeed: this._config.chargeSpeed
        };
    }
}

/*** 冲锋移动事件*/
class ChargeMovementEvent implements ITimelineEvent {
    private _id: string;
    private _type: TimelineEventType = TimelineEventType.MOVE;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _speed: number;

    constructor(id: string, caster: ICharacter, target: ICharacter, speed: number) {
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._speed = speed;
    }

    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }

    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {
        // 计算冲锋方向和距离
        const startPos = this._caster.node.position;
        const targetPos = this._target.node.position;
        const direction = targetPos.subtract(startPos).normalize();
        // 冲锋到目标附近（保持一定距离）
        const chargeDistance = cc.Vec3.distance(startPos, targetPos) - 50; // 保持50单位距离
        const endPos = startPos.add(direction.multiplyScalar(chargeDistance));
        console.log(`${this._caster.characterName} charges from (${startPos.x}, ${startPos.y}) to (${endPos.x}, ${endPos.y})`);
        // 这里应该实现实际的移动逻辑，比如使用Tween动画
        // cc.tween(this._caster.node)
        //     .to(0.7, { position: endPos })
        //     .start();
    }
}

/*** 冲锋攻击事件*/
class ChargeAttackEvent implements ITimelineEvent {
    private _id: string;
    private _type: TimelineEventType = TimelineEventType.DAMAGE;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _damage: number;

    constructor(id: string, caster: ICharacter, target: ICharacter, damage: number) {
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._damage = damage;
    }

    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }

    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {
        if (this._target && !this._target.isDead) {
            const damageInfo = {
                amount: this._damage,
                type: DamageType.PHYSICAL,
                source: this._caster,
                isCritical: Math.random() < 0.2, // 20%暴击率
                element: "physical"
            };
            this._target.takeDamageSimple(damageInfo.amount, this._caster);
            console.log(`${this._caster.characterName} charge attacks ${this._target.characterName} for ${this._damage} physical damage`);
        }
    }
}

/*** 添加攻击力提升buff事件*/
class AddAttackBuffEvent implements ITimelineEvent {
    private _id: string;
    private _type: TimelineEventType = TimelineEventType.ADD_BUFF;
    private _caster: ICharacter;
    private _duration: number;
    private _multiplier: number;

    constructor(id: string, caster: ICharacter, duration: number, multiplier: number) {
        this._id = id;
        this._caster = caster;
        this._duration = duration;
        this._multiplier = multiplier;
    }

    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }

    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {
        console.log(`${this._caster.characterName} gains attack boost (${this._multiplier}x) for ${this._duration} seconds`);

        // 实现AttackBoostBuff
        const attackBuff = new AttackBoostBuff(this._caster, this._caster, this._duration, this._multiplier);
        this._caster.buffManager.addBuff(attackBuff);
    }
}
