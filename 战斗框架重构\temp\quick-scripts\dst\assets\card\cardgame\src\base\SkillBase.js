
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/SkillBase.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'be2a3fmujlJ04XeBAKsSCgN', 'SkillBase');
// card/cardgame/src/base/SkillBase.ts

// import BuffBase from './BuffBase';
// import CardBase from "./CardBase";
// import BuffManager from '../../Controller/BuffManager';
// /**冷却类型 */
// export enum EFrozen {
//     /**回合类型 */
//     round = `round`,
//     /**全局类型 */
//     global = `global`,
//     /**计时 */
//     timeOut = `timeOut`,
//     /**次数 */
//     timeCount = `timeCount`,
// }
// declare global {
//     /**冷却时间接口 */
//     export interface ISkillFrozenType {
//         /**冷却类型 */
//         type: EFrozen;
//         /**次数，计时，回合都是number 全局分为计时和次数 */
//         value: number | ISkillFrozenType;
//     }
//     /**技能数据接口 */
//     export interface ISkillDataType extends IBaseDataType {
//         /**buff  列表 */
//         buffData: IBuffDataType[];
//         /**使用次数 */
//         useCount: number;
//         /**技能等级 */
//         level: number;
//         /**等级上限 */
//         levelHeight: number;
//         /**是否可以使用此技能 */
//         isCanUse: boolean;
//         /**是否可以升级 */
//         isCanLevelUp: boolean;
//         /**是否正在冷却 */
//         isFrozen: boolean;
//         /**冷却信息 */
//         frozen: ISkillFrozenType;
//     }
// }
// /**
//  * @features : 技能控制基类
//  * @description: 对于一个卡牌或角色的基类控制
//  * @Date : 2020-08-12 23:29:09
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:58:43
//  * @LastEditors : judu233
//  */
// export default class SkillBase {
//     /**技能数据 */
//     data: ISkillDataType;
//     /**使用者 */
//     useCard: CardBase;
//     /**被使用者列表 */
//     targetMap: Map<string, CardBase> = new Map();
//     /**buffManager */
//     buffManager = new BuffManager
//     /**初始化技能数据 */
//     initSkillData() {
//         //根据data数据创建buff
//         let buffTypeList = this.data.buffData;
//         try {
//             let buffList: BuffBase[] = [];
//             //创建Buff
//             for (let buffType of buffTypeList) {
//                 let buffClassName: { new(): BuffBase; } = eval(buffType.type);
//                 let buff = new buffClassName();
//                 buffList.push(buff);
//             }
//             //添加到buff管理器中
//             this.buffManager.initBuffManager(buffList);
//         } catch (error) {
//             cc.error(`技能创建buff失败，信息:${error}`);
//         }
//     }
//     /**使用技能，子类负责实现*/
//     useSkill() { }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcY2FyZFxcY2FyZGdhbWVcXHNyY1xcYmFzZVxcU2tpbGxCYXNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHFDQUFxQztBQUNyQyxxQ0FBcUM7QUFDckMsMERBQTBEO0FBRTFELGFBQWE7QUFDYix3QkFBd0I7QUFDeEIsaUJBQWlCO0FBQ2pCLHVCQUF1QjtBQUN2QixpQkFBaUI7QUFDakIseUJBQXlCO0FBQ3pCLGVBQWU7QUFDZiwyQkFBMkI7QUFDM0IsZUFBZTtBQUNmLCtCQUErQjtBQUMvQixJQUFJO0FBRUosbUJBQW1CO0FBQ25CLG1CQUFtQjtBQUNuQiwwQ0FBMEM7QUFDMUMscUJBQXFCO0FBQ3JCLHlCQUF5QjtBQUN6QiwyQ0FBMkM7QUFDM0MsNENBQTRDO0FBQzVDLFFBQVE7QUFDUixtQkFBbUI7QUFDbkIsOERBQThEO0FBQzlELHlCQUF5QjtBQUN6QixxQ0FBcUM7QUFDckMscUJBQXFCO0FBQ3JCLDRCQUE0QjtBQUM1QixxQkFBcUI7QUFDckIseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQiwrQkFBK0I7QUFDL0IsMEJBQTBCO0FBQzFCLDZCQUE2QjtBQUM3Qix1QkFBdUI7QUFDdkIsaUNBQWlDO0FBQ2pDLHVCQUF1QjtBQUN2Qiw2QkFBNkI7QUFDN0IscUJBQXFCO0FBQ3JCLG9DQUFvQztBQUNwQyxRQUFRO0FBQ1IsSUFBSTtBQUVKLE1BQU07QUFDTix3QkFBd0I7QUFDeEIsa0NBQWtDO0FBQ2xDLGlDQUFpQztBQUNqQyx5Q0FBeUM7QUFDekMseUNBQXlDO0FBQ3pDLDRCQUE0QjtBQUM1QixNQUFNO0FBQ04sbUNBQW1DO0FBQ25DLGlCQUFpQjtBQUNqQiw0QkFBNEI7QUFDNUIsZ0JBQWdCO0FBQ2hCLHlCQUF5QjtBQUN6QixtQkFBbUI7QUFDbkIsb0RBQW9EO0FBQ3BELHdCQUF3QjtBQUN4QixvQ0FBb0M7QUFFcEMsb0JBQW9CO0FBQ3BCLHdCQUF3QjtBQUN4QiwyQkFBMkI7QUFDM0IsaURBQWlEO0FBQ2pELGdCQUFnQjtBQUNoQiw2Q0FBNkM7QUFDN0MsdUJBQXVCO0FBQ3ZCLG1EQUFtRDtBQUNuRCxpRkFBaUY7QUFDakYsa0RBQWtEO0FBQ2xELHVDQUF1QztBQUN2QyxnQkFBZ0I7QUFDaEIsNEJBQTRCO0FBQzVCLDBEQUEwRDtBQUMxRCw0QkFBNEI7QUFDNUIsa0RBQWtEO0FBQ2xELFlBQVk7QUFDWixRQUFRO0FBRVIsdUJBQXVCO0FBQ3ZCLHFCQUFxQjtBQUNyQixJQUFJIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy8gaW1wb3J0IEJ1ZmZCYXNlIGZyb20gJy4vQnVmZkJhc2UnO1xyXG4vLyBpbXBvcnQgQ2FyZEJhc2UgZnJvbSBcIi4vQ2FyZEJhc2VcIjtcclxuLy8gaW1wb3J0IEJ1ZmZNYW5hZ2VyIGZyb20gJy4uLy4uL0NvbnRyb2xsZXIvQnVmZk1hbmFnZXInO1xyXG5cclxuLy8gLyoq5Ya35Y2057G75Z6LICovXHJcbi8vIGV4cG9ydCBlbnVtIEVGcm96ZW4ge1xyXG4vLyAgICAgLyoq5Zue5ZCI57G75Z6LICovXHJcbi8vICAgICByb3VuZCA9IGByb3VuZGAsXHJcbi8vICAgICAvKirlhajlsYDnsbvlnosgKi9cclxuLy8gICAgIGdsb2JhbCA9IGBnbG9iYWxgLFxyXG4vLyAgICAgLyoq6K6h5pe2ICovXHJcbi8vICAgICB0aW1lT3V0ID0gYHRpbWVPdXRgLFxyXG4vLyAgICAgLyoq5qyh5pWwICovXHJcbi8vICAgICB0aW1lQ291bnQgPSBgdGltZUNvdW50YCxcclxuLy8gfVxyXG5cclxuLy8gZGVjbGFyZSBnbG9iYWwge1xyXG4vLyAgICAgLyoq5Ya35Y205pe26Ze05o6l5Y+jICovXHJcbi8vICAgICBleHBvcnQgaW50ZXJmYWNlIElTa2lsbEZyb3plblR5cGUge1xyXG4vLyAgICAgICAgIC8qKuWGt+WNtOexu+WeiyAqL1xyXG4vLyAgICAgICAgIHR5cGU6IEVGcm96ZW47XHJcbi8vICAgICAgICAgLyoq5qyh5pWw77yM6K6h5pe277yM5Zue5ZCI6YO95pivbnVtYmVyIOWFqOWxgOWIhuS4uuiuoeaXtuWSjOasoeaVsCAqL1xyXG4vLyAgICAgICAgIHZhbHVlOiBudW1iZXIgfCBJU2tpbGxGcm96ZW5UeXBlO1xyXG4vLyAgICAgfVxyXG4vLyAgICAgLyoq5oqA6IO95pWw5o2u5o6l5Y+jICovXHJcbi8vICAgICBleHBvcnQgaW50ZXJmYWNlIElTa2lsbERhdGFUeXBlIGV4dGVuZHMgSUJhc2VEYXRhVHlwZSB7XHJcbi8vICAgICAgICAgLyoqYnVmZiAg5YiX6KGoICovXHJcbi8vICAgICAgICAgYnVmZkRhdGE6IElCdWZmRGF0YVR5cGVbXTtcclxuLy8gICAgICAgICAvKirkvb/nlKjmrKHmlbAgKi9cclxuLy8gICAgICAgICB1c2VDb3VudDogbnVtYmVyO1xyXG4vLyAgICAgICAgIC8qKuaKgOiDveetiee6pyAqL1xyXG4vLyAgICAgICAgIGxldmVsOiBudW1iZXI7XHJcbi8vICAgICAgICAgLyoq562J57qn5LiK6ZmQICovXHJcbi8vICAgICAgICAgbGV2ZWxIZWlnaHQ6IG51bWJlcjtcclxuLy8gICAgICAgICAvKirmmK/lkKblj6/ku6Xkvb/nlKjmraTmioDog70gKi9cclxuLy8gICAgICAgICBpc0NhblVzZTogYm9vbGVhbjtcclxuLy8gICAgICAgICAvKirmmK/lkKblj6/ku6XljYfnuqcgKi9cclxuLy8gICAgICAgICBpc0NhbkxldmVsVXA6IGJvb2xlYW47XHJcbi8vICAgICAgICAgLyoq5piv5ZCm5q2j5Zyo5Ya35Y20ICovXHJcbi8vICAgICAgICAgaXNGcm96ZW46IGJvb2xlYW47XHJcbi8vICAgICAgICAgLyoq5Ya35Y205L+h5oGvICovXHJcbi8vICAgICAgICAgZnJvemVuOiBJU2tpbGxGcm96ZW5UeXBlO1xyXG4vLyAgICAgfVxyXG4vLyB9XHJcblxyXG4vLyAvKipcclxuLy8gICogQGZlYXR1cmVzIDog5oqA6IO95o6n5Yi25Z+657G7XHJcbi8vICAqIEBkZXNjcmlwdGlvbjog5a+55LqO5LiA5Liq5Y2h54mM5oiW6KeS6Imy55qE5Z+657G75o6n5Yi2XHJcbi8vICAqIEBEYXRlIDogMjAyMC0wOC0xMiAyMzoyOTowOVxyXG4vLyAgKiBAQXV0aG9yIDoganVkdTIzMyg3Njk0NzE0MjRAcXEuY29tKVxyXG4vLyAgKiBATGFzdEVkaXRUaW1lIDogMjAyMy0xMS0xNCAxNDo1ODo0M1xyXG4vLyAgKiBATGFzdEVkaXRvcnMgOiBqdWR1MjMzXHJcbi8vICAqL1xyXG4vLyBleHBvcnQgZGVmYXVsdCBjbGFzcyBTa2lsbEJhc2Uge1xyXG4vLyAgICAgLyoq5oqA6IO95pWw5o2uICovXHJcbi8vICAgICBkYXRhOiBJU2tpbGxEYXRhVHlwZTtcclxuLy8gICAgIC8qKuS9v+eUqOiAhSAqL1xyXG4vLyAgICAgdXNlQ2FyZDogQ2FyZEJhc2U7XHJcbi8vICAgICAvKirooqvkvb/nlKjogIXliJfooaggKi9cclxuLy8gICAgIHRhcmdldE1hcDogTWFwPHN0cmluZywgQ2FyZEJhc2U+ID0gbmV3IE1hcCgpO1xyXG4vLyAgICAgLyoqYnVmZk1hbmFnZXIgKi9cclxuLy8gICAgIGJ1ZmZNYW5hZ2VyID0gbmV3IEJ1ZmZNYW5hZ2VyXHJcblxyXG4vLyAgICAgLyoq5Yid5aeL5YyW5oqA6IO95pWw5o2uICovXHJcbi8vICAgICBpbml0U2tpbGxEYXRhKCkge1xyXG4vLyAgICAgICAgIC8v5qC55o2uZGF0YeaVsOaNruWIm+W7umJ1ZmZcclxuLy8gICAgICAgICBsZXQgYnVmZlR5cGVMaXN0ID0gdGhpcy5kYXRhLmJ1ZmZEYXRhO1xyXG4vLyAgICAgICAgIHRyeSB7XHJcbi8vICAgICAgICAgICAgIGxldCBidWZmTGlzdDogQnVmZkJhc2VbXSA9IFtdO1xyXG4vLyAgICAgICAgICAgICAvL+WIm+W7ukJ1ZmZcclxuLy8gICAgICAgICAgICAgZm9yIChsZXQgYnVmZlR5cGUgb2YgYnVmZlR5cGVMaXN0KSB7XHJcbi8vICAgICAgICAgICAgICAgICBsZXQgYnVmZkNsYXNzTmFtZTogeyBuZXcoKTogQnVmZkJhc2U7IH0gPSBldmFsKGJ1ZmZUeXBlLnR5cGUpO1xyXG4vLyAgICAgICAgICAgICAgICAgbGV0IGJ1ZmYgPSBuZXcgYnVmZkNsYXNzTmFtZSgpO1xyXG4vLyAgICAgICAgICAgICAgICAgYnVmZkxpc3QucHVzaChidWZmKTtcclxuLy8gICAgICAgICAgICAgfVxyXG4vLyAgICAgICAgICAgICAvL+a3u+WKoOWIsGJ1ZmbnrqHnkIblmajkuK1cclxuLy8gICAgICAgICAgICAgdGhpcy5idWZmTWFuYWdlci5pbml0QnVmZk1hbmFnZXIoYnVmZkxpc3QpO1xyXG4vLyAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbi8vICAgICAgICAgICAgIGNjLmVycm9yKGDmioDog73liJvlu7pidWZm5aSx6LSl77yM5L+h5oGvOiR7ZXJyb3J9YCk7XHJcbi8vICAgICAgICAgfVxyXG4vLyAgICAgfVxyXG5cclxuLy8gICAgIC8qKuS9v+eUqOaKgOiDve+8jOWtkOexu+i0n+i0o+WunueOsCovXHJcbi8vICAgICB1c2VTa2lsbCgpIHsgfVxyXG4vLyB9XHJcbiJdfQ==