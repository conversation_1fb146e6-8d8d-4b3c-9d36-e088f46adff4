{"version": 3, "sources": ["assets\\fight\\types\\ISkill.ts"], "names": [], "mappings": ";;;;;AAAA;;;GAGG;;;AAkHH,aAAa;AACb,IAAY,SASX;AATD,WAAY,SAAS;IACjB,WAAW;IACX,8BAAiB,CAAA;IACjB,WAAW;IACX,gCAAmB,CAAA;IACnB,WAAW;IACX,0CAA6B,CAAA;IAC7B,WAAW;IACX,kCAAqB,CAAA;AACzB,CAAC,EATW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QASpB;AAED,eAAe;AACf,IAAY,eAmBX;AAnBD,WAAY,eAAe;IACvB,UAAU;IACV,gCAAa,CAAA;IACb,WAAW;IACX,gDAA6B,CAAA;IAC7B,WAAW;IACX,wDAAqC,CAAA;IACrC,WAAW;IACX,8CAA2B,CAAA;IAC3B,WAAW;IACX,sDAAmC,CAAA;IACnC,SAAS;IACT,gCAAa,CAAA;IACb,WAAW;IACX,oCAAiB,CAAA;IACjB,WAAW;IACX,8CAA2B,CAAA;IAC3B,WAAW;IACX,4CAAyB,CAAA;AAC7B,CAAC,EAnBW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAmB1B;AAED,eAAe;AACf,IAAY,eAiBX;AAjBD,WAAY,eAAe;IACvB,SAAS;IACT,oCAAiB,CAAA;IACjB,SAAS;IACT,gCAAa,CAAA;IACb,aAAa;IACb,wCAAqB,CAAA;IACrB,aAAa;IACb,8CAA2B,CAAA;IAC3B,SAAS;IACT,oCAAiB,CAAA;IACjB,SAAS;IACT,wCAAqB,CAAA;IACrB,SAAS;IACT,0CAAuB,CAAA;IACvB,SAAS;IACT,gCAAa,CAAA;AACjB,CAAC,EAjBW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAiB1B", "file": "", "sourceRoot": "/", "sourcesContent": ["/**\n * 技能系统接口定义\n * 基于Timeline的技能系统，保留原有的复杂效果支持\n */\n\nimport { ICharacter } from \"./ICharacter\";\nimport { IBuff } from \"./IBuff\";\nimport { ITimeline } from \"./ITimeline\";\n\n/*** 技能接口*/\nexport interface ISkill {\n    /** 技能ID */\n    readonly id: string;\n    /** 技能名称 */\n    readonly name: string;\n    /** 技能描述 */\n    readonly description: string;\n    /** 冷却时间（秒） */\n    readonly cooldown: number;\n    /** 当前冷却剩余时间 */\n    remainingCooldown: number;\n    /** 魔法消耗 */\n    readonly mpCost: number;\n    /** 耐力消耗 */\n    readonly staminaCost: number;\n    /** 技能等级 */\n    readonly level: number;\n    /** 技能类型 */\n    readonly type: SkillType;\n    /** 目标类型 */\n    readonly targetType: SkillTargetType;\n    /** 技能范围 */\n    readonly range: number;\n    /** 技能效果Timeline */\n    readonly timeline: ITimeline;\n    /** 学习技能时获得的被动Buff */\n    readonly passiveBuffs: IBuff[];\n    /** 是否可以使用 */\n    readonly canUse: boolean;\n    /**\n     * 检查是否可以对目标使用技能\n     * @param caster 施法者\n     * @param target 目标\n     * @returns 是否可以使用\n     */\n    canCastOn(caster: ICharacter, target?: ICharacter): boolean;\n    /**\n     * 释放技能\n     * @param caster 施法者\n     * @param target 目标\n     * @param targets 多个目标\n     * @param position 目标位置\n     * @returns 是否释放成功\n     */\n    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean;\n    /**\n     * 更新技能状态（主要是冷却时间）\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n    /** * 重置冷却时间 */\n    resetCooldown(): void;\n    /**\n     * 技能被添加时的回调（用于多目标技能等特殊情况）\n     * @param targets 目标列表\n     */\n    onAdd?(targets: ICharacter[]): void;\n}\n\n/** * 技能效果接口 */\nexport interface ISkillEffect {\n    /** 效果ID */\n    readonly id: string;\n    /** 效果类型 */\n    readonly type: SkillEffectType;\n    /**\n     * 执行效果\n     * @param caster 施法者\n     * @param target 目标\n     * @param skill 技能\n     * @param context 上下文信息\n     */\n    execute(caster: ICharacter, target: ICharacter, skill: ISkill, context?: any): void;\n}\n\n/*** 技能配置接口*/\nexport interface ISkillConfig {\n    /** 技能ID */\n    id: string;\n    /** 技能名称 */\n    name: string;\n    /** 技能描述 */\n    description: string;\n    /** 冷却时间 */\n    cooldown: number;\n    /** 魔法消耗 */\n    mpCost: number;\n    /** 耐力消耗 */\n    staminaCost: number;\n    /** 技能类型 */\n    type: SkillType;\n    /** 目标类型 */\n    targetType: SkillTargetType;\n    /** 技能范围 */\n    range: number;\n    /** 技能效果列表 */\n    effects: ISkillEffect[];\n    /** 学习技能时获得的Buff */\n    passiveBuffs?: IBuff[];\n    /** 动画名称 */\n    animationName?: string;\n    /** 音效ID */\n    soundId?: string;\n    /** 特效预制体 */\n    effectPrefab?: string;\n}\n\n/*** 技能类型枚举*/\nexport enum SkillType {\n    /** 主动技能 */\n    ACTIVE = \"active\",\n    /** 被动技能 */\n    PASSIVE = \"passive\",\n    /** 普通攻击 */\n    BASIC_ATTACK = \"basic_attack\",\n    /** 终极技能 */\n    ULTIMATE = \"ultimate\"\n}\n\n/*** 技能目标类型枚举*/\nexport enum SkillTargetType {\n    /** 无目标 */\n    NONE = \"none\",\n    /** 单个敌人 */\n    SINGLE_ENEMY = \"single_enemy\",\n    /** 多个敌人 */\n    MULTIPLE_ENEMIES = \"multiple_enemies\",\n    /** 单个友军 */\n    SINGLE_ALLY = \"single_ally\",\n    /** 多个友军 */\n    MULTIPLE_ALLIES = \"multiple_allies\",\n    /** 自己 */\n    SELF = \"self\",\n    /** 地面位置 */\n    GROUND = \"ground\",\n    /** 所有敌人 */\n    ALL_ENEMIES = \"all_enemies\",\n    /** 所有友军 */\n    ALL_ALLIES = \"all_allies\"\n}\n\n/*** 技能效果类型枚举*/\nexport enum SkillEffectType {\n    /** 伤害 */\n    DAMAGE = \"damage\",\n    /** 治疗 */\n    HEAL = \"heal\",\n    /** 添加Buff */\n    ADD_BUFF = \"add_buff\",\n    /** 移除Buff */\n    REMOVE_BUFF = \"remove_buff\",\n    /** 召唤 */\n    SUMMON = \"summon\",\n    /** 传送 */\n    TELEPORT = \"teleport\",\n    /** 击退 */\n    KNOCKBACK = \"knockback\",\n    /** 眩晕 */\n    STUN = \"stun\"\n}\n\n/*** 技能学习条件接口*/\nexport interface ISkillRequirement {\n    /** 需要的等级 */\n    requiredLevel?: number;\n    /** 前置技能 */\n    prerequisiteSkills?: string[];\n    /** 需要的属性 */\n    requiredAttributes?: { [key: string]: number };\n    /**\n     * 检查是否满足学习条件\n     * @param character 角色\n     * @returns 是否满足条件\n     */\n    checkRequirement(character: ICharacter): boolean;\n}\n\n/** * 攻击动作属性接口 */\nexport interface IAttackActionProps {\n    /** 前摇时间，攻击开始到造成伤害的时间（毫秒） */\n    hurtStartTimeMs: number;\n    /** 攻击持续时间，从开始到结束的总时间（毫秒） */\n    hurtEndTimeMs: number;\n    /** 开始造成伤害时的回调 */\n    onHurtStart?: () => void;\n    /** 攻击结束时的回调 */\n    onHurtEnd?: () => void;\n    /** 攻击过程中的回调（可选） */\n    onAttackProgress?: (progress: number) => void;\n}"]}