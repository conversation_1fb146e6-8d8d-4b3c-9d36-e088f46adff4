
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/EventManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c60bfrcG7dJg4vBdP2xJv5B', 'EventManager');
// card/cardgame/src/EventManager.ts

// import { Broadcast } from "@judu233/cc-broadcast";
// /**消息名字 */
// interface ITestKey extends broadcast.IMsgKey {
//     /**流程部分 */
//     /**战斗开始 */
//     FightStart: `FightStart`,
//     /**战斗结束 */
//     FightOver: `FightOver`,
//     /**回合开始 */
//     RoundStart: `RoundStart`,
//     /**回合结束 */
//     RoundOver: `RoundOver`,
//     /**使用卡牌 */
//     UseCard: `UseCard`,
//     /**丢弃卡牌 */
//     DiscardCard: `DiscardCard`,
//     /**显示使用卡片 */
//     ShowUseCard: `ShowUseCard`,
//     /**显示丢弃卡片 */
//     ShowDiscardCard: `ShowDiscardCard`,
//     /**取消显示所有 */
//     CancelShowAll: `CancelShowAll`,
//     /**污染卡片 */
//     PolluteCard: `PolluteCard`,
//     /**战斗控制部分 */
//     /**触发玩家攻击角色 */
//     PlayerAttack: `PlayerAttack`,
//     /**触发玩家技能 */
//     PlayerSkill: `PlayerSkill`,
//     /**属性监听 */
//     /**刷新UI界面 */
//     RefreshUI: `RefreshUI`,
//     /**触发玩家卡牌血量增减 */
//     PlayerHpChange: `PlayerHpChange`,
//     /**玩家卡牌死亡 */
//     PlayerCardDeath: `PlayerCardDeath`,
//     /**玩家卡牌复活 */
//     PlayerCardResurrection: `PlayerCardResurrection`,
//     /**触发电脑敌人卡牌血量增减 */
//     ComputerHpChange: `ComputerHpChange`,
//     /**敌方卡牌死亡 */
//     ComputerCardDeath: `ComputerCardDeath`,
//     /**敌方卡牌复活 */
//     ComputerCardResurrection: `ComputerCardResurrection`,
//     /**技能·buff */
// }
// /**消息广播传参值*/
// interface ITestValueType extends broadcast.IMsgValueType {
//     /**流程部分 */
//     /**战斗开始 */
//     FightStart: any,
//     /**战斗结束 */
//     FightOver: any,
//     /**回合开始 */
//     RoundStart: any,
//     /**回合结束 */
//     RoundOver: any,
//     /**战斗控制部分 */
//     /**触发玩家战斗 */
//     PlayerAttack: any,
//     /**触发玩家技能 */
//     PlayerSkill: any,
//     /**属性监听 */
//     /**触发玩家卡牌血量增减 */
//     PlayerHpChange: any,
//     /**玩家卡牌死亡 */
//     PlayerCardDeath: any,
//     /**玩家卡牌复活 */
//     PlayerCardResurrection: any,
//     /**触发电脑敌人卡牌血量增减 */
//     ComputerHpChange: any,
//     /**敌方卡牌死亡 */
//     ComputerCardDeath: any,
//     /**敌方卡牌复活 */
//     ComputerCardResurrection: any,
//     /**技能·buff */
// }
// /**消息回传传参值 */
// interface ITestResultType extends broadcast.IMsgValueType {
// }
// /**事件管理 */
// export const BRO = new Broadcast<ITestKey, ITestValueType, ITestResultType>();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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