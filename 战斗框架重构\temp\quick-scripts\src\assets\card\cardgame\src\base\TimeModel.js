"use strict";
cc._RF.push(module, '1ff16ajd85Dg56Rc5p3oHun', 'TimeModel');
// card/cardgame/src/base/TimeModel.ts

// import { desLoadDataProxy } from "@judu233/cc-vm-core";
// @desLoadDataProxy('TimeModel', 'TimeModel')
// export class TimeModel {
//     /**是否是第一次进入游戏 */
//     static isFirstInGame = true;
//     /*第一次进入的游戏时间 */
//     static firstInDate: Date = null;
//     /**是否是当天第一次进入游戏 */
//     static isTodayFirstInGame = true;
//     /**当天第一次进入的游戏时间 */
//     static todayFirstInDate: Date = null;
// }

cc._RF.pop();