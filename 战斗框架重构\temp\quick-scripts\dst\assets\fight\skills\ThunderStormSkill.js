
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/skills/ThunderStormSkill.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'adb5eBTg+hEP5u0BLqw2tMS', 'ThunderStormSkill');
// fight/skills/ThunderStormSkill.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThunderStormSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var ITimeline_1 = require("../types/ITimeline");
var SkillName_1 = require("../types/SkillName");
var StunBuff_1 = require("../buff/StunBuff");
var BattleManager_1 = require("../systems/BattleManager");
/**
 * 雷暴术技能
 * 范围攻击技能，在指定区域召唤雷电攻击所有敌人
 */
var ThunderStormSkill = /** @class */ (function () {
    function ThunderStormSkill() {
        this._id = SkillName_1.default.thunder_storm;
        this._name = "雷暴术";
        this._description = "在目标区域召唤雷暴，对范围内所有敌人造成雷电伤害并有概率眩晕";
        this._cooldown = 8.0;
        this._remainingCooldown = 0;
        this._mpCost = 60;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.GROUND;
        this._range = 400;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_thunder_storm",
            soundId: "thunder_cast",
            effectPath: "prefabs/effects/ThunderStorm",
            areaRadius: 150,
            damage: 0,
            damageMultiplier: 2.0,
            stunChance: 0.3,
            stunDuration: 2.0,
            lightningCount: 5,
            lightningInterval: 0.3 // 闪电间隔
        };
    }
    Object.defineProperty(ThunderStormSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    ThunderStormSkill.prototype.canCastOn = function (_caster, _target) {
        // 地面目标技能，不需要特定目标
        return true;
    };
    /** 检查资源消耗 */
    ThunderStormSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    ThunderStormSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    ThunderStormSkill.prototype.cast = function (caster, target, targets, position) {
        if (!position) {
            console.warn("ThunderStormSkill requires a target position");
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " at position (" + position.x + ", " + position.y + ")");
        return true;
    };
    /** 创建技能Timeline */
    ThunderStormSkill.prototype.createTimeline = function (caster, _target, _targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 3.0, caster);
        // 0.0s: 播放施法动画
        var castNode = new Timeline_1.TimelineNode(timelineId + "_cast", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_animation", this._config.animationName), false);
        timeline.addNode(castNode);
        // 0.5s: 播放施法音效
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.5, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        // 1.0s: 开始雷暴效果
        var stormStartNode = new Timeline_1.TimelineNode(timelineId + "_storm_start", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("storm_effect", this._config.effectPath, true, position), false);
        timeline.addNode(stormStartNode);
        // 1.2s - 2.8s: 连续闪电攻击
        for (var i = 0; i < this._config.lightningCount; i++) {
            var lightningTime = 1.2 + i * this._config.lightningInterval;
            var lightningNode = new Timeline_1.TimelineNode(timelineId + "_lightning_" + i, lightningTime, new LightningStrikeEvent("lightning_" + i, caster, position, this._config.areaRadius, this.calculateDamage(caster)), false);
            timeline.addNode(lightningNode);
        }
        return timeline;
    };
    /** 计算伤害 */
    ThunderStormSkill.prototype.calculateDamage = function (caster) {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        var baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.damageMultiplier);
    };
    /** 更新技能冷却 */
    ThunderStormSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    ThunderStormSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    ThunderStormSkill.prototype.levelUp = function () {
        this._level++;
        this._mpCost = Math.max(40, this._mpCost - 3);
        this._cooldown = Math.max(5.0, this._cooldown - 0.5);
        this._range += 30;
        this._config.areaRadius += 10;
        this._config.damageMultiplier += 0.2;
        this._config.stunChance = Math.min(0.6, this._config.stunChance + 0.05);
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    ThunderStormSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            areaRadius: this._config.areaRadius,
            damageMultiplier: this._config.damageMultiplier,
            stunChance: this._config.stunChance
        };
    };
    return ThunderStormSkill;
}());
exports.ThunderStormSkill = ThunderStormSkill;
/**
 * 闪电攻击Timeline事件
 */
var LightningStrikeEvent = /** @class */ (function () {
    function LightningStrikeEvent(id, caster, position, radius, damage) {
        this._type = ITimeline_1.TimelineEventType.DAMAGE;
        this._id = id;
        this._caster = caster;
        this._position = position;
        this._radius = radius;
        this._damage = damage;
    }
    Object.defineProperty(LightningStrikeEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LightningStrikeEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    LightningStrikeEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        var e_1, _a;
        // 查找范围内的所有敌人
        var enemies = this.findEnemiesInRange();
        try {
            for (var enemies_1 = __values(enemies), enemies_1_1 = enemies_1.next(); !enemies_1_1.done; enemies_1_1 = enemies_1.next()) {
                var enemy = enemies_1_1.value;
                if (!enemy.isDead) {
                    // 造成雷电伤害
                    var damageInfo = {
                        amount: this._damage,
                        type: IDamage_1.DamageType.MAGIC,
                        source: this._caster,
                        isCritical: false,
                        element: "thunder"
                    };
                    enemy.takeDamage(damageInfo.amount, this._caster);
                    console.log("Lightning strikes " + enemy.characterName + " for " + this._damage + " thunder damage");
                    // 眩晕概率检查
                    if (Math.random() < 0.3) { // 30%概率眩晕
                        console.log(enemy.characterName + " is stunned by lightning!");
                        var stunBuff = new StunBuff_1.StunBuff(this._caster, enemy, 2.0);
                        enemy.buffManager.addBuff(stunBuff);
                    }
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (enemies_1_1 && !enemies_1_1.done && (_a = enemies_1.return)) _a.call(enemies_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放闪电特效和音效
        this.playEffect("prefabs/effects/LightningStrike", this._position);
        this.playSound("lightning_strike");
    };
    /** 查找范围内的敌人 */
    LightningStrikeEvent.prototype.findEnemiesInRange = function () {
        // 这里需要实现范围查找逻辑
        // 暂时返回空数组，实际应该通过BattleManager或场景管理器查找
        console.log("Searching for enemies in radius " + this._radius + " around position (" + this._position.x + ", " + this._position.y + ")");
        return [];
    };
    LightningStrikeEvent.prototype.playEffect = function (effectId, position) {
        console.log("Playing effect " + effectId + " at position (" + (position === null || position === void 0 ? void 0 : position.x) + ", " + (position === null || position === void 0 ? void 0 : position.y) + ")");
    };
    LightningStrikeEvent.prototype.playSound = function (soundId) {
        console.log("Playing sound " + soundId);
    };
    return LightningStrikeEvent;
}());

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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