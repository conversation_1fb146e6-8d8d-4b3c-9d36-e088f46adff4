import { EventManager } from "../systems/EventManager";
import FightEvent from "../types/FightEvent";
import { IBuff, BuffType } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { IAttributeModifier, AttributeModifierType } from "../types/ICharacterAttributes";
import { IDamageInfo } from "../types/IDamage";
import { AttributeModifier } from "../characters/AttributeModifier";
import { EBuffEffectType } from "../types/Buff";

/**
 * 攻击力提升Buff
 * 提升角色的攻击力和魔法攻击力
 */
export class AttackBoostBuff implements IBuff {
    private _id: string;
    private _name: string = "攻击强化";
    private _description: string = "提升攻击力和魔法攻击力";
    private _type: BuffType = BuffType.BUFF;
    private _duration: number;
    private _remainingTime: number;
    private _stackCount: number = 1;
    private _maxStack: number = 5;
    private _caster: ICharacter;
    private _target: ICharacter;
    private _isExpired: boolean = false;
    private _attributeModifiers: AttributeModifier[] = [];
    private _eventManager: EventManager;

    // 攻击力提升配置
    private _attackMultiplier: number;
    private _magicAttackMultiplier: number;

    // 视觉效果
    private _iconPath: string = "icons/buffs/attack_boost";
    private _effectPrefabPath: string = "prefabs/effects/AttackAura";

    constructor(caster: ICharacter, target: ICharacter, duration: number, attackMultiplier: number, magicAttackMultiplier?: number) {
        this._id = `attack_boost_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._attackMultiplier = attackMultiplier;
        this._magicAttackMultiplier = magicAttackMultiplier || attackMultiplier;
        this._eventManager = EventManager.createLocal(`buff_${this._id}`);
        this._description = `攻击力提升${Math.round((attackMultiplier - 1) * 100)}%，持续${duration}秒`;
        this.createAttributeModifiers();
    }

    // 实现IBuff接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get type(): BuffType { return this._type; }
    get duration(): number { return this._duration; }
    get maxStack(): number { return this._maxStack; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter { return this._target; }
    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }
    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }
    get iconPath(): string { return this._iconPath; }
    get effectPrefabPath(): string { return this._effectPrefabPath; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }
    get stackCount(): number { return this._stackCount; }
    set stackCount(value: number) {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(0, Math.min(value, this._maxStack));
        if (oldStack !== this._stackCount) {
            this.updateAttributeModifiers();
        }
    }

    /** 创建属性修改器 */
    private createAttributeModifiers(): void {
        // 攻击力修改器
        const attackModifier = new AttributeModifier(
            `${this._id}_attack`,
            "攻击力提升",
            "attack",
            AttributeModifierType.PERCENTAGE,
            (this._attackMultiplier - 1) * this._stackCount,
            this._duration
        );
        // 魔法攻击力修改器
        const magicAttackModifier = new AttributeModifier(
            `${this._id}_magic_attack`,
            "魔法攻击力提升",
            "magicAttack",
            AttributeModifierType.PERCENTAGE,
            (this._magicAttackMultiplier - 1) * this._stackCount,
            this._duration
        );
        this._attributeModifiers = [attackModifier, magicAttackModifier];
    }

    /** 更新属性修改器 */
    private updateAttributeModifiers(): void {
        for (const modifier of this._attributeModifiers) {
            const attributeModifier = modifier
            if (attributeModifier.attributeName === "attack") {
                attributeModifier.setValue((this._attackMultiplier - 1) * this._stackCount);
            } else if (attributeModifier.attributeName === "magicAttack") {
                attributeModifier.setValue((this._magicAttackMultiplier - 1) * this._stackCount);
            }
        }
    }

    /** Buff被添加时触发 */
    onApply(): void {
        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);
        // 应用属性修改器到目标
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.addModifier(modifier);
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });
    }

    /** Buff每帧更新时触发 */
    onTick(deltaTime: number): void {
        // 更新属性修改器
        for (const modifier of this._attributeModifiers) {
            modifier.update(deltaTime);
        }
    }

    /** Buff被移除时触发 */
    onRemove(): void {
        console.log(`${this._name} removed from ${this._target.characterName}`);
        // 移除属性修改器
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.removeModifier(modifier.id);
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });
    }

    /** 造成伤害时触发 */
    onDealDamage?(damageInfo: IDamageInfo, _target: ICharacter): IDamageInfo {
        // 攻击力提升buff可以在这里添加额外的伤害加成
        const bonusDamagePercentage = 0.1 * this._stackCount; // 每层额外10%伤害
        const bonusDamage = Math.floor(damageInfo.baseDamage * bonusDamagePercentage);
        console.log(`${this._name} adds ${bonusDamage} bonus damage (${bonusDamagePercentage * 100}% of base damage, Stack: ${this._stackCount})`);
        // 正确的方式：通过damageAmplification来增加伤害
        // 这样伤害计算系统会重新计算finalDamage
        damageInfo.damageAmplification += bonusDamage;
        return damageInfo;
    }

    /** 更新Buff状态 */
    update(deltaTime: number): boolean {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    }

    /** 刷新Buff持续时间 */
    refresh(): void {
        this._remainingTime = this._duration;
        for (const modifier of this._attributeModifiers) {
            modifier.remainingTime = this._duration;
        }
        console.log(`${this._name} refreshed on ${this._target.characterName}`);
    }

    /** 增加叠加层数 */
    addStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 减少叠加层数 */
    removeStack(count: number = 1): void {
        const oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);
            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });
        }
    }

    /** 获取Buff的当前效果值 */
    getEffectValue(effectType: string): number {
        switch (effectType) {
            case EBuffEffectType.attack_multiplier:
                return this._attackMultiplier * this._stackCount;
            case EBuffEffectType.magic_attack_multiplier:
                return this._magicAttackMultiplier * this._stackCount;
            case EBuffEffectType.attack_bonus_percentage:
                return (this._attackMultiplier - 1) * this._stackCount * 100;
            default:
                return 0;
        }
    }

    /** 检查Buff是否与另一个Buff冲突 */
    conflictsWith(_otherBuff: IBuff): boolean {
        // 同类型的攻击力提升buff不冲突，可以叠加
        return false;
    }

    /** 播放应用特效 */
    private playApplyEffect(): void {
        console.log(`Playing apply effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效播放逻辑
    }

    /** 停止特效 */
    private stopEffect(): void {
        console.log(`Stopping effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效停止逻辑
    }

    /** 获取调试信息 */
    getDebugInfo() {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            attackMultiplier: this._attackMultiplier,
            magicAttackMultiplier: this._magicAttackMultiplier,
            currentAttackBonus: this.getEffectValue(EBuffEffectType.attack_bonus_percentage),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    }
}
