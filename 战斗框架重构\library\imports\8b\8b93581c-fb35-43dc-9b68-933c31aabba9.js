"use strict";
cc._RF.push(module, '8b935gc+zVD3Jtokzwxqrup', 'BuffManager');
// fight/systems/BuffManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffManager = void 0;
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var EventManager_1 = require("./EventManager");
/*** Buff管理器类*/
var BuffManager = /** @class */ (function () {
    function BuffManager(character) {
        var e_1, _a;
        this._buffs = new Map();
        this._buffsByType = new Map();
        this._character = character;
        this._eventManager = EventManager_1.EventManager.createLocal("BuffManager_" + character.characterName);
        try {
            // 初始化按类型分组的映射
            for (var _b = __values(Object.values(IBuff_1.BuffType)), _c = _b.next(); !_c.done; _c = _b.next()) {
                var type = _c.value;
                this._buffsByType.set(type, new Set());
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    }
    Object.defineProperty(BuffManager.prototype, "buffs", {
        /** * 获取所有Buff */
        get: function () {
            return Array.from(this._buffs.values());
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 添加Buff
     * @param buff Buff实例
     */
    BuffManager.prototype.addBuff = function (buff) {
        var _a;
        var existingBuff = this._buffs.get(buff.id);
        if (existingBuff) {
            // 如果Buff已存在，处理叠加逻辑
            this.handleBuffStack(existingBuff, buff);
        }
        else {
            // 添加新Buff
            this._buffs.set(buff.id, buff);
            (_a = this._buffsByType.get(buff.type)) === null || _a === void 0 ? void 0 : _a.add(buff.id);
            // 触发Buff应用效果
            buff.onApply();
            this._eventManager.emit(FightEvent_1.default.buffAdded, { buff: buff, character: this._character });
        }
    };
    /**
     * 移除Buff
     * @param buffId Buff ID
     * @returns 是否移除成功
     */
    BuffManager.prototype.removeBuff = function (buffId) {
        var _a;
        var buff = this._buffs.get(buffId);
        if (!buff) {
            return false;
        }
        // 触发Buff移除效果
        buff.onRemove();
        // 从映射中移除
        this._buffs.delete(buffId);
        (_a = this._buffsByType.get(buff.type)) === null || _a === void 0 ? void 0 : _a.delete(buffId);
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: buff, character: this._character });
        return true;
    };
    /**
     * 根据ID获取Buff
     * @param buffId Buff ID
     * @returns Buff实例或null
     */
    BuffManager.prototype.getBuff = function (buffId) {
        return this._buffs.get(buffId) || null;
    };
    /**
     * 根据类型获取Buff列表
     * @param type Buff类型
     * @returns Buff列表
     */
    BuffManager.prototype.getBuffsByType = function (type) {
        var e_2, _a;
        var buffIds = this._buffsByType.get(type);
        if (!buffIds) {
            return [];
        }
        var buffs = [];
        try {
            for (var buffIds_1 = __values(buffIds), buffIds_1_1 = buffIds_1.next(); !buffIds_1_1.done; buffIds_1_1 = buffIds_1.next()) {
                var buffId = buffIds_1_1.value;
                var buff = this._buffs.get(buffId);
                if (buff) {
                    buffs.push(buff);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (buffIds_1_1 && !buffIds_1_1.done && (_a = buffIds_1.return)) _a.call(buffIds_1);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return buffs;
    };
    /**
     * 检查是否拥有指定Buff
     * @param buffId Buff ID
     * @returns 是否拥有
     */
    BuffManager.prototype.hasBuff = function (buffId) {
        return this._buffs.has(buffId);
    };
    /**
     * 检查是否拥有指定类型的Buff
     * @param type Buff类型
     * @returns 是否拥有
     */
    BuffManager.prototype.hasBuffOfType = function (type) {
        var buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size > 0 : false;
    };
    /**
     * 获取指定类型的Buff数量
     * @param type Buff类型
     * @returns Buff数量
     */
    BuffManager.prototype.getBuffCountByType = function (type) {
        var buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size : 0;
    };
    /** * 清除所有Buff */
    BuffManager.prototype.clearAllBuffs = function () {
        var e_3, _a;
        var buffsToRemove = Array.from(this._buffs.keys());
        try {
            for (var buffsToRemove_1 = __values(buffsToRemove), buffsToRemove_1_1 = buffsToRemove_1.next(); !buffsToRemove_1_1.done; buffsToRemove_1_1 = buffsToRemove_1.next()) {
                var buffId = buffsToRemove_1_1.value;
                this.removeBuff(buffId);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (buffsToRemove_1_1 && !buffsToRemove_1_1.done && (_a = buffsToRemove_1.return)) _a.call(buffsToRemove_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /**
     * 清除指定类型的Buff
     * @param type Buff类型
     */
    BuffManager.prototype.clearBuffsByType = function (type) {
        var e_4, _a;
        var buffIds = this._buffsByType.get(type);
        if (buffIds) {
            var idsToRemove = Array.from(buffIds);
            try {
                for (var idsToRemove_1 = __values(idsToRemove), idsToRemove_1_1 = idsToRemove_1.next(); !idsToRemove_1_1.done; idsToRemove_1_1 = idsToRemove_1.next()) {
                    var buffId = idsToRemove_1_1.value;
                    this.removeBuff(buffId);
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (idsToRemove_1_1 && !idsToRemove_1_1.done && (_a = idsToRemove_1.return)) _a.call(idsToRemove_1);
                }
                finally { if (e_4) throw e_4.error; }
            }
        }
    };
    /**
     * 刷新Buff持续时间
     * @param buffId Buff ID
     */
    BuffManager.prototype.refreshBuff = function (buffId) {
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.refresh();
            this._eventManager.emit(FightEvent_1.default.buffRefreshed, { buff: buff, character: this._character });
        }
    };
    /**
     * 增加Buff叠加层数
     * @param buffId Buff ID
     * @param count 增加的层数
     */
    BuffManager.prototype.addBuffStack = function (buffId, count) {
        if (count === void 0) { count = 1; }
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.addStack(count);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: buff, character: this._character });
        }
    };
    /**
     * 减少Buff叠加层数
     * @param buffId Buff ID
     * @param count 减少的层数
     */
    BuffManager.prototype.removeBuffStack = function (buffId, count) {
        if (count === void 0) { count = 1; }
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.removeStack(count);
            // 如果叠加层数为0，移除Buff
            if (buff.stackCount <= 0) {
                this.removeBuff(buffId);
            }
            else {
                this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: buff, character: this._character });
            }
        }
    };
    /**
     * 更新所有Buff
     * @param deltaTime 时间间隔
     */
    BuffManager.prototype.update = function (deltaTime) {
        var e_5, _a, e_6, _b;
        var expiredBuffs = [];
        try {
            for (var _c = __values(this._buffs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), buffId = _e[0], buff = _e[1];
                // 更新Buff状态
                var isExpired = buff.update(deltaTime);
                if (isExpired) {
                    expiredBuffs.push(buffId);
                }
                else {
                    // 触发Buff每帧效果
                    buff.onTick(deltaTime);
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            // 移除过期的Buff
            for (var expiredBuffs_1 = __values(expiredBuffs), expiredBuffs_1_1 = expiredBuffs_1.next(); !expiredBuffs_1_1.done; expiredBuffs_1_1 = expiredBuffs_1.next()) {
                var buffId = expiredBuffs_1_1.value;
                this.removeBuff(buffId);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (expiredBuffs_1_1 && !expiredBuffs_1_1.done && (_b = expiredBuffs_1.return)) _b.call(expiredBuffs_1);
            }
            finally { if (e_6) throw e_6.error; }
        }
    };
    /**
     * 处理Buff叠加逻辑
     * @param existingBuff 已存在的Buff
     * @param newBuff 新的Buff
     */
    BuffManager.prototype.handleBuffStack = function (existingBuff, newBuff) {
        if (existingBuff.maxStack > 1) {
            // 可以叠加
            existingBuff.addStack(newBuff.stackCount);
            existingBuff.refresh(); // 刷新持续时间
            this._eventManager.emit(FightEvent_1.default.buffStacked, {
                buff: existingBuff,
                character: this._character,
                addedStacks: newBuff.stackCount
            });
        }
        else {
            // 不能叠加，刷新持续时间
            existingBuff.refresh();
            this._eventManager.emit(FightEvent_1.default.buffRefreshed, {
                buff: existingBuff,
                character: this._character
            });
        }
    };
    /**
     * 获取所有Buff的ID列表
     * @returns Buff ID数组
     */
    BuffManager.prototype.getAllBuffIds = function () {
        return Array.from(this._buffs.keys());
    };
    /**
     * 获取所有Buff列表
     * @returns Buff数组
     */
    BuffManager.prototype.getAllBuffs = function () {
        return Array.from(this._buffs.values());
    };
    /**
     * 获取Buff统计信息
     * @returns 统计信息
     */
    BuffManager.prototype.getBuffStats = function () {
        var e_7, _a;
        var stats = {
            totalBuffs: this._buffs.size,
            buffsByType: {},
            expiredBuffs: 0,
            stackableBuffs: 0
        };
        try {
            for (var _b = __values(this._buffs.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (!stats.buffsByType[buff.type]) {
                    stats.buffsByType[buff.type] = 0;
                }
                stats.buffsByType[buff.type]++;
                if (buff.isExpired) {
                    stats.expiredBuffs++;
                }
                if (buff.maxStack > 1) {
                    stats.stackableBuffs++;
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return stats;
    };
    /**
     * 导出Buff数据
     * @returns Buff数据
     */
    BuffManager.prototype.exportBuffData = function () {
        var e_8, _a;
        var data = {};
        try {
            for (var _b = __values(this._buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), id = _d[0], buff = _d[1];
                data[id] = {
                    id: buff.id,
                    name: buff.name,
                    type: buff.type,
                    duration: buff.duration,
                    remainingTime: buff.remainingTime,
                    stackCount: buff.stackCount,
                    maxStack: buff.maxStack,
                    isExpired: buff.isExpired
                };
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return data;
    };
    Object.defineProperty(BuffManager.prototype, "eventManager", {
        /**
         * 获取事件管理器（供外部直接使用，避免包装方法冗余）
         */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理Buff管理器 */
    BuffManager.prototype.cleanup = function () {
        this.clearAllBuffs();
        this._buffsByType.clear();
        this._eventManager.cleanup();
    };
    /** * 获取调试信息 */
    BuffManager.prototype.getDebugInfo = function () {
        return {
            characterId: this._character.id,
            buffCount: this._buffs.size,
            buffs: this.exportBuffData(),
            stats: this.getBuffStats()
        };
    };
    return BuffManager;
}());
exports.BuffManager = BuffManager;

cc._RF.pop();