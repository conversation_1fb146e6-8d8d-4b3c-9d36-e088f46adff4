{"version": 3, "sources": ["assets\\fight\\types\\FightEvent.ts"], "names": [], "mappings": ";;;;;;AAAA;IAAA;IAgHA,CAAC;IA/GG,YAAY;IACL,6BAAkB,GAAG,oBAAoB,CAAA;IAChD,kBAAkB;IACX,sBAAW,GAAG,aAAa,CAAA;IAClC,gBAAgB;IACT,sBAAW,GAAG,aAAa,CAAA;IAClC,gBAAgB;IACT,wBAAa,GAAG,eAAe,CAAA;IACtC,YAAY;IACL,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,UAAU;IACH,wBAAa,GAAG,eAAe,CAAA;IACtC,YAAY;IACL,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,UAAU;IACH,oBAAS,GAAG,WAAW,CAAA;IAC9B,YAAY;IACL,oBAAS,GAAG,WAAW,CAAA;IAC9B,UAAU;IACH,qBAAU,GAAG,YAAY,CAAA;IAChC,QAAQ;IACD,eAAI,GAAG,MAAM,CAAA;IACpB,QAAQ;IACD,gBAAK,GAAG,OAAO,CAAA;IACtB,cAAc;IACP,uBAAY,GAAG,cAAc,CAAA;IACpC,UAAU;IACH,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,WAAW;IACJ,oBAAS,GAAG,WAAW,CAAA;IAC9B,WAAW;IACJ,oBAAS,GAAG,WAAW,CAAA;IAC9B,UAAU;IACH,yBAAc,GAAG,gBAAgB,CAAA;IACxC,oBAAoB;IACb,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,cAAc;IACP,sBAAW,GAAG,aAAa,CAAA;IAClC,UAAU;IACH,oBAAS,GAAG,WAAW,CAAA;IAC9B,UAAU;IACH,0BAAe,GAAG,iBAAiB,CAAA;IAC1C,UAAU;IACH,sBAAW,GAAG,aAAa,CAAA;IAClC,UAAU;IACH,qBAAU,GAAG,YAAY,CAAA;IAChC,UAAU;IACH,uBAAY,GAAG,cAAc,CAAA;IACpC,YAAY;IACL,6BAAkB,GAAG,oBAAoB,CAAA;IAChD,gBAAgB;IACT,iCAAsB,GAAG,wBAAwB,CAAA;IACxD,UAAU;IACH,oBAAS,GAAG,WAAW,CAAA;IAC9B,gBAAgB;IACT,wBAAa,GAAG,eAAe,CAAA;IACtC,gBAAgB;IACT,0BAAe,GAAG,iBAAiB,CAAA;IAC1C,kBAAkB;IACX,6BAAkB,GAAG,oBAAoB,CAAA;IAChD,kBAAkB;IACX,8BAAmB,GAAG,qBAAqB,CAAA;IAClD,kBAAkB;IACX,8BAAmB,GAAG,qBAAqB,CAAA;IAClD,kBAAkB;IACX,yBAAc,GAAG,gBAAgB,CAAA;IACxC,kBAAkB;IACX,0BAAe,GAAG,iBAAiB,CAAA;IAC1C,oBAAoB;IACb,4BAAiB,GAAG,mBAAmB,CAAA;IAC9C,oBAAoB;IACb,0BAAe,GAAG,iBAAiB,CAAA;IAC1C,gBAAgB;IACT,qBAAU,GAAG,YAAY,CAAA;IAChC,gBAAgB;IACT,kBAAO,GAAG,SAAS,CAAA;IAC1B,gBAAgB;IACT,mBAAQ,GAAG,UAAU,CAAA;IAC5B,gBAAgB;IACT,mBAAQ,GAAG,UAAU,CAAA;IAC5B,gBAAgB;IACT,iBAAM,GAAG,QAAQ,CAAA;IACxB,uBAAuB;IAChB,kBAAO,GAAG,SAAS,CAAA;IAE1B,UAAU;IACV,UAAU;IACH,wBAAa,GAAG,eAAe,CAAA;IACtC,UAAU;IACH,sBAAW,GAAG,aAAa,CAAA;IAClC,UAAU;IACH,uBAAY,GAAG,cAAc,CAAA;IACpC,UAAU;IACH,wBAAa,GAAG,eAAe,CAAA;IACtC,WAAW;IACJ,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,WAAW;IACJ,6BAAkB,GAAG,oBAAoB,CAAA;IAChD,UAAU;IACH,wBAAa,GAAG,eAAe,CAAA;IACtC,WAAW;IACJ,2BAAgB,GAAG,kBAAkB,CAAA;IAC5C,YAAY;IACL,6BAAkB,GAAG,oBAAoB,CAAA;IAChD,UAAU;IACH,4BAAiB,GAAG,mBAAmB,CAAA;IAC9C,UAAU;IACH,4BAAiB,GAAG,mBAAmB,CAAA;IAC9C,WAAW;IACJ,0BAAe,GAAG,iBAAiB,CAAA;IAE9C,iBAAC;CAhHD,AAgHC,IAAA;kBAhHoB,UAAU", "file": "", "sourceRoot": "/", "sourcesContent": ["export default class FightEvent {\r\n    /**攻击状态改变 */\r\n    static attackStateChanged = 'attackStateChanged'\r\n    /**Buff被添加时触发事件 */\r\n    static buffApplied = 'buffApplied'\r\n    /**Buff被移除时触发 */\r\n    static buffRemoved = 'buffRemoved'\r\n    /**刷新Buff持续时间 */\r\n    static buffRefreshed = 'buffRefreshed'\r\n    /**增加叠加层数 */\r\n    static buffStackChanged = 'buffStackChanged'\r\n    /**执行反击 */\r\n    static counterAttack = 'counterAttack'\r\n    /**移动状态改变 */\r\n    static moveStateChanged = 'moveStateChanged'\r\n    /**释放技能 */\r\n    static skillCast = 'skillCast'\r\n    /**添加Buff */\r\n    static buffAdded = 'buffAdded'\r\n    /**受到伤害 */\r\n    static takeDamage = 'takeDamage'\r\n    /**治疗 */\r\n    static heal = 'heal'\r\n    /**死亡 */\r\n    static death = 'death'\r\n    /**设置buff状态 */\r\n    static stateChanged = 'stateChanged'\r\n    /**移除角色 */\r\n    static characterRemoved = 'characterRemoved'\r\n    /**修改生命值 */\r\n    static hpChanged = 'hpChanged'\r\n    /**修改魔法值 */\r\n    static mpChanged = 'mpChanged'\r\n    /**修改耐力 */\r\n    static staminaChanged = 'staminaChanged'\r\n    /**直接设置属性值（用于修改器） */\r\n    static attributeChanged = 'attributeChanged'\r\n    /**buff层级叠加 */\r\n    static buffStacked = 'buffStacked'\r\n    /**子弹击中 */\r\n    static bulletHit = 'bulletHit'\r\n    /**子弹销毁 */\r\n    static bulletDestroyed = 'bulletDestroyed'\r\n    /**子弹射击 */\r\n    static bulletFired = 'bulletFired'\r\n    /**添加技能 */\r\n    static skillAdded = 'skillAdded'\r\n    /**移除技能 */\r\n    static skillRemoved = 'skillRemoved'\r\n    /**重置技能冷却 */\r\n    static skillCooldownReset = 'skillCooldownReset'\r\n    /**重置技能冷却（多个） */\r\n    static allSkillsCooldownReset = 'allSkillsCooldownReset'\r\n    /**使用技能 */\r\n    static skillUsed = 'skillUsed'\r\n    /**添加Timeline */\r\n    static timelineAdded = 'timelineAdded'\r\n    /**移除Timeline */\r\n    static timelineRemoved = 'timelineRemoved'\r\n    /**暂停所有timeline */\r\n    static allTimelinesPaused = 'allTimelinesPaused'\r\n    /**恢复所有timeline */\r\n    static allTimelinesResumed = 'allTimelinesResumed'\r\n    /**清除所有Timeline */\r\n    static allTimelinesCleared = 'allTimelinesCleared'\r\n    /**暂停指定Timeline */\r\n    static timelinePaused = 'timelinePaused'\r\n    /**恢复指定Timeline */\r\n    static timelineResumed = 'timelineResumed'\r\n    /**Timeline完成时的回调 */\r\n    static timelineCompleted = 'timelineCompleted'\r\n    /**Timeline停止时的回调 */\r\n    static timelineStopped = 'timelineStopped'\r\n    /**更新Timeline */\r\n    static completedT = 'completedT'\r\n    /**暂停Timeline */\r\n    static pausedT = 'pausedT'\r\n    /**恢复Timeline */\r\n    static resumedT = 'resumedT'\r\n    /**停止Timeline */\r\n    static stoppedT = 'stoppedT'\r\n    /**重置Timeline */\r\n    static resetT = 'resetT'\r\n    /***跳转到指定时间点timeline */\r\n    static seekedT = 'seekedT'\r\n\r\n    // 战斗管理器事件\r\n    /**战斗开始 */\r\n    static battleStarted = 'battleStarted'\r\n    /**战斗结束 */\r\n    static battleEnded = 'battleEnded'\r\n    /**战斗暂停 */\r\n    static battlePaused = 'battlePaused'\r\n    /**战斗恢复 */\r\n    static battleResumed = 'battleResumed'\r\n    /**参战者加入 */\r\n    static participantAdded = 'participantAdded'\r\n    /**参战者离开 */\r\n    static participantRemoved = 'participantRemoved'\r\n    /**角色死亡 */\r\n    static characterDied = 'characterDied'\r\n    /**角色被眩晕 */\r\n    static characterStunned = 'characterStunned'\r\n    /**角色眩晕结束 */\r\n    static characterStunEnded = 'characterStunEnded'\r\n    /**角色中毒 */\r\n    static characterPoisoned = 'characterPoisoned'\r\n    /**毒素伤害 */\r\n    static poisonDamageDealt = 'poisonDamageDealt'\r\n    /**角色被治疗 */\r\n    static characterHealed = 'characterHealed'\r\n\r\n}"]}