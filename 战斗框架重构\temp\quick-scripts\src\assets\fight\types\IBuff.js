"use strict";
cc._RF.push(module, '8b303G5rv5OHpSF/JPdV3mo', 'IBuff');
// fight/types/IBuff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffPeriodicEffectType = exports.BuffModifierType = exports.BuffType = void 0;
/*** Buff类型枚举*/
var BuffType;
(function (BuffType) {
    /** 增益 */
    BuffType["BUFF"] = "buff";
    /** 减益 */
    BuffType["DEBUFF"] = "debuff";
    /** 中性 */
    BuffType["NEUTRAL"] = "neutral";
})(BuffType = exports.BuffType || (exports.BuffType = {}));
/*** Buff修改器类型枚举*/
var BuffModifierType;
(function (BuffModifierType) {
    /** 加法 */
    BuffModifierType["ADD"] = "add";
    /** 乘法 */
    BuffModifierType["MULTIPLY"] = "multiply";
    /** 百分比 */
    BuffModifierType["PERCENTAGE"] = "percentage";
})(BuffModifierType = exports.BuffModifierType || (exports.BuffModifierType = {}));
/*** Buff周期性效果类型枚举*/
var BuffPeriodicEffectType;
(function (BuffPeriodicEffectType) {
    /** 持续伤害 */
    BuffPeriodicEffectType["DAMAGE_OVER_TIME"] = "damage_over_time";
    /** 持续治疗 */
    BuffPeriodicEffectType["HEAL_OVER_TIME"] = "heal_over_time";
    /** 魔法恢复 */
    BuffPeriodicEffectType["MP_RECOVERY"] = "mp_recovery";
    /** 耐力恢复 */
    BuffPeriodicEffectType["STAMINA_RECOVERY"] = "stamina_recovery";
})(BuffPeriodicEffectType = exports.BuffPeriodicEffectType || (exports.BuffPeriodicEffectType = {}));

cc._RF.pop();