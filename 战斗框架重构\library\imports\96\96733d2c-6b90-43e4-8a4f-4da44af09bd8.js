"use strict";
cc._RF.push(module, '967330sa5BD5IpPTaRK8JvY', 'BuffModelBeHurtFight');
// fight/buff/BuffModelBeHurtFight.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffModelBeHurtFight = void 0;
var EventManager_1 = require("../systems/EventManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var IDamage_1 = require("../types/IDamage");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/*** 受伤反击Buff实现*/
var BuffModelBeHurtFight = /** @class */ (function () {
    function BuffModelBeHurtFight(caster, target) {
        this._id = "buff_be_hurt_fight";
        this._name = "受伤反击";
        this._description = "受到伤害时有概率反击攻击者";
        this._type = IBuff_1.BuffType.BUFF;
        /**持续时间，以s计算 */
        this._duration = 30.0;
        this._stackCount = 1;
        this._maxStack = 3;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._iconPath = "textures/buffs/be_hurt_fight";
        this._effectPrefabPath = "prefabs/effects/CounterAttackAura";
        /**Buff特有属性  - 默认 30%反击概率 */
        this._counterAttackChance = 0.3; //
        /**反击伤害倍率， 默认 1.5*/
        this._counterAttackDamageMultiplier = 1.5;
        this._caster = caster;
        this._target = target;
        this._remainingTime = this._duration;
        this._eventManager = new EventManager_1.EventManager();
        this.initializeAttributeModifiers();
    }
    Object.defineProperty(BuffModelBeHurtFight.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "periodicEffect", {
        get: function () { return this._periodicEffect; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); },
        enumerable: false,
        configurable: true
    });
    /*** 初始化属性修改器*/
    BuffModelBeHurtFight.prototype.initializeAttributeModifiers = function () {
        // 增加攻击力（每层增加10%）
        var attackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_attack_modifier", "反击攻击力加成", "attack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, 0.1 * this._stackCount, // 每层10%
        this._duration);
        // 增加暴击率（每层增加5%）
        var criticalRateModifier = new AttributeModifier_1.AttributeModifier(this._id + "_critical_rate_modifier", "反击暴击率加成", CharacterTypes_1.CharacterAttributeName.criticalRate, ICharacterAttributes_1.AttributeModifierType.ADD, 0.05 * this._stackCount, // 每层5%
        this._duration);
        this._attributeModifiers = [attackModifier, criticalRateModifier];
    };
    /*** Buff被添加时触发*/
    BuffModelBeHurtFight.prototype.onApply = function () {
        var e_1, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /*** Buff每帧更新时触发*/
    BuffModelBeHurtFight.prototype.onTick = function (deltaTime) {
        var e_2, _a;
        try {
            // 更新属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 这里可以添加其他每帧更新的逻辑
        // 比如粒子特效的更新等
    };
    /** * Buff被移除时触发 */
    BuffModelBeHurtFight.prototype.onRemove = function () {
        var e_3, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** * 受到伤害时触发（核心功能） */
    BuffModelBeHurtFight.prototype.onTakeDamage = function (damageInfo, attacker) {
        // 检查是否触发反击
        if (this.shouldCounterAttack(damageInfo, attacker)) {
            this.performCounterAttack(attacker);
        }
        // 返回原始伤害信息（不修改伤害）
        return damageInfo;
    };
    /** * 更新Buff状态 */
    BuffModelBeHurtFight.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        // 检查是否过期
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** * 刷新Buff持续时间 */
    BuffModelBeHurtFight.prototype.refresh = function () {
        this._remainingTime = this._duration;
        this._isExpired = false;
        console.log(this._name + " refreshed on " + this._target.characterName);
        this._eventManager.emit(FightEvent_1.default.buffRefreshed, { buff: this, target: this._target });
    };
    /** * 增加叠加层数 */
    BuffModelBeHurtFight.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._maxStack, this._stackCount + count);
        if (this._stackCount !== oldStack) {
            // 重新初始化属性修改器以反映新的层数
            this.updateAttributeModifiersForStack();
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target, oldStack: oldStack, newStack: this._stackCount });
        }
    };
    /** * 减少叠加层数 */
    BuffModelBeHurtFight.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(0, this._stackCount - count);
        if (this._stackCount !== oldStack) {
            if (this._stackCount === 0) {
                this._isExpired = true;
            }
            else {
                // 重新初始化属性修改器以反映新的层数
                this.updateAttributeModifiersForStack();
            }
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target, oldStack: oldStack, newStack: this._stackCount });
        }
    };
    /*** 获取Buff的当前效果值*/
    BuffModelBeHurtFight.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.counterAttackChance: return this._counterAttackChance;
            case Buff_1.EBuffEffectType.counterAttackDamageMultiplier: return this._counterAttackDamageMultiplier;
            case Buff_1.EBuffEffectType.attackBonus: return 0.1 * this._stackCount;
            case Buff_1.EBuffEffectType.criticalRateBonus: return 0.05 * this._stackCount;
            default: return 0;
        }
    };
    /*** 检查Buff是否与另一个Buff冲突*/
    BuffModelBeHurtFight.prototype.conflictsWith = function (otherBuff) {
        // 同类型的反击Buff冲突
        return otherBuff.id === this._id || otherBuff.id.includes("counter_attack");
    };
    /** * 检查是否应该反击 */
    BuffModelBeHurtFight.prototype.shouldCounterAttack = function (damageInfo, attacker) {
        // 检查攻击者是否有效
        if (!attacker || attacker.isDead || attacker === this._target) {
            return false;
        }
        // 检查伤害类型（只对直接伤害反击）
        if (damageInfo.damageType === IDamage_1.DamageType.TRUE) {
            return false; // 真实伤害不触发反击
        }
        // 概率检查
        var random = Math.random();
        var chance = this._counterAttackChance * this._stackCount; // 每层增加反击概率
        return random < chance;
    };
    /** * 执行反击 */
    BuffModelBeHurtFight.prototype.performCounterAttack = function (attacker) {
        console.log(this._target.characterName + " counter attacks " + attacker.characterName + "!");
        // 计算反击伤害
        var baseDamage = this._target.attributes.attack;
        var counterDamage = Math.floor(baseDamage * this._counterAttackDamageMultiplier);
        // 造成反击伤害
        attacker.takeDamage(counterDamage, this._target);
        this.playCounterAttackEffect();
        this._eventManager.emit(FightEvent_1.default.counterAttack, { buff: this, attacker: this._target, victim: attacker, damage: counterDamage });
    };
    /** * 更新属性修改器以反映新的层数 */
    BuffModelBeHurtFight.prototype.updateAttributeModifiersForStack = function () {
        var e_4, _a, e_5, _b;
        try {
            for (var _c = __values(this._attributeModifiers), _d = _c.next(); !_d.done; _d = _c.next()) {
                var modifier = _d.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        // 重新初始化修改器
        this._attributeModifiers.length = 0;
        this.initializeAttributeModifiers();
        try {
            for (var _e = __values(this._attributeModifiers), _f = _e.next(); !_f.done; _f = _e.next()) {
                var modifier = _f.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
    };
    /** * 播放应用特效 */
    BuffModelBeHurtFight.prototype.playApplyEffect = function () {
        // 这里应该调用特效管理器
        console.log("Playing apply effect for " + this._name);
    };
    /** * 停止特效 */
    BuffModelBeHurtFight.prototype.stopEffect = function () {
        // 这里应该停止特效
        console.log("Stopping effect for " + this._name);
    };
    /** * 播放反击特效 */
    BuffModelBeHurtFight.prototype.playCounterAttackEffect = function () {
        // 这里应该播放反击特效
        console.log("Playing counter attack effect for " + this._name);
    };
    /**  * 清理资源  */
    BuffModelBeHurtFight.prototype.cleanup = function () {
        this._eventManager.cleanup();
        this._attributeModifiers.length = 0;
    };
    return BuffModelBeHurtFight;
}());
exports.BuffModelBeHurtFight = BuffModelBeHurtFight;

cc._RF.pop();