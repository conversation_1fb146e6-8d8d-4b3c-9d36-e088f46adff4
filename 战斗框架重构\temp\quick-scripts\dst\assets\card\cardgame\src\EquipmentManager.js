
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/EquipmentManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '87ff2ah6EFJJaMUK4tTEHu0', 'EquipmentManager');
// card/cardgame/src/EquipmentManager.ts

// import Base from "./Base/Base";
// import Equipment from "./Base/Equipment";
// import { ECamp } from "./CampManager";
// import FightManager from "./FightManager";
// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }
// declare global {
// }
// /** 
//  * @features :  游戏装备管理
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class EquipmentManager extends Base {
//     /**技能数据 */
//     data: IBuffDataType;
//     /**所有装备 */
//     equipmentList: Equipment[] = []
//     addEquipment(name: string, data: { defence?: number, attack?: number }) {
//         const equipment = new Equipment()
//         equipment.durable = data.defence ?? data.attack ?? 3
//         equipment.defence = data.defence ?? 2
//         equipment.attack = data.attack ?? 2
//         equipment.orginValue = data.defence ?? data.attack ?? equipment.durable
//         equipment.equipmentName = name
//         equipment.camp = ECamp.Player
//         this.equipmentList.push(equipment);
//         FightManager.campManager.getPlayerCamp().getRole().attack += equipment.attack
//         FightManager.campManager.getPlayerCamp().getRole().defence += equipment.defence
//         return equipment
//     }
//     hasEquipment(name: string) {
//         return this.equipmentList.some(e => e.equipmentName === name);
//     }
//     getEquipment(name?: string) {
//         if (name == null) {
//             return this.equipmentList.arrayRand<Equipment>();
//         }
//         return this.equipmentList.find(e => e.equipmentName === name);
//     }
//     deletEquipment(name: string) {
//         const index = this.equipmentList.findIndex(e => e.equipmentName === name);
//         if (index >= 0) {
//             let eq = this.equipmentList.splice(index, 1)?.[0]
//             if (eq) {
//                 FightManager.campManager.getPlayerCamp().getRole().attack -= eq.attack
//                 FightManager.campManager.getPlayerCamp().getRole().defence -= eq.defence
//             }
//         }
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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