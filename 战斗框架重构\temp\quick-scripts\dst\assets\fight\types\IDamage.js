
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/IDamage.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd5aceRFOsBF96dPMgv7wSgL', 'IDamage');
// fight/types/IDamage.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageTag = exports.DamageType = void 0;
/*** 伤害类型枚举*/
var DamageType;
(function (DamageType) {
    /** 物理伤害 */
    DamageType["PHYSICAL"] = "physical";
    /** 魔法伤害 */
    DamageType["MAGIC"] = "magic";
    /** 真实伤害 */
    DamageType["TRUE"] = "true";
    /** 治疗 */
    DamageType["HEAL"] = "heal";
    /** 毒素伤害 */
    DamageType["POISON"] = "poison";
})(DamageType = exports.DamageType || (exports.DamageType = {}));
/** * 伤害标签枚举 */
var DamageTag;
(function (DamageTag) {
    /** 直接伤害 */
    DamageTag["DIRECT"] = "direct";
    /** 持续伤害 */
    DamageTag["OVER_TIME"] = "over_time";
    /** 反射伤害 */
    DamageTag["REFLECT"] = "reflect";
    /** 溅射伤害 */
    DamageTag["SPLASH"] = "splash";
    /** 穿透伤害 */
    DamageTag["PENETRATING"] = "penetrating";
    /** 暴击伤害 */
    DamageTag["CRITICAL"] = "critical";
    /** 技能伤害 */
    DamageTag["SKILL"] = "skill";
    /** 普攻伤害 */
    DamageTag["BASIC_ATTACK"] = "basic_attack";
})(DamageTag = exports.DamageTag || (exports.DamageTag = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXHR5cGVzXFxJRGFtYWdlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXNEQSxhQUFhO0FBQ2IsSUFBWSxVQVdYO0FBWEQsV0FBWSxVQUFVO0lBQ2xCLFdBQVc7SUFDWCxtQ0FBcUIsQ0FBQTtJQUNyQixXQUFXO0lBQ1gsNkJBQWUsQ0FBQTtJQUNmLFdBQVc7SUFDWCwyQkFBYSxDQUFBO0lBQ2IsU0FBUztJQUNULDJCQUFhLENBQUE7SUFDYixXQUFXO0lBQ1gsK0JBQWlCLENBQUE7QUFDckIsQ0FBQyxFQVhXLFVBQVUsR0FBVixrQkFBVSxLQUFWLGtCQUFVLFFBV3JCO0FBQ0QsZUFBZTtBQUNmLElBQVksU0FpQlg7QUFqQkQsV0FBWSxTQUFTO0lBQ2pCLFdBQVc7SUFDWCw4QkFBaUIsQ0FBQTtJQUNqQixXQUFXO0lBQ1gsb0NBQXVCLENBQUE7SUFDdkIsV0FBVztJQUNYLGdDQUFtQixDQUFBO0lBQ25CLFdBQVc7SUFDWCw4QkFBaUIsQ0FBQTtJQUNqQixXQUFXO0lBQ1gsd0NBQTJCLENBQUE7SUFDM0IsV0FBVztJQUNYLGtDQUFxQixDQUFBO0lBQ3JCLFdBQVc7SUFDWCw0QkFBZSxDQUFBO0lBQ2YsV0FBVztJQUNYLDBDQUE2QixDQUFBO0FBQ2pDLENBQUMsRUFqQlcsU0FBUyxHQUFULGlCQUFTLEtBQVQsaUJBQVMsUUFpQnBCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSUNoYXJhY3RlciB9IGZyb20gXCIuL0lDaGFyYWN0ZXJcIjtcbmltcG9ydCB7IElCdWZmIH0gZnJvbSBcIi4vSUJ1ZmZcIjtcbi8qKiog5Lyk5a6z5L+h5oGv5o6l5Y+jKi9cbmV4cG9ydCBpbnRlcmZhY2UgSURhbWFnZUluZm8ge1xuICAgIC8qKiDkvKTlrrNJRCAqL1xuICAgIHJlYWRvbmx5IGlkOiBzdHJpbmc7XG4gICAgLyoqIOaUu+WHu+iAhSAqL1xuICAgIHJlYWRvbmx5IGF0dGFja2VyOiBJQ2hhcmFjdGVyIHwgbnVsbDtcbiAgICAvKiog5Y+X5a6z6ICFICovXG4gICAgcmVhZG9ubHkgdGFyZ2V0OiBJQ2hhcmFjdGVyO1xuICAgIC8qKiDln7rnoYDkvKTlrrMgKi9cbiAgICBiYXNlRGFtYWdlOiBudW1iZXI7XG4gICAgLyoqIOacgOe7iOS8pOWusyAqL1xuICAgIHJlYWRvbmx5IGZpbmFsRGFtYWdlOiBudW1iZXI7XG4gICAgLyoqIOS8pOWus+exu+WeiyAqL1xuICAgIHJlYWRvbmx5IGRhbWFnZVR5cGU6IERhbWFnZVR5cGU7XG4gICAgLyoqIOS8pOWus+agh+etviAqL1xuICAgIHJlYWRvbmx5IHRhZ3M6IERhbWFnZVRhZ1tdO1xuICAgIC8qKiDmmK/lkKbmmrTlh7sgKi9cbiAgICBpc0NyaXRpY2FsOiBib29sZWFuO1xuICAgIC8qKiDmmrTlh7vlgI3njocgKi9cbiAgICBjcml0aWNhbE11bHRpcGxpZXI6IG51bWJlcjtcbiAgICAvKiog5Lyk5a6z5YeP5YWNICovXG4gICAgZGFtYWdlUmVkdWN0aW9uOiBudW1iZXI7XG4gICAgLyoqIOS8pOWus+WinuW5hSAqL1xuICAgIGRhbWFnZUFtcGxpZmljYXRpb246IG51bWJlcjtcbiAgICAvKiog6ZmE5Yqg55qEQnVmZiAqL1xuICAgIGF0dGFjaGVkQnVmZnM6IElCdWZmW107XG4gICAgLyoqIOS8pOWus+adpea6kO+8iOaKgOiDveOAgeaZruaUu+etie+8iSAqL1xuICAgIHNvdXJjZT86IHN0cmluZztcbiAgICAvKiog5piv5ZCm6Zeq6YG/ICovXG4gICAgaXNEb2RnZWQ6IGJvb2xlYW47XG4gICAgLyoqXG4gICAgICog6K6h566X5pyA57uI5Lyk5a6zXG4gICAgICogQHJldHVybnMg5pyA57uI5Lyk5a6z5YC8XG4gICAgICovXG4gICAgY2FsY3VsYXRlRmluYWxEYW1hZ2UoKTogbnVtYmVyO1xuICAgIC8qKlxuICAgICAqIOa3u+WKoOS8pOWus+agh+etvlxuICAgICAqIEBwYXJhbSB0YWcg5Lyk5a6z5qCH562+XG4gICAgICovXG4gICAgYWRkVGFnKHRhZzogRGFtYWdlVGFnKTogdm9pZDtcbiAgICAvKipcbiAgICAgKiDmo4Dmn6XmmK/lkKbljIXlkKvmoIfnrb5cbiAgICAgKiBAcGFyYW0gdGFnIOS8pOWus+agh+etvlxuICAgICAqIEByZXR1cm5zIOaYr+WQpuWMheWQq1xuICAgICAqL1xuICAgIGhhc1RhZyh0YWc6IERhbWFnZVRhZyk6IGJvb2xlYW47XG4gICAgLyoqXG4gICAgICog5re75Yqg6ZmE5YqgQnVmZlxuICAgICAqIEBwYXJhbSBidWZmIEJ1ZmZcbiAgICAgKi9cbiAgICBhZGRBdHRhY2hlZEJ1ZmYoYnVmZjogSUJ1ZmYpOiB2b2lkO1xufVxuLyoqKiDkvKTlrrPnsbvlnovmnprkuL4qL1xuZXhwb3J0IGVudW0gRGFtYWdlVHlwZSB7XG4gICAgLyoqIOeJqeeQhuS8pOWusyAqL1xuICAgIFBIWVNJQ0FMID0gXCJwaHlzaWNhbFwiLFxuICAgIC8qKiDprZTms5XkvKTlrrMgKi9cbiAgICBNQUdJQyA9IFwibWFnaWNcIixcbiAgICAvKiog55yf5a6e5Lyk5a6zICovXG4gICAgVFJVRSA9IFwidHJ1ZVwiLFxuICAgIC8qKiDmsrvnlpcgKi9cbiAgICBIRUFMID0gXCJoZWFsXCIsXG4gICAgLyoqIOavkue0oOS8pOWusyAqL1xuICAgIFBPSVNPTiA9IFwicG9pc29uXCJcbn1cbi8qKiAqIOS8pOWus+agh+etvuaemuS4viAqL1xuZXhwb3J0IGVudW0gRGFtYWdlVGFnIHtcbiAgICAvKiog55u05o6l5Lyk5a6zICovXG4gICAgRElSRUNUID0gXCJkaXJlY3RcIixcbiAgICAvKiog5oyB57ut5Lyk5a6zICovXG4gICAgT1ZFUl9USU1FID0gXCJvdmVyX3RpbWVcIixcbiAgICAvKiog5Y+N5bCE5Lyk5a6zICovXG4gICAgUkVGTEVDVCA9IFwicmVmbGVjdFwiLFxuICAgIC8qKiDmuoXlsITkvKTlrrMgKi9cbiAgICBTUExBU0ggPSBcInNwbGFzaFwiLFxuICAgIC8qKiDnqb/pgI/kvKTlrrMgKi9cbiAgICBQRU5FVFJBVElORyA9IFwicGVuZXRyYXRpbmdcIixcbiAgICAvKiog5pq05Ye75Lyk5a6zICovXG4gICAgQ1JJVElDQUwgPSBcImNyaXRpY2FsXCIsXG4gICAgLyoqIOaKgOiDveS8pOWusyAqL1xuICAgIFNLSUxMID0gXCJza2lsbFwiLFxuICAgIC8qKiDmma7mlLvkvKTlrrMgKi9cbiAgICBCQVNJQ19BVFRBQ0sgPSBcImJhc2ljX2F0dGFja1wiXG59XG5cbi8qKiAqIOS8pOWus+iuoeeul+WZqOaOpeWPoyAqL1xuZXhwb3J0IGludGVyZmFjZSBJRGFtYWdlQ2FsY3VsYXRvciB7XG4gICAgLyoqXG4gICAgICog6K6h566X5Lyk5a6zXG4gICAgICogQHBhcmFtIGF0dGFja2VyIOaUu+WHu+iAhVxuICAgICAqIEBwYXJhbSB0YXJnZXQg55uu5qCHXG4gICAgICogQHBhcmFtIGJhc2VEYW1hZ2Ug5Z+656GA5Lyk5a6zXG4gICAgICogQHBhcmFtIGRhbWFnZVR5cGUg5Lyk5a6z57G75Z6LXG4gICAgICogQHBhcmFtIHRhZ3Mg5Lyk5a6z5qCH562+XG4gICAgICogQHJldHVybnMg5Lyk5a6z5L+h5oGvXG4gICAgICovXG4gICAgY2FsY3VsYXRlRGFtYWdlKGF0dGFja2VyOiBJQ2hhcmFjdGVyIHwgbnVsbCwgdGFyZ2V0OiBJQ2hhcmFjdGVyLCBiYXNlRGFtYWdlOiBudW1iZXIsIGRhbWFnZVR5cGU6IERhbWFnZVR5cGUsIHRhZ3M/OiBEYW1hZ2VUYWdbXSk6IElEYW1hZ2VJbmZvO1xuICAgIC8qKlxuICAgICAqIOiuoeeul+aatOWHu1xuICAgICAqIEBwYXJhbSBhdHRhY2tlciDmlLvlh7vogIVcbiAgICAgKiBAcGFyYW0gdGFyZ2V0IOebruagh1xuICAgICAqIEByZXR1cm5zIOaYr+WQpuaatOWHu+WSjOaatOWHu+WAjeeOh1xuICAgICAqL1xuICAgIGNhbGN1bGF0ZUNyaXRpY2FsKGF0dGFja2VyOiBJQ2hhcmFjdGVyLCB0YXJnZXQ6IElDaGFyYWN0ZXIpOiB7IGlzQ3JpdGljYWw6IGJvb2xlYW47IG11bHRpcGxpZXI6IG51bWJlciB9O1xuICAgIC8qKlxuICAgICAqIOiuoeeul+S8pOWus+WHj+WFjVxuICAgICAqIEBwYXJhbSB0YXJnZXQg55uu5qCHXG4gICAgICogQHBhcmFtIGRhbWFnZVR5cGUg5Lyk5a6z57G75Z6LXG4gICAgICogQHBhcmFtIGJhc2VEYW1hZ2Ug5Z+656GA5Lyk5a6zXG4gICAgICogQHJldHVybnMg5Lyk5a6z5YeP5YWN5YC8XG4gICAgICovXG4gICAgY2FsY3VsYXRlRGFtYWdlUmVkdWN0aW9uKHRhcmdldDogSUNoYXJhY3RlciwgZGFtYWdlVHlwZTogRGFtYWdlVHlwZSwgYmFzZURhbWFnZTogbnVtYmVyKTogbnVtYmVyO1xufVxuXG4vKiogKiDkvKTlrrPlpITnkIblmajmjqXlj6MgKi9cbmV4cG9ydCBpbnRlcmZhY2UgSURhbWFnZVByb2Nlc3NvciB7XG4gICAgLyoqXG4gICAgICog5aSE55CG5Lyk5a6zXG4gICAgICogQHBhcmFtIGRhbWFnZUluZm8g5Lyk5a6z5L+h5oGvXG4gICAgICovXG4gICAgcHJvY2Vzc0RhbWFnZShkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbyk6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog5bqU55So5Lyk5a6z5YmN55qE5aSE55CGXG4gICAgICogQHBhcmFtIGRhbWFnZUluZm8g5Lyk5a6z5L+h5oGvXG4gICAgICogQHJldHVybnMg5L+u5pS55ZCO55qE5Lyk5a6z5L+h5oGvXG4gICAgICovXG4gICAgYmVmb3JlRGFtYWdlKGRhbWFnZUluZm86IElEYW1hZ2VJbmZvKTogSURhbWFnZUluZm87XG4gICAgLyoqXG4gICAgICog5bqU55So5Lyk5a6z5ZCO55qE5aSE55CGXG4gICAgICogQHBhcmFtIGRhbWFnZUluZm8g5Lyk5a6z5L+h5oGvXG4gICAgICovXG4gICAgYWZ0ZXJEYW1hZ2UoZGFtYWdlSW5mbzogSURhbWFnZUluZm8pOiB2b2lkO1xufVxuXG4vKioqIOS8pOWus+S6i+S7tuaOpeWPoyovXG5leHBvcnQgaW50ZXJmYWNlIElEYW1hZ2VFdmVudHMge1xuICAgIC8qKiDkvKTlrrPorqHnrpfliY3kuovku7YgKi9cbiAgICBvbkJlZm9yZURhbWFnZUNhbGN1bGF0aW9uPzogKGRhbWFnZUluZm86IElEYW1hZ2VJbmZvKSA9PiBJRGFtYWdlSW5mbztcbiAgICAvKiog5Lyk5a6z5bqU55So5YmN5LqL5Lu2ICovXG4gICAgb25CZWZvcmVEYW1hZ2VBcHBsaWNhdGlvbj86IChkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbykgPT4gSURhbWFnZUluZm87XG4gICAgLyoqIOS8pOWus+W6lOeUqOWQjuS6i+S7tiAqL1xuICAgIG9uQWZ0ZXJEYW1hZ2VBcHBsaWNhdGlvbj86IChkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbykgPT4gdm9pZDtcbiAgICAvKiog5pq05Ye75LqL5Lu2ICovXG4gICAgb25Dcml0aWNhbEhpdD86IChkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbykgPT4gdm9pZDtcbiAgICAvKiog5q275Lqh5LqL5Lu2ICovXG4gICAgb25EZWF0aD86IChkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbykgPT4gdm9pZDtcbn1cblxuLyoqKiDkvKTlrrPnrqHnkIblmajmjqXlj6MqL1xuZXhwb3J0IGludGVyZmFjZSBJRGFtYWdlTWFuYWdlciB7XG4gICAgLyoqIOS8pOWus+iuoeeul+WZqCAqL1xuICAgIHJlYWRvbmx5IGNhbGN1bGF0b3I6IElEYW1hZ2VDYWxjdWxhdG9yO1xuICAgIC8qKiDkvKTlrrPlpITnkIblmaggKi9cbiAgICByZWFkb25seSBwcm9jZXNzb3I6IElEYW1hZ2VQcm9jZXNzb3I7XG4gICAgLyoqXG4gICAgICog6YCg5oiQ5Lyk5a6zXG4gICAgICogQHBhcmFtIGF0dGFja2VyIOaUu+WHu+iAhVxuICAgICAqIEBwYXJhbSB0YXJnZXQg55uu5qCHXG4gICAgICogQHBhcmFtIGJhc2VEYW1hZ2Ug5Z+656GA5Lyk5a6zXG4gICAgICogQHBhcmFtIGRhbWFnZVR5cGUg5Lyk5a6z57G75Z6LXG4gICAgICogQHBhcmFtIHRhZ3Mg5Lyk5a6z5qCH562+XG4gICAgICogQHBhcmFtIHNvdXJjZSDkvKTlrrPmnaXmupBcbiAgICAgKi9cbiAgICBkZWFsRGFtYWdlKGF0dGFja2VyOiBJQ2hhcmFjdGVyIHwgbnVsbCwgdGFyZ2V0OiBJQ2hhcmFjdGVyLCBiYXNlRGFtYWdlOiBudW1iZXIsIGRhbWFnZVR5cGU6IERhbWFnZVR5cGUsIHRhZ3M/OiBEYW1hZ2VUYWdbXSwgc291cmNlPzogc3RyaW5nKTogdm9pZDtcbiAgICAvKipcbiAgICAgKiDmsrvnlpdcbiAgICAgKiBAcGFyYW0gaGVhbGVyIOayu+eWl+iAhVxuICAgICAqIEBwYXJhbSB0YXJnZXQg55uu5qCHXG4gICAgICogQHBhcmFtIGhlYWxBbW91bnQg5rK755aX6YePXG4gICAgICogQHBhcmFtIHNvdXJjZSDmsrvnlpfmnaXmupBcbiAgICAgKi9cbiAgICBoZWFsKGhlYWxlcjogSUNoYXJhY3RlciB8IG51bGwsIHRhcmdldDogSUNoYXJhY3RlciwgaGVhbEFtb3VudDogbnVtYmVyLCBzb3VyY2U/OiBzdHJpbmcpOiB2b2lkO1xuICAgIC8qKlxuICAgICAqIOazqOWGjOS8pOWus+S6i+S7tuebkeWQrOWZqFxuICAgICAqIEBwYXJhbSBldmVudHMg5LqL5Lu255uR5ZCs5ZmoXG4gICAgICovXG4gICAgcmVnaXN0ZXJFdmVudHMoZXZlbnRzOiBJRGFtYWdlRXZlbnRzKTogdm9pZDtcbn1cbiJdfQ==