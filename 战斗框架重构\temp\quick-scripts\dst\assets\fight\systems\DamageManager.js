
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/DamageManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc5fegInK5EULVPX5PRYkTd', 'DamageManager');
// fight/systems/DamageManager.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageManager = void 0;
var IDamage_1 = require("../types/IDamage");
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
/**
 * 简化的伤害信息实现
 */
var DamageInfo = /** @class */ (function () {
    function DamageInfo(attacker, target, baseDamage, damageType, tags) {
        if (tags === void 0) { tags = []; }
        this.isCritical = false;
        this.criticalMultiplier = 1.0;
        this.damageReduction = 0;
        this.damageAmplification = 0;
        this.attachedBuffs = [];
        this.isDodged = false;
        this.id = "damage_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this.attacker = attacker;
        this.target = target;
        this.baseDamage = baseDamage;
        this._finalDamage = baseDamage;
        this.damageType = damageType;
        this.tags = __spread(tags);
    }
    Object.defineProperty(DamageInfo.prototype, "finalDamage", {
        get: function () { return this._finalDamage; },
        set: function (value) { this._finalDamage = value; },
        enumerable: false,
        configurable: true
    });
    DamageInfo.prototype.calculateFinalDamage = function () {
        return this._finalDamage;
    };
    DamageInfo.prototype.addTag = function (tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    };
    DamageInfo.prototype.hasTag = function (tag) {
        return this.tags.includes(tag);
    };
    DamageInfo.prototype.addAttachedBuff = function (buff) {
        this.attachedBuffs.push(buff);
    };
    return DamageInfo;
}());
/**
 * 伤害管理器
 * 负责处理复杂的伤害计算，包括暴击、抗性、伤害修正等
 */
var DamageManager = /** @class */ (function () {
    function DamageManager() {
        this._totalDamageDealt = 0;
        this._totalDamageTaken = 0;
        this._criticalHits = 0;
        this._totalHits = 0;
        this._eventManager = new EventManager_1.EventManager();
    }
    /**
     * 处理伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @param damageType 伤害类型
     * @param tags 伤害标签
     * @returns 最终伤害信息
     */
    DamageManager.prototype.dealDamage = function (attacker, target, baseDamage, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        // 创建伤害信息
        var damageInfo = new DamageInfo(attacker, target, baseDamage, damageType, tags);
        // 检查闪避
        if (this.checkDodge(attacker, target)) {
            damageInfo.isDodged = true;
            damageInfo.finalDamage = 0;
            console.log(target.characterName + " dodged attack from " + attacker.characterName);
            this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
            return damageInfo;
        }
        // 计算暴击
        if (this.checkCritical(attacker, target)) {
            damageInfo.isCritical = true;
            var criticalDamage = attacker.attributes.criticalDamage || 1.5;
            damageInfo.finalDamage *= criticalDamage;
            this._criticalHits++;
            console.log("Critical hit! " + attacker.characterName + " deals critical damage to " + target.characterName);
        }
        // 应用防御力
        damageInfo.finalDamage = this.applyDefense(damageInfo.finalDamage, target, damageType);
        // 应用伤害减免
        damageInfo.finalDamage = this.applyDamageReduction(damageInfo, target);
        // 应用攻击者的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyAttackerModifiers(damageInfo, attacker);
        // 应用目标的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyTargetModifiers(damageInfo, target);
        // 确保伤害不为负数
        damageInfo.finalDamage = Math.max(0, Math.floor(damageInfo.finalDamage));
        // 计算实际伤害减免
        damageInfo.damageReduction = damageInfo.baseDamage - damageInfo.finalDamage;
        // 更新统计
        this._totalDamageDealt += damageInfo.finalDamage;
        this._totalHits++;
        // 应用伤害到目标
        if (damageInfo.finalDamage > 0 || damageInfo.isDodged) {
            target.takeDamage(damageInfo);
            if (!damageInfo.isDodged) {
                this._totalDamageTaken += damageInfo.finalDamage;
            }
        }
        console.log(attacker.characterName + " deals " + damageInfo.finalDamage + " " + damageType + " damage to " + target.characterName);
        // 触发伤害事件
        this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
        return damageInfo;
    };
    /** 检查是否闪避 */
    DamageManager.prototype.checkDodge = function (attacker, target) {
        var hitRate = attacker.attributes.accuracy || 1.0;
        var dodgeRate = target.attributes.evasion || 0.0;
        var finalHitRate = Math.max(0, hitRate - dodgeRate);
        return Math.random() > finalHitRate;
    };
    /** 检查是否暴击 */
    DamageManager.prototype.checkCritical = function (attacker, _target) {
        var criticalRate = attacker.attributes.criticalRate || 0.0;
        // 这里可以添加目标的暴击抗性
        return Math.random() < criticalRate;
    };
    /** 应用防御力 */
    DamageManager.prototype.applyDefense = function (damage, target, damageType) {
        var defense = 0;
        switch (damageType) {
            case IDamage_1.DamageType.PHYSICAL:
                defense = target.attributes.defense || 0;
                break;
            case IDamage_1.DamageType.MAGIC:
                // 魔法防御力（如果有的话）
                defense = target.attributes.magicDefense || target.attributes.defense * 0.5;
                break;
            case IDamage_1.DamageType.TRUE:
                // 真实伤害无视防御
                return damage;
        }
        // 简单的防御计算：伤害 = 基础伤害 - 防御力
        // 可以改为更复杂的公式，如：伤害 = 基础伤害 * (100 / (100 + 防御力))
        return Math.max(1, damage - defense);
    };
    /** 应用伤害减免 */
    DamageManager.prototype.applyDamageReduction = function (damageInfo, _target) {
        var damage = damageInfo.finalDamage;
        // 这里可以添加基于伤害类型的减免
        // 例如：火焰抗性、冰霜抗性等
        return damage;
    };
    /** 应用攻击者的伤害修正 */
    DamageManager.prototype.applyAttackerModifiers = function (damageInfo, attacker) {
        var e_1, _a;
        var damage = damageInfo.finalDamage;
        try {
            // 遍历攻击者的所有Buff，应用伤害修正
            for (var _b = __values(attacker.buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (buff.onDealDamage) {
                    var modifiedInfo = buff.onDealDamage(damageInfo, damageInfo.target);
                    damage = modifiedInfo.finalDamage;
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return damage;
    };
    /** 应用目标的伤害修正 */
    DamageManager.prototype.applyTargetModifiers = function (damageInfo, target) {
        var e_2, _a;
        var damage = damageInfo.finalDamage;
        try {
            // 遍历目标的所有Buff，应用伤害修正
            for (var _b = __values(target.buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (buff.onTakeDamage) {
                    var modifiedInfo = buff.onTakeDamage(damageInfo, damageInfo.attacker);
                    damage = modifiedInfo.finalDamage;
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return damage;
    };
    /** 获取统计信息 */
    DamageManager.prototype.getStats = function () {
        return {
            totalDamageDealt: this._totalDamageDealt,
            totalDamageTaken: this._totalDamageTaken,
            totalHits: this._totalHits,
            criticalHits: this._criticalHits,
            criticalRate: this._totalHits > 0 ? this._criticalHits / this._totalHits : 0
        };
    };
    /** 重置统计信息 */
    DamageManager.prototype.resetStats = function () {
        this._totalDamageDealt = 0;
        this._totalDamageTaken = 0;
        this._criticalHits = 0;
        this._totalHits = 0;
    };
    Object.defineProperty(DamageManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    DamageManager.prototype.cleanup = function () {
        this.resetStats();
        this._eventManager.cleanup();
    };
    return DamageManager;
}());
exports.DamageManager = DamageManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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