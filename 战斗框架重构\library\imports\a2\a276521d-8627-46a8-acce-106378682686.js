"use strict";
cc._RF.push(module, 'a2765IdhidGqKzOEGN4aCaG', 'AttackBoostBuff');
// fight/buff/AttackBoostBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackBoostBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 攻击力提升Buff
 * 提升角色的攻击力和魔法攻击力
 */
var AttackBoostBuff = /** @class */ (function () {
    function AttackBoostBuff(caster, target, duration, attackMultiplier, magicAttackMultiplier) {
        this._name = "攻击强化";
        this._description = "提升攻击力和魔法攻击力";
        this._type = IBuff_1.BuffType.BUFF;
        this._stackCount = 1;
        this._maxStack = 5;
        this._isExpired = false;
        this._attributeModifiers = [];
        // 视觉效果
        this._iconPath = "icons/buffs/attack_boost";
        this._effectPrefabPath = "prefabs/effects/AttackAura";
        this._id = "attack_boost_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._attackMultiplier = attackMultiplier;
        this._magicAttackMultiplier = magicAttackMultiplier || attackMultiplier;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u653B\u51FB\u529B\u63D0\u5347" + Math.round((attackMultiplier - 1) * 100) + "%\uFF0C\u6301\u7EED" + duration + "\u79D2";
        this.createAttributeModifiers();
    }
    Object.defineProperty(AttackBoostBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) {
            var oldStack = this._stackCount;
            this._stackCount = Math.max(0, Math.min(value, this._maxStack));
            if (oldStack !== this._stackCount) {
                this.updateAttributeModifiers();
            }
        },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    AttackBoostBuff.prototype.createAttributeModifiers = function () {
        // 攻击力修改器
        var attackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_attack", "攻击力提升", "attack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, (this._attackMultiplier - 1) * this._stackCount, this._duration);
        // 魔法攻击力修改器
        var magicAttackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_magic_attack", "魔法攻击力提升", "magicAttack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, (this._magicAttackMultiplier - 1) * this._stackCount, this._duration);
        this._attributeModifiers = [attackModifier, magicAttackModifier];
    };
    /** 更新属性修改器 */
    AttackBoostBuff.prototype.updateAttributeModifiers = function () {
        var e_1, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                var attributeModifier = modifier;
                if (attributeModifier.attributeName === "attack") {
                    attributeModifier.setValue((this._attackMultiplier - 1) * this._stackCount);
                }
                else if (attributeModifier.attributeName === "magicAttack") {
                    attributeModifier.setValue((this._magicAttackMultiplier - 1) * this._stackCount);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** Buff被添加时触发 */
    AttackBoostBuff.prototype.onApply = function () {
        var e_2, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /** Buff每帧更新时触发 */
    AttackBoostBuff.prototype.onTick = function (deltaTime) {
        var e_3, _a;
        try {
            // 更新属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /** Buff被移除时触发 */
    AttackBoostBuff.prototype.onRemove = function () {
        var e_4, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            // 移除属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 造成伤害时触发 */
    AttackBoostBuff.prototype.onDealDamage = function (damageInfo, _target) {
        // 攻击力提升buff可以在这里添加额外的伤害加成
        var bonusDamagePercentage = 0.1 * this._stackCount; // 每层额外10%伤害
        var bonusDamage = Math.floor(damageInfo.baseDamage * bonusDamagePercentage);
        console.log(this._name + " adds " + bonusDamage + " bonus damage (" + bonusDamagePercentage * 100 + "% of base damage, Stack: " + this._stackCount + ")");
        // 正确的方式：通过damageAmplification来增加伤害
        // 这样伤害计算系统会重新计算finalDamage
        damageInfo.damageAmplification += bonusDamage;
        return damageInfo;
    };
    /** 更新Buff状态 */
    AttackBoostBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    AttackBoostBuff.prototype.refresh = function () {
        var e_5, _a;
        this._remainingTime = this._duration;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    AttackBoostBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    AttackBoostBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    AttackBoostBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.attack_multiplier:
                return this._attackMultiplier * this._stackCount;
            case Buff_1.EBuffEffectType.magic_attack_multiplier:
                return this._magicAttackMultiplier * this._stackCount;
            case Buff_1.EBuffEffectType.attack_bonus_percentage:
                return (this._attackMultiplier - 1) * this._stackCount * 100;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    AttackBoostBuff.prototype.conflictsWith = function (_otherBuff) {
        // 同类型的攻击力提升buff不冲突，可以叠加
        return false;
    };
    /** 播放应用特效 */
    AttackBoostBuff.prototype.playApplyEffect = function () {
        console.log("Playing apply effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效播放逻辑
    };
    /** 停止特效 */
    AttackBoostBuff.prototype.stopEffect = function () {
        console.log("Stopping effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    AttackBoostBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            attackMultiplier: this._attackMultiplier,
            magicAttackMultiplier: this._magicAttackMultiplier,
            currentAttackBonus: this.getEffectValue(Buff_1.EBuffEffectType.attack_bonus_percentage),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return AttackBoostBuff;
}());
exports.AttackBoostBuff = AttackBoostBuff;

cc._RF.pop();