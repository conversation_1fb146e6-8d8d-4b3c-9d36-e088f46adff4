"use strict";
cc._RF.push(module, 'd0704wfIcNG5p+J854MfOUW', 'Timeline');
// fight/timeline/Timeline.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineEvent = exports.TimelineNode = exports.Timeline = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
/*** Timeline实现类*/
var Timeline = /** @class */ (function () {
    function Timeline(id, name, duration, caster, target, targets, targetPosition) {
        this._timeElapsed = 0;
        this._nodes = [];
        this._isCompleted = false;
        this._isPaused = false;
        this._id = id;
        this._name = name;
        this._duration = duration;
        this._caster = caster;
        this._target = target;
        this._targets = targets;
        this._targetPosition = targetPosition;
        this._eventManager = EventManager_1.EventManager.createLocal("Timeline_" + id);
    }
    Timeline.getTimeLineId = function (id) { return id + "_" + Date.now(); };
    Object.defineProperty(Timeline.prototype, "id", {
        // 实现ITimeline接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "timeElapsed", {
        get: function () { return this._timeElapsed; },
        set: function (value) { this._timeElapsed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "nodes", {
        get: function () { return this._nodes; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "targets", {
        get: function () { return this._targets; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "targetPosition", {
        get: function () { return this._targetPosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "isCompleted", {
        get: function () { return this._isCompleted; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "isPaused", {
        get: function () { return this._isPaused; },
        set: function (value) { this._isPaused = value; },
        enumerable: false,
        configurable: true
    });
    /** * 添加Timeline节点 */
    Timeline.prototype.addNode = function (node) {
        this._nodes.push(node);
        // 按触发时间排序
        this._nodes.sort(function (a, b) { return a.triggerTime - b.triggerTime; });
    };
    /** * 更新Timeline */
    Timeline.prototype.update = function (deltaTime) {
        var e_1, _a;
        if (this._isPaused || this._isCompleted) {
            return this._isCompleted;
        }
        // const previousTime = this._timeElapsed;
        this._timeElapsed += deltaTime;
        try {
            // 检查并触发节点事件
            for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var node = _c.value;
                if (node.shouldTrigger(this._timeElapsed, deltaTime)) {
                    node.trigger(this, this._nodes.indexOf(node));
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 检查是否完成
        if (this._timeElapsed >= this._duration) {
            this._isCompleted = true;
            this._eventManager.emit(FightEvent_1.default.completedT, { timeline: this });
        }
        return this._isCompleted;
    };
    /*** 暂停Timeline*/
    Timeline.prototype.pause = function () {
        this._isPaused = true;
        this._eventManager.emit(FightEvent_1.default.pausedT, { timeline: this });
    };
    /*** 恢复Timeline*/
    Timeline.prototype.resume = function () {
        this._isPaused = false;
        this._eventManager.emit(FightEvent_1.default.resumedT, { timeline: this });
    };
    /*** 停止Timeline*/
    Timeline.prototype.stop = function () {
        this._isCompleted = true;
        this._eventManager.emit(FightEvent_1.default.stoppedT, { timeline: this });
    };
    /** * 重置Timeline */
    Timeline.prototype.reset = function () {
        var e_2, _a;
        this._timeElapsed = 0;
        this._isCompleted = false;
        this._isPaused = false;
        try {
            for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var node = _c.value;
                node.reset();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        this._eventManager.emit(FightEvent_1.default.resetT, { timeline: this });
    };
    /** * 跳转到指定时间点 */
    Timeline.prototype.seekTo = function (time) {
        var e_3, _a;
        var oldTime = this._timeElapsed;
        this._timeElapsed = Math.max(0, Math.min(time, this._duration));
        // 如果是向前跳转，需要触发中间的事件
        if (time > oldTime) {
            try {
                for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var node = _c.value;
                    if (node.triggerTime > oldTime && node.triggerTime <= time) {
                        if (!node.isTriggered || node.repeatable) {
                            node.trigger(this, this._nodes.indexOf(node));
                        }
                    }
                }
            }
            catch (e_3_1) { e_3 = { error: e_3_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_3) throw e_3.error; }
            }
        }
        this._eventManager.emit(FightEvent_1.default.seekedT, { timeline: this, oldTime: oldTime, newTime: time });
    };
    Object.defineProperty(Timeline.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理资源 */
    Timeline.prototype.cleanup = function () {
        this._eventManager.cleanup();
        this._nodes.length = 0;
    };
    return Timeline;
}());
exports.Timeline = Timeline;
/** * Timeline节点实现类 */
var TimelineNode = /** @class */ (function () {
    function TimelineNode(id, triggerTime, event, repeatable, repeatInterval, maxRepeats) {
        if (repeatable === void 0) { repeatable = false; }
        this._isTriggered = false;
        this._currentRepeats = 0;
        this._lastTriggerTime = -1;
        this._id = id;
        this._triggerTime = triggerTime;
        this._event = event;
        this._repeatable = repeatable;
        this._repeatInterval = repeatInterval;
        this._maxRepeats = maxRepeats;
    }
    Object.defineProperty(TimelineNode.prototype, "id", {
        // 实现ITimelineNode接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "triggerTime", {
        get: function () { return this._triggerTime; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "event", {
        get: function () { return this._event; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "isTriggered", {
        get: function () { return this._isTriggered; },
        set: function (value) { this._isTriggered = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "repeatable", {
        get: function () { return this._repeatable; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "repeatInterval", {
        get: function () { return this._repeatInterval; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "maxRepeats", {
        get: function () { return this._maxRepeats; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "currentRepeats", {
        get: function () { return this._currentRepeats; },
        set: function (value) { this._currentRepeats = value; },
        enumerable: false,
        configurable: true
    });
    /** * 检查是否应该触发 */
    TimelineNode.prototype.shouldTrigger = function (currentTime, _deltaTime) {
        // 首次触发检查
        if (!this._isTriggered && currentTime >= this._triggerTime) {
            return true;
        }
        // 重复触发检查
        if (this._repeatable && this._isTriggered && this._repeatInterval) {
            // 检查是否达到最大重复次数
            if (this._maxRepeats !== undefined && this._currentRepeats >= this._maxRepeats) {
                return false;
            }
            // 检查是否到了下一次触发时间
            var nextTriggerTime = this._lastTriggerTime + this._repeatInterval;
            if (currentTime >= nextTriggerTime) {
                return true;
            }
        }
        return false;
    };
    /** * 触发节点事件 */
    TimelineNode.prototype.trigger = function (timeline, nodeIndex) {
        try {
            this._event.execute(timeline, nodeIndex);
            if (!this._isTriggered) {
                this._isTriggered = true;
            }
            if (this._repeatable) {
                this._currentRepeats++;
                this._lastTriggerTime = timeline.timeElapsed;
            }
        }
        catch (error) {
            console.error("Error executing timeline event " + this._event.id + ":", error);
        }
    };
    /** * 重置节点状态 */
    TimelineNode.prototype.reset = function () {
        this._isTriggered = false;
        this._currentRepeats = 0;
        this._lastTriggerTime = -1;
    };
    return TimelineNode;
}());
exports.TimelineNode = TimelineNode;
/*** Timeline事件基类*/
var TimelineEvent = /** @class */ (function () {
    function TimelineEvent(id, type) {
        this._id = id;
        this._type = type;
    }
    Object.defineProperty(TimelineEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    /*** 播放音效*/
    TimelineEvent.prototype.playSound = function (soundId) {
        // 这里应该调用音效管理器
        // AudioManager.getInstance().playEffect(soundId);
        console.log("Playing sound: " + soundId);
    };
    /** * 播放特效 */
    TimelineEvent.prototype.playEffect = function (effectId, position) {
        // 这里应该调用特效管理器
        // EffectManager.getInstance().playEffect(effectId, position);
        console.log("Playing effect: " + effectId + " at position:", position);
    };
    /** * 获取目标列表 */
    TimelineEvent.prototype.getTargets = function (timeline, _nodeIndex) {
        var targets = [];
        if (timeline.target) {
            targets.push(timeline.target);
        }
        if (timeline.targets) {
            targets.push.apply(targets, __spread(timeline.targets));
        }
        return targets.filter(function (target) { return target && !target.isDead; });
    };
    /** * 获取有效的目标（排除已死亡的） */
    TimelineEvent.prototype.getValidTarget = function (timeline, nodeIndex) {
        var e_4, _a;
        // 如果指定了节点索引，尝试获取对应的目标
        if (nodeIndex !== undefined && timeline.targets && timeline.targets[nodeIndex]) {
            var target = timeline.targets[nodeIndex];
            return target && !target.isDead ? target : null;
        }
        // 否则返回单个目标
        if (timeline.target && !timeline.target.isDead) {
            return timeline.target;
        }
        // 或者返回第一个有效的多目标
        if (timeline.targets) {
            try {
                for (var _b = __values(timeline.targets), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var target = _c.value;
                    if (target && !target.isDead) {
                        return target;
                    }
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_4) throw e_4.error; }
            }
        }
        return null;
    };
    return TimelineEvent;
}());
exports.TimelineEvent = TimelineEvent;

cc._RF.pop();