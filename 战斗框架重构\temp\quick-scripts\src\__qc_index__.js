
require('./assets/card/cardgame/src/BuffManager');
require('./assets/card/cardgame/src/CampManager');
require('./assets/card/cardgame/src/CardManager');
require('./assets/card/cardgame/src/EquipmentManager');
require('./assets/card/cardgame/src/EventManager');
require('./assets/card/cardgame/src/FightManager');
require('./assets/card/cardgame/src/RoleManager');
require('./assets/card/cardgame/src/SkillManager');
require('./assets/card/cardgame/src/base/BuffBase');
require('./assets/card/cardgame/src/base/CampBase');
require('./assets/card/cardgame/src/base/CardBase');
require('./assets/card/cardgame/src/base/CardModel');
require('./assets/card/cardgame/src/base/Equipment');
require('./assets/card/cardgame/src/base/RoleBase');
require('./assets/card/cardgame/src/base/SkillBase');
require('./assets/card/cardgame/src/base/TimeModel');
require('./assets/fight/actions/AttackAction');
require('./assets/fight/actions/MoveAction');
require('./assets/fight/buff/AttackBoostBuff');
require('./assets/fight/buff/BuffModelBeHurtFight');
require('./assets/fight/buff/HealOverTimeBuff');
require('./assets/fight/buff/PoisonBuff');
require('./assets/fight/buff/StunBuff');
require('./assets/fight/characters/AttributeModifier');
require('./assets/fight/characters/BaseCharacter');
require('./assets/fight/characters/Character');
require('./assets/fight/characters/CharacterAttributes');
require('./assets/fight/examples/BattleTestScene');
require('./assets/fight/examples/SkillAndBuffExample');
require('./assets/fight/skills/ChargeAttackSkill');
require('./assets/fight/skills/HealingLightSkill');
require('./assets/fight/skills/PlayerSkillFire1');
require('./assets/fight/skills/ThunderStormSkill');
require('./assets/fight/systems/BattleManager');
require('./assets/fight/systems/BuffManager');
require('./assets/fight/systems/BulletManager');
require('./assets/fight/systems/BulletSystem');
require('./assets/fight/systems/DamageManager');
require('./assets/fight/systems/EventManager');
require('./assets/fight/systems/SkillManager');
require('./assets/fight/systems/TimelineManager');
require('./assets/fight/timeline/Timeline');
require('./assets/fight/timeline/TimelineEvents');
require('./assets/fight/types/Buff');
require('./assets/fight/types/CharacterTypes');
require('./assets/fight/types/FightEvent');
require('./assets/fight/types/IBuff');
require('./assets/fight/types/IBullet');
require('./assets/fight/types/ICharacter');
require('./assets/fight/types/ICharacterAttributes');
require('./assets/fight/types/IDamage');
require('./assets/fight/types/ISkill');
require('./assets/fight/types/ITimeline');
require('./assets/fight/types/SkillName');
