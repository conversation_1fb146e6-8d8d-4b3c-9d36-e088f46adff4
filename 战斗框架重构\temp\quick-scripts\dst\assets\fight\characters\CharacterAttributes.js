
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/characters/CharacterAttributes.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c652blzZLJLhpyDcZ7JiikF', 'CharacterAttributes');
// fight/characters/CharacterAttributes.ts

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CharacterAttributes = void 0;
var EventManager_1 = require("../systems/EventManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
/*** 角色属性实现*/
var CharacterAttributes = /** @class */ (function () {
    function CharacterAttributes(initialData) {
        /**属性修改器 */
        this._modifiers = new Map();
        this._eventManager = EventManager_1.EventManager.createLocal("CharacterAttributes");
        this.initializeAttributes(initialData);
    }
    Object.defineProperty(CharacterAttributes.prototype, "currentHp", {
        // ICharacterAttributes 接口实现
        get: function () { return this._resourceData.currentHp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxHp", {
        get: function () { return this._currentAttributes.maxHp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attack", {
        get: function () { return this._currentAttributes.attack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "defense", {
        get: function () { return this._currentAttributes.defense; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attackSpeed", {
        get: function () { return this._currentAttributes.attackSpeed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "moveSpeed", {
        get: function () { return this._currentAttributes.moveSpeed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "attackRange", {
        get: function () { return this._currentAttributes.attackRange; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "level", {
        get: function () { return this._currentAttributes.level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "currentMp", {
        // ICharacterResource 接口实现
        get: function () { return this._resourceData.currentMp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxMp", {
        get: function () { return this._currentAttributes.maxMp; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "currentStamina", {
        get: function () { return this._resourceData.currentStamina; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "maxStamina", {
        get: function () { return this._resourceData.maxStamina; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "magicAttack", {
        // 额外属性访问器
        get: function () { return this._currentAttributes.attack; } // 暂时使用attack作为magicAttack
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "magicDefense", {
        get: function () { return this._currentAttributes.defense; } // 暂时使用defense作为magicDefense
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "speed", {
        get: function () { return this._currentAttributes.moveSpeed; } // 暂时使用moveSpeed作为speed
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "accuracy", {
        get: function () { return this._currentAttributes.hitRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "evasion", {
        get: function () { return this._currentAttributes.dodgeRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "criticalRate", {
        get: function () { return this._currentAttributes.criticalRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "criticalDamage", {
        get: function () { return this._currentAttributes.criticalDamage; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "hitRate", {
        get: function () { return this._currentAttributes.hitRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "dodgeRate", {
        get: function () { return this._currentAttributes.dodgeRate; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CharacterAttributes.prototype, "experience", {
        get: function () { return this._currentAttributes.experience; },
        enumerable: false,
        configurable: true
    });
    /** * 初始化属性 */
    CharacterAttributes.prototype.initializeAttributes = function (initialData) {
        // 设置默认值
        var defaultAttributes = {
            hp: 100,
            maxHp: 100,
            mp: 50,
            maxMp: 50,
            maxStamina: 100,
            attack: 10,
            defense: 5,
            attackSpeed: 1,
            moveSpeed: 100,
            attackRange: 150,
            criticalRate: 0.05,
            criticalDamage: 1.5,
            hitRate: 0.95,
            dodgeRate: 0.05,
            level: 1,
            experience: 0
        };
        // 合并初始数据
        this._baseAttributes = __assign(__assign({}, defaultAttributes), initialData);
        this._currentAttributes = __assign({}, this._baseAttributes);
        // 初始化资源数据
        this._resourceData = {
            currentHp: this._baseAttributes.hp,
            currentMp: this._baseAttributes.mp,
            currentStamina: this._baseAttributes.maxStamina || 100,
            maxStamina: this._baseAttributes.maxStamina || 100
        };
    };
    /*** 修改生命值*/
    CharacterAttributes.prototype.modifyHp = function (amount) {
        var oldHp = this._resourceData.currentHp;
        this._resourceData.currentHp = Math.max(0, Math.min(this.maxHp, this._resourceData.currentHp + amount));
        if (oldHp !== this._resourceData.currentHp) {
            this._eventManager.emit(FightEvent_1.default.hpChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentHp,
                oldValue: oldHp,
                newValue: this._resourceData.currentHp,
                delta: this._resourceData.currentHp - oldHp
            });
        }
    };
    /** * 修改魔法值 */
    CharacterAttributes.prototype.modifyMp = function (amount) {
        var oldMp = this._resourceData.currentMp;
        this._resourceData.currentMp = Math.max(0, Math.min(this.maxMp, this._resourceData.currentMp + amount));
        if (oldMp !== this._resourceData.currentMp) {
            this._eventManager.emit(FightEvent_1.default.mpChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentMp,
                oldValue: oldMp,
                newValue: this._resourceData.currentMp,
                delta: this._resourceData.currentMp - oldMp
            });
        }
    };
    /** * 修改耐力 */
    CharacterAttributes.prototype.modifyStamina = function (amount) {
        var oldStamina = this._resourceData.currentStamina;
        this._resourceData.currentStamina = Math.max(0, Math.min(this.maxStamina, this._resourceData.currentStamina + amount));
        if (oldStamina !== this._resourceData.currentStamina) {
            this._eventManager.emit(FightEvent_1.default.staminaChanged, {
                attributeName: CharacterTypes_1.CharacterAttributeName.currentStamina,
                oldValue: oldStamina,
                newValue: this._resourceData.currentStamina,
                delta: this._resourceData.currentStamina - oldStamina
            });
        }
    };
    /** * 获取生命值百分比 */
    CharacterAttributes.prototype.getHpPercentage = function () {
        return this.maxHp > 0 ? this._resourceData.currentHp / this.maxHp : 0;
    };
    /** * 是否死亡 */
    CharacterAttributes.prototype.isDead = function () {
        return this._resourceData.currentHp <= 0;
    };
    /** * 重置到满血状态 */
    CharacterAttributes.prototype.resetToFull = function () {
        this.modifyHp(this.maxHp - this._resourceData.currentHp);
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    };
    /** * 检查是否有足够的魔法值 */
    CharacterAttributes.prototype.hasEnoughMp = function (amount) {
        return this._resourceData.currentMp >= amount;
    };
    /** * 检查是否有足够的耐力 */
    CharacterAttributes.prototype.hasEnoughStamina = function (amount) {
        return this._resourceData.currentStamina >= amount;
    };
    /** * 获取魔法值百分比 */
    CharacterAttributes.prototype.getMpPercentage = function () {
        return this.maxMp > 0 ? this._resourceData.currentMp / this.maxMp : 0;
    };
    /**  * 获取耐力百分比  */
    CharacterAttributes.prototype.getStaminaPercentage = function () {
        return this.maxStamina > 0 ? this._resourceData.currentStamina / this.maxStamina : 0;
    };
    /** * 重置资源到满值状态 */
    CharacterAttributes.prototype.resetResourcesToFull = function () {
        this.modifyMp(this.maxMp - this._resourceData.currentMp);
        this.modifyStamina(this.maxStamina - this._resourceData.currentStamina);
    };
    /** * 添加属性修改器 */
    CharacterAttributes.prototype.addModifier = function (modifier) {
        this._modifiers.set(modifier.id, modifier);
        modifier.apply(this);
        this.recalculateAttributes();
    };
    /*** 移除属性修改器  */
    CharacterAttributes.prototype.removeModifier = function (modifierId) {
        var modifier = this._modifiers.get(modifierId);
        if (modifier) {
            modifier.remove(this);
            this._modifiers.delete(modifierId);
            this.recalculateAttributes();
        }
    };
    /** * 获取属性修改器 */
    CharacterAttributes.prototype.getModifier = function (modifierId) {
        return this._modifiers.get(modifierId);
    };
    /*** 更新所有修改器*/
    CharacterAttributes.prototype.updateModifiers = function (deltaTime) {
        var e_1, _a;
        var expiredModifiers = [];
        this._modifiers.forEach(function (modifier, id) {
            if (modifier.update(deltaTime)) {
                expiredModifiers.push(id);
            }
        });
        try {
            // 移除过期的修改器
            for (var expiredModifiers_1 = __values(expiredModifiers), expiredModifiers_1_1 = expiredModifiers_1.next(); !expiredModifiers_1_1.done; expiredModifiers_1_1 = expiredModifiers_1.next()) {
                var id = expiredModifiers_1_1.value;
                this.removeModifier(id);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (expiredModifiers_1_1 && !expiredModifiers_1_1.done && (_a = expiredModifiers_1.return)) _a.call(expiredModifiers_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** * 重新计算属性 */
    CharacterAttributes.prototype.recalculateAttributes = function () {
        var _this = this;
        // 重置为基础属性
        this._currentAttributes = __assign({}, this._baseAttributes);
        // 应用所有修改器
        this._modifiers.forEach(function (modifier) {
            modifier.apply(_this);
        });
        // 确保资源不超过最大值
        this._resourceData.currentHp = Math.min(this._resourceData.currentHp, this.maxHp);
        this._resourceData.currentMp = Math.min(this._resourceData.currentMp, this.maxMp);
        this._resourceData.currentStamina = Math.min(this._resourceData.currentStamina, this.maxStamina);
    };
    /** * 直接设置属性值（用于修改器） */
    CharacterAttributes.prototype.setAttributeValue = function (attributeName, value) {
        var key = attributeName;
        if (key in this._currentAttributes) {
            var oldValue = this._currentAttributes[key];
            this._currentAttributes[key] = value;
            this._eventManager.emit(FightEvent_1.default.attributeChanged, { attributeName: attributeName, oldValue: oldValue, newValue: value, delta: value - oldValue });
        }
    };
    /** * 获取基础属性值 */
    CharacterAttributes.prototype.getBaseAttributeValue = function (attributeName) {
        return this._baseAttributes[attributeName];
    };
    /**  * 获取当前属性值  */
    CharacterAttributes.prototype.getCurrentAttributeValue = function (attributeName) {
        var key = attributeName;
        if (key in this._currentAttributes) {
            return this._currentAttributes[key];
        }
        return 0;
    };
    Object.defineProperty(CharacterAttributes.prototype, "eventManager", {
        /** * 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 获取属性数据的副本 */
    CharacterAttributes.prototype.getAttributeData = function () {
        return __assign({}, this._currentAttributes);
    };
    /**  * 获取资源数据的副本  */
    CharacterAttributes.prototype.getResourceData = function () {
        return __assign({}, this._resourceData);
    };
    /** 消耗魔法值 */
    CharacterAttributes.prototype.consumeMp = function (amount) {
        this.modifyMp(-amount);
    };
    /** 消耗耐力 */
    CharacterAttributes.prototype.consumeStamina = function (amount) {
        this.modifyStamina(-amount);
    };
    /** 造成伤害 */
    CharacterAttributes.prototype.takeDamage = function (amount) {
        this.modifyHp(-amount);
    };
    /** * 清理资源 */
    CharacterAttributes.prototype.cleanup = function () {
        this._modifiers.clear();
        this._eventManager.cleanup();
    };
    return CharacterAttributes;
}());
exports.CharacterAttributes = CharacterAttributes;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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