{"version": 3, "sources": ["assets\\fight\\characters\\AttributeModifier.ts"], "names": [], "mappings": ";;;;;;;AAAA,sEAAgH;AAEhH;;GAEG;AACH;IASI,2BAAY,EAAU,EAAE,IAAY,EAAE,aAAqB,EAAE,IAA2B,EAAE,KAAa,EAAE,QAAqB;QAArB,yBAAA,EAAA,YAAoB,CAAC;QAC1H,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;IACnC,CAAC;IAGD,sBAAI,iCAAE;QADN,yBAAyB;aACzB,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,mCAAI;aAAR,cAAoC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,uCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,4CAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAG3D,sBAAI,4CAAa;QADjB,OAAO;aACP,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;;;OAAA;IAE3D,eAAe;IACf,iCAAK,GAAL,UAAM,UAAgC;QAClC,IAAM,YAAY,GAAG,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9E,IAAI,QAAQ,GAAG,YAAY,CAAC;QAC5B,QAAQ,IAAI,CAAC,KAAK,EAAE;YAChB,KAAK,4CAAqB,CAAC,GAAG;gBAC1B,QAAQ,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;gBACtC,MAAM;YACV,KAAK,4CAAqB,CAAC,QAAQ;gBAC/B,QAAQ,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;gBACtC,MAAM;YACV,KAAK,4CAAqB,CAAC,UAAU;gBACjC,QAAQ,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,4CAAqB,CAAC,QAAQ;gBAC/B,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;gBACvB,MAAM;SACb;QACD,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,aAAW,IAAI,CAAC,KAAK,UAAK,IAAI,CAAC,cAAc,SAAI,YAAY,YAAO,QAAU,CAAC,CAAC;IAChG,CAAC;IACD,cAAc;IACd,kCAAM,GAAN,UAAO,WAAiC;QACpC,yBAAyB;QACzB,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,aAAW,IAAI,CAAC,KAAK,cAAS,IAAI,CAAC,cAAgB,CAAC,CAAC;IACrE,CAAC;IACD,cAAc;IACd,kCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC,CAAC,QAAQ;SACzB;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IACpC,CAAC;IACD,YAAY;IACZ,oCAAQ,GAAR,UAAS,KAAa;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IACD,eAAe;IACf,wCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;SACrC,CAAC;IACN,CAAC;IACL,wBAAC;AAAD,CAjFA,AAiFC,IAAA;AAjFY,8CAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ICharacterAttributes, IAttributeModifier, AttributeModifierType } from \"../types/ICharacterAttributes\";\n\n/**\n * 属性修改器实现类\n */\nexport class AttributeModifier implements IAttributeModifier {\n    private _id: string;\n    private _name: string;\n    private _type: AttributeModifierType;\n    private _value: number;\n    private _duration: number;\n    private _remainingTime: number;\n    private _attributeName: string;\n\n    constructor(id: string, name: string, attributeName: string, type: AttributeModifierType, value: number, duration: number = -1) {\n        this._id = id;\n        this._name = name;\n        this._attributeName = attributeName;\n        this._type = type;\n        this._value = value;\n        this._duration = duration;\n        this._remainingTime = duration;\n    }\n\n    // 实现IAttributeModifier接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get type(): AttributeModifierType { return this._type; }\n    get value(): number { return this._value; }\n    get duration(): number { return this._duration; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = value; }\n    // 额外属性\n    get attributeName(): string { return this._attributeName; }\n\n    /*** 应用修改器到属性*/\n    apply(attributes: ICharacterAttributes): void {\n        const currentValue = attributes.getCurrentAttributeValue(this._attributeName);\n        let newValue = currentValue;\n        switch (this._type) {\n            case AttributeModifierType.ADD:\n                newValue = currentValue + this._value;\n                break;\n            case AttributeModifierType.MULTIPLY:\n                newValue = currentValue * this._value;\n                break;\n            case AttributeModifierType.PERCENTAGE:\n                newValue = currentValue * (1 + this._value);\n                break;\n            case AttributeModifierType.OVERRIDE:\n                newValue = this._value;\n                break;\n        }\n        attributes.setAttributeValue(this._attributeName, newValue);\n        console.log(`Applied ${this._name}: ${this._attributeName} ${currentValue} -> ${newValue}`);\n    }\n    /*** 移除修改器效果*/\n    remove(_attributes: ICharacterAttributes): void {\n        // 这里需要恢复原始值，但由于可能有多个修改器，\n        // 实际实现中应该重新计算所有修改器\n        console.log(`Removed ${this._name} from ${this._attributeName}`);\n    }\n    /** * 更新修改器 */\n    update(deltaTime: number): boolean {\n        if (this._duration < 0) {\n            return false; // 永久修改器\n        }\n        this._remainingTime -= deltaTime;\n        return this._remainingTime <= 0;\n    }\n    /*** 设置新的值*/\n    setValue(value: number): void {\n        this._value = value;\n    }\n    /** * 获取调试信息 */\n    getDebugInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            attributeName: this._attributeName,\n            type: this._type,\n            value: this._value,\n            duration: this._duration,\n            remainingTime: this._remainingTime\n        };\n    }\n}\n"]}