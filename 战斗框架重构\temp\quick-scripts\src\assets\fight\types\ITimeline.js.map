{"version": 3, "sources": ["assets\\fight\\types\\ITimeline.ts"], "names": [], "mappings": ";;;;;AAAA;;;GAGG;;;AAgHH,qBAAqB;AACrB,IAAY,iBAyBX;AAzBD,WAAY,iBAAiB;IACzB,WAAW;IACX,sCAAiB,CAAA;IACjB,WAAW;IACX,kCAAa,CAAA;IACb,aAAa;IACb,gDAA2B,CAAA;IAC3B,eAAe;IACf,0CAAqB,CAAA;IACrB,eAAe;IACf,gDAA2B,CAAA;IAC3B,aAAa;IACb,sDAAiC,CAAA;IACjC,aAAa;IACb,8CAAyB,CAAA;IACzB,aAAa;IACb,gDAA2B,CAAA;IAC3B,WAAW;IACX,kCAAa,CAAA;IACb,WAAW;IACX,0CAAqB,CAAA;IACrB,WAAW;IACX,sCAAiB,CAAA;IACjB,YAAY;IACZ,sCAAiB,CAAA;AACrB,CAAC,EAzBW,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QAyB5B", "file": "", "sourceRoot": "/", "sourcesContent": ["/**\n * Timeline系统接口定义\n * 基于时间轴的技能效果系统，支持复杂的时序控制\n */\n\nimport { ICharacter } from \"./ICharacter\";\nimport { EventManager } from \"../systems/EventManager\";\n\n/** * Timeline接口 */\nexport interface ITimeline {\n    /** Timeline ID */\n    readonly id: string;\n    /** Timeline名称 */\n    readonly name: string;\n    /** 总持续时间（秒） */\n    readonly duration: number;\n    /** 已经运行的时间 */\n    timeElapsed: number;\n    /** Timeline节点列表 */\n    readonly nodes: ReadonlyArray<ITimelineNode>;\n    /** 施法者 */\n    readonly caster: ICharacter;\n    /** 单个目标 */\n    readonly target?: ICharacter;\n    /** 多个目标 */\n    readonly targets?: ICharacter[];\n    /** 目标位置 */\n    readonly targetPosition?: cc.Vec3;\n    /** 是否已完成 */\n    readonly isCompleted: boolean;\n    /** 是否暂停 */\n    isPaused: boolean;\n    /**\n     * 更新Timeline\n     * @param deltaTime 时间间隔\n     * @returns 是否已完成\n     */\n    update(deltaTime: number): boolean;\n    /** * 暂停Timeline */\n    pause(): void;\n    /**  * 恢复Timeline  */\n    resume(): void;\n    /**  * 停止Timeline  */\n    stop(): void;\n    /**  * 重置Timeline  */\n    reset(): void;\n    /**\n     * 跳转到指定时间点\n     * @param time 目标时间\n     */\n    seekTo(time: number): void;\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    readonly eventManager: EventManager;\n}\n\n/** * Timeline节点接口 */\nexport interface ITimelineNode {\n    /** 节点ID */\n    readonly id: string;\n    /** 触发时间点（秒） */\n    readonly triggerTime: number;\n    /** 节点事件 */\n    readonly event: ITimelineEvent;\n    /** 是否已触发 */\n    isTriggered: boolean;\n    /** 是否可重复触发 */\n    readonly repeatable: boolean;\n    /** 重复间隔（如果可重复） */\n    readonly repeatInterval?: number;\n    /** 最大重复次数（-1表示无限） */\n    readonly maxRepeats?: number;\n    /** 当前重复次数 */\n    currentRepeats: number;\n    /**\n     * 检查是否应该触发\n     * @param currentTime 当前时间\n     * @param deltaTime 时间间隔\n     * @returns 是否应该触发\n     */\n    shouldTrigger(currentTime: number, deltaTime: number): boolean;\n    /**\n     * 触发节点事件\n     * @param timeline Timeline实例\n     * @param nodeIndex 节点索引\n     */\n    trigger(timeline: ITimeline, nodeIndex: number): void;\n    /** * 重置节点状态 */\n    reset(): void;\n}\n/*** Timeline事件接口*/\nexport interface ITimelineEvent {\n    /** 事件ID */\n    readonly id: string;\n    /** 事件类型 */\n    readonly type: TimelineEventType;\n    /**\n     * 执行事件\n     * @param timeline Timeline实例\n     * @param nodeIndex 节点索引\n     * @param context 上下文信息\n     */\n    execute(timeline: ITimeline, nodeIndex: number, context?: any): void;\n    /**\n     * 播放音效（辅助方法）\n     * @param soundId 音效ID\n     */\n    playSound?(soundId: string): void;\n    /**\n     * 播放特效（辅助方法）\n     * @param effectId 特效ID\n     * @param position 位置\n     */\n    playEffect?(effectId: string, position?: cc.Vec3): void;\n}\n\n/*** Timeline事件类型枚举*/\nexport enum TimelineEventType {\n    /** 伤害事件 */\n    DAMAGE = \"damage\",\n    /** 治疗事件 */\n    HEAL = \"heal\",\n    /** 子弹发射事件 */\n    FIRE_BULLET = \"fire_bullet\",\n    /** Buff添加事件 */\n    ADD_BUFF = \"add_buff\",\n    /** Buff移除事件 */\n    REMOVE_BUFF = \"remove_buff\",\n    /** 动画播放事件 */\n    PLAY_ANIMATION = \"play_animation\",\n    /** 音效播放事件 */\n    PLAY_SOUND = \"play_sound\",\n    /** 特效播放事件 */\n    PLAY_EFFECT = \"play_effect\",\n    /** 移动事件 */\n    MOVE = \"move\",\n    /** 传送事件 */\n    TELEPORT = \"teleport\",\n    /** 召唤事件 */\n    SUMMON = \"summon\",\n    /** 自定义事件 */\n    CUSTOM = \"custom\"\n}\n/*** Timeline配置接口*/\nexport interface ITimelineConfig {\n    /** Timeline ID */\n    id: string;\n    /** Timeline名称 */\n    name: string;\n    /** 总持续时间 */\n    duration: number;\n    /** 节点配置列表 */\n    nodes: ITimelineNodeConfig[];\n    /** 是否循环 */\n    loop?: boolean;\n    /** 循环次数（-1表示无限循环） */\n    loopCount?: number;\n    /** 时间缩放 */\n    timeScale?: number;\n}\n/*** Timeline节点配置接口*/\nexport interface ITimelineNodeConfig {\n    /** 节点ID */\n    id: string;\n    /** 触发时间点 */\n    triggerTime: number;\n    /** 事件配置 */\n    event: ITimelineEventConfig;\n    /** 是否可重复触发 */\n    repeatable?: boolean;\n    /** 重复间隔 */\n    repeatInterval?: number;\n    /** 最大重复次数 */\n    maxRepeats?: number;\n}\n/*** Timeline事件配置接口*/\nexport interface ITimelineEventConfig {\n    /** 事件ID */\n    id: string;\n    /** 事件类型 */\n    type: TimelineEventType;\n    /** 事件参数 */\n    params?: any;\n    /** 条件检查函数 */\n    condition?: (timeline: ITimeline) => boolean;\n}\n\n/*** Timeline管理器接口*/\nexport interface ITimelineManager {\n    /** 所有活跃的Timeline */\n    readonly activeTimelines: ReadonlyArray<ITimeline>;\n    /**\n     * 添加Timeline\n     * @param timeline Timeline实例\n     */\n    addTimeline(timeline: ITimeline): void;\n    /**\n     * 根据配置创建并添加Timeline\n     * @param config Timeline配置\n     * @param caster 施法者\n     * @param target 目标\n     * @param targets 多个目标\n     * @param position 目标位置\n     */\n    createTimeline(\n        config: ITimelineConfig,\n        caster: ICharacter,\n        target?: ICharacter,\n        targets?: ICharacter[],\n        position?: cc.Vec3\n    ): ITimeline;\n    /**\n     * 移除Timeline\n     * @param timelineId Timeline ID\n     */\n    removeTimeline(timelineId: string): void;\n    /**\n     * 根据施法者移除Timeline\n     * @param caster 施法者\n     */\n    removeTimelinesByCaster(caster: ICharacter): void;\n    /** * 暂停所有Timeline */\n    pauseAll(): void;\n    /** * 恢复所有Timeline */\n    resumeAll(): void;\n    /** * 清除所有Timeline */\n    clearAll(): void;\n    /**\n     * 更新所有Timeline\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n    /** * 获取Timeline统计信息 */\n    getStats(): {\n        activeCount: number;\n        pausedCount: number;\n        totalExecutedEvents: number;\n    };\n}\n"]}