"use strict";
cc._RF.push(module, 'adb5eBTg+hEP5u0BLqw2tMS', 'ThunderStormSkill');
// fight/skills/ThunderStormSkill.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThunderStormSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var ITimeline_1 = require("../types/ITimeline");
var SkillName_1 = require("../types/SkillName");
var StunBuff_1 = require("../buff/StunBuff");
var BattleManager_1 = require("../systems/BattleManager");
/**
 * 雷暴术技能
 * 范围攻击技能，在指定区域召唤雷电攻击所有敌人
 */
var ThunderStormSkill = /** @class */ (function () {
    function ThunderStormSkill() {
        this._id = SkillName_1.default.thunder_storm;
        this._name = "雷暴术";
        this._description = "在目标区域召唤雷暴，对范围内所有敌人造成雷电伤害并有概率眩晕";
        this._cooldown = 8.0;
        this._remainingCooldown = 0;
        this._mpCost = 60;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.GROUND;
        this._range = 400;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_thunder_storm",
            soundId: "thunder_cast",
            effectPath: "prefabs/effects/ThunderStorm",
            areaRadius: 150,
            damage: 0,
            damageMultiplier: 2.0,
            stunChance: 0.3,
            stunDuration: 2.0,
            lightningCount: 5,
            lightningInterval: 0.3 // 闪电间隔
        };
    }
    Object.defineProperty(ThunderStormSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ThunderStormSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    ThunderStormSkill.prototype.canCastOn = function (_caster, _target) {
        // 地面目标技能，不需要特定目标
        return true;
    };
    /** 检查资源消耗 */
    ThunderStormSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    ThunderStormSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    ThunderStormSkill.prototype.cast = function (caster, target, targets, position) {
        if (!position) {
            console.warn("ThunderStormSkill requires a target position");
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " at position (" + position.x + ", " + position.y + ")");
        return true;
    };
    /** 创建技能Timeline */
    ThunderStormSkill.prototype.createTimeline = function (caster, _target, _targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 3.0, caster);
        // 0.0s: 播放施法动画
        var castNode = new Timeline_1.TimelineNode(timelineId + "_cast", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_animation", this._config.animationName), false);
        timeline.addNode(castNode);
        // 0.5s: 播放施法音效
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.5, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        // 1.0s: 开始雷暴效果
        var stormStartNode = new Timeline_1.TimelineNode(timelineId + "_storm_start", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("storm_effect", this._config.effectPath, true, position), false);
        timeline.addNode(stormStartNode);
        // 1.2s - 2.8s: 连续闪电攻击
        for (var i = 0; i < this._config.lightningCount; i++) {
            var lightningTime = 1.2 + i * this._config.lightningInterval;
            var lightningNode = new Timeline_1.TimelineNode(timelineId + "_lightning_" + i, lightningTime, new LightningStrikeEvent("lightning_" + i, caster, position, this._config.areaRadius, this.calculateDamage(caster)), false);
            timeline.addNode(lightningNode);
        }
        return timeline;
    };
    /** 计算伤害 */
    ThunderStormSkill.prototype.calculateDamage = function (caster) {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        var baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.damageMultiplier);
    };
    /** 更新技能冷却 */
    ThunderStormSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    ThunderStormSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    ThunderStormSkill.prototype.levelUp = function () {
        this._level++;
        this._mpCost = Math.max(40, this._mpCost - 3);
        this._cooldown = Math.max(5.0, this._cooldown - 0.5);
        this._range += 30;
        this._config.areaRadius += 10;
        this._config.damageMultiplier += 0.2;
        this._config.stunChance = Math.min(0.6, this._config.stunChance + 0.05);
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    ThunderStormSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            areaRadius: this._config.areaRadius,
            damageMultiplier: this._config.damageMultiplier,
            stunChance: this._config.stunChance
        };
    };
    return ThunderStormSkill;
}());
exports.ThunderStormSkill = ThunderStormSkill;
/**
 * 闪电攻击Timeline事件
 */
var LightningStrikeEvent = /** @class */ (function () {
    function LightningStrikeEvent(id, caster, position, radius, damage) {
        this._type = ITimeline_1.TimelineEventType.DAMAGE;
        this._id = id;
        this._caster = caster;
        this._position = position;
        this._radius = radius;
        this._damage = damage;
    }
    Object.defineProperty(LightningStrikeEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LightningStrikeEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    LightningStrikeEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        var e_1, _a;
        // 查找范围内的所有敌人
        var enemies = this.findEnemiesInRange();
        try {
            for (var enemies_1 = __values(enemies), enemies_1_1 = enemies_1.next(); !enemies_1_1.done; enemies_1_1 = enemies_1.next()) {
                var enemy = enemies_1_1.value;
                if (!enemy.isDead) {
                    // 造成雷电伤害
                    var damageInfo = {
                        amount: this._damage,
                        type: IDamage_1.DamageType.MAGIC,
                        source: this._caster,
                        isCritical: false,
                        element: "thunder"
                    };
                    enemy.takeDamage(damageInfo.amount, this._caster);
                    console.log("Lightning strikes " + enemy.characterName + " for " + this._damage + " thunder damage");
                    // 眩晕概率检查
                    if (Math.random() < 0.3) { // 30%概率眩晕
                        console.log(enemy.characterName + " is stunned by lightning!");
                        var stunBuff = new StunBuff_1.StunBuff(this._caster, enemy, 2.0);
                        enemy.buffManager.addBuff(stunBuff);
                    }
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (enemies_1_1 && !enemies_1_1.done && (_a = enemies_1.return)) _a.call(enemies_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放闪电特效和音效
        this.playEffect("prefabs/effects/LightningStrike", this._position);
        this.playSound("lightning_strike");
    };
    /** 查找范围内的敌人 */
    LightningStrikeEvent.prototype.findEnemiesInRange = function () {
        // 这里需要实现范围查找逻辑
        // 暂时返回空数组，实际应该通过BattleManager或场景管理器查找
        console.log("Searching for enemies in radius " + this._radius + " around position (" + this._position.x + ", " + this._position.y + ")");
        return [];
    };
    LightningStrikeEvent.prototype.playEffect = function (effectId, position) {
        console.log("Playing effect " + effectId + " at position (" + (position === null || position === void 0 ? void 0 : position.x) + ", " + (position === null || position === void 0 ? void 0 : position.y) + ")");
    };
    LightningStrikeEvent.prototype.playSound = function (soundId) {
        console.log("Playing sound " + soundId);
    };
    return LightningStrikeEvent;
}());

cc._RF.pop();