{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\SkillBase.ts"], "names": [], "mappings": ";;;;AAAA,qCAAqC;AACrC,qCAAqC;AACrC,0DAA0D;AAE1D,aAAa;AACb,wBAAwB;AACxB,iBAAiB;AACjB,uBAAuB;AACvB,iBAAiB;AACjB,yBAAyB;AACzB,eAAe;AACf,2BAA2B;AAC3B,eAAe;AACf,+BAA+B;AAC/B,IAAI;AAEJ,mBAAmB;AACnB,mBAAmB;AACnB,0CAA0C;AAC1C,qBAAqB;AACrB,yBAAyB;AACzB,2CAA2C;AAC3C,4CAA4C;AAC5C,QAAQ;AACR,mBAAmB;AACnB,8DAA8D;AAC9D,yBAAyB;AACzB,qCAAqC;AACrC,qBAAqB;AACrB,4BAA4B;AAC5B,qBAAqB;AACrB,yBAAyB;AACzB,qBAAqB;AACrB,+BAA+B;AAC/B,0BAA0B;AAC1B,6BAA6B;AAC7B,uBAAuB;AACvB,iCAAiC;AACjC,uBAAuB;AACvB,6BAA6B;AAC7B,qBAAqB;AACrB,oCAAoC;AACpC,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,wBAAwB;AACxB,kCAAkC;AAClC,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,mCAAmC;AACnC,iBAAiB;AACjB,4BAA4B;AAC5B,gBAAgB;AAChB,yBAAyB;AACzB,mBAAmB;AACnB,oDAAoD;AACpD,wBAAwB;AACxB,oCAAoC;AAEpC,oBAAoB;AACpB,wBAAwB;AACxB,2BAA2B;AAC3B,iDAAiD;AACjD,gBAAgB;AAChB,6CAA6C;AAC7C,uBAAuB;AACvB,mDAAmD;AACnD,iFAAiF;AACjF,kDAAkD;AAClD,uCAAuC;AACvC,gBAAgB;AAChB,4BAA4B;AAC5B,0DAA0D;AAC1D,4BAA4B;AAC5B,kDAAkD;AAClD,YAAY;AACZ,QAAQ;AAER,uBAAuB;AACvB,qBAAqB;AACrB,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import BuffBase from './BuffBase';\r\n// import CardBase from \"./CardBase\";\r\n// import BuffManager from '../../Controller/BuffManager';\r\n\r\n// /**冷却类型 */\r\n// export enum EFrozen {\r\n//     /**回合类型 */\r\n//     round = `round`,\r\n//     /**全局类型 */\r\n//     global = `global`,\r\n//     /**计时 */\r\n//     timeOut = `timeOut`,\r\n//     /**次数 */\r\n//     timeCount = `timeCount`,\r\n// }\r\n\r\n// declare global {\r\n//     /**冷却时间接口 */\r\n//     export interface ISkillFrozenType {\r\n//         /**冷却类型 */\r\n//         type: EFrozen;\r\n//         /**次数，计时，回合都是number 全局分为计时和次数 */\r\n//         value: number | ISkillFrozenType;\r\n//     }\r\n//     /**技能数据接口 */\r\n//     export interface ISkillDataType extends IBaseDataType {\r\n//         /**buff  列表 */\r\n//         buffData: IBuffDataType[];\r\n//         /**使用次数 */\r\n//         useCount: number;\r\n//         /**技能等级 */\r\n//         level: number;\r\n//         /**等级上限 */\r\n//         levelHeight: number;\r\n//         /**是否可以使用此技能 */\r\n//         isCanUse: boolean;\r\n//         /**是否可以升级 */\r\n//         isCanLevelUp: boolean;\r\n//         /**是否正在冷却 */\r\n//         isFrozen: boolean;\r\n//         /**冷却信息 */\r\n//         frozen: ISkillFrozenType;\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 技能控制基类\r\n//  * @description: 对于一个卡牌或角色的基类控制\r\n//  * @Date : 2020-08-12 23:29:09\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:58:43\r\n//  * @LastEditors : judu233\r\n//  */\r\n// export default class SkillBase {\r\n//     /**技能数据 */\r\n//     data: ISkillDataType;\r\n//     /**使用者 */\r\n//     useCard: CardBase;\r\n//     /**被使用者列表 */\r\n//     targetMap: Map<string, CardBase> = new Map();\r\n//     /**buffManager */\r\n//     buffManager = new BuffManager\r\n\r\n//     /**初始化技能数据 */\r\n//     initSkillData() {\r\n//         //根据data数据创建buff\r\n//         let buffTypeList = this.data.buffData;\r\n//         try {\r\n//             let buffList: BuffBase[] = [];\r\n//             //创建Buff\r\n//             for (let buffType of buffTypeList) {\r\n//                 let buffClassName: { new(): BuffBase; } = eval(buffType.type);\r\n//                 let buff = new buffClassName();\r\n//                 buffList.push(buff);\r\n//             }\r\n//             //添加到buff管理器中\r\n//             this.buffManager.initBuffManager(buffList);\r\n//         } catch (error) {\r\n//             cc.error(`技能创建buff失败，信息:${error}`);\r\n//         }\r\n//     }\r\n\r\n//     /**使用技能，子类负责实现*/\r\n//     useSkill() { }\r\n// }\r\n"]}