
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/SkillName.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aaaabQyOL1N4bKHNtZ7DUW5', 'SkillName');
// fight/types/SkillName.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var SkillName = /** @class */ (function () {
    function SkillName() {
    }
    /**火球术 */
    SkillName.player_skill_fire1 = "player_skill_fire1";
    /**治疗之光 */
    SkillName.healing_light = "healing_light";
    /**雷暴术 */
    SkillName.thunder_storm = "thunder_storm";
    /**冲锋攻击 */
    SkillName.charge_attack = "charge_attack";
    SkillName.skill1 = "skill1";
    SkillName.skill2 = "skill2";
    SkillName.skill3 = "skill3";
    SkillName.skill4 = "skill4";
    SkillName.skill5 = "skill5";
    SkillName.skill6 = "skill6";
    return SkillName;
}());
exports.default = SkillName;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXHR5cGVzXFxTa2lsbE5hbWUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtJQUFBO0lBZ0JBLENBQUM7SUFmRyxTQUFTO0lBQ08sNEJBQWtCLEdBQUcsb0JBQW9CLENBQUM7SUFDMUQsVUFBVTtJQUNNLHVCQUFhLEdBQUcsZUFBZSxDQUFDO0lBQ2hELFNBQVM7SUFDTyx1QkFBYSxHQUFHLGVBQWUsQ0FBQztJQUNoRCxVQUFVO0lBQ00sdUJBQWEsR0FBRyxlQUFlLENBQUM7SUFFaEMsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDbEIsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDbEIsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDbEIsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDbEIsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDbEIsZ0JBQU0sR0FBRyxRQUFRLENBQUM7SUFDdEMsZ0JBQUM7Q0FoQkQsQUFnQkMsSUFBQTtrQkFoQm9CLFNBQVMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBjbGFzcyBTa2lsbE5hbWUge1xyXG4gICAgLyoq54Gr55CD5pyvICovXHJcbiAgICBzdGF0aWMgcmVhZG9ubHkgcGxheWVyX3NraWxsX2ZpcmUxID0gXCJwbGF5ZXJfc2tpbGxfZmlyZTFcIjtcclxuICAgIC8qKuayu+eWl+S5i+WFiSAqL1xyXG4gICAgc3RhdGljIHJlYWRvbmx5IGhlYWxpbmdfbGlnaHQgPSBcImhlYWxpbmdfbGlnaHRcIjtcclxuICAgIC8qKumbt+aatOacryAqL1xyXG4gICAgc3RhdGljIHJlYWRvbmx5IHRodW5kZXJfc3Rvcm0gPSBcInRodW5kZXJfc3Rvcm1cIjtcclxuICAgIC8qKuWGsumUi+aUu+WHuyAqL1xyXG4gICAgc3RhdGljIHJlYWRvbmx5IGNoYXJnZV9hdHRhY2sgPSBcImNoYXJnZV9hdHRhY2tcIjtcclxuXHJcbiAgICBzdGF0aWMgcmVhZG9ubHkgc2tpbGwxID0gXCJza2lsbDFcIjtcclxuICAgIHN0YXRpYyByZWFkb25seSBza2lsbDIgPSBcInNraWxsMlwiO1xyXG4gICAgc3RhdGljIHJlYWRvbmx5IHNraWxsMyA9IFwic2tpbGwzXCI7XHJcbiAgICBzdGF0aWMgcmVhZG9ubHkgc2tpbGw0ID0gXCJza2lsbDRcIjtcclxuICAgIHN0YXRpYyByZWFkb25seSBza2lsbDUgPSBcInNraWxsNVwiO1xyXG4gICAgc3RhdGljIHJlYWRvbmx5IHNraWxsNiA9IFwic2tpbGw2XCI7XHJcbn0iXX0=