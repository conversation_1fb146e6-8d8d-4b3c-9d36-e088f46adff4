
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/IBullet.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5614bBH2MRKu5sSaQoS4jIQ', 'IBullet');
// fight/types/IBullet.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrajectoryType = exports.BulletType = void 0;
/** * 子弹类型枚举 */
var BulletType;
(function (BulletType) {
    /** 直线子弹 */
    BulletType["STRAIGHT"] = "straight";
    /** 追踪子弹 */
    BulletType["HOMING"] = "homing";
    /** 抛物线子弹 */
    BulletType["PARABOLIC"] = "parabolic";
    /** 激光 */
    BulletType["LASER"] = "laser";
    /** 范围爆炸 */
    BulletType["AREA_EXPLOSION"] = "area_explosion";
    /** 穿透子弹 */
    BulletType["PIERCING"] = "piercing";
    /** 反弹子弹 */
    BulletType["BOUNCING"] = "bouncing";
    /** 分裂子弹 */
    BulletType["SPLITTING"] = "splitting";
})(BulletType = exports.BulletType || (exports.BulletType = {}));
/*** 轨迹类型枚举*/
var TrajectoryType;
(function (TrajectoryType) {
    /** 直线轨迹 */
    TrajectoryType["LINEAR"] = "linear";
    /** 追踪轨迹 */
    TrajectoryType["HOMING"] = "homing";
    /** 抛物线轨迹 */
    TrajectoryType["PARABOLIC"] = "parabolic";
    /** 贝塞尔曲线轨迹 */
    TrajectoryType["BEZIER"] = "bezier";
    /** 螺旋轨迹 */
    TrajectoryType["SPIRAL"] = "spiral";
    /** 正弦波轨迹 */
    TrajectoryType["SINE_WAVE"] = "sine_wave";
})(TrajectoryType = exports.TrajectoryType || (exports.TrajectoryType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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