
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/CampManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4fb43ThVNFGP7OahraZx720', 'CampManager');
// card/cardgame/src/CampManager.ts

// import Base from "./Base/Base";
// import CampBase from "./Base/CampBase";
// import RoleBase from "./Base/RoleBase";
// const { ccclass, property } = cc._decorator;
// /**卡牌阵营 */
// export enum ECamp {
//     /**基类 */
//     CampBase = `CampBase`,
//     /**玩家阵营 */
//     Player = `Player`,
//     /**电脑阵营 */
//     Computer = `Computer`,
// }
// declare global {
//     /**阵营数据 */
//     export interface ICampMgrDataType {
//         /**存储阵营的数据 */
//         campData: ICampDataType,
//         cardData: ICardDataType[],
//         roleData: IRoleBaseData[],
//     }
// }
// /**
//  * @features : 阵营管理
//  * @description : 说明
//  * @Date : 2020-08-17 10:24:21
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:08:50
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class CampManager extends Base {
//     /**卡牌阵营管理信息 */
//     data: ICampMgrDataType;
//     /**阵营列表 */
//     campList: CampBase[] = [];
//     /******关于阵营的操作*********** */
//     /**初始化阵营 */
//     initAllCamp(dataList: ICampMgrDataType[]) {
//         for (let campData of dataList) {
//             let camp = new CampBase
//             camp.initCamp(campData);
//             this.campList.push(camp)
//         }
//     }
//     /**根据阵营名字获取阵营 */
//     getCampByName(camp: ECamp) { return this.campList.find(predicate => predicate.campName == camp) }
//     /**获取先手方 */
//     getPlayerCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Player); }
//     /**获取后手方 */
//     getEnemyCamp() { return this.campList.find(predicate => predicate.campName == ECamp.Computer); }
//     /**获取以玩家为首的先手方 */
//     getFirstPlayerCamp() {
//         return { firstCamp: this.getPlayerCamp(), lastCamp: this.getEnemyCamp() }
//     }
//     /**检查是否有任何一方已经全部死亡 */
//     checkDeath() {
//         return this.getPlayerCamp().isAllDeath || this.getEnemyCamp().isAllDeath;
//     }
//     checkMonsterDeath() {
//         return this.getEnemyCamp().curRole.isDeath;
//     }
//     checkPlayerCampIsDeath() {
//         return this.getPlayerCamp().isAllDeath;
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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