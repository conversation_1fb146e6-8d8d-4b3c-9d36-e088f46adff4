{"version": 3, "sources": ["assets\\card\\cardgame\\src\\RoleManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,0CAA0C;AAE1C,+CAA+C;AAE/C,mBAAmB;AACnB,sCAAsC;AACtC,QAAQ;AACR,IAAI;AAGJ,MAAM;AACN,oBAAoB;AACpB,sBAAsB;AACtB,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,cAAc;AACd,kDAAkD;AAClD,0BAA0B;AAC1B,oBAAoB;AACpB,8BAA8B;AAE9B,2CAA2C;AAC3C,iDAAiD;AACjD,yCAAyC;AACzC,uCAAuC;AACvC,+CAA+C;AAC/C,qCAAqC;AACrC,YAAY;AACZ,QAAQ;AAER,oCAAoC;AACpC,0CAA0C;AAC1C,sEAAsE;AACtE,sCAAsC;AACtC,YAAY;AACZ,QAAQ;AAER,uBAAuB;AACvB,4BAA4B;AAC5B,wDAAwD;AACxD,QAAQ;AACR,uBAAuB;AACvB,8BAA8B;AAC9B,yDAAyD;AACzD,QAAQ;AAER,wBAAwB;AACxB,mCAAmC;AACnC,uEAAuE;AACvE,QAAQ;AAER,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import RoleBase from \"./Base/RoleBase\";\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n\r\n// declare global {\r\n//     export interface IRoleManager {\r\n//     }\r\n// }\r\n\r\n\r\n// /**\r\n//  * @features : 功能\r\n//  * @description: 说明\r\n//  * @Date : 2020-08-17 10:25:03\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 13:58:24\r\n//  * @LastEditors : judu233\r\n//  */\r\n// // @ccclass\r\n// export default class RoleManager extends Base {\r\n//     data: IRoleManager;\r\n//     /**所有的角色列表 */\r\n//     roles: RoleBase[] = [];\r\n\r\n//     initRoles(datas: ICampMgrDataType) {\r\n//         for (let roleData of datas.roleData) {\r\n//             let role = new RoleBase();\r\n//             role.initRole(roleData);\r\n//             role.cardMgr.initCardList(datas)\r\n//             this.roles.push(role);\r\n//         }\r\n//     }\r\n\r\n//     deleateRole(role: RoleBase) {\r\n//         if (role && cc.isValid(role)) {\r\n//             cc.log(`删除:roleName:${role.roleName}, gui:${role.GID}`)\r\n//             this.roles.delete(role)\r\n//         }\r\n//     }\r\n\r\n//     /**检查所有角色是否死亡 */\r\n//     checkCardAllDeath() {\r\n//         return this.roles.every(role => role.isDeath)\r\n//     }\r\n//     /**检查所有角色是否活着 */\r\n//     checkCardAllSurvive() {\r\n//         return this.roles.every(role => !role.isDeath)\r\n//     }\r\n\r\n//     /**获取1位角色的随机卡片 */\r\n//     getRoleCard(count: number) {\r\n//         return this.roles[0]?.cardMgr.getSurviveCardForRandom(count)\r\n//     }\r\n\r\n// }\r\n"]}