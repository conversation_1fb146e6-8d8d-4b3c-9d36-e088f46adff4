{"version": 3, "sources": ["assets\\fight\\systems\\TimelineManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAgD;AAChD,kDAA6C;AAG7C,+CAA8C;AAE9C,oBAAoB;AACpB;IAOI;QANQ,qBAAgB,GAA2B,IAAI,GAAG,EAAE,CAAC;QACrD,qBAAgB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAE1C,yBAAoB,GAAW,CAAC,CAAC;QACjC,gBAAW,GAAY,KAAK,CAAC;QAC7B,qBAAgB,GAAa,EAAE,CAAC;QAEpC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACrE,CAAC;IAED,sBAAI,4CAAe;QADnB,wBAAwB;aACxB;YACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QACtD,CAAC;;;OAAA;IACD,mBAAmB;IACnB,qCAAW,GAAX,UAAY,QAAmB;QAC3B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,cAAY,QAAQ,CAAC,EAAE,kCAA+B,CAAC,CAAC;SACxE;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjD,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,oBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrF,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,oBAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,CAAC,cAAY,QAAQ,CAAC,EAAE,sBAAmB,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;IACpE,CAAC;IACD,wBAAwB;IACxB,wCAAc,GAAd,UAAe,MAAuB,EAAE,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;;QACvH,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;;YAC1G,KAAyB,IAAA,KAAA,SAAA,MAAM,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAAlC,IAAM,UAAU,WAAA;gBACjB,6CAA6C;gBAC7C,OAAO,CAAC,GAAG,CAAC,6BAA2B,UAAU,CAAC,EAAI,CAAC,CAAC;aAC3D;;;;;;;;;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,QAAQ,CAAC;IACpB,CAAC;IACD,mBAAmB;IACnB,wCAAc,GAAd,UAAe,UAAkB;QAC7B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,eAAe;YACf,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,OAAO;SACV;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACtF,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClF,SAAS;YACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,cAAY,UAAU,0BAAuB,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IACD,wBAAwB;IACxB,iDAAuB,GAAvB,UAAwB,MAAkB;;QACtC,IAAM,iBAAiB,GAAa,EAAE,CAAC;;YACvC,KAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;gBAAzC,IAAA,KAAA,mBAAc,EAAb,EAAE,QAAA,EAAE,QAAQ,QAAA;gBACpB,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC5B,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC9B;aACJ;;;;;;;;;;YACD,KAAiB,IAAA,sBAAA,SAAA,iBAAiB,CAAA,oDAAA,mFAAE;gBAA/B,IAAM,EAAE,8BAAA;gBACT,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAC,aAAW,iBAAiB,CAAC,MAAM,8BAAyB,MAAM,CAAC,aAAe,CAAC,CAAC;IACpG,CAAC;IACD,qBAAqB;IACrB,kCAAQ,GAAR;;;YACI,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAlD,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBACpB,QAAQ,CAAC,KAAK,EAAE,CAAC;oBACjB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;iBAC1C;aACJ;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,eAAY,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,kBAAkB,CAAC,CAAC;IAC3D,CAAC;IACD,qBAAqB;IACrB,mCAAS,GAAT;;;YACI,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAlD,IAAM,QAAQ,WAAA;gBACf,IAAI,QAAQ,CAAC,QAAQ,EAAE;oBACnB,QAAQ,CAAC,MAAM,EAAE,CAAC;oBAClB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;iBAC7C;aACJ;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,mBAAmB,CAAC,CAAC;IAC5D,CAAC;IACD,mBAAmB;IACnB,kCAAQ,GAAR;;QACI,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;;YAC7D,KAAiB,IAAA,gBAAA,SAAA,WAAW,CAAA,wCAAA,iEAAE;gBAAzB,IAAM,EAAE,wBAAA;gBACT,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;;;;;;;;;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,mBAAmB,CAAC,CAAC;IAC5D,CAAC;IACD,qBAAqB;IACrB,gCAAM,GAAN,UAAO,SAAiB;;QACpB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,CAAC,EAAE;YAClC,OAAO;SACV;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAM,kBAAkB,GAAa,EAAE,CAAC;;YACxC,eAAe;YACf,KAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;gBAAzC,IAAA,KAAA,mBAAc,EAAb,EAAE,QAAA,EAAE,QAAQ,QAAA;gBACpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBACpB,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC/C,IAAI,WAAW,EAAE;wBACb,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;qBAC/B;iBACJ;aACJ;;;;;;;;;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;;YACzB,iBAAiB;YACjB,KAAiB,IAAA,uBAAA,SAAA,kBAAkB,CAAA,sDAAA,sFAAE;gBAAhC,IAAM,EAAE,+BAAA;gBACT,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;;;;;;;;;QACD,kBAAkB;QAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;;gBAClC,KAAiB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;oBAAnC,IAAM,EAAE,WAAA;oBACT,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;iBAC3B;;;;;;;;;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;SACpC;IACL,CAAC;IACD,uBAAuB;IACvB,kCAAQ,GAAR;QACI,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACvC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACvC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;SACjD,CAAC;IACN,CAAC;IACD,uBAAuB;IACvB,qCAAW,GAAX,UAAY,UAAkB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACzD,CAAC;IACD,wBAAwB;IACxB,8CAAoB,GAApB,UAAqB,MAAkB;;QACnC,IAAM,SAAS,GAAgB,EAAE,CAAC;;YAClC,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAAlD,IAAM,QAAQ,WAAA;gBACf,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC5B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC5B;aACJ;;;;;;;;;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IACD,qBAAqB;IACrB,uCAAa,GAAb,UAAc,UAAkB;QAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAChC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,cAAY,UAAU,YAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,cAAc,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SACpE;IACL,CAAC;IACD,uBAAuB;IACvB,wCAAc,GAAd,UAAe,UAAkB;QAC7B,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;YAC/B,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,cAAY,UAAU,aAAU,CAAC,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IACD,qBAAqB;IACb,6CAAmB,GAA3B,UAA4B,KAAU;QAClC,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAqB,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,cAAY,QAAQ,CAAC,EAAE,eAAY,CAAC,CAAC;QACjD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,iBAAiB,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;QACpE,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;IACD,qBAAqB;IACb,2CAAiB,GAAzB,UAA0B,KAAU;QAChC,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAqB,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,cAAY,QAAQ,CAAC,EAAE,aAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;QAClE,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;IACD,mBAAmB;IACX,yCAAe,GAAvB,UAAwB,UAAkB;QACtC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SACnC;IACL,CAAC;IAED,sBAAI,yCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACD,aAAa;IACb,sCAAY,GAAZ;;;QACI,IAAM,SAAS,GAAG,EAAe,CAAA;;YACjC,KAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;gBAAzC,IAAA,KAAA,mBAAc,EAAb,EAAE,QAAA,EAAE,QAAQ,QAAA;gBACpB,SAAS,CAAC,EAAE,CAAC,GAAG;oBACZ,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;oBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;oBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM;oBAChC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,aAAa;oBACrC,MAAM,EAAE,OAAA,QAAQ,CAAC,MAAM,0CAAE,aAAa,KAAI,IAAI;iBACjD,CAAC;aACL;;;;;;;;;QACD,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,SAAS,WAAA;YACT,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;SAChD,CAAC;IACN,CAAC;IACD,aAAa;IACb,wCAAc,GAAd;QACI,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IACpE,CAAC;IACD,YAAY;IACZ,iCAAO,GAAP;QACI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACL,sBAAC;AAAD,CAtOA,AAsOC,IAAA;AAtOY,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Timeline } from \"../timeline/Timeline\";\nimport FightEvent from \"../types/FightEvent\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { ITimelineManager, ITimeline, ITimelineConfig } from \"../types/ITimeline\";\nimport { EventManager } from \"./EventManager\";\n\n/*** Timeline管理器实现*/\nexport class TimelineManager implements ITimelineManager {\n    private _activeTimelines: Map<string, ITimeline> = new Map();\n    private _pausedTimelines: Set<string> = new Set();\n    private _eventManager: EventManager;\n    private _totalExecutedEvents: number = 0;\n    private _isUpdating: boolean = false;\n    private _pendingRemovals: string[] = [];\n    constructor() {\n        this._eventManager = EventManager.createLocal(\"TimelineManager\");\n    }\n    /** * 获取所有活跃的Timeline */\n    get activeTimelines(): ReadonlyArray<ITimeline> {\n        return Array.from(this._activeTimelines.values());\n    }\n    /** * 添加Timeline */\n    addTimeline(timeline: ITimeline): void {\n        if (this._activeTimelines.has(timeline.id)) {\n            console.warn(`Timeline ${timeline.id} already exists, replacing...`);\n        }\n        this._activeTimelines.set(timeline.id, timeline);\n        timeline.eventManager.on(FightEvent.completedT, this.onTimelineCompleted.bind(this));\n        timeline.eventManager.on(FightEvent.stoppedT, this.onTimelineStopped.bind(this));\n        console.log(`Timeline ${timeline.id} added to manager`);\n        this._eventManager.emit(FightEvent.timelineAdded, { timeline });\n    }\n    /*** 根据配置创建并添加Timeline*/\n    createTimeline(config: ITimelineConfig, caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {\n        const timeline = new Timeline(config.id, config.name, config.duration, caster, target, targets, position);\n        for (const nodeConfig of config.nodes) {\n            // 这里应该根据nodeConfig创建具体的TimelineNode， 现在先简化处理\n            console.log(`Creating timeline node: ${nodeConfig.id}`);\n        }\n        this.addTimeline(timeline);\n        return timeline;\n    }\n    /** * 移除Timeline */\n    removeTimeline(timelineId: string): void {\n        if (this._isUpdating) {\n            // 如果正在更新中，延迟移除\n            this._pendingRemovals.push(timelineId);\n            return;\n        }\n        const timeline = this._activeTimelines.get(timelineId);\n        if (timeline) {\n            timeline.stop();\n            timeline.eventManager.off(FightEvent.completedT, this.onTimelineCompleted.bind(this));\n            timeline.eventManager.off(FightEvent.stoppedT, this.onTimelineStopped.bind(this));\n            // 从映射中移除\n            this._activeTimelines.delete(timelineId);\n            this._pausedTimelines.delete(timelineId);\n            console.log(`Timeline ${timelineId} removed from manager`);\n            this._eventManager.emit(FightEvent.timelineRemoved, { timeline });\n        }\n    }\n    /** * 根据施法者移除Timeline */\n    removeTimelinesByCaster(caster: ICharacter): void {\n        const timelinesToRemove: string[] = [];\n        for (const [id, timeline] of this._activeTimelines) {\n            if (timeline.caster === caster) {\n                timelinesToRemove.push(id);\n            }\n        }\n        for (const id of timelinesToRemove) {\n            this.removeTimeline(id);\n        }\n        console.log(`Removed ${timelinesToRemove.length} timelines for caster ${caster.characterName}`);\n    }\n    /** * 暂停所有Timeline */\n    pauseAll(): void {\n        for (const timeline of this._activeTimelines.values()) {\n            if (!timeline.isPaused) {\n                timeline.pause();\n                this._pausedTimelines.add(timeline.id);\n            }\n        }\n        console.log(`Paused ${this._pausedTimelines.size} timelines`);\n        this._eventManager.emit(FightEvent.allTimelinesPaused);\n    }\n    /** * 恢复所有Timeline */\n    resumeAll(): void {\n        for (const timeline of this._activeTimelines.values()) {\n            if (timeline.isPaused) {\n                timeline.resume();\n                this._pausedTimelines.delete(timeline.id);\n            }\n        }\n        console.log(`Resumed all timelines`);\n        this._eventManager.emit(FightEvent.allTimelinesResumed);\n    }\n    /*** 清除所有Timeline*/\n    clearAll(): void {\n        const timelineIds = Array.from(this._activeTimelines.keys());\n        for (const id of timelineIds) {\n            this.removeTimeline(id);\n        }\n        this._pausedTimelines.clear();\n        this._totalExecutedEvents = 0;\n        console.log(`Cleared all timelines`);\n        this._eventManager.emit(FightEvent.allTimelinesCleared);\n    }\n    /** * 更新所有Timeline */\n    update(deltaTime: number): void {\n        if (this._activeTimelines.size === 0) {\n            return;\n        }\n        this._isUpdating = true;\n        const completedTimelines: string[] = [];\n        // 更新所有Timeline\n        for (const [id, timeline] of this._activeTimelines) {\n            if (!timeline.isPaused) {\n                const isCompleted = timeline.update(deltaTime);\n                if (isCompleted) {\n                    completedTimelines.push(id);\n                }\n            }\n        }\n        this._isUpdating = false;\n        // 移除已完成的Timeline\n        for (const id of completedTimelines) {\n            this.removeTimeline(id);\n        }\n        // 处理延迟移除的Timeline\n        if (this._pendingRemovals.length > 0) {\n            for (const id of this._pendingRemovals) {\n                this.removeTimeline(id);\n            }\n            this._pendingRemovals.length = 0;\n        }\n    }\n    /** * 获取Timeline统计信息 */\n    getStats(): { activeCount: number; pausedCount: number; totalExecutedEvents: number } {\n        return {\n            activeCount: this._activeTimelines.size,\n            pausedCount: this._pausedTimelines.size,\n            totalExecutedEvents: this._totalExecutedEvents\n        };\n    }\n    /** * 根据ID获取Timeline */\n    getTimeline(timelineId: string): ITimeline | null {\n        return this._activeTimelines.get(timelineId) || null;\n    }\n    /*** 根据施法者获取Timeline列表*/\n    getTimelinesByCaster(caster: ICharacter): ITimeline[] {\n        const timelines: ITimeline[] = [];\n        for (const timeline of this._activeTimelines.values()) {\n            if (timeline.caster === caster) {\n                timelines.push(timeline);\n            }\n        }\n        return timelines;\n    }\n    /** * 暂停指定Timeline */\n    pauseTimeline(timelineId: string): void {\n        const timeline = this._activeTimelines.get(timelineId);\n        if (timeline && !timeline.isPaused) {\n            timeline.pause();\n            this._pausedTimelines.add(timelineId);\n            console.log(`Timeline ${timelineId} paused`);\n            this._eventManager.emit(FightEvent.timelinePaused, { timeline });\n        }\n    }\n    /**  * 恢复指定Timeline  */\n    resumeTimeline(timelineId: string): void {\n        const timeline = this._activeTimelines.get(timelineId);\n        if (timeline && timeline.isPaused) {\n            timeline.resume();\n            this._pausedTimelines.delete(timelineId);\n            console.log(`Timeline ${timelineId} resumed`);\n            this._eventManager.emit(FightEvent.timelineResumed, { timeline });\n        }\n    }\n    /*** Timeline完成时的回调*/\n    private onTimelineCompleted(event: any): void {\n        const timeline = event.timeline as ITimeline;\n        console.log(`Timeline ${timeline.id} completed`);\n        this._totalExecutedEvents++;\n        this._eventManager.emit(FightEvent.timelineCompleted, { timeline });\n        // 延迟移除，避免在更新过程中修改集合\n        this.scheduleRemoval(timeline.id);\n    }\n    /*** Timeline停止时的回调*/\n    private onTimelineStopped(event: any): void {\n        const timeline = event.timeline as ITimeline;\n        console.log(`Timeline ${timeline.id} stopped`);\n        this._eventManager.emit(FightEvent.timelineStopped, { timeline });\n        // 延迟移除，避免在更新过程中修改集合\n        this.scheduleRemoval(timeline.id);\n    }\n    /*** 安排移除Timeline*/\n    private scheduleRemoval(timelineId: string): void {\n        if (this._isUpdating) {\n            this._pendingRemovals.push(timelineId);\n        } else {\n            this.removeTimeline(timelineId);\n        }\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n    /*** 获取调试信息*/\n    getDebugInfo() {\n        const timelines = {} as ITimeline\n        for (const [id, timeline] of this._activeTimelines) {\n            timelines[id] = {\n                name: timeline.name,\n                duration: timeline.duration,\n                timeElapsed: timeline.timeElapsed,\n                isCompleted: timeline.isCompleted,\n                isPaused: timeline.isPaused,\n                nodeCount: timeline.nodes.length,\n                caster: timeline.caster.characterName,\n                target: timeline.target?.characterName || null\n            };\n        }\n        return {\n            stats: this.getStats(),\n            timelines,\n            pendingRemovals: this._pendingRemovals.length\n        };\n    }\n    /*** 打印调试信息*/\n    printDebugInfo(): void {\n        console.log(\"TimelineManager Debug Info:\", this.getDebugInfo());\n    }\n    /*** 清理管理器*/\n    cleanup(): void {\n        this.clearAll();\n        this._eventManager.cleanup();\n    }\n}\n"]}