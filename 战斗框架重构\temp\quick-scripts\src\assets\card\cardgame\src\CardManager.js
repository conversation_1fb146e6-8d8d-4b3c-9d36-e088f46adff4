"use strict";
cc._RF.push(module, 'd5d7dTDjNRC6a7VfP8NoQ/B', 'CardManager');
// card/cardgame/src/CardManager.ts

// import Base from "./Base/Base";
// import CardBase from "./Base/CardBase";
// const { ccclass, property } = cc._decorator;
// declare global {
//     /**卡牌或角色的基础数据 */
//     export interface ICardMgr extends IBaseDataType {
//     }
// }
// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class CardManager extends Base {
//     /**存储卡牌的数据 */
//     data: ICardMgr;
//     /**当前所有卡牌的信息 */
//     cardList: CardBase[] = [];
//     /**卡片的上限 */
//     @Base.ViewLinked
//     maxCard: number;
//     /***************和卡牌相关的接口*************** */
//     /**
//      * 初始化卡牌列表 
//      * @param initData 卡牌的初始化数据
//      */
//     initCardList(data: ICampMgrDataType) {
//         let initData = data.cardData
//         let cardLis = initData.map(card => new CardBase)
//         for (let [index, card] of Object.entries(cardLis)) {
//             card.data = initData[index];
//             this.cardList.push(card);
//             card.initCard();
//         }
//     }
//     addCard(card: ICardDataType) {
//         let newCard = new CardBase;
//         newCard.data = card;
//         this.cardList.push(newCard);
//         newCard.initCard();
//         return card
//     }
//     deletCard(cardId: string) {
//         let card = this.cardList.find(card => card.data.id === cardId);
//         if (card) {
//             this.cardList.splice(this.cardList.indexOf(card), 1);
//             if (this.cardList.includes(card)) {
//                 debugger
//             }
//             card.unscheduleAllCallbacks()
//             return true;
//         }
//         return false;
//     }
//     /**更新卡牌所有数据的视图  */
//     refreshCardView(card: CardBase) {
//         // card.refreshView();
//     }
//     /**获取卡牌列表的所有数据 */
//     getCardListData() {
//         let cardData: ICampDataType[] = [];
//         // this.cardList.forEach(card => cardData.push(card.data))
//         return cardData;
//     }
//     /**************** 所有卡牌列表 相关的接口 ***********************/
//     /**
//      * 根据卡牌名字从存储列表中返回卡牌
//      * @param name 要获取卡牌的名字
//      */
//     getCardByName(name: string) {
//         const card = this.cardList.find(c => c.cardName == name);
//         if (card) return card
//         cc.warn(`[${this.className}]根据名字获取卡牌${name}失败，请检查卡牌名字和卡牌列表！`);
//     }
//     /**
//     * 根据卡牌id从存储列表中返回卡牌
//     * @param name 要获取卡牌的名字
//     */
//     getCardById(id: string) {
//         const card = this.cardList.find(c => c.id == id);
//         if (card) return card
//         cc.warn(`[${this.className}]根据id获取卡牌${id}失败，请检查卡牌id和卡牌列表！`);
//     }
//     /*************** 存活卡牌 相关的接口 *******************/
//     /**根据名字从存活列表返回卡牌 */
//     getSurviveCardByName(name: string) { return this.cardList.find(card => card.cardName == name && !card.isDeath); }
//     /**根据id从存活列表返回卡牌 */
//     getSurviveCardById(id: string) { return this.cardList.find(card => card.id == id && !card.isDeath); }
//     /**根据名字返回死亡列表的卡牌 */
//     getDeathCardByName(name: string) { return this.cardList.find(card => card.cardName == name && card.isDeath); }
//     /** 根据id从死亡列表中获取卡牌*/
//     getDeathCardById(id: string) { return this.cardList.find(card => card.id == id && card.isDeath); }
//     // /**
//     //  * 从存活列表中删除卡牌，并存入死亡卡牌，且改变卡牌的状态
//     //  * @param name 要删除存活卡牌的名字
//     //  */
//     // delSurviveCardByName(name: string) {
//     //     if (this.surviveMap.delete(name)) {
//     //         let card = this.cardMap.get(name);
//     //         this.deathMap.set(name, card);
//     //         card.death = true;
//     //         // switch (this.camp) {
//     //         //     case ECamp.Player:
//     //         //         EM.broadcast(EM.keys.PlayerCardDeath);
//     //         //         break;
//     //         //     case ECamp.Computer:
//     //         //         EM.broadcast(EM.keys.ComputerCardDeath);
//     //         //         break;
//     //         //     default:
//     //         //         cc.warn(`[${this.className}删除存活卡牌，发送事件失败，触发事件阵营：${this.camp}]`);
//     //         // }
//     //     } else cc.warn(`[${this.className}]删除存活卡牌${name}失败，请检查卡牌名字和卡牌列表`);
//     // }
//     /**
//      * 从存活列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)
//      * @param count 要随机获取的数量
//      */
//     getSurviveCardForRandom(count: number = 1) {
//         let cardList = this.cardList.filter(card => !card.isDeath)
//         let resultList: CardBase[] = [];
//         for (let i = 0; i < count && cardList.length >= 1; i++) {
//             let randomIndex = Math.floor(Math.random() * cardList.length);
//             resultList.push(cardList.splice(randomIndex, 1)[0]);
//         }
//         return resultList;
//     }
//     getSurviveOneCard() {
//         let cardList = this.getSurviveCardForRandom(1);
//         if (cardList.length != 0) {
//             return cardList[0]
//         }
//     }
//     /*************** 死亡列表 相关的接口 ***************** */
//     // /**
//     //  * 从死亡列表中删除卡牌，并存入存活列表,且改变卡牌的状态
//     //  * @param name 要删除死亡卡牌的名字
//     //  */
//     // delDeathCardByName(name: string) {
//     //     if (this.deathMap.delete(name)) {
//     //         let card = this.cardMap.get(name);
//     //         this.surviveMap.set(name, card);
//     //         card.death = false;
//     //         card.hp = card.hpUp;
//     //         // switch (this.camp) {
//     //         //     case ECamp.Player:
//     //         //         EM.broadcast(EM.keys.PlayerCardResurrection);
//     //         //         break;
//     //         //     case ECamp.Computer:
//     //         //         EM.broadcast(EM.keys.ComputerCardResurrection);
//     //         //         break;
//     //         //     default:
//     //         //         cc.warn(`[${this.className}删除死亡卡牌，发送事件失败，触发事件阵营：${this.camp}]`);
//     //         // }
//     //     } else cc.warn(`[${this.className}]删除死亡卡牌${name}失败，请检查卡牌名字和卡牌列表`);
//     // }
//     /**
//     * 从死亡列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)
//     * @param count 要随机获取的数量
//     */
//     getDeathCardForRandom(count: number = 1) {
//         let cardList = this.cardList.filter(card => !card.isDeath)
//         let resultList: CardBase[] = [];
//         for (let i = 0; i < count && cardList.length >= 1; i++) {
//             let randomIndex = Math.floor(Math.random() * cardList.length);
//             resultList.push(cardList.splice(randomIndex, 1)[0]);
//         }
//         return resultList;
//     }
// }

cc._RF.pop();