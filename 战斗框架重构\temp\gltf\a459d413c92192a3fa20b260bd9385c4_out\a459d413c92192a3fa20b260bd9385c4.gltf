{"asset": {"generator": "FBX2glTF", "version": "2.0"}, "scene": 0, "buffers": [{"byteLength": 161028, "uri": "buffer.bin"}], "bufferViews": [{"buffer": 0, "byteLength": 12288, "byteOffset": 0, "target": 34963}, {"buffer": 0, "byteLength": 13860, "byteOffset": 12288, "target": 34962}, {"buffer": 0, "byteLength": 13860, "byteOffset": 26148, "target": 34962}, {"buffer": 0, "byteLength": 9240, "byteOffset": 40008, "target": 34962}, {"buffer": 0, "byteLength": 1200, "byteOffset": 49248, "target": 34963}, {"buffer": 0, "byteLength": 1452, "byteOffset": 50448, "target": 34962}, {"buffer": 0, "byteLength": 1452, "byteOffset": 51900, "target": 34962}, {"buffer": 0, "byteLength": 968, "byteOffset": 53352, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 54320, "target": 34963}, {"buffer": 0, "byteLength": 1548, "byteOffset": 54704, "target": 34962}, {"buffer": 0, "byteLength": 1548, "byteOffset": 56252, "target": 34962}, {"buffer": 0, "byteLength": 1032, "byteOffset": 57800, "target": 34962}, {"buffer": 0, "byteLength": 12288, "byteOffset": 58832, "target": 34963}, {"buffer": 0, "byteLength": 13068, "byteOffset": 71120, "target": 34962}, {"buffer": 0, "byteLength": 13068, "byteOffset": 84188, "target": 34962}, {"buffer": 0, "byteLength": 8712, "byteOffset": 97256, "target": 34962}, {"buffer": 0, "byteLength": 12288, "byteOffset": 105968, "target": 34963}, {"buffer": 0, "byteLength": 13068, "byteOffset": 118256, "target": 34962}, {"buffer": 0, "byteLength": 13068, "byteOffset": 131324, "target": 34962}, {"buffer": 0, "byteLength": 8712, "byteOffset": 144392, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 153104, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 153116, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 153164, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 153212, "target": 34962}, {"buffer": 0, "byteLength": 768, "byteOffset": 153244, "target": 34963}, {"buffer": 0, "byteLength": 2316, "byteOffset": 154012, "target": 34962}, {"buffer": 0, "byteLength": 2316, "byteOffset": 156328, "target": 34962}, {"buffer": 0, "byteLength": 1544, "byteOffset": 158644, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 160188, "target": 34963}, {"buffer": 0, "byteLength": 288, "byteOffset": 160260, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 160548, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 160836, "target": 34962}], "scenes": [{"name": "Root Scene", "nodes": [0]}], "accessors": [{"componentType": 5123, "type": "SCALAR", "count": 6144, "bufferView": 0, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1155, "bufferView": 1, "byteOffset": 0, "min": [-0.5, -1.0, -0.5], "max": [0.5, 1.0, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 1155, "bufferView": 2, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1155, "bufferView": 3, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 600, "bufferView": 4, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 121, "bufferView": 5, "byteOffset": 0, "min": [-5.0, 0.0, -5.0], "max": [5.0, 0.0, 5.0]}, {"componentType": 5126, "type": "VEC3", "count": 121, "bufferView": 6, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 121, "bufferView": 7, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 192, "bufferView": 8, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 129, "bufferView": 9, "byteOffset": 0, "min": [-0.5, -0.5, -0.499999940395355], "max": [0.5, 0.5, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 129, "bufferView": 10, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 129, "bufferView": 11, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6144, "bufferView": 12, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1089, "bufferView": 13, "byteOffset": 0, "min": [-0.5, -0.0999999940395355, -0.5], "max": [0.5, 0.0999999940395355, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 1089, "bufferView": 14, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1089, "bufferView": 15, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6144, "bufferView": 16, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1089, "bufferView": 17, "byteOffset": 0, "min": [-0.5, -0.5, -0.5], "max": [0.5, 0.5, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 1089, "bufferView": 18, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1089, "bufferView": 19, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6, "bufferView": 20, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 4, "bufferView": 21, "byteOffset": 0, "min": [-0.5, -0.5, 0.0], "max": [0.5, 0.5, 0.0]}, {"componentType": 5126, "type": "VEC3", "count": 4, "bufferView": 22, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4, "bufferView": 23, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 384, "bufferView": 24, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 193, "bufferView": 25, "byteOffset": 0, "min": [-0.5, -1.0, -0.5], "max": [0.5, 1.0, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 193, "bufferView": 26, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 193, "bufferView": 27, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 36, "bufferView": 28, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 24, "bufferView": 29, "byteOffset": 0, "min": [-0.5, -0.5, -0.5], "max": [0.5, 0.5, 0.5]}, {"componentType": 5126, "type": "VEC3", "count": 24, "bufferView": 30, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 24, "bufferView": 31, "byteOffset": 0}], "samplers": [{}], "materials": [{"name": "DefaultMaterial", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.5, 0.5, 0.5, 1.0], "metallicFactor": 0.200000002980232, "roughnessFactor": 0.800000011920929}}], "meshes": [{"name": "capsule", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 2, "POSITION": 1, "TEXCOORD_0": 3}, "indices": 0}]}, {"name": "plane", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 6, "POSITION": 5, "TEXCOORD_0": 7}, "indices": 4}]}, {"name": "cone", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 10, "POSITION": 9, "TEXCOORD_0": 11}, "indices": 8}]}, {"name": "torus", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 14, "POSITION": 13, "TEXCOORD_0": 15}, "indices": 12}]}, {"name": "sphere", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 18, "POSITION": 17, "TEXCOORD_0": 19}, "indices": 16}]}, {"name": "quad", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 22, "POSITION": 21, "TEXCOORD_0": 23}, "indices": 20}]}, {"name": "cylinder", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 26, "POSITION": 25, "TEXCOORD_0": 27}, "indices": 24}]}, {"name": "box", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 30, "POSITION": 29, "TEXCOORD_0": 31}, "indices": 28}]}], "nodes": [{"name": "RootNode", "translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "children": [1, 2, 3, 4, 5, 6, 7, 8]}, {"name": "capsule", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 0}, {"name": "plane", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 1}, {"name": "cone", "translation": [0.0, 0.0, 0.0], "rotation": [-0.707106828689575, 0.0, 0.0, 0.707106709480286], "scale": [100.0, 100.0, 100.0], "mesh": 2}, {"name": "torus", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 3}, {"name": "sphere", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 4}, {"name": "quad", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 5}, {"name": "cylinder", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 6}, {"name": "box", "translation": [0.0, 0.0, 0.0], "rotation": [8.14603353660459e-08, 0.0, 0.0, 1.0], "scale": [100.0, 100.0, 100.0], "mesh": 7}]}