{"version": 3, "sources": ["assets\\card\\cardgame\\src\\SkillManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,4CAA4C;AAE5C,+CAA+C;AAE/C,mBAAmB;AACnB,uCAAuC;AACvC,QAAQ;AACR,IAAI;AAGJ,MAAM;AACN,oBAAoB;AACpB,sBAAsB;AACtB,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,WAAW;AACX,mDAAmD;AACnD,uBAAuB;AACvB,yDAAyD;AAEzD,oBAAoB;AACpB,6BAA6B;AAC7B,QAAQ;AAER,wBAAwB;AACxB,qCAAqC;AACrC,gCAAgC;AAChC,gBAAgB;AAChB,oEAAoE;AACpE,4CAA4C;AAC5C,qBAAqB;AACrB,kDAAkD;AAClD,qCAAqC;AACrC,4BAA4B;AAC5B,yDAAyD;AACzD,YAAY;AACZ,wBAAwB;AACxB,QAAQ;AACR,iBAAiB;AACjB,qCAAqC;AAErC,iBAAiB;AACjB,yCAAyC;AAEzC,iBAAiB;AACjB,2CAA2C;AAE3C,iBAAiB;AACjB,wCAAwC;AACxC,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import SkillBase from \"./Base/SkillBase\";\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n\r\n// declare global {\r\n//     export interface ISkillManager {\r\n//     }\r\n// }\r\n\r\n\r\n// /**\r\n//  * @features : 功能\r\n//  * @description: 说明\r\n//  * @Date : 2020-08-17 10:25:03\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 13:58:24\r\n//  * @LastEditors : judu233\r\n//  */\r\n// @ccclass\r\n// export default class SkillManager extends Base {\r\n//     /**所有已加载的技能列表 */\r\n//     skillMap: Map<string, ISkillDataType> = new Map();\r\n\r\n//     /**加载技能数据表 */\r\n//     loadSkillTableData() {\r\n//     }\r\n\r\n//     /**根据id获取技能表数据 */\r\n//     getSkillDataById(id: string) {\r\n//         let skill: SkillBase;\r\n//         try {\r\n//             let skillClassName: { new(): SkillBase; } = eval(id);\r\n//             skill = new skillClassName();\r\n//             //设置数据\r\n//             skill.data = this.skillMap.get(id);\r\n//             skill.initSkillData();\r\n//         } catch (error) {\r\n//             cc.error(`创建技能失败，id：${id},err: ${error}`);\r\n//         }\r\n//         return skill;\r\n//     }\r\n//     /**使用技能 */\r\n//     useSkill(skill: SkillBase) { }\r\n\r\n//     /**升级技能 */\r\n//     levelUpSkill(skill: SkillBase) { }\r\n\r\n//     /**降级技能 */\r\n//     downgradeSkill(skill: SkillBase) { }\r\n\r\n//     /**冻结技能 */\r\n//     frozenSkill(skill: SkillBase) { }\r\n// }\r\n"]}