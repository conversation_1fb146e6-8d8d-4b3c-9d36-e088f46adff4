{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\TimeModel.ts"], "names": [], "mappings": ";;;;AAAA,0DAA0D;AAE1D,8CAA8C;AAC9C,2BAA2B;AAC3B,uBAAuB;AACvB,mCAAmC;AACnC,sBAAsB;AACtB,uCAAuC;AACvC,yBAAyB;AACzB,wCAAwC;AACxC,yBAAyB;AACzB,4CAA4C;AAC5C,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import { desLoadDataProxy } from \"@judu233/cc-vm-core\";\r\n\r\n// @desLoadDataProxy('TimeModel', 'TimeModel')\r\n// export class TimeModel {\r\n//     /**是否是第一次进入游戏 */\r\n//     static isFirstInGame = true;\r\n//     /*第一次进入的游戏时间 */\r\n//     static firstInDate: Date = null;\r\n//     /**是否是当天第一次进入游戏 */\r\n//     static isTodayFirstInGame = true;\r\n//     /**当天第一次进入的游戏时间 */\r\n//     static todayFirstInDate: Date = null;\r\n// }"]}