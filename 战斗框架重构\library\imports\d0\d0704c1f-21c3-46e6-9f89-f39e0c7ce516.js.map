{"version": 3, "sources": ["assets\\fight\\timeline\\Timeline.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,kDAA6C;AAI7C,kBAAkB;AAClB;IAeI,kBAAY,EAAU,EAAE,IAAY,EAAE,QAAgB,EAAE,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,cAAwB;QAXzI,iBAAY,GAAW,CAAC,CAAC;QACzB,WAAM,GAAmB,EAAE,CAAC;QAK5B,iBAAY,GAAY,KAAK,CAAC;QAC9B,cAAS,GAAY,KAAK,CAAC;QAK/B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,cAAY,EAAI,CAAC,CAAC;IACpE,CAAC;IAXM,sBAAa,GAApB,UAAqB,EAAU,IAAI,OAAU,EAAE,SAAI,IAAI,CAAC,GAAG,EAAI,CAAA,CAAC,CAAC;IAcjE,sBAAI,wBAAE;QADN,gBAAgB;aAChB,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,0BAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,8BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,iCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aACvD,UAAgB,KAAa,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAEvD,sBAAI,2BAAK;aAAT,cAA4C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,4BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,4BAAM;aAAV,cAAuC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7D,sBAAI,6BAAO;aAAX,cAA0C,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,oCAAc;aAAlB,cAA4C,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;OAAA;IAC1E,sBAAI,iCAAW;aAAf,cAA6B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAI,8BAAQ;aAAZ,cAA0B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAClD,UAAa,KAAc,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAGlD,qBAAqB;IACrB,0BAAO,GAAP,UAAQ,IAAmB;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAoB,CAAC,CAAC;QACvC,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAA7B,CAA6B,CAAC,CAAC;IAC9D,CAAC;IAED,mBAAmB;IACnB,yBAAM,GAAN,UAAO,SAAiB;;QACpB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QACD,0CAA0C;QAC1C,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC;;YAC/B,YAAY;YACZ,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAA3B,IAAM,IAAI,WAAA;gBACX,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE;oBAClD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;iBACjD;aACJ;;;;;;;;;QACD,SAAS;QACT,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE;YACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;SACtE;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,iBAAiB;IACjB,wBAAK,GAAL;QACI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACpE,CAAC;IACD,iBAAiB;IACjB,yBAAM,GAAN;QACI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IACD,iBAAiB;IACjB,uBAAI,GAAJ;QACI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IACD,mBAAmB;IACnB,wBAAK,GAAL;;QACI,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;;YACvB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAA3B,IAAM,IAAI,WAAA;gBACX,IAAI,CAAC,KAAK,EAAE,CAAC;aAChB;;;;;;;;;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC;IACD,iBAAiB;IACjB,yBAAM,GAAN,UAAO,IAAY;;QACf,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAChE,oBAAoB;QACpB,IAAI,IAAI,GAAG,OAAO,EAAE;;gBAChB,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;oBAA3B,IAAM,IAAI,WAAA;oBACX,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE;wBACxD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;4BACtC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;yBACjD;qBACJ;iBACJ;;;;;;;;;SACJ;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,SAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED,sBAAI,kCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACD,aAAa;IACb,0BAAO,GAAP;QACI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,CAAC;IACL,eAAC;AAAD,CAtHA,AAsHC,IAAA;AAtHY,4BAAQ;AAwHrB,sBAAsB;AACtB;IAUI,sBAAY,EAAU,EAAE,WAAmB,EAAE,KAAqB,EAAE,UAA2B,EAAE,cAAuB,EAAE,UAAmB;QAAzE,2BAAA,EAAA,kBAA2B;QANvF,iBAAY,GAAY,KAAK,CAAC;QAI9B,oBAAe,GAAW,CAAC,CAAC;QAC5B,qBAAgB,GAAW,CAAC,CAAC,CAAC;QAElC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAGD,sBAAI,4BAAE;QADN,oBAAoB;aACpB,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,qCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,+BAAK;aAAT,cAA8B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IACnD,sBAAI,qCAAW;aAAf,cAA6B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aACxD,UAAgB,KAAc,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAExD,sBAAI,oCAAU;aAAd,cAA4B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IACtD,sBAAI,wCAAc;aAAlB,cAA2C,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;OAAA;IACzE,sBAAI,oCAAU;aAAd,cAAuC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,wCAAc;aAAlB,cAA+B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;aAC7D,UAAmB,KAAa,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAG7D,iBAAiB;IACjB,oCAAa,GAAb,UAAc,WAAmB,EAAE,UAAkB;QACjD,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;YACxD,OAAO,IAAI,CAAC;SACf;QACD,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/D,eAAe;YACf,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC5E,OAAO,KAAK,CAAC;aAChB;YACD,gBAAgB;YAChB,IAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YACrE,IAAI,WAAW,IAAI,eAAe,EAAE;gBAChC,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,eAAe;IACf,8BAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YACD,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,WAAW,CAAC;aAChD;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,oCAAkC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAG,EAAE,KAAK,CAAC,CAAC;SAC7E;IACL,CAAC;IACD,eAAe;IACf,4BAAK,GAAL;QACI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC;IACL,mBAAC;AAAD,CAzEA,AAyEC,IAAA;AAzEY,oCAAY;AA0EzB,mBAAmB;AACnB;IAGI,uBAAY,EAAU,EAAE,IAAuB;QAC3C,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IACD,sBAAI,6BAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,+BAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAGpD,WAAW;IACX,iCAAS,GAAT,UAAU,OAAe;QACrB,cAAc;QACd,kDAAkD;QAClD,OAAO,CAAC,GAAG,CAAC,oBAAkB,OAAS,CAAC,CAAC;IAC7C,CAAC;IACD,aAAa;IACb,kCAAU,GAAV,UAAW,QAAgB,EAAE,QAAkB;QAC3C,cAAc;QACd,8DAA8D;QAC9D,OAAO,CAAC,GAAG,CAAC,qBAAmB,QAAQ,kBAAe,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IACD,eAAe;IACL,kCAAU,GAApB,UAAqB,QAAmB,EAAE,UAAmB;QACzD,IAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACjC;QACD,IAAI,QAAQ,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,IAAI,OAAZ,OAAO,WAAS,QAAQ,CAAC,OAAO,GAAE;SACrC;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAxB,CAAwB,CAAC,CAAC;IAC9D,CAAC;IACD,wBAAwB;IACd,sCAAc,GAAxB,UAAyB,QAAmB,EAAE,SAAkB;;QAC5D,sBAAsB;QACtB,IAAI,SAAS,KAAK,SAAS,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC5E,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC3C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;SACnD;QACD,WAAW;QACX,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5C,OAAO,QAAQ,CAAC,MAAM,CAAC;SAC1B;QACD,gBAAgB;QAChB,IAAI,QAAQ,CAAC,OAAO,EAAE;;gBAClB,KAAqB,IAAA,KAAA,SAAA,QAAQ,CAAC,OAAO,CAAA,gBAAA,4BAAE;oBAAlC,IAAM,MAAM,WAAA;oBACb,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;wBAC1B,OAAO,MAAM,CAAC;qBACjB;iBACJ;;;;;;;;;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACL,oBAAC;AAAD,CAvDA,AAuDC,IAAA;AAvDqB,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { ITimeline, ITimelineNode, ITimelineEvent, TimelineEventType } from \"../types/ITimeline\";\n\n/*** Timeline实现类*/\nexport class Timeline implements ITimeline {\n    private _id: string;\n    private _name: string;\n    private _duration: number;\n    private _timeElapsed: number = 0;\n    private _nodes: TimelineNode[] = [];\n    private _caster: ICharacter;\n    private _target?: ICharacter;\n    private _targets?: ICharacter[];\n    private _targetPosition?: cc.Vec3;\n    private _isCompleted: boolean = false;\n    private _isPaused: boolean = false;\n    private _eventManager: EventManager;\n    static getTimeLineId(id: string) { return `${id}_${Date.now()}` }\n\n    constructor(id: string, name: string, duration: number, caster: ICharacter, target?: ICharacter, targets?: ICharacter[], targetPosition?: cc.Vec3) {\n        this._id = id;\n        this._name = name;\n        this._duration = duration;\n        this._caster = caster;\n        this._target = target;\n        this._targets = targets;\n        this._targetPosition = targetPosition;\n        this._eventManager = EventManager.createLocal(`Timeline_${id}`);\n    }\n\n    // 实现ITimeline接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get duration(): number { return this._duration; }\n    get timeElapsed(): number { return this._timeElapsed; }\n    set timeElapsed(value: number) { this._timeElapsed = value; }\n    get nodes(): ReadonlyArray<ITimelineNode> { return this._nodes; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter | undefined { return this._target; }\n    get targets(): ICharacter[] | undefined { return this._targets; }\n    get targetPosition(): cc.Vec3 | undefined { return this._targetPosition; }\n    get isCompleted(): boolean { return this._isCompleted; }\n    get isPaused(): boolean { return this._isPaused; }\n    set isPaused(value: boolean) { this._isPaused = value; }\n\n    /** * 添加Timeline节点 */\n    addNode(node: ITimelineNode): void {\n        this._nodes.push(node as TimelineNode);\n        // 按触发时间排序\n        this._nodes.sort((a, b) => a.triggerTime - b.triggerTime);\n    }\n\n    /** * 更新Timeline */\n    update(deltaTime: number): boolean {\n        if (this._isPaused || this._isCompleted) {\n            return this._isCompleted;\n        }\n        // const previousTime = this._timeElapsed;\n        this._timeElapsed += deltaTime;\n        // 检查并触发节点事件\n        for (const node of this._nodes) {\n            if (node.shouldTrigger(this._timeElapsed, deltaTime)) {\n                node.trigger(this, this._nodes.indexOf(node));\n            }\n        }\n        // 检查是否完成\n        if (this._timeElapsed >= this._duration) {\n            this._isCompleted = true;\n            this._eventManager.emit(FightEvent.completedT, { timeline: this });\n        }\n        return this._isCompleted;\n    }\n    /*** 暂停Timeline*/\n    pause(): void {\n        this._isPaused = true;\n        this._eventManager.emit(FightEvent.pausedT, { timeline: this });\n    }\n    /*** 恢复Timeline*/\n    resume(): void {\n        this._isPaused = false;\n        this._eventManager.emit(FightEvent.resumedT, { timeline: this });\n    }\n    /*** 停止Timeline*/\n    stop(): void {\n        this._isCompleted = true;\n        this._eventManager.emit(FightEvent.stoppedT, { timeline: this });\n    }\n    /** * 重置Timeline */\n    reset(): void {\n        this._timeElapsed = 0;\n        this._isCompleted = false;\n        this._isPaused = false;\n        for (const node of this._nodes) {\n            node.reset();\n        }\n        this._eventManager.emit(FightEvent.resetT, { timeline: this });\n    }\n    /** * 跳转到指定时间点 */\n    seekTo(time: number): void {\n        const oldTime = this._timeElapsed;\n        this._timeElapsed = Math.max(0, Math.min(time, this._duration));\n        // 如果是向前跳转，需要触发中间的事件\n        if (time > oldTime) {\n            for (const node of this._nodes) {\n                if (node.triggerTime > oldTime && node.triggerTime <= time) {\n                    if (!node.isTriggered || node.repeatable) {\n                        node.trigger(this, this._nodes.indexOf(node));\n                    }\n                }\n            }\n        }\n        this._eventManager.emit(FightEvent.seekedT, { timeline: this, oldTime, newTime: time });\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n    /** * 清理资源 */\n    cleanup(): void {\n        this._eventManager.cleanup();\n        this._nodes.length = 0;\n    }\n}\n\n/** * Timeline节点实现类 */\nexport class TimelineNode implements ITimelineNode {\n    private _id: string;\n    private _triggerTime: number;\n    private _event: ITimelineEvent;\n    private _isTriggered: boolean = false;\n    private _repeatable: boolean;\n    private _repeatInterval?: number;\n    private _maxRepeats?: number;\n    private _currentRepeats: number = 0;\n    private _lastTriggerTime: number = -1;\n    constructor(id: string, triggerTime: number, event: ITimelineEvent, repeatable: boolean = false, repeatInterval?: number, maxRepeats?: number) {\n        this._id = id;\n        this._triggerTime = triggerTime;\n        this._event = event;\n        this._repeatable = repeatable;\n        this._repeatInterval = repeatInterval;\n        this._maxRepeats = maxRepeats;\n    }\n\n    // 实现ITimelineNode接口\n    get id(): string { return this._id; }\n    get triggerTime(): number { return this._triggerTime; }\n    get event(): ITimelineEvent { return this._event; }\n    get isTriggered(): boolean { return this._isTriggered; }\n    set isTriggered(value: boolean) { this._isTriggered = value; }\n    get repeatable(): boolean { return this._repeatable; }\n    get repeatInterval(): number | undefined { return this._repeatInterval; }\n    get maxRepeats(): number | undefined { return this._maxRepeats; }\n    get currentRepeats(): number { return this._currentRepeats; }\n    set currentRepeats(value: number) { this._currentRepeats = value; }\n\n    /** * 检查是否应该触发 */\n    shouldTrigger(currentTime: number, _deltaTime: number): boolean {\n        // 首次触发检查\n        if (!this._isTriggered && currentTime >= this._triggerTime) {\n            return true;\n        }\n        // 重复触发检查\n        if (this._repeatable && this._isTriggered && this._repeatInterval) {\n            // 检查是否达到最大重复次数\n            if (this._maxRepeats !== undefined && this._currentRepeats >= this._maxRepeats) {\n                return false;\n            }\n            // 检查是否到了下一次触发时间\n            const nextTriggerTime = this._lastTriggerTime + this._repeatInterval;\n            if (currentTime >= nextTriggerTime) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    /** * 触发节点事件 */\n    trigger(timeline: ITimeline, nodeIndex: number): void {\n        try {\n            this._event.execute(timeline, nodeIndex);\n            if (!this._isTriggered) {\n                this._isTriggered = true;\n            }\n            if (this._repeatable) {\n                this._currentRepeats++;\n                this._lastTriggerTime = timeline.timeElapsed;\n            }\n        } catch (error) {\n            console.error(`Error executing timeline event ${this._event.id}:`, error);\n        }\n    }\n    /** * 重置节点状态 */\n    reset(): void {\n        this._isTriggered = false;\n        this._currentRepeats = 0;\n        this._lastTriggerTime = -1;\n    }\n}\n/*** Timeline事件基类*/\nexport abstract class TimelineEvent implements ITimelineEvent {\n    protected _id: string;\n    protected _type: TimelineEventType;\n    constructor(id: string, type: TimelineEventType) {\n        this._id = id;\n        this._type = type;\n    }\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n    /*** 执行事件（抽象方法，子类实现）*/\n    abstract execute(timeline: ITimeline, nodeIndex: number, context?: any): void;\n    /*** 播放音效*/\n    playSound(soundId: string): void {\n        // 这里应该调用音效管理器\n        // AudioManager.getInstance().playEffect(soundId);\n        console.log(`Playing sound: ${soundId}`);\n    }\n    /** * 播放特效 */\n    playEffect(effectId: string, position?: cc.Vec3): void {\n        // 这里应该调用特效管理器\n        // EffectManager.getInstance().playEffect(effectId, position);\n        console.log(`Playing effect: ${effectId} at position:`, position);\n    }\n    /** * 获取目标列表 */\n    protected getTargets(timeline: ITimeline, _nodeIndex?: number): ICharacter[] {\n        const targets: ICharacter[] = [];\n        if (timeline.target) {\n            targets.push(timeline.target);\n        }\n        if (timeline.targets) {\n            targets.push(...timeline.targets);\n        }\n        return targets.filter(target => target && !target.isDead);\n    }\n    /** * 获取有效的目标（排除已死亡的） */\n    protected getValidTarget(timeline: ITimeline, nodeIndex?: number): ICharacter | null {\n        // 如果指定了节点索引，尝试获取对应的目标\n        if (nodeIndex !== undefined && timeline.targets && timeline.targets[nodeIndex]) {\n            const target = timeline.targets[nodeIndex];\n            return target && !target.isDead ? target : null;\n        }\n        // 否则返回单个目标\n        if (timeline.target && !timeline.target.isDead) {\n            return timeline.target;\n        }\n        // 或者返回第一个有效的多目标\n        if (timeline.targets) {\n            for (const target of timeline.targets) {\n                if (target && !target.isDead) {\n                    return target;\n                }\n            }\n        }\n        return null;\n    }\n}\n"]}