
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/CardModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5330cusLLFCWr2XPrV6QmeK', 'CardModel');
// card/cardgame/src/base/CardModel.ts

// import { desLoadDataProxy } from "@judu233/cc-vm-core";
// declare global {
//     interface ICardModel {
//         /**心动 */
//         hert: number;
//         /**金钱 */
//         money: number;
//         /**体质 */
//         physical: number;
//         /**颜值 */
//         face: number;
//     }
// }
// @desLoadDataProxy('CardModel', 'CardModel')
// export class CardModel {
//     static hert: 10;
//     static money: 10;
//     static physical: 10;
//     static face: 1;
//     static data: IEventBar[][] = [];
//     static curDay = 1;
//     static curTab = 0;
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcY2FyZFxcY2FyZGdhbWVcXHNyY1xcYmFzZVxcQ2FyZE1vZGVsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBEQUEwRDtBQUUxRCxtQkFBbUI7QUFDbkIsNkJBQTZCO0FBQzdCLG1CQUFtQjtBQUNuQix3QkFBd0I7QUFDeEIsbUJBQW1CO0FBQ25CLHlCQUF5QjtBQUN6QixtQkFBbUI7QUFDbkIsNEJBQTRCO0FBQzVCLG1CQUFtQjtBQUNuQix3QkFBd0I7QUFDeEIsUUFBUTtBQUNSLElBQUk7QUFDSiw4Q0FBOEM7QUFDOUMsMkJBQTJCO0FBQzNCLHVCQUF1QjtBQUN2Qix3QkFBd0I7QUFDeEIsMkJBQTJCO0FBQzNCLHNCQUFzQjtBQUN0Qix1Q0FBdUM7QUFDdkMseUJBQXlCO0FBQ3pCLHlCQUF5QjtBQUN6QixJQUFJIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy8gaW1wb3J0IHsgZGVzTG9hZERhdGFQcm94eSB9IGZyb20gXCJAanVkdTIzMy9jYy12bS1jb3JlXCI7XHJcblxyXG4vLyBkZWNsYXJlIGdsb2JhbCB7XHJcbi8vICAgICBpbnRlcmZhY2UgSUNhcmRNb2RlbCB7XHJcbi8vICAgICAgICAgLyoq5b+D5YqoICovXHJcbi8vICAgICAgICAgaGVydDogbnVtYmVyO1xyXG4vLyAgICAgICAgIC8qKumHkemSsSAqL1xyXG4vLyAgICAgICAgIG1vbmV5OiBudW1iZXI7XHJcbi8vICAgICAgICAgLyoq5L2T6LSoICovXHJcbi8vICAgICAgICAgcGh5c2ljYWw6IG51bWJlcjtcclxuLy8gICAgICAgICAvKirpopzlgLwgKi9cclxuLy8gICAgICAgICBmYWNlOiBudW1iZXI7XHJcbi8vICAgICB9XHJcbi8vIH1cclxuLy8gQGRlc0xvYWREYXRhUHJveHkoJ0NhcmRNb2RlbCcsICdDYXJkTW9kZWwnKVxyXG4vLyBleHBvcnQgY2xhc3MgQ2FyZE1vZGVsIHtcclxuLy8gICAgIHN0YXRpYyBoZXJ0OiAxMDtcclxuLy8gICAgIHN0YXRpYyBtb25leTogMTA7XHJcbi8vICAgICBzdGF0aWMgcGh5c2ljYWw6IDEwO1xyXG4vLyAgICAgc3RhdGljIGZhY2U6IDE7XHJcbi8vICAgICBzdGF0aWMgZGF0YTogSUV2ZW50QmFyW11bXSA9IFtdO1xyXG4vLyAgICAgc3RhdGljIGN1ckRheSA9IDE7XHJcbi8vICAgICBzdGF0aWMgY3VyVGFiID0gMDtcclxuLy8gfSJdfQ==