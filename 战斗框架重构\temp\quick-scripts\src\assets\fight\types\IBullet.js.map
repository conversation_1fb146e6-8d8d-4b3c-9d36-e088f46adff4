{"version": 3, "sources": ["assets\\fight\\types\\IBullet.ts"], "names": [], "mappings": ";;;;;;;AA8DA,eAAe;AACf,IAAY,UAiBX;AAjBD,WAAY,UAAU;IAClB,WAAW;IACX,mCAAqB,CAAA;IACrB,WAAW;IACX,+BAAiB,CAAA;IACjB,YAAY;IACZ,qCAAuB,CAAA;IACvB,SAAS;IACT,6BAAe,CAAA;IACf,WAAW;IACX,+CAAiC,CAAA;IACjC,WAAW;IACX,mCAAqB,CAAA;IACrB,WAAW;IACX,mCAAqB,CAAA;IACrB,WAAW;IACX,qCAAuB,CAAA;AAC3B,CAAC,EAjBW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAiBrB;AA2BD,aAAa;AACb,IAAY,cAaX;AAbD,WAAY,cAAc;IACtB,WAAW;IACX,mCAAiB,CAAA;IACjB,WAAW;IACX,mCAAiB,CAAA;IACjB,YAAY;IACZ,yCAAuB,CAAA;IACvB,cAAc;IACd,mCAAiB,CAAA;IACjB,WAAW;IACX,mCAAiB,CAAA;IACjB,YAAY;IACZ,yCAAuB,CAAA;AAC3B,CAAC,EAbW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAazB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ICharacter } from \"./ICharacter\";\n\n/*** 子弹接口*/\nexport interface IBullet {\n    /** 子弹ID */\n    readonly id: string;\n    /** 子弹类型 */\n    readonly type: BulletType;\n    /** 子弹节点 */\n    readonly node: cc.Node;\n    /** 施法者 */\n    readonly caster: ICharacter;\n    /** 目标 */\n    readonly target?: ICharacter;\n    /** 目标位置 */\n    readonly targetPosition?: cc.Vec3;\n    /** 发射位置 */\n    readonly firePosition: cc.Vec3;\n    /** 当前位置 */\n    readonly currentPosition: cc.Vec3;\n    /** 移动速度 */\n    readonly speed: number;\n    /** 生存时间 */\n    readonly lifeTime: number;\n    /** 已存在时间 */\n    timeElapsed: number;\n    /** 剩余命中次数 */\n    remainingHits: number;\n    /** 最大命中次数 */\n    readonly maxHits: number;\n    /** 是否已碰撞 */\n    hasCollided: boolean;\n    /** 是否已销毁 */\n    readonly isDestroyed: boolean;\n    /** 弹道轨迹 */\n    readonly trajectory: IBulletTrajectory;\n    /**\n     * 更新子弹状态\n     * @param deltaTime 时间间隔\n     * @returns 是否应该销毁\n     */\n    update(deltaTime: number): boolean;\n    /**\n     * 命中目标\n     * @param target 命中的目标\n     * @returns 是否成功命中\n     */\n    hit(target: ICharacter): boolean;\n    /** * 销毁子弹 */\n    destroy(): void;\n    /**\n     * 设置目标\n     * @param target 新目标\n     */\n    setTarget(target: ICharacter): void;\n    /**\n     * 设置目标位置\n     * @param position 目标位置\n     */\n    setTargetPosition(position: cc.Vec3): void;\n}\n\n/** * 子弹类型枚举 */\nexport enum BulletType {\n    /** 直线子弹 */\n    STRAIGHT = \"straight\",\n    /** 追踪子弹 */\n    HOMING = \"homing\",\n    /** 抛物线子弹 */\n    PARABOLIC = \"parabolic\",\n    /** 激光 */\n    LASER = \"laser\",\n    /** 范围爆炸 */\n    AREA_EXPLOSION = \"area_explosion\",\n    /** 穿透子弹 */\n    PIERCING = \"piercing\",\n    /** 反弹子弹 */\n    BOUNCING = \"bouncing\",\n    /** 分裂子弹 */\n    SPLITTING = \"splitting\"\n}\n/*** 子弹轨迹接口*/\nexport interface IBulletTrajectory {\n    /** 轨迹类型 */\n    readonly type: TrajectoryType;\n    /**\n     * 计算下一帧的位置\n     * @param bullet 子弹实例\n     * @param deltaTime 时间间隔\n     * @returns 新位置\n     */\n    calculateNextPosition(bullet: IBullet, deltaTime: number): cc.Vec3;\n    /**\n     * 计算朝向角度\n     * @param bullet 子弹实例\n     * @returns 角度（度）\n     */\n    calculateRotation(bullet: IBullet): number;\n    /**\n     * 检查是否到达目标\n     * @param bullet 子弹实例\n     * @param threshold 距离阈值\n     * @returns 是否到达\n     */\n    hasReachedTarget(bullet: IBullet, threshold: number): boolean;\n}\n\n/*** 轨迹类型枚举*/\nexport enum TrajectoryType {\n    /** 直线轨迹 */\n    LINEAR = \"linear\",\n    /** 追踪轨迹 */\n    HOMING = \"homing\",\n    /** 抛物线轨迹 */\n    PARABOLIC = \"parabolic\",\n    /** 贝塞尔曲线轨迹 */\n    BEZIER = \"bezier\",\n    /** 螺旋轨迹 */\n    SPIRAL = \"spiral\",\n    /** 正弦波轨迹 */\n    SINE_WAVE = \"sine_wave\"\n}\n\n/*** 子弹配置接口*/\nexport interface IBulletConfig {\n    /** 子弹ID */\n    id: string;\n    /** 子弹类型 */\n    type: BulletType;\n    /** 预制体路径 */\n    prefabPath: string;\n    /** 移动速度 */\n    speed: number;\n    /** 生存时间 */\n    lifeTime: number;\n    /** 最大命中次数 */\n    maxHits: number;\n    /** 轨迹配置 */\n    trajectory: ITrajectoryConfig;\n    /** 碰撞检测配置 */\n    collision: ICollisionConfig;\n    /** 视觉效果配置 */\n    visual: IVisualConfig;\n    /** 音效配置 */\n    audio: IAudioConfig;\n    /** 是否没有目标时自动回收 */\n    recycleWhenNoTarget?: boolean;\n    /** 自定义参数 */\n    customParams?: any;\n}\n\n/** * 轨迹配置接口 */\nexport interface ITrajectoryConfig {\n    /** 轨迹类型 */\n    type: TrajectoryType;\n    /** 轨迹参数 */\n    params?: any;\n}\n\n/** * 碰撞检测配置接口 */\nexport interface ICollisionConfig {\n    /** 碰撞半径 */\n    radius: number;\n    /** 是否穿透 */\n    piercing: boolean;\n    /** 碰撞层级 */\n    layers: string[];\n    /** 碰撞检测频率 */\n    checkFrequency: number;\n}\n\n/*** 视觉效果配置接口*/\nexport interface IVisualConfig {\n    /** 拖尾效果 */\n    trail?: {\n        enabled: boolean;\n        length: number;\n        width: number;\n        color: cc.Color;\n    };\n    /** 旋转效果 */\n    rotation?: {\n        enabled: boolean;\n        speed: number;\n        axis: cc.Vec3;\n    };\n    /** 缩放效果 */\n    scale?: {\n        enabled: boolean;\n        startScale: number;\n        endScale: number;\n        curve: string;\n    };\n    /** 命中特效 */\n    hitEffect?: {\n        prefabPath: string;\n        duration: number;\n        scale: number;\n    };\n}\n/** * 音效配置接口 */\nexport interface IAudioConfig {\n    /** 发射音效 */\n    fireSound?: string;\n    /** 飞行音效 */\n    flySound?: string;\n    /** 命中音效 */\n    hitSound?: string;\n    /** 销毁音效 */\n    destroySound?: string;\n}\n/*** 子弹发射器接口*/\nexport interface IBulletLauncher {\n    /** 发射器ID */\n    readonly id: string;\n    /** 施法者 */\n    readonly caster: ICharacter;\n    /** 子弹配置 */\n    readonly bulletConfig: IBulletConfig;\n    /** 发射位置 */\n    firePosition: cc.Vec3;\n    /** 发射方向 */\n    fireDirection: cc.Vec3;\n    /** 发射角度 */\n    fireAngle: number;\n    /** 发射速度 */\n    fireSpeed: number;\n    /**\n     * 发射子弹\n     * @param target 目标\n     * @param targetPosition 目标位置\n     * @returns 创建的子弹实例\n     */\n    fire(target?: ICharacter, targetPosition?: cc.Vec3): IBullet;\n    /**\n     * 批量发射子弹\n     * @param count 发射数量\n     * @param spread 散布角度\n     * @param target 目标\n     * @returns 创建的子弹实例数组\n     */\n    fireBurst(count: number, spread: number, target?: ICharacter): IBullet[];\n    /**\n     * 设置发射参数\n     * @param position 发射位置\n     * @param direction 发射方向\n     * @param speed 发射速度\n     */\n    setFireParams(position: cc.Vec3, direction: cc.Vec3, speed?: number): void;\n}\n\n/*** 子弹管理器接口*/\nexport interface IBulletManager {\n    /** 所有活跃的子弹 */\n    readonly activeBullets: ReadonlyArray<IBullet>;\n    /**\n     * 创建子弹\n     * @param config 子弹配置\n     * @param caster 施法者\n     * @param target 目标\n     * @param targetPosition 目标位置\n     * @returns 创建的子弹实例\n     */\n    createBullet(\n        config: IBulletConfig,\n        caster: ICharacter,\n        target?: ICharacter,\n        targetPosition?: cc.Vec3\n    ): IBullet;\n    /**\n     * 移除子弹\n     * @param bulletId 子弹ID\n     */\n    removeBullet(bulletId: string): void;\n    /**\n     * 根据施法者移除子弹\n     * @param caster 施法者\n     */\n    removeBulletsByCaster(caster: ICharacter): void;\n    /** * 清除所有子弹 */\n    clearAllBullets(): void;\n    /**\n     * 更新所有子弹\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n    /**  * 获取子弹统计信息  */\n    getStats(): {\n        activeCount: number;\n        totalCreated: number;\n        totalDestroyed: number;\n    };\n}\n"]}