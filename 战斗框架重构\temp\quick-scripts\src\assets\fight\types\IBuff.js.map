{"version": 3, "sources": ["assets\\fight\\types\\IBuff.ts"], "names": [], "mappings": ";;;;;;;AAgKA,eAAe;AACf,IAAY,QAOX;AAPD,WAAY,QAAQ;IAChB,SAAS;IACT,yBAAa,CAAA;IACb,SAAS;IACT,6BAAiB,CAAA;IACjB,SAAS;IACT,+BAAmB,CAAA;AACvB,CAAC,EAPW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAOnB;AACD,kBAAkB;AAClB,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IACxB,SAAS;IACT,+BAAW,CAAA;IACX,SAAS;IACT,yCAAqB,CAAA;IACrB,UAAU;IACV,6CAAyB,CAAA;AAC7B,CAAC,EAPW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAO3B;AACD,oBAAoB;AACpB,IAAY,sBASX;AATD,WAAY,sBAAsB;IAC9B,WAAW;IACX,+DAAqC,CAAA;IACrC,WAAW;IACX,2DAAiC,CAAA;IACjC,WAAW;IACX,qDAA2B,CAAA;IAC3B,WAAW;IACX,+DAAqC,CAAA;AACzC,CAAC,EATW,sBAAsB,GAAtB,8BAAsB,KAAtB,8BAAsB,QASjC", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ICharacter } from \"./ICharacter\";\nimport { ISkill } from \"./ISkill\";\nimport { IDamageInfo } from \"./IDamage\";\nimport { ITimeline } from \"./ITimeline\";\nimport { IAttributeModifier } from \"./ICharacterAttributes\";\n\n/*** Buff接口*/\nexport interface IBuff {\n    /** Buff ID */\n    readonly id: string;\n    /** Buff名称 */\n    readonly name: string;\n    /** Buff描述 */\n    readonly description: string;\n    /** Buff类型 */\n    readonly type: BuffType;\n    /** 持续时间（秒，-1表示永久） */\n    readonly duration: number;\n    /** 剩余时间 */\n    remainingTime: number;\n    /** 叠加层数 */\n    stackCount: number;\n    /** 最大叠加层数 */\n    readonly maxStack: number;\n    /** 施法者 */\n    readonly caster: ICharacter;\n    /** 目标 */\n    readonly target: ICharacter;\n    /** 是否已过期 */\n    readonly isExpired: boolean;\n    /** 属性修改器列表（解决属性更新Bug的关键） */\n    readonly attributeModifiers: ReadonlyArray<IAttributeModifier>;\n    /** 周期性效果配置 */\n    readonly periodicEffect?: IBuffPeriodicEffect;\n    /** 图标路径 */\n    readonly iconPath?: string;\n    /** 特效预制体路径 */\n    readonly effectPrefabPath?: string;\n    /** * Buff被添加时触发 */\n    onApply(): void;\n    /**\n     * Buff每帧更新时触发\n     * @param deltaTime 时间间隔\n     */\n    onTick(deltaTime: number): void;\n    /** * Buff被移除时触发 */\n    onRemove(): void;\n    /**\n     * 释放技能时触发\n     * @param skill 释放的技能\n     * @param timeline 技能的Timeline\n     * @returns 修改后的Timeline\n     */\n    onSkillCast?(skill: ISkill, timeline: ITimeline): ITimeline;\n    /**\n     * 造成伤害时触发\n     * @param damageInfo 伤害信息\n     * @param target 目标\n     * @returns 修改后的伤害信息\n     */\n    onDealDamage?(damageInfo: IDamageInfo, target: ICharacter): IDamageInfo;\n    /**\n     * 受到伤害时触发\n     * @param damageInfo 伤害信息\n     * @param attacker 攻击者\n     * @returns 修改后的伤害信息\n     */\n    onTakeDamage?(damageInfo: IDamageInfo, attacker: ICharacter): IDamageInfo;\n    /**\n     * 击杀敌人时触发\n     * @param victim 被击杀的敌人\n     */\n    onKill?(victim: ICharacter): void;\n    /**\n     * 被击杀时触发\n     * @param killer 击杀者\n     */\n    onDeath?(killer: ICharacter): void;\n    /**\n     * 更新Buff状态\n     * @param deltaTime 时间间隔\n     * @returns 是否已过期\n     */\n    update(deltaTime: number): boolean;\n    /**\n     * 刷新Buff持续时间\n     */\n    refresh(): void;\n    /**\n     * 增加叠加层数\n     * @param count 增加的层数\n     */\n    addStack(count?: number): void;\n    /**\n     * 减少叠加层数\n     * @param count 减少的层数\n     */\n    removeStack(count?: number): void;\n    /**\n     * 获取Buff的当前效果值\n     * @param effectType 效果类型\n     * @returns 效果值\n     */\n    getEffectValue(effectType: string): number;\n    /**\n     * 检查Buff是否与另一个Buff冲突\n     * @param otherBuff 另一个Buff\n     * @returns 是否冲突\n     */\n    conflictsWith(otherBuff: IBuff): boolean;\n}\n\n/*** Buff配置接口*/\nexport interface IBuffConfig {\n    /** Buff ID */\n    id: string;\n    /** Buff名称 */\n    name: string;\n    /** Buff描述 */\n    description: string;\n    /** Buff类型 */\n    type: BuffType;\n    /** 持续时间 */\n    duration: number;\n    /** 最大叠加层数 */\n    maxStack: number;\n    /** 图标路径 */\n    iconPath?: string;\n    /** 特效预制体 */\n    effectPrefab?: string;\n    /** 音效ID */\n    soundId?: string;\n    /** 属性修改 */\n    attributeModifiers?: IBuffAttributeModifier[];\n    /** 周期性效果 */\n    periodicEffect?: IBuffPeriodicEffect;\n}\n\n/*** Buff属性修改器*/\nexport interface IBuffAttributeModifier {\n    /** 属性名称 */\n    attributeName: string;\n    /** 修改类型 */\n    modifierType: BuffModifierType;\n    /** 修改值 */\n    value: number;\n    /** 是否按叠加层数计算 */\n    stackable: boolean;\n}\n/*** Buff周期性效果*/\nexport interface IBuffPeriodicEffect {\n    /** 触发间隔（秒） */\n    interval: number;\n    /** 效果类型 */\n    effectType: BuffPeriodicEffectType;\n    /** 效果值 */\n    value: number;\n    /** 是否按叠加层数计算 */\n    stackable: boolean;\n}\n/*** Buff类型枚举*/\nexport enum BuffType {\n    /** 增益 */\n    BUFF = \"buff\",\n    /** 减益 */\n    DEBUFF = \"debuff\",\n    /** 中性 */\n    NEUTRAL = \"neutral\"\n}\n/*** Buff修改器类型枚举*/\nexport enum BuffModifierType {\n    /** 加法 */\n    ADD = \"add\",\n    /** 乘法 */\n    MULTIPLY = \"multiply\",\n    /** 百分比 */\n    PERCENTAGE = \"percentage\"\n}\n/*** Buff周期性效果类型枚举*/\nexport enum BuffPeriodicEffectType {\n    /** 持续伤害 */\n    DAMAGE_OVER_TIME = \"damage_over_time\",\n    /** 持续治疗 */\n    HEAL_OVER_TIME = \"heal_over_time\",\n    /** 魔法恢复 */\n    MP_RECOVERY = \"mp_recovery\",\n    /** 耐力恢复 */\n    STAMINA_RECOVERY = \"stamina_recovery\"\n}\n/*** Buff管理器接口*/\nexport interface IBuffManager {\n    /** 所有Buff列表 */\n    readonly buffs: ReadonlyArray<IBuff>;\n    /**\n     * 添加Buff\n     * @param buff Buff实例\n     */\n    addBuff(buff: IBuff): void;\n    /**\n     * 移除Buff\n     * @param buffId Buff ID\n     */\n    removeBuff(buffId: string): void;\n    /**\n     * 根据ID获取Buff\n     * @param buffId Buff ID\n     * @returns Buff实例或null\n     */\n    getBuff(buffId: string): IBuff | null;\n    /**\n     * 根据类型获取Buff列表\n     * @param type Buff类型\n     * @returns Buff列表\n     */\n    getBuffsByType(type: BuffType): IBuff[];\n    /**\n     * 清除所有Buff\n     */\n    clearAllBuffs(): void;\n    /**\n     * 清除指定类型的Buff\n     * @param type Buff类型\n     */\n    clearBuffsByType(type: BuffType): void;\n    /**\n     * 更新所有Buff\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n}\n"]}