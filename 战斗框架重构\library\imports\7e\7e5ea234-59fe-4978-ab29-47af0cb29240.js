"use strict";
cc._RF.push(module, '7e5eaI0Wf5JeKspR68MspJA', 'ISkill');
// fight/types/ISkill.ts

"use strict";
/**
 * 技能系统接口定义
 * 基于Timeline的技能系统，保留原有的复杂效果支持
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillEffectType = exports.SkillTargetType = exports.SkillType = void 0;
/*** 技能类型枚举*/
var SkillType;
(function (SkillType) {
    /** 主动技能 */
    SkillType["ACTIVE"] = "active";
    /** 被动技能 */
    SkillType["PASSIVE"] = "passive";
    /** 普通攻击 */
    SkillType["BASIC_ATTACK"] = "basic_attack";
    /** 终极技能 */
    SkillType["ULTIMATE"] = "ultimate";
})(SkillType = exports.SkillType || (exports.SkillType = {}));
/*** 技能目标类型枚举*/
var SkillTargetType;
(function (SkillTargetType) {
    /** 无目标 */
    SkillTargetType["NONE"] = "none";
    /** 单个敌人 */
    SkillTargetType["SINGLE_ENEMY"] = "single_enemy";
    /** 多个敌人 */
    SkillTargetType["MULTIPLE_ENEMIES"] = "multiple_enemies";
    /** 单个友军 */
    SkillTargetType["SINGLE_ALLY"] = "single_ally";
    /** 多个友军 */
    SkillTargetType["MULTIPLE_ALLIES"] = "multiple_allies";
    /** 自己 */
    SkillTargetType["SELF"] = "self";
    /** 地面位置 */
    SkillTargetType["GROUND"] = "ground";
    /** 所有敌人 */
    SkillTargetType["ALL_ENEMIES"] = "all_enemies";
    /** 所有友军 */
    SkillTargetType["ALL_ALLIES"] = "all_allies";
})(SkillTargetType = exports.SkillTargetType || (exports.SkillTargetType = {}));
/*** 技能效果类型枚举*/
var SkillEffectType;
(function (SkillEffectType) {
    /** 伤害 */
    SkillEffectType["DAMAGE"] = "damage";
    /** 治疗 */
    SkillEffectType["HEAL"] = "heal";
    /** 添加Buff */
    SkillEffectType["ADD_BUFF"] = "add_buff";
    /** 移除Buff */
    SkillEffectType["REMOVE_BUFF"] = "remove_buff";
    /** 召唤 */
    SkillEffectType["SUMMON"] = "summon";
    /** 传送 */
    SkillEffectType["TELEPORT"] = "teleport";
    /** 击退 */
    SkillEffectType["KNOCKBACK"] = "knockback";
    /** 眩晕 */
    SkillEffectType["STUN"] = "stun";
})(SkillEffectType = exports.SkillEffectType || (exports.SkillEffectType = {}));

cc._RF.pop();