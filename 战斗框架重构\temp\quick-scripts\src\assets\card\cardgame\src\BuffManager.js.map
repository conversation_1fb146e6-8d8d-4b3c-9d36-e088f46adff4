{"version": 3, "sources": ["assets\\card\\cardgame\\src\\BuffManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,0CAA0C;AAE1C,QAAQ;AACR,yBAAyB;AACzB,0DAA0D;AAC1D,OAAO;AACP,kDAAkD;AAClD,mBAAmB;AACnB,+CAA+C;AAC/C,sBAAsB;AACtB,yDAAyD;AACzD,oBAAoB;AACpB,yDAAyD;AAEzD,sBAAsB;AACtB,8CAA8C;AAC9C,qCAAqC;AACrC,iDAAiD;AACjD,cAAc;AACd,QAAQ;AAER,mBAAmB;AACnB,gCAAgC;AAChC,0BAA0B;AAC1B,uDAAuD;AACvD,QAAQ;AAER,wBAAwB;AACxB,mCAAmC;AACnC,wDAAwD;AACxD,2DAA2D;AAC3D,eAAe;AACf,uGAAuG;AACvG,QAAQ;AAER,mBAAmB;AACnB,sCAAsC;AAEtC,mBAAmB;AACnB,oCAAoC;AACpC,QAAQ;AACR,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import BuffBase from \"./Base/BuffBase\";\r\n\r\n// /**  \r\n//     * @Title : buff管理器\r\n//     * @Description : 该类又技能类管理 - 关系： 技能 <-> buff  =  一对多\r\n//  **/\r\n// export default class BuffManager extends Base {\r\n//     /**所有Buff */\r\n//     buff: Map<string, BuffBase> = new Map();\r\n//     /**正在释放的buff */\r\n//     releaseBuffMap: Map<string, BuffBase> = new Map();\r\n//     /**已失效Buff */\r\n//     failureBuffMap: Map<string, BuffBase> = new Map();\r\n\r\n//     /**初始化buff管理 */\r\n//     initBuffManager(buffList: BuffBase[]) {\r\n//         buffList.forEach(buff => {\r\n//             this.buff.set(buff.data.id, buff);\r\n//         });\r\n//     }\r\n\r\n//     /**使用buff */\r\n//     useBuff(buff: BuffBase) {\r\n//         buff.useBuff();\r\n//         this.releaseBuffMap.set(buff.data.id, buff);\r\n//     }\r\n\r\n//     /**使用完毕，移除Buff */\r\n//     removeBuff(buff: BuffBase) {\r\n//         if (this.releaseBuffMap.delete(buff.data.id))\r\n//             this.failureBuffMap.set(buff.data.id, buff);\r\n//         else\r\n//             cc.warn(`${this.className}移除Buff失败，未找到id:${buff.data.id}, name:${buff.data.name}的buff`);\r\n//     }\r\n\r\n//     /**叠加buff */\r\n//     overlayBuff(buff: BuffBase) { }\r\n\r\n//     /**释放buff */\r\n//     releaseBuff(buff: BuffBase) {\r\n//     }\r\n// }\r\n"]}