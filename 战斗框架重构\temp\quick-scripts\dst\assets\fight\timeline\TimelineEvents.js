
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/timeline/TimelineEvents.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a8ca9HIeOBKr6U/ra2jDluH', 'TimelineEvents');
// fight/timeline/TimelineEvents.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionalTimelineEvent = exports.MultiTargetDamageTimelineEvent = exports.CustomTimelineEvent = exports.MoveTimelineEvent = exports.PlayEffectTimelineEvent = exports.PlaySoundTimelineEvent = exports.PlayAnimationTimelineEvent = exports.AddBuffTimelineEvent = exports.FireBulletTimelineEvent = exports.HealTimelineEvent = exports.DamageTimelineEvent = void 0;
var IDamage_1 = require("../types/IDamage");
var ITimeline_1 = require("../types/ITimeline");
var Timeline_1 = require("./Timeline");
var BuffModelBeHurtFight_1 = require("../buff/BuffModelBeHurtFight");
var BattleManager_1 = require("../systems/BattleManager");
/*** 伤害事件*/
var DamageTimelineEvent = /** @class */ (function (_super) {
    __extends(DamageTimelineEvent, _super);
    function DamageTimelineEvent(id, damageAmount, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.DAMAGE) || this;
        _this.damageAmount = damageAmount;
        _this.damageType = damageType;
        _this.tags = tags;
        return _this;
    }
    DamageTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("DamageTimelineEvent: No valid target found");
            return;
        }
        // 使用伤害管理器处理复杂的伤害计算
        var damageManager = BattleManager_1.BattleManager.instance.damageManager;
        var baseDamage = this.damageAmount === 0 ? caster.attributes.attack : this.damageAmount;
        var damageInfo = damageManager.dealDamage(caster, target, baseDamage, this.damageType, this.tags);
        console.log("[Timeline-Event] " + caster.characterName + " deals " + damageInfo.finalDamage + " " + this.damageType + " damage to " + target.characterName + (damageInfo.isCritical ? ' (Critical!)' : ''));
    };
    return DamageTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.DamageTimelineEvent = DamageTimelineEvent;
/*** 治疗事件*/
var HealTimelineEvent = /** @class */ (function (_super) {
    __extends(HealTimelineEvent, _super);
    function HealTimelineEvent(id, healAmount) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.HEAL) || this;
        _this.healAmount = healAmount;
        return _this;
    }
    HealTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex) || caster; // 默认治疗自己
        console.log("[Timeline-Event] " + caster.characterName + " heals " + target.characterName + " for " + this.healAmount);
        target.heal(this.healAmount);
    };
    return HealTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.HealTimelineEvent = HealTimelineEvent;
/** * 子弹发射事件 */
var FireBulletTimelineEvent = /** @class */ (function (_super) {
    __extends(FireBulletTimelineEvent, _super);
    function FireBulletTimelineEvent(id, bulletLauncher, effectPrefabPath, hitAnimationName, soundId) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.FIRE_BULLET) || this;
        _this.bulletLauncher = bulletLauncher;
        _this.effectPrefabPath = effectPrefabPath;
        _this.hitAnimationName = hitAnimationName;
        _this.soundId = soundId;
        return _this;
    }
    FireBulletTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("FireBulletTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] " + caster.characterName + " fires bullet at " + target.characterName);
        if (this.soundId) {
            this.playSound(this.soundId);
        }
        this.bulletLauncher.firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var bullet = this.bulletLauncher.fire(target);
        // 将子弹添加到子弹管理器
        if (bullet) {
            var bulletManager = BattleManager_1.BattleManager.instance.bulletManager;
            bulletManager.addBullet(bullet);
            console.log("[Timeline-Event] Bullet " + bullet.id + " added to BulletManager");
        }
        if (this.effectPrefabPath) {
            this.playEffect(this.effectPrefabPath, target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        }
    };
    return FireBulletTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.FireBulletTimelineEvent = FireBulletTimelineEvent;
/*** Buff添加事件*/
var AddBuffTimelineEvent = /** @class */ (function (_super) {
    __extends(AddBuffTimelineEvent, _super);
    function AddBuffTimelineEvent(id, buffId, targetSelf) {
        if (targetSelf === void 0) { targetSelf = false; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.ADD_BUFF) || this;
        _this.buffId = buffId;
        _this.targetSelf = targetSelf;
        return _this;
    }
    AddBuffTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("AddBuffTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] Adding buff " + this.buffId + " to " + target.characterName);
        // 简单的 Buff 创建逻辑（可以后续扩展为 BuffFactory）
        var buff = this.createBuff(this.buffId, caster, target);
        if (buff) {
            target.addBuff(buff);
            console.log("[Timeline-Event] Successfully added buff " + this.buffId + " to " + target.characterName);
        }
        else {
            console.warn("[Timeline-Event] Failed to create buff " + this.buffId);
        }
    };
    /** 创建 Buff 实例（简单工厂模式） */
    AddBuffTimelineEvent.prototype.createBuff = function (buffId, caster, target) {
        switch (buffId) {
            case "counter_attack":
            case "hurt_fight":
                return new BuffModelBeHurtFight_1.BuffModelBeHurtFight(caster, target);
            // 可以在这里添加更多 Buff 类型
            default:
                console.warn("Unknown buff ID: " + buffId);
                return null;
        }
    };
    return AddBuffTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.AddBuffTimelineEvent = AddBuffTimelineEvent;
/*** 动画播放事件*/
var PlayAnimationTimelineEvent = /** @class */ (function (_super) {
    __extends(PlayAnimationTimelineEvent, _super);
    function PlayAnimationTimelineEvent(id, animationName, loop, targetSelf) {
        if (loop === void 0) { loop = false; }
        if (targetSelf === void 0) { targetSelf = true; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_ANIMATION) || this;
        _this.animationName = animationName;
        _this.loop = loop;
        _this.targetSelf = targetSelf;
        return _this;
    }
    PlayAnimationTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("PlayAnimationTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] Playing animation " + this.animationName + " on " + target.characterName);
        var spine = target.node.getComponent(sp.Skeleton);
        if (spine) {
            spine.setAnimation(0, this.animationName, this.loop);
        }
    };
    return PlayAnimationTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlayAnimationTimelineEvent = PlayAnimationTimelineEvent;
/*** 音效播放事件*/
var PlaySoundTimelineEvent = /** @class */ (function (_super) {
    __extends(PlaySoundTimelineEvent, _super);
    function PlaySoundTimelineEvent(id, soundId, volume) {
        if (volume === void 0) { volume = 1.0; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_SOUND) || this;
        _this.soundId = soundId;
        _this.volume = volume;
        return _this;
    }
    PlaySoundTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        console.log("[Timeline-Event] Playing sound " + this.soundId);
        this.playSound(this.soundId);
    };
    return PlaySoundTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlaySoundTimelineEvent = PlaySoundTimelineEvent;
/*** 特效播放事件*/
var PlayEffectTimelineEvent = /** @class */ (function (_super) {
    __extends(PlayEffectTimelineEvent, _super);
    function PlayEffectTimelineEvent(id, effectId, targetPosition, offset) {
        if (targetPosition === void 0) { targetPosition = true; }
        if (offset === void 0) { offset = cc.Vec3.ZERO; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_EFFECT) || this;
        _this.effectId = effectId;
        _this.targetPosition = targetPosition;
        _this.offset = offset;
        return _this;
    }
    PlayEffectTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var position;
        if (this.targetPosition) {
            var target = this.getValidTarget(timeline, nodeIndex);
            if (target) {
                position = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            }
            else {
                position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            }
        }
        else {
            position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
        }
        console.log("[Timeline-Event] Playing effect " + this.effectId + " at position", position);
        this.playEffect(this.effectId, position);
    };
    return PlayEffectTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlayEffectTimelineEvent = PlayEffectTimelineEvent;
/** * 移动事件 */
var MoveTimelineEvent = /** @class */ (function (_super) {
    __extends(MoveTimelineEvent, _super);
    function MoveTimelineEvent(id, targetPosition, duration, easing) {
        if (duration === void 0) { duration = 1.0; }
        if (easing === void 0) { easing = "linear"; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.MOVE) || this;
        _this.targetPosition = targetPosition;
        _this.duration = duration;
        _this.easing = easing;
        return _this;
    }
    MoveTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        console.log("[Timeline-Event] Moving " + caster.characterName + " to position", this.targetPosition);
        cc.tween(caster.node)
            .to(this.duration, { position: this.targetPosition })
            .start();
    };
    return MoveTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.MoveTimelineEvent = MoveTimelineEvent;
/*** 自定义事件*/
var CustomTimelineEvent = /** @class */ (function (_super) {
    __extends(CustomTimelineEvent, _super);
    function CustomTimelineEvent(id, customFunction) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.CUSTOM) || this;
        _this.customFunction = customFunction;
        return _this;
    }
    CustomTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        try {
            this.customFunction(timeline, nodeIndex);
        }
        catch (error) {
            console.error("Error executing custom timeline event " + this.id + ":", error);
        }
    };
    return CustomTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.CustomTimelineEvent = CustomTimelineEvent;
/*** 多目标伤害事件（用于AOE技能）*/
var MultiTargetDamageTimelineEvent = /** @class */ (function (_super) {
    __extends(MultiTargetDamageTimelineEvent, _super);
    function MultiTargetDamageTimelineEvent(id, damageAmount, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.DAMAGE) || this;
        _this.damageAmount = damageAmount;
        _this.damageType = damageType;
        _this.tags = tags;
        return _this;
    }
    MultiTargetDamageTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var e_1, _a;
        var caster = timeline.caster;
        var targets = this.getTargets(timeline);
        if (targets.length === 0) {
            console.warn("MultiTargetDamageTimelineEvent: No valid targets found");
            return;
        }
        console.log("[Timeline-Event] " + caster.characterName + " deals AOE damage to " + targets.length + " targets");
        try {
            // 对所有目标造成伤害
            for (var targets_1 = __values(targets), targets_1_1 = targets_1.next(); !targets_1_1.done; targets_1_1 = targets_1.next()) {
                var target = targets_1_1.value;
                var finalDamage = this.calculateDamage(caster, target);
                target.takeDamage(finalDamage, caster);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (targets_1_1 && !targets_1_1.done && (_a = targets_1.return)) _a.call(targets_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    MultiTargetDamageTimelineEvent.prototype.calculateDamage = function (caster, target) {
        var baseAttack = caster.attributes.attack;
        var defense = target.attributes.defense;
        var finalDamage = this.damageAmount;
        if (this.damageAmount === 0) {
            finalDamage = baseAttack;
        }
        if (this.damageType === IDamage_1.DamageType.PHYSICAL) {
            finalDamage = Math.max(1, finalDamage - defense);
        }
        return Math.floor(finalDamage);
    };
    return MultiTargetDamageTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.MultiTargetDamageTimelineEvent = MultiTargetDamageTimelineEvent;
/** * 条件事件（只有满足条件时才执行） */
var ConditionalTimelineEvent = /** @class */ (function (_super) {
    __extends(ConditionalTimelineEvent, _super);
    function ConditionalTimelineEvent(id, condition, wrappedEvent) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.CUSTOM) || this;
        _this.condition = condition;
        _this.wrappedEvent = wrappedEvent;
        return _this;
    }
    ConditionalTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        if (this.condition(timeline)) {
            this.wrappedEvent.execute(timeline, nodeIndex);
        }
        else {
            console.log("[Timeline-Event] Condition not met for event " + this.id);
        }
    };
    return ConditionalTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.ConditionalTimelineEvent = ConditionalTimelineEvent;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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