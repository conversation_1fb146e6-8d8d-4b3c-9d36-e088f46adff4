
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/characters/AttributeModifier.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '86724jQcgZEKYvZN4mQ8lO8', 'AttributeModifier');
// fight/characters/AttributeModifier.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeModifier = void 0;
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
/**
 * 属性修改器实现类
 */
var AttributeModifier = /** @class */ (function () {
    function AttributeModifier(id, name, attributeName, type, value, duration) {
        if (duration === void 0) { duration = -1; }
        this._id = id;
        this._name = name;
        this._attributeName = attributeName;
        this._type = type;
        this._value = value;
        this._duration = duration;
        this._remainingTime = duration;
    }
    Object.defineProperty(AttributeModifier.prototype, "id", {
        // 实现IAttributeModifier接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "value", {
        get: function () { return this._value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "attributeName", {
        // 额外属性
        get: function () { return this._attributeName; },
        enumerable: false,
        configurable: true
    });
    /*** 应用修改器到属性*/
    AttributeModifier.prototype.apply = function (attributes) {
        var currentValue = attributes.getCurrentAttributeValue(this._attributeName);
        var newValue = currentValue;
        switch (this._type) {
            case ICharacterAttributes_1.AttributeModifierType.ADD:
                newValue = currentValue + this._value;
                break;
            case ICharacterAttributes_1.AttributeModifierType.MULTIPLY:
                newValue = currentValue * this._value;
                break;
            case ICharacterAttributes_1.AttributeModifierType.PERCENTAGE:
                newValue = currentValue * (1 + this._value);
                break;
            case ICharacterAttributes_1.AttributeModifierType.OVERRIDE:
                newValue = this._value;
                break;
        }
        attributes.setAttributeValue(this._attributeName, newValue);
        console.log("Applied " + this._name + ": " + this._attributeName + " " + currentValue + " -> " + newValue);
    };
    /*** 移除修改器效果*/
    AttributeModifier.prototype.remove = function (_attributes) {
        // 这里需要恢复原始值，但由于可能有多个修改器，
        // 实际实现中应该重新计算所有修改器
        console.log("Removed " + this._name + " from " + this._attributeName);
    };
    /** * 更新修改器 */
    AttributeModifier.prototype.update = function (deltaTime) {
        if (this._duration < 0) {
            return false; // 永久修改器
        }
        this._remainingTime -= deltaTime;
        return this._remainingTime <= 0;
    };
    /*** 设置新的值*/
    AttributeModifier.prototype.setValue = function (value) {
        this._value = value;
    };
    /** * 获取调试信息 */
    AttributeModifier.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            attributeName: this._attributeName,
            type: this._type,
            value: this._value,
            duration: this._duration,
            remainingTime: this._remainingTime
        };
    };
    return AttributeModifier;
}());
exports.AttributeModifier = AttributeModifier;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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