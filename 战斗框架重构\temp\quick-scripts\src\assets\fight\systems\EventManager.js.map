{"version": 3, "sources": ["assets\\fight\\systems\\EventManager.ts"], "names": [], "mappings": ";;;;;AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASH;;GAEG;AACH;IAMI,sBAAY,UAAmB;QAJvB,eAAU,GAAkC,IAAI,GAAG,EAAE,CAAC;QACtD,iBAAY,GAAY,KAAK,CAAC;QAIlC,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,kBAAgB,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;IACjH,CAAC;IAED,eAAe;IACR,sBAAS,GAAhB;QACI,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;YAC/B,YAAY,CAAC,eAAe,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7D;QACD,OAAO,YAAY,CAAC,eAAe,CAAC;IACxC,CAAC;IAED,eAAe;IACR,wBAAW,GAAlB,UAAmB,UAAmB;QAClC,OAAO,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,eAAe;IACR,eAAE,GAAT,UAAU,KAAa,EAAE,QAAkB,EAAE,OAAa;QACtD,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAEM,iBAAI,GAAX,UAAY,KAAa,EAAE,QAAkB,EAAE,OAAa;QACxD,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEM,gBAAG,GAAV,UAAW,KAAa,EAAE,QAAmB,EAAE,OAAa;QACxD,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAEM,iBAAI,GAAX,UAAY,KAAa,EAAE,IAAU;QACjC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAEM,yBAAY,GAAnB,UAAoB,KAAa;QAC7B,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACxD,CAAC;IAEM,6BAAgB,GAAvB,UAAwB,KAAa;QACjC,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAEM,0BAAa,GAApB;QACI,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;IAEM,oBAAO,GAAd;QACI,IAAI,YAAY,CAAC,eAAe,EAAE;YAC9B,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YACvC,YAAY,CAAC,eAAe,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;IAEM,yBAAY,GAAnB;QACI,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;IACnD,CAAC;IAGD,sBAAI,oCAAU;QADd,aAAa;aACb,cAA+B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAEzD;;;;;OAKG;IACH,yBAAE,GAAF,UAAG,KAAa,EAAE,QAAkB,EAAE,OAAa;QAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACrE,OAAO;SACV;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SAClC;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;QAC9C,SAAS,CAAC,IAAI,CAAC,EAAM,QAAQ,UAAA,EAAM,OAAO,SAAA,EAAM,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,2BAAI,GAAJ,UAAK,KAAa,EAAE,QAAkB,EAAE,OAAa;QACjD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACrE,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SAClC;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;QAC9C,SAAS,CAAC,IAAI,CAAC;YACX,QAAQ,UAAA;YACR,OAAO,SAAA;YACP,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACH,0BAAG,GAAH,UAAI,KAAa,EAAE,QAAmB,EAAE,OAAa;QACjD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO;SACV;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,EAAE;YACX,qBAAqB;YACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;SACV;QACD,WAAW;QACX,IAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ;YAC/C,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAChC,OAAO,IAAI,CAAC;aACf;YACD,IAAI,OAAO,KAAK,SAAS,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;gBACvD,OAAO,IAAI,CAAC;aACf;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;;;OAIG;IACH,2BAAI,GAAJ,UAAK,KAAa,EAAE,IAAU;;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO;SACV;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,OAAO;SACV;QACD,6BAA6B;QAC7B,IAAM,aAAa,YAAO,SAAS,CAAC,CAAC;QACrC,IAAM,aAAa,GAAqB,EAAE,CAAC;;YAC3C,KAAuB,IAAA,kBAAA,SAAA,aAAa,CAAA,4CAAA,uEAAE;gBAAjC,IAAM,QAAQ,0BAAA;gBACf,IAAI;oBACA,IAAI,QAAQ,CAAC,OAAO,EAAE;wBAClB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAClD;yBAAM;wBACH,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;qBAC3B;oBACD,WAAW;oBACX,IAAI,QAAQ,CAAC,IAAI,EAAE;wBACf,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAChC;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,yCAAsC,KAAK,QAAI,EAAE,KAAK,CAAC,CAAC;iBACzE;aACJ;;;;;;;;;QACD,WAAW;QACX,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAjC,CAAiC,CAAC,CAAC;YAC3F,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;aAClD;SACJ;IACL,CAAC;IAED;;;;OAIG;IACH,mCAAY,GAAZ,UAAa,KAAa;QACtB,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,uCAAgB,GAAhB,UAAiB,KAAa;QAC1B,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,oCAAa,GAAb;QACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,yCAAkB,GAAlB;QACI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,iDAA0B,GAA1B,UAA2B,KAAa;QACpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,8BAAO,GAAP;QACI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,mCAAY,GAAZ;;QACI,IAAM,IAAI,GAAQ,EAAE,CAAC;;YACrB,KAAiC,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAA,gBAAA,4BAAE;gBAAvC,IAAA,KAAA,mBAAkB,EAAjB,KAAK,QAAA,EAAE,SAAS,QAAA;gBACxB,IAAI,CAAC,KAAK,CAAC,GAAG;oBACV,KAAK,EAAE,SAAS,CAAC,MAAM;oBACvB,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,CAAC;wBAClC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO;wBAC9B,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI;wBACvB,YAAY,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW;qBACtD,CAAC,EAJmC,CAInC,CAAC;iBACN,CAAC;aACL;;;;;;;;;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,qCAAc,GAAd;QACI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IACjE,CAAC;IACL,mBAAC;AAAD,CArQA,AAqQC,IAAA;AArQY,oCAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["/**\n * 事件管理器\n * 提供事件的注册、触发和管理功能\n */\n\n/*** 事件监听器接口*/\ninterface IEventListener {\n    callback: Function;\n    context?: any;\n    once?: boolean;\n}\n\n/**\n * 事件管理器类\n */\nexport class EventManager {\n    private static _globalInstance: EventManager;\n    private _listeners: Map<string, IEventListener[]> = new Map();\n    private _isDestroyed: boolean = false;\n    private _instanceId: string;\n\n    constructor(instanceId?: string) {\n        this._instanceId = instanceId || `eventManager_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n    }\n\n    /** 获取全局单例实例 */\n    static getGlobal(): EventManager {\n        if (!EventManager._globalInstance) {\n            EventManager._globalInstance = new EventManager(\"global\");\n        }\n        return EventManager._globalInstance;\n    }\n\n    /** 创建新的局部实例 */\n    static createLocal(instanceId?: string): EventManager {\n        return new EventManager(instanceId);\n    }\n\n    /** 全局事件快捷方法 */\n    static on(event: string, callback: Function, context?: any): void {\n        EventManager.getGlobal().on(event, callback, context);\n    }\n\n    static once(event: string, callback: Function, context?: any): void {\n        EventManager.getGlobal().once(event, callback, context);\n    }\n\n    static off(event: string, callback?: Function, context?: any): void {\n        EventManager.getGlobal().off(event, callback, context);\n    }\n\n    static emit(event: string, data?: any): void {\n        EventManager.getGlobal().emit(event, data);\n    }\n\n    static hasListeners(event: string): boolean {\n        return EventManager.getGlobal().hasListeners(event);\n    }\n\n    static getListenerCount(event: string): number {\n        return EventManager.getGlobal().getListenerCount(event);\n    }\n\n    static getEventNames(): string[] {\n        return EventManager.getGlobal().getEventNames();\n    }\n\n    static cleanup(): void {\n        if (EventManager._globalInstance) {\n            EventManager._globalInstance.cleanup();\n            EventManager._globalInstance = null;\n        }\n    }\n\n    static getDebugInfo(): any {\n        return EventManager.getGlobal().getDebugInfo();\n    }\n\n    /** 获取实例ID */\n    get instanceId(): string {     return this._instanceId; }\n\n    /**\n     * 注册事件监听器\n     * @param event 事件名称\n     * @param callback 回调函数\n     * @param context 上下文对象\n     */\n    on(event: string, callback: Function, context?: any): void {\n        if (this._isDestroyed) {\n            console.warn(\"EventManager has been destroyed, cannot add listener\");\n            return;\n        }\n        if (!this._listeners.has(event)) {\n            this._listeners.set(event, []);\n        }\n        const listeners = this._listeners.get(event)!;\n        listeners.push({     callback,     context,     once: false });\n    }\n\n    /**\n     * 注册一次性事件监听器\n     * @param event 事件名称\n     * @param callback 回调函数\n     * @param context 上下文对象\n     */\n    once(event: string, callback: Function, context?: any): void {\n        if (this._isDestroyed) {\n            console.warn(\"EventManager has been destroyed, cannot add listener\");\n            return;\n        }\n\n        if (!this._listeners.has(event)) {\n            this._listeners.set(event, []);\n        }\n\n        const listeners = this._listeners.get(event)!;\n        listeners.push({\n            callback,\n            context,\n            once: true\n        });\n    }\n\n    /**\n     * 移除事件监听器\n     * @param event 事件名称\n     * @param callback 回调函数\n     * @param context 上下文对象\n     */\n    off(event: string, callback?: Function, context?: any): void {\n        if (this._isDestroyed) {\n            return;\n        }\n        const listeners = this._listeners.get(event);\n        if (!listeners) {\n            return;\n        }\n        if (!callback) {\n            // 如果没有指定回调函数，移除所有监听器\n            this._listeners.delete(event);\n            return;\n        }\n        // 移除匹配的监听器\n        const filteredListeners = listeners.filter(listener => {\n            if (listener.callback !== callback) {\n                return true;\n            }\n            if (context !== undefined && listener.context !== context) {\n                return true;\n            }\n            return false;\n        });\n        if (filteredListeners.length === 0) {\n            this._listeners.delete(event);\n        } else {\n            this._listeners.set(event, filteredListeners);\n        }\n    }\n\n    /**\n     * 触发事件\n     * @param event 事件名称\n     * @param data 事件数据\n     */\n    emit(event: string, data?: any): void {\n        if (this._isDestroyed) {\n            return;\n        }\n        const listeners = this._listeners.get(event);\n        if (!listeners || listeners.length === 0) {\n            return;\n        }\n        // 创建监听器副本，避免在回调中修改监听器列表时出现问题\n        const listenersCopy = [...listeners];\n        const onceListeners: IEventListener[] = [];\n        for (const listener of listenersCopy) {\n            try {\n                if (listener.context) {\n                    listener.callback.call(listener.context, data);\n                } else {\n                    listener.callback(data);\n                }\n                // 收集一次性监听器\n                if (listener.once) {\n                    onceListeners.push(listener);\n                }\n            } catch (error) {\n                console.error(`Error in event listener for event \"${event}\":`, error);\n            }\n        }\n        // 移除一次性监听器\n        if (onceListeners.length > 0) {\n            const remainingListeners = listeners.filter(listener => !onceListeners.includes(listener));\n            if (remainingListeners.length === 0) {\n                this._listeners.delete(event);\n            } else {\n                this._listeners.set(event, remainingListeners);\n            }\n        }\n    }\n\n    /**\n     * 检查是否有指定事件的监听器\n     * @param event 事件名称\n     * @returns 是否有监听器\n     */\n    hasListeners(event: string): boolean {\n        const listeners = this._listeners.get(event);\n        return listeners !== undefined && listeners.length > 0;\n    }\n\n    /**\n     * 获取指定事件的监听器数量\n     * @param event 事件名称\n     * @returns 监听器数量\n     */\n    getListenerCount(event: string): number {\n        const listeners = this._listeners.get(event);\n        return listeners ? listeners.length : 0;\n    }\n\n    /**\n     * 获取所有事件名称\n     * @returns 事件名称数组\n     */\n    getEventNames(): string[] {\n        return Array.from(this._listeners.keys());\n    }\n\n    /**\n     * 移除所有监听器\n     */\n    removeAllListeners(): void {\n        this._listeners.clear();\n    }\n\n    /**\n     * 移除指定事件的所有监听器\n     * @param event 事件名称\n     */\n    removeAllListenersForEvent(event: string): void {\n        this._listeners.delete(event);\n    }\n\n    /**\n     * 清理事件管理器\n     */\n    cleanup(): void {\n        this.removeAllListeners();\n        this._isDestroyed = true;\n    }\n\n    /**\n     * 获取调试信息\n     */\n    getDebugInfo(): any {\n        const info: any = {};\n        for (const [event, listeners] of this._listeners) {\n            info[event] = {\n                count: listeners.length,\n                listeners: listeners.map(listener => ({\n                    hasContext: !!listener.context,\n                    isOnce: !!listener.once,\n                    functionName: listener.callback.name || 'anonymous'\n                }))\n            };\n        }\n        return info;\n    }\n\n    /**\n     * 打印调试信息\n     */\n    printDebugInfo(): void {\n        console.log(\"EventManager Debug Info:\", this.getDebugInfo());\n    }\n}\n"]}