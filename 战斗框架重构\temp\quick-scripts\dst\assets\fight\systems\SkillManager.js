
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/SkillManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0f286gbfotB7b10POxiFsbs', 'SkillManager');
// fight/systems/SkillManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillManager = void 0;
var FightEvent_1 = require("../types/FightEvent");
var ISkill_1 = require("../types/ISkill");
var EventManager_1 = require("./EventManager");
/**
 * 技能管理器
 * 负责管理角色的技能学习、使用和冷却
 */
var SkillManager = /** @class */ (function () {
    function SkillManager(character) {
        this._skills = new Map();
        this._basicAttackSkill = null;
        this._character = character;
        this._eventManager = EventManager_1.EventManager.createLocal("SkillManager_" + character.characterName);
    }
    /**
     * 添加技能
     * @param skill 技能实例
     */
    SkillManager.prototype.addSkill = function (skill) {
        if (this._skills.has(skill.id)) {
            console.warn("Skill " + skill.id + " already exists, replacing...");
        }
        this._skills.set(skill.id, skill);
        // 如果是基础攻击技能，设置为默认攻击技能
        if (skill.type === ISkill_1.SkillType.BASIC_ATTACK) {
            this._basicAttackSkill = skill;
        }
        this._eventManager.emit(FightEvent_1.default.skillAdded, { skill: skill, character: this._character });
    };
    /**
     * 移除技能
     * @param skillId 技能ID
     * @returns 是否移除成功
     */
    SkillManager.prototype.removeSkill = function (skillId) {
        var _a;
        var skill = this._skills.get(skillId);
        if (!skill) {
            return false;
        }
        this._skills.delete(skillId);
        // 如果移除的是基础攻击技能，清空引用
        if (((_a = this._basicAttackSkill) === null || _a === void 0 ? void 0 : _a.id) === skillId) {
            this._basicAttackSkill = null;
        }
        this._eventManager.emit(FightEvent_1.default.skillRemoved, { skill: skill, character: this._character });
        return true;
    };
    /**
     * 获取技能
     * @param skillId 技能ID
     * @returns 技能实例或null
     */
    SkillManager.prototype.getSkill = function (skillId) {
        return this._skills.get(skillId) || null;
    };
    /**
     * 获取基础攻击技能
     * @returns 基础攻击技能或null
     */
    SkillManager.prototype.getBasicAttackSkill = function () {
        return this._basicAttackSkill;
    };
    /**
     * 设置基础攻击技能
     * @param skillId 技能ID
     */
    SkillManager.prototype.setBasicAttackSkill = function (skillId) {
        var skill = this._skills.get(skillId);
        if (skill) {
            this._basicAttackSkill = skill;
        }
    };
    /**
     * 获取所有技能
     * @returns 技能数组
     */
    SkillManager.prototype.getAllSkills = function () {
        return Array.from(this._skills.values());
    };
    /**
     * 根据类型获取技能
     * @param type 技能类型
     * @returns 技能数组
     */
    SkillManager.prototype.getSkillsByType = function (type) {
        return this.getAllSkills().filter(function (skill) { return skill.type === type; });
    };
    /**
     * 获取可用的技能
     * @returns 可用技能数组
     */
    SkillManager.prototype.getAvailableSkills = function () {
        return this.getAllSkills().filter(function (skill) { return skill.canUse; });
    };
    /**
     * 获取冷却中的技能
     * @returns 冷却中技能数组
     */
    SkillManager.prototype.getCooldownSkills = function () {
        return this.getAllSkills().filter(function (skill) { return !skill.canUse && skill.remainingCooldown > 0; });
    };
    /**
     * 检查是否拥有技能
     * @param skillId 技能ID
     * @returns 是否拥有
     */
    SkillManager.prototype.hasSkill = function (skillId) {
        return this._skills.has(skillId);
    };
    /**
     * 检查技能是否可用
     * @param skillId 技能ID
     * @returns 是否可用
     */
    SkillManager.prototype.isSkillAvailable = function (skillId) {
        var skill = this._skills.get(skillId);
        return skill ? skill.canUse : false;
    };
    /**
     * 获取技能冷却剩余时间
     * @param skillId 技能ID
     * @returns 剩余冷却时间（秒）
     */
    SkillManager.prototype.getSkillCooldownRemaining = function (skillId) {
        var skill = this._skills.get(skillId);
        return skill ? skill.remainingCooldown : 0;
    };
    /**
     * 重置技能冷却
     * @param skillId 技能ID，如果不指定则重置所有技能
     */
    SkillManager.prototype.resetCooldown = function (skillId) {
        var e_1, _a;
        if (skillId) {
            var skill = this._skills.get(skillId);
            if (skill) {
                skill.resetCooldown();
                this._eventManager.emit(FightEvent_1.default.skillCooldownReset, { skill: skill, character: this._character });
            }
        }
        else {
            try {
                for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var skill = _c.value;
                    skill.resetCooldown();
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_1) throw e_1.error; }
            }
            this._eventManager.emit(FightEvent_1.default.allSkillsCooldownReset, { character: this._character });
        }
    };
    /**
     * 使用技能
     * @param skillId 技能ID
     * @param target 目标角色
     * @param position 目标位置
     * @returns 是否使用成功
     */
    SkillManager.prototype.useSkill = function (skillId, target, position) {
        var skill = this._skills.get(skillId);
        if (!skill) {
            console.warn("Skill " + skillId + " not found");
            return false;
        }
        if (!skill.canCastOn(this._character, target)) {
            console.warn("Cannot cast skill " + skillId + " on target");
            return false;
        }
        var success = skill.cast(this._character, target, [], position);
        if (success) {
            this._eventManager.emit(FightEvent_1.default.skillUsed, { skill: skill, character: this._character, target: target, position: position });
        }
        return success;
    };
    /**
     * 更新所有技能状态
     * @param deltaTime 时间间隔
     */
    SkillManager.prototype.update = function (deltaTime) {
        var e_2, _a;
        try {
            for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var skill = _c.value;
                skill.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
    };
    /**
     * 获取技能统计信息
     * @returns 统计信息
     */
    SkillManager.prototype.getSkillStats = function () {
        var e_3, _a;
        var stats = {
            totalSkills: this._skills.size,
            availableSkills: 0,
            cooldownSkills: 0,
            skillsByType: {}
        };
        try {
            for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var skill = _c.value;
                if (skill.canUse) {
                    stats.availableSkills++;
                }
                else {
                    stats.cooldownSkills++;
                }
                if (!stats.skillsByType[skill.type]) {
                    stats.skillsByType[skill.type] = 0;
                }
                stats.skillsByType[skill.type]++;
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return stats;
    };
    /**
     * 导出技能数据
     * @returns 技能数据
     */
    SkillManager.prototype.exportSkillData = function () {
        var e_4, _a;
        var data = {};
        try {
            for (var _b = __values(this._skills), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), id = _d[0], skill = _d[1];
                data[id] = {
                    id: skill.id,
                    name: skill.name,
                    type: skill.type,
                    level: skill.level,
                    cooldown: skill.cooldown,
                    remainingCooldown: skill.remainingCooldown,
                    canUse: skill.canUse
                };
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return data;
    };
    Object.defineProperty(SkillManager.prototype, "eventManager", {
        /**
         * 获取事件管理器（供外部直接使用，避免包装方法冗余）
         */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理技能管理器 */
    SkillManager.prototype.cleanup = function () {
        this._skills.clear();
        this._basicAttackSkill = null;
        this._eventManager.cleanup();
    };
    /** * 获取调试信息 */
    SkillManager.prototype.getDebugInfo = function () {
        var _a;
        return {
            characterId: this._character.id,
            skillCount: this._skills.size,
            basicAttackSkill: ((_a = this._basicAttackSkill) === null || _a === void 0 ? void 0 : _a.id) || null,
            skills: this.exportSkillData(),
            stats: this.getSkillStats()
        };
    };
    return SkillManager;
}());
exports.SkillManager = SkillManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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