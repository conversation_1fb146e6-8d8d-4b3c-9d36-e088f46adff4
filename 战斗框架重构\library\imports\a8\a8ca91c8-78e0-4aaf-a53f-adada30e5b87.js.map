{"version": 3, "sources": ["assets\\fight\\timeline\\TimelineEvents.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,4CAAyD;AACzD,gDAAkE;AAClE,uCAA2C;AAC3C,qEAAoE;AAGpE,0DAAyD;AAGzD,WAAW;AACX;IAAyC,uCAAa;IAClD,6BACI,EAAU,EACF,YAAoB,EACpB,UAA4C,EAC5C,IAAsB;QADtB,2BAAA,EAAA,aAAyB,oBAAU,CAAC,QAAQ;QAC5C,qBAAA,EAAA,SAAsB;QAJlC,YAKI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,MAAM,CAAC,SAAG;QAH9B,kBAAY,GAAZ,YAAY,CAAQ;QACpB,gBAAU,GAAV,UAAU,CAAkC;QAC5C,UAAI,GAAJ,IAAI,CAAkB;;IACO,CAAC;IAE1C,qCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO;SACV;QACD,mBAAmB;QACnB,IAAM,aAAa,GAAG,6BAAa,CAAC,QAAQ,CAAC,aAAa,CAAA;QAC1D,IAAM,UAAU,GAAG,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC1F,IAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpG,OAAO,CAAC,GAAG,CAAC,sBAAoB,MAAM,CAAC,aAAa,eAAU,UAAU,CAAC,WAAW,SAAI,IAAI,CAAC,UAAU,mBAAc,MAAM,CAAC,aAAa,IAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC,CAAC;IAC/L,CAAC;IACL,0BAAC;AAAD,CArBA,AAqBC,CArBwC,wBAAa,GAqBrD;AArBY,kDAAmB;AAuBhC,WAAW;AACX;IAAuC,qCAAa;IAChD,2BACI,EAAU,EACF,UAAkB;QAF9B,YAGI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,IAAI,CAAC,SAAG;QAD5B,gBAAU,GAAV,UAAU,CAAQ;;IACS,CAAC;IACxC,mCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,CAAC,SAAS;QAC5E,OAAO,CAAC,GAAG,CAAC,sBAAoB,MAAM,CAAC,aAAa,eAAU,MAAM,CAAC,aAAa,aAAQ,IAAI,CAAC,UAAY,CAAC,CAAC;QAC7G,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IACL,wBAAC;AAAD,CAXA,AAWC,CAXsC,wBAAa,GAWnD;AAXY,8CAAiB;AAY9B,eAAe;AACf;IAA6C,2CAAa;IACtD,iCACI,EAAU,EACF,cAA+B,EAC/B,gBAAyB,EACzB,gBAAyB,EACzB,OAAgB;QAL5B,YAMI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,WAAW,CAAC,SAAG;QAJnC,oBAAc,GAAd,cAAc,CAAiB;QAC/B,sBAAgB,GAAhB,gBAAgB,CAAS;QACzB,sBAAgB,GAAhB,gBAAgB,CAAS;QACzB,aAAO,GAAP,OAAO,CAAS;;IACkB,CAAC;IAC/C,yCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC/D,OAAO;SACV;QACD,OAAO,CAAC,GAAG,CAAC,sBAAoB,MAAM,CAAC,aAAa,yBAAoB,MAAM,CAAC,aAAe,CAAC,CAAC;QAChG,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhD,cAAc;QACd,IAAI,MAAM,EAAE;YACR,IAAM,aAAa,GAAG,6BAAa,CAAC,QAAQ,CAAC,aAAa,CAAA;YAC1D,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,6BAA2B,MAAM,CAAC,EAAE,4BAAyB,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC3F;IACL,CAAC;IACL,8BAAC;AAAD,CAjCA,AAiCC,CAjC4C,wBAAa,GAiCzD;AAjCY,0DAAuB;AAmCpC,eAAe;AACf;IAA0C,wCAAa;IACnD,8BACI,EAAU,EACF,MAAc,EACd,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAHvC,YAII,kBAAM,EAAE,EAAE,6BAAiB,CAAC,QAAQ,CAAC,SAAG;QAFhC,YAAM,GAAN,MAAM,CAAQ;QACd,gBAAU,GAAV,UAAU,CAAiB;;IACI,CAAC;IAC5C,sCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnF,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO;SACV;QACD,OAAO,CAAC,GAAG,CAAC,kCAAgC,IAAI,CAAC,MAAM,YAAO,MAAM,CAAC,aAAe,CAAC,CAAC;QAEtF,qCAAqC;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,IAAI,EAAE;YACN,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,8CAA4C,IAAI,CAAC,MAAM,YAAO,MAAM,CAAC,aAAe,CAAC,CAAC;SACrG;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4CAA0C,IAAI,CAAC,MAAQ,CAAC,CAAC;SACzE;IACL,CAAC;IAED,yBAAyB;IACjB,yCAAU,GAAlB,UAAmB,MAAc,EAAE,MAAkB,EAAE,MAAkB;QACrE,QAAQ,MAAM,EAAE;YACZ,KAAK,gBAAgB,CAAC;YACtB,KAAK,YAAY;gBACb,OAAO,IAAI,2CAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACpD,oBAAoB;YACpB;gBACI,OAAO,CAAC,IAAI,CAAC,sBAAoB,MAAQ,CAAC,CAAC;gBAC3C,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IACL,2BAAC;AAAD,CArCA,AAqCC,CArCyC,wBAAa,GAqCtD;AArCY,oDAAoB;AAuCjC,aAAa;AACb;IAAgD,8CAAa;IACzD,oCACI,EAAU,EACF,aAAqB,EACrB,IAAqB,EACrB,UAA0B;QAD1B,qBAAA,EAAA,YAAqB;QACrB,2BAAA,EAAA,iBAA0B;QAJtC,YAKI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,cAAc,CAAC,SAAG;QAHtC,mBAAa,GAAb,aAAa,CAAQ;QACrB,UAAI,GAAJ,IAAI,CAAiB;QACrB,gBAAU,GAAV,UAAU,CAAgB;;IACW,CAAC;IAClD,4CAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnF,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAClE,OAAO;SACV;QACD,OAAO,CAAC,GAAG,CAAC,wCAAsC,IAAI,CAAC,aAAa,YAAO,MAAM,CAAC,aAAe,CAAC,CAAC;QACnG,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SACxD;IACL,CAAC;IACL,iCAAC;AAAD,CApBA,AAoBC,CApB+C,wBAAa,GAoB5D;AApBY,gEAA0B;AAsBvC,aAAa;AACb;IAA4C,0CAAa;IACrD,gCACI,EAAU,EACF,OAAe,EACf,MAAoB;QAApB,uBAAA,EAAA,YAAoB;QAHhC,YAII,kBAAM,EAAE,EAAE,6BAAiB,CAAC,UAAU,CAAC,SAAG;QAFlC,aAAO,GAAP,OAAO,CAAQ;QACf,YAAM,GAAN,MAAM,CAAc;;IACa,CAAC;IAC9C,wCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,OAAO,CAAC,GAAG,CAAC,oCAAkC,IAAI,CAAC,OAAS,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IACL,6BAAC;AAAD,CAVA,AAUC,CAV2C,wBAAa,GAUxD;AAVY,wDAAsB;AAWnC,aAAa;AACb;IAA6C,2CAAa;IACtD,iCACI,EAAU,EACF,QAAgB,EAChB,cAA8B,EAC9B,MAA8B;QAD9B,+BAAA,EAAA,qBAA8B;QAC9B,uBAAA,EAAA,SAAkB,EAAE,CAAC,IAAI,CAAC,IAAI;QAJ1C,YAKI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,WAAW,CAAC,SAAG;QAHnC,cAAQ,GAAR,QAAQ,CAAQ;QAChB,oBAAc,GAAd,cAAc,CAAgB;QAC9B,YAAM,GAAN,MAAM,CAAwB;;IACI,CAAC;IAC/C,yCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAI,QAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACxD,IAAI,MAAM,EAAE;gBACR,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC/E;iBAAM;gBACH,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxF;SACJ;aAAM;YACH,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACxF;QACD,OAAO,CAAC,GAAG,CAAC,qCAAmC,IAAI,CAAC,QAAQ,iBAAc,EAAE,QAAQ,CAAC,CAAC;QACtF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC7C,CAAC;IACL,8BAAC;AAAD,CAtBA,AAsBC,CAtB4C,wBAAa,GAsBzD;AAtBY,0DAAuB;AAuBpC,aAAa;AACb;IAAuC,qCAAa;IAChD,2BACI,EAAU,EACF,cAAuB,EACvB,QAAsB,EACtB,MAAyB;QADzB,yBAAA,EAAA,cAAsB;QACtB,uBAAA,EAAA,iBAAyB;QAJrC,YAKI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,IAAI,CAAC,SAAG;QAH5B,oBAAc,GAAd,cAAc,CAAS;QACvB,cAAQ,GAAR,QAAQ,CAAc;QACtB,YAAM,GAAN,MAAM,CAAmB;;IACE,CAAC;IACxC,mCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,6BAA2B,MAAM,CAAC,aAAa,iBAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAChG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;aAChB,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;aACpD,KAAK,EAAE,CAAA;IAChB,CAAC;IACL,wBAAC;AAAD,CAdA,AAcC,CAdsC,wBAAa,GAcnD;AAdY,8CAAiB;AAe9B,YAAY;AACZ;IAAyC,uCAAa;IAClD,6BACI,EAAU,EACF,cAAgE;QAF5E,YAGI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,MAAM,CAAC,SAAG;QAD9B,oBAAc,GAAd,cAAc,CAAkD;;IACnC,CAAC;IAC1C,qCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAI;YACA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;SAC5C;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2CAAyC,IAAI,CAAC,EAAE,MAAG,EAAE,KAAK,CAAC,CAAC;SAC7E;IACL,CAAC;IACL,0BAAC;AAAD,CAZA,AAYC,CAZwC,wBAAa,GAYrD;AAZY,kDAAmB;AAahC,uBAAuB;AACvB;IAAoD,kDAAa;IAC7D,wCACI,EAAU,EACF,YAAoB,EACpB,UAA4C,EAC5C,IAAsB;QADtB,2BAAA,EAAA,aAAyB,oBAAU,CAAC,QAAQ;QAC5C,qBAAA,EAAA,SAAsB;QAJlC,YAKI,kBAAM,EAAE,EAAE,6BAAiB,CAAC,MAAM,CAAC,SAAG;QAH9B,kBAAY,GAAZ,YAAY,CAAQ;QACpB,gBAAU,GAAV,UAAU,CAAkC;QAC5C,UAAI,GAAJ,IAAI,CAAkB;;IACO,CAAC;IAC1C,gDAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;;QAC1C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YACvE,OAAO;SACV;QACD,OAAO,CAAC,GAAG,CAAC,sBAAoB,MAAM,CAAC,aAAa,6BAAwB,OAAO,CAAC,MAAM,aAAU,CAAC,CAAC;;YACtG,YAAY;YACZ,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;gBAAzB,IAAM,MAAM,oBAAA;gBACb,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;aAC1C;;;;;;;;;IACL,CAAC;IACO,wDAAe,GAAvB,UAAwB,MAAkB,EAAE,MAAkB;QAC1D,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QAC5C,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;QAC1C,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACpC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YACzB,WAAW,GAAG,UAAU,CAAC;SAC5B;QACD,IAAI,IAAI,CAAC,UAAU,KAAK,oBAAU,CAAC,QAAQ,EAAE;YACzC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,CAAC;SACpD;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IACL,qCAAC;AAAD,CAjCA,AAiCC,CAjCmD,wBAAa,GAiChE;AAjCY,wEAA8B;AAkC3C,yBAAyB;AACzB;IAA8C,4CAAa;IACvD,kCACI,EAAU,EACF,SAA2C,EAC3C,YAA2B;QAHvC,YAII,kBAAM,EAAE,EAAE,6BAAiB,CAAC,MAAM,CAAC,SAAG;QAF9B,eAAS,GAAT,SAAS,CAAkC;QAC3C,kBAAY,GAAZ,YAAY,CAAe;;IACE,CAAC;IAC1C,0CAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB;QAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;SAClD;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,kDAAgD,IAAI,CAAC,EAAI,CAAC,CAAC;SAC1E;IACL,CAAC;IACL,+BAAC;AAAD,CAbA,AAaC,CAb6C,wBAAa,GAa1D;AAbY,4DAAwB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { IBulletLauncher } from \"../types/IBullet\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { DamageType, DamageTag } from \"../types/IDamage\";\nimport { TimelineEventType, ITimeline } from \"../types/ITimeline\";\nimport { TimelineEvent } from \"./Timeline\";\nimport { BuffModelBeHurtFight } from \"../buff/BuffModelBeHurtFight\";\nimport { BulletManager } from \"../systems/BulletManager\";\nimport { DamageManager } from \"../systems/DamageManager\";\nimport { BattleManager } from \"../systems/BattleManager\";\n\n\n/*** 伤害事件*/\nexport class DamageTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private damageAmount: number,\n        private damageType: DamageType = DamageType.PHYSICAL,\n        private tags: DamageTag[] = []\n    ) { super(id, TimelineEventType.DAMAGE); }\n\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const target = this.getValidTarget(timeline, nodeIndex);\n        if (!target) {\n            console.warn(`DamageTimelineEvent: No valid target found`);\n            return;\n        }\n        // 使用伤害管理器处理复杂的伤害计算\n        const damageManager = BattleManager.instance.damageManager\n        const baseDamage = this.damageAmount === 0 ? caster.attributes.attack : this.damageAmount;\n        const damageInfo = damageManager.dealDamage(caster, target, baseDamage, this.damageType, this.tags);\n        console.log(`[Timeline-Event] ${caster.characterName} deals ${damageInfo.finalDamage} ${this.damageType} damage to ${target.characterName}${damageInfo.isCritical ? ' (Critical!)' : ''}`);\n    }\n}\n\n/*** 治疗事件*/\nexport class HealTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private healAmount: number\n    ) { super(id, TimelineEventType.HEAL); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const target = this.getValidTarget(timeline, nodeIndex) || caster; // 默认治疗自己\n        console.log(`[Timeline-Event] ${caster.characterName} heals ${target.characterName} for ${this.healAmount}`);\n        target.heal(this.healAmount);\n    }\n}\n/** * 子弹发射事件 */\nexport class FireBulletTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private bulletLauncher: IBulletLauncher,\n        private effectPrefabPath?: string,\n        private hitAnimationName?: string,\n        private soundId?: string\n    ) { super(id, TimelineEventType.FIRE_BULLET); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const target = this.getValidTarget(timeline, nodeIndex);\n        if (!target) {\n            console.warn(`FireBulletTimelineEvent: No valid target found`);\n            return;\n        }\n        console.log(`[Timeline-Event] ${caster.characterName} fires bullet at ${target.characterName}`);\n        if (this.soundId) {\n            this.playSound(this.soundId);\n        }\n        this.bulletLauncher.firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const bullet = this.bulletLauncher.fire(target);\n\n        // 将子弹添加到子弹管理器\n        if (bullet) {\n            const bulletManager = BattleManager.instance.bulletManager\n            bulletManager.addBullet(bullet);\n            console.log(`[Timeline-Event] Bullet ${bullet.id} added to BulletManager`);\n        }\n\n        if (this.effectPrefabPath) {\n            this.playEffect(this.effectPrefabPath, target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));\n        }\n    }\n}\n\n/*** Buff添加事件*/\nexport class AddBuffTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private buffId: string,\n        private targetSelf: boolean = false\n    ) { super(id, TimelineEventType.ADD_BUFF); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);\n        if (!target) {\n            console.warn(`AddBuffTimelineEvent: No valid target found`);\n            return;\n        }\n        console.log(`[Timeline-Event] Adding buff ${this.buffId} to ${target.characterName}`);\n\n        // 简单的 Buff 创建逻辑（可以后续扩展为 BuffFactory）\n        const buff = this.createBuff(this.buffId, caster, target);\n        if (buff) {\n            target.addBuff(buff);\n            console.log(`[Timeline-Event] Successfully added buff ${this.buffId} to ${target.characterName}`);\n        } else {\n            console.warn(`[Timeline-Event] Failed to create buff ${this.buffId}`);\n        }\n    }\n\n    /** 创建 Buff 实例（简单工厂模式） */\n    private createBuff(buffId: string, caster: ICharacter, target: ICharacter): any {\n        switch (buffId) {\n            case \"counter_attack\":\n            case \"hurt_fight\":\n                return new BuffModelBeHurtFight(caster, target);\n            // 可以在这里添加更多 Buff 类型\n            default:\n                console.warn(`Unknown buff ID: ${buffId}`);\n                return null;\n        }\n    }\n}\n\n/*** 动画播放事件*/\nexport class PlayAnimationTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private animationName: string,\n        private loop: boolean = false,\n        private targetSelf: boolean = true\n    ) { super(id, TimelineEventType.PLAY_ANIMATION); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);\n        if (!target) {\n            console.warn(`PlayAnimationTimelineEvent: No valid target found`);\n            return;\n        }\n        console.log(`[Timeline-Event] Playing animation ${this.animationName} on ${target.characterName}`);\n        const spine = target.node.getComponent(sp.Skeleton);\n        if (spine) {\n            spine.setAnimation(0, this.animationName, this.loop);\n        }\n    }\n}\n\n/*** 音效播放事件*/\nexport class PlaySoundTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private soundId: string,\n        private volume: number = 1.0\n    ) { super(id, TimelineEventType.PLAY_SOUND); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        console.log(`[Timeline-Event] Playing sound ${this.soundId}`);\n        this.playSound(this.soundId);\n    }\n}\n/*** 特效播放事件*/\nexport class PlayEffectTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private effectId: string,\n        private targetPosition: boolean = true,\n        private offset: cc.Vec3 = cc.Vec3.ZERO\n    ) { super(id, TimelineEventType.PLAY_EFFECT); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        let position: cc.Vec3;\n        if (this.targetPosition) {\n            const target = this.getValidTarget(timeline, nodeIndex);\n            if (target) {\n                position = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);\n            } else {\n                position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);\n            }\n        } else {\n            position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);\n        }\n        console.log(`[Timeline-Event] Playing effect ${this.effectId} at position`, position);\n        this.playEffect(this.effectId, position);\n    }\n}\n/** * 移动事件 */\nexport class MoveTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private targetPosition: cc.Vec3,\n        private duration: number = 1.0,\n        private easing: string = \"linear\"\n    ) { super(id, TimelineEventType.MOVE); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        console.log(`[Timeline-Event] Moving ${caster.characterName} to position`, this.targetPosition);\n        cc.tween(caster.node)\n            .to(this.duration, { position: this.targetPosition })\n            .start()\n    }\n}\n/*** 自定义事件*/\nexport class CustomTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private customFunction: (timeline: ITimeline, nodeIndex: number) => void\n    ) { super(id, TimelineEventType.CUSTOM); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        try {\n            this.customFunction(timeline, nodeIndex);\n        } catch (error) {\n            console.error(`Error executing custom timeline event ${this.id}:`, error);\n        }\n    }\n}\n/*** 多目标伤害事件（用于AOE技能）*/\nexport class MultiTargetDamageTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private damageAmount: number,\n        private damageType: DamageType = DamageType.PHYSICAL,\n        private tags: DamageTag[] = []\n    ) { super(id, TimelineEventType.DAMAGE); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        const caster = timeline.caster;\n        const targets = this.getTargets(timeline);\n        if (targets.length === 0) {\n            console.warn(`MultiTargetDamageTimelineEvent: No valid targets found`);\n            return;\n        }\n        console.log(`[Timeline-Event] ${caster.characterName} deals AOE damage to ${targets.length} targets`);\n        // 对所有目标造成伤害\n        for (const target of targets) {\n            const finalDamage = this.calculateDamage(caster, target);\n            target.takeDamage(finalDamage, caster);\n        }\n    }\n    private calculateDamage(caster: ICharacter, target: ICharacter): number {\n        const baseAttack = caster.attributes.attack;\n        const defense = target.attributes.defense;\n        let finalDamage = this.damageAmount;\n        if (this.damageAmount === 0) {\n            finalDamage = baseAttack;\n        }\n        if (this.damageType === DamageType.PHYSICAL) {\n            finalDamage = Math.max(1, finalDamage - defense);\n        }\n        return Math.floor(finalDamage);\n    }\n}\n/** * 条件事件（只有满足条件时才执行） */\nexport class ConditionalTimelineEvent extends TimelineEvent {\n    constructor(\n        id: string,\n        private condition: (timeline: ITimeline) => boolean,\n        private wrappedEvent: TimelineEvent\n    ) { super(id, TimelineEventType.CUSTOM); }\n    execute(timeline: ITimeline, nodeIndex: number): void {\n        if (this.condition(timeline)) {\n            this.wrappedEvent.execute(timeline, nodeIndex);\n        } else {\n            console.log(`[Timeline-Event] Condition not met for event ${this.id}`);\n        }\n    }\n}\n"]}