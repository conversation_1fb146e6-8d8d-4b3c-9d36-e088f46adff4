{"version": 3, "sources": ["assets\\card\\cardgame\\src\\EquipmentManager.ts"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,4CAA4C;AAC5C,yCAAyC;AACzC,6CAA6C;AAE7C,eAAe;AACf,0BAA0B;AAC1B,qBAAqB;AACrB,IAAI;AAEJ,mBAAmB;AACnB,IAAI;AAEJ,OAAO;AACP,yBAAyB;AACzB,mEAAmE;AACnE,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,uDAAuD;AACvD,iBAAiB;AACjB,2BAA2B;AAC3B,iBAAiB;AACjB,sCAAsC;AAEtC,gFAAgF;AAChF,4CAA4C;AAC5C,+DAA+D;AAC/D,gDAAgD;AAChD,8CAA8C;AAC9C,kFAAkF;AAClF,yCAAyC;AACzC,wCAAwC;AACxC,8CAA8C;AAE9C,wFAAwF;AACxF,0FAA0F;AAC1F,2BAA2B;AAC3B,QAAQ;AAER,mCAAmC;AACnC,yEAAyE;AACzE,QAAQ;AAER,oCAAoC;AACpC,8BAA8B;AAC9B,gEAAgE;AAChE,YAAY;AACZ,yEAAyE;AACzE,QAAQ;AAER,qCAAqC;AACrC,qFAAqF;AACrF,4BAA4B;AAC5B,gEAAgE;AAChE,wBAAwB;AACxB,yFAAyF;AACzF,2FAA2F;AAC3F,gBAAgB;AAChB,YAAY;AACZ,QAAQ;AACR,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import Base from \"./Base/Base\";\r\n// import Equipment from \"./Base/Equipment\";\r\n// import { ECamp } from \"./CampManager\";\r\n// import FightManager from \"./FightManager\";\r\n\r\n// /**buff类型 */\r\n// export enum EBuffType {\r\n//     None = `None`,\r\n// }\r\n\r\n// declare global {\r\n// }\r\n\r\n// /** \r\n//  * @features :  游戏装备管理\r\n//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中\r\n//  * @Date : 2020-08-12 23:28:43\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:01:01\r\n//  * @LastEditors : judu233\r\n//  */\r\n// export default class EquipmentManager extends Base {\r\n//     /**技能数据 */\r\n//     data: IBuffDataType;\r\n//     /**所有装备 */\r\n//     equipmentList: Equipment[] = []\r\n\r\n//     addEquipment(name: string, data: { defence?: number, attack?: number }) {\r\n//         const equipment = new Equipment()\r\n//         equipment.durable = data.defence ?? data.attack ?? 3\r\n//         equipment.defence = data.defence ?? 2\r\n//         equipment.attack = data.attack ?? 2\r\n//         equipment.orginValue = data.defence ?? data.attack ?? equipment.durable\r\n//         equipment.equipmentName = name\r\n//         equipment.camp = ECamp.Player\r\n//         this.equipmentList.push(equipment);\r\n\r\n//         FightManager.campManager.getPlayerCamp().getRole().attack += equipment.attack\r\n//         FightManager.campManager.getPlayerCamp().getRole().defence += equipment.defence\r\n//         return equipment\r\n//     }\r\n\r\n//     hasEquipment(name: string) {\r\n//         return this.equipmentList.some(e => e.equipmentName === name);\r\n//     }\r\n\r\n//     getEquipment(name?: string) {\r\n//         if (name == null) {\r\n//             return this.equipmentList.arrayRand<Equipment>();\r\n//         }\r\n//         return this.equipmentList.find(e => e.equipmentName === name);\r\n//     }\r\n\r\n//     deletEquipment(name: string) {\r\n//         const index = this.equipmentList.findIndex(e => e.equipmentName === name);\r\n//         if (index >= 0) {\r\n//             let eq = this.equipmentList.splice(index, 1)?.[0]\r\n//             if (eq) {\r\n//                 FightManager.campManager.getPlayerCamp().getRole().attack -= eq.attack\r\n//                 FightManager.campManager.getPlayerCamp().getRole().defence -= eq.defence\r\n//             }\r\n//         }\r\n//     }\r\n// }\r\n"]}