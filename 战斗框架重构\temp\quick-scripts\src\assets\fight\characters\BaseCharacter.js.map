{"version": 3, "sources": ["assets\\fight\\characters\\BaseCharacter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAqD;AACrD,wDAAuD;AACvD,wDAAuD;AACvD,0DAA4F;AAC5F,kDAA6C;AAK7C,6DAA4D;AAE5D,WAAW;AACX;IAA4C,iCAAY;IAAxD;QAAA,qEA2QC;QAtQa,YAAM,GAAmB,+BAAc,CAAC,IAAI,CAAC;QAC7C,gBAAU,GAAuB,mCAAkB,CAAC,IAAI,CAAC;QAanE,OAAO;QACG,aAAO,GAAqB,EAAE,CAAC;;IAuP7C,CAAC;IApPG,sBAAI,6BAAE;QADN,iBAAiB;aACjB,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,wCAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAClD,sBAAI,+BAAI;aAAR,cAA4B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAChD,sBAAI,qCAAU;aAAd,cAAyC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IACnE,sBAAI,iCAAM;aAAV,cAAwB,OAAO,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IACrE,sBAAI,iCAAM;aAAV,cAAsC,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;;;OAAA;IACjF,sBAAI,gCAAK;aAAT,cAAoC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACrE,sBAAI,uCAAY;aAAhB,cAAmC,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC/D,sBAAI,sCAAW;aAAf,cAAiC,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IAE5D,sBAAI,gCAAK;QADT,OAAO;aACP,cAA8B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IACnD,sBAAI,oCAAS;aAAb,cAAsC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aAE/D,UAAc,KAAyB,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;;;OAFN;IAC/D,sBAAI,gCAAK;aAAT,cAA2B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAGhD,YAAY;IACF,8BAAM,GAAhB;QACI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,aAAa;IACH,6BAAK,GAAf;QACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,eAAe;IACf,8BAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO;QACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED,aAAa;IACH,iCAAS,GAAnB;QACI,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,cAAc;IACJ,oCAAY,GAAtB;QACI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACJ,4CAAoB,GAA9B;QACI,IAAI,CAAC,WAAW,GAAG,IAAI,yCAAmB,EAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,gBAAa,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAE,CAAC,CAAC;IACzF,CAAC;IAED,gBAAgB;IACN,uCAAe,GAAzB;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IAClE,CAAC;IAED,gBAAgB;IACN,2CAAmB,GAA7B;QACI,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAU,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,oBAAU,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,aAAa;IACb,4BAAI,GAAJ,UAAK,SAAkB;QACnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO,EAAE;YACvD,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC,QAAQ,CAAC,+BAAc,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACb,8BAAM,GAAN,UAAO,MAAmB;QACtB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO,EAAE;YACvD,OAAO,KAAK,CAAC;SAChB;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;QAC7D,IAAI,WAAW,EAAE;YACb,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC,CAAC;SACvD;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACb,iCAAS,GAAT,UAAU,SAAiB,EAAE,MAAgB;;QACzC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,QAAQ,EAAE;YACxD,OAAO,KAAK,CAAC;SAChB;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAChC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,+BAAc,CAAC,OAAO,CAAC,CAAC;QACtC,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9G,IAAI,OAAO,EAAE;YACT,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,WAAW,mDAAG,IAAI,EAAE,SAAS,EAAE;YAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;SACjF;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IACD,aAAa;IACb,kCAAU,GAAV,UAAW,KAAa;QACpB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IACD,eAAe;IACf,+BAAO,GAAP,UAAQ,IAAW;;QACf,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChC,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,WAAW,mDAAG,IAAI,EAAE,IAAI,EAAE;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;IAC7E,CAAC;IACD,aAAa;IACb,kCAAU,GAAV,UAAW,MAAc;;QACrB,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,OAAO,EAAE;YACT,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,aAAa,mDAAG,IAAI,EAAE,MAAM,EAAE;YAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;SAChF;IACL,CAAC;IACD,WAAW;IACX,kCAAU,GAAV,UAAW,MAAc,EAAE,QAAqB;;QAC5C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,UAAU,EAAE;YAC1D,OAAO;SACV;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;QACnC,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,YAAY,mDAAG,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE;QACpD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;QACtF,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;SACd;IACL,CAAC;IACD,WAAW;IACX,4BAAI,GAAJ,UAAK,UAAkB;;QACnB,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO;QACxB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACtC,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,MAAM,mDAAG,IAAI,EAAE,UAAU,EAAE;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,YAAA,EAAE,CAAC,CAAC;IAC9E,CAAC;IACD,aAAa;IACb,2BAAG,GAAH;;QACI,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO;QACxB,IAAI,CAAC,QAAQ,CAAC,+BAAc,CAAC,IAAI,CAAC,CAAC;QACnC,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,OAAO,mDAAG,IAAI,EAAE;QAC7B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IACD,gBAAgB;IAChB,iCAAS,GAAT,UAAU,MAAwB;QAC9B,IAAI,CAAC,OAAO,yBAAQ,IAAI,CAAC,OAAO,GAAK,MAAM,CAAE,CAAC;IAClD,CAAC;IACD,eAAe;IACL,gCAAQ,GAAlB,UAAmB,QAAwB;QACvC,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;YAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAC7B,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;SAC7F;IACL,CAAC;IACD,qBAAqB;IACX,+BAAO,GAAjB;QACI,OAAO,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,MAAM,CAAC;IACxF,CAAC;IACD,eAAe;IACL,yCAAiB,GAA3B,UAA4B,MAAc;QACtC,iBAAiB;QACjB,OAAO,IAAI,CAAC,CAAC,OAAO;IACxB,CAAC;IACD,eAAe;IACL,wCAAgB,GAA1B,UAA2B,MAAc;QACrC,iBAAiB;IACrB,CAAC;IACD,gBAAgB;IACN,4CAAoB,GAA9B,UAA+B,IAAc;QACzC,OAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,aAAa,CAAe,CAAC;IAC3D,CAAC;IACD,eAAe;IACL,wCAAgB,GAA1B,UAA2B,IAAc;QACrC,OAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;IACrD,CAAC;IACD,eAAe;IACL,wCAAgB,GAA1B;QACI,OAAO,UAAQ,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;IAC/E,CAAC;IACD,WAAW;IACD,+BAAO,GAAjB;;QACI,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,GAAG;QAC9B,MAAA,IAAI,CAAC,YAAY,0CAAE,OAAO,GAAG;QAC7B,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,GAAG;IAClC,CAAC;IAMD,YAAY;IACF,0CAAkB,GAA5B,UAA6B,MAAW;IACxC,CAAC;IACS,sCAAc,GAAxB,UAAyB,MAAW;QAChC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IACS,+BAAO,GAAjB,UAAkB,MAAW;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IACD,WAAW;IACD,uCAAe,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO;QACnD,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,QAAQ,IAAI,CAAC,MAAM,EAAE;YACjB,KAAK,+BAAc,CAAC,IAAI;gBACpB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC3C,MAAM;YACV,KAAK,+BAAc,CAAC,MAAM;gBACtB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC3C,MAAM;YACV,KAAK,+BAAc,CAAC,SAAS;gBACzB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAC7C,MAAM;YACV,KAAK,+BAAc,CAAC,IAAI;gBACpB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBAC5C,MAAM;SACb;QACD,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,IAAI,CAAC,CAAC;SACnF;IACL,CAAC;IACD,eAAe;IACL,0CAAkB,GAA5B;;QACI,IAAI,IAAI,CAAC,MAAM,WAAI,IAAI,CAAC,gBAAgB,0CAAE,KAAK,CAAA,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACnE;IACL,CAAC;IACL,oBAAC;AAAD,CA3QA,AA2QC,CA3Q2C,EAAE,CAAC,SAAS,GA2QvD;AA3QqB,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BuffManager } from \"../systems/BuffManager\";\nimport { EventManager } from \"../systems/EventManager\";\nimport { SkillManager } from \"../systems/SkillManager\";\nimport { CharacterRole, CharacterState, CharacterSelectTag } from \"../types/CharacterTypes\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff } from \"../types/IBuff\";\nimport { ICharacter, ICharacterEvents } from \"../types/ICharacter\";\nimport { ICharacterAttributes } from \"../types/ICharacterAttributes\";\nimport { ISkill } from \"../types/ISkill\";\nimport { CharacterAttributes } from \"./CharacterAttributes\";\n\n/*** 角色基类*/\nexport abstract class BaseCharacter extends cc.Component implements ICharacter {\n    // 基本属性\n    protected _id: string;\n    protected _name: string;\n    protected _role: CharacterRole;\n    protected _state: CharacterState = CharacterState.IDLE;\n    protected _selectTag: CharacterSelectTag = CharacterSelectTag.NONE;\n    // 组件管理器\n    protected _attributes: ICharacterAttributes;\n    protected _skillManager: SkillManager;\n    protected _buffManager: BuffManager;\n    protected _eventManager: EventManager;\n    // 动画和音效\n    protected _spine: sp.Skeleton;\n    protected _animationConfig: any;\n    protected _soundConfig: any;\n    // 节点引用\n    protected _fireNode: cc.Node;\n    protected _fireBaseNode: cc.Node;\n    // 事件回调\n    protected _events: ICharacterEvents = {};\n\n    // 实现ICharacter接口\n    get id(): string { return this._id; }\n    get characterName(): string { return this._name; }\n    get role(): CharacterRole { return this._role; }\n    get attributes(): ICharacterAttributes { return this._attributes; }\n    get isDead(): boolean { return this._state === CharacterState.DEAD; }\n    get skills(): ReadonlyArray<ISkill> { return this._skillManager.getAllSkills(); }\n    get buffs(): ReadonlyArray<IBuff> { return this._buffManager.buffs; }\n    get skillManager(): SkillManager { return this._skillManager; }\n    get buffManager(): BuffManager { return this._buffManager; }\n    // 额外属性\n    get state(): CharacterState { return this._state; }\n    get selectTag(): CharacterSelectTag { return this._selectTag; }\n    get spine(): sp.Skeleton { return this._spine; }\n    set selectTag(value: CharacterSelectTag) { this._selectTag = value; }\n\n    /*** 初始化角色*/\n    protected onLoad(): void {\n        this.initializeId();\n        this.initializeComponents();\n        this.initializeNodes();\n        this.setupEventListeners();\n    }\n\n    /** * 启动角色 */\n    protected start(): void {\n        this.onCharacterStart();\n    }\n\n    /** * 更新角色状态 */\n    update(deltaTime: number): void {\n        if (this.isDead) return;\n        this._skillManager.update(deltaTime);\n        this._buffManager.update(deltaTime);\n        this.onCharacterUpdate(deltaTime);\n    }\n\n    /** * 销毁角色 */\n    protected onDestroy(): void {\n        this.cleanup();\n    }\n\n    /** * 初始化ID */\n    protected initializeId(): void {\n        this._id = this.generateUniqueId();\n    }\n\n    /** * 初始化组件 */\n    protected initializeComponents(): void {\n        this._attributes = new CharacterAttributes();\n        this._skillManager = new SkillManager(this);\n        this._buffManager = new BuffManager(this);\n        this._eventManager = EventManager.createLocal(`Character_${this._name || this._id}`);\n    }\n\n    /** * 初始化节点引用 */\n    protected initializeNodes(): void {\n        this._spine = this.getComponent(sp.Skeleton);\n        this._fireNode = this.node.getChildByName(\"fireNode\");\n        this._fireBaseNode = this.node.getChildByName(\"fireBaseNode\");\n    }\n\n    /** * 设置事件监听器 */\n    protected setupEventListeners(): void {\n        this._eventManager.on(FightEvent.attributeChanged, this.onAttributeChanged.bind(this));\n        this._eventManager.on(FightEvent.stateChanged, this.onStateChanged.bind(this));\n        this._eventManager.on(FightEvent.death, this.onDeath.bind(this));\n    }\n\n    /** * 移动角色 */\n    move(direction: cc.Vec3): boolean {\n        if (this.isDead || this._state === CharacterState.STUNNED) {\n            return false;\n        }\n        if (this.canMove()) {\n            this.setState(CharacterState.MOVING);\n            this.performMove(direction);\n            return true;\n        }\n        return false;\n    }\n\n    /** * 攻击目标 */\n    attack(target?: ICharacter): boolean {\n        if (this.isDead || this._state === CharacterState.STUNNED) {\n            return false;\n        }\n        const attackSkill = this._skillManager.getBasicAttackSkill();\n        if (attackSkill) {\n            return this.castSkill(attackSkill.id, target?.node);\n        }\n        return false;\n    }\n\n    /** * 释放技能 */\n    castSkill(skillName: string, target?: cc.Node): boolean {\n        if (this.isDead || this._state === CharacterState.SILENCED) {\n            return false;\n        }\n        const skill = this._skillManager.getSkill(skillName);\n        if (!skill || !skill.canUse) {\n            return false;\n        }\n        if (!this.checkResourceCost(skill)) {\n            return false;\n        }\n        this.consumeResources(skill);\n        this.setState(CharacterState.CASTING);\n        const success = skill.cast(this, this.getCharacterFromNode(target), undefined, this.getWorldPosition(target));\n        if (success) {\n            this._events.onSkillCast?.(this, skillName);\n            this._eventManager.emit(FightEvent.skillCast, { character: this, skillName });\n        }\n        return success;\n    }\n    /** * 学习技能 */\n    learnSkill(skill: ISkill): void {\n        this._skillManager.addSkill(skill);\n    }\n    /** * 添加Buff */\n    addBuff(buff: IBuff): void {\n        this._buffManager.addBuff(buff);\n        this._events.onBuffAdded?.(this, buff);\n        this._eventManager.emit(FightEvent.buffAdded, { character: this, buff });\n    }\n    /*** 移除Buff*/\n    removeBuff(buffId: string): void {\n        const removed = this._buffManager.removeBuff(buffId);\n        if (removed) {\n            this._events.onBuffRemoved?.(this, buffId);\n            this._eventManager.emit(FightEvent.buffRemoved, { character: this, buffId });\n        }\n    }\n    /*** 受到伤害*/\n    takeDamage(damage: number, attacker?: ICharacter): void {\n        if (this.isDead || this._state === CharacterState.INVINCIBLE) {\n            return;\n        }\n        this._attributes.modifyHp(-damage);\n        this._events.onTakeDamage?.(this, damage, attacker);\n        this._eventManager.emit(FightEvent.takeDamage, { character: this, damage, attacker });\n        if (this._attributes.isDead()) {\n            this.die();\n        }\n    }\n    /** * 治疗 */\n    heal(healAmount: number): void {\n        if (this.isDead) return;\n        this._attributes.modifyHp(healAmount);\n        this._events.onHeal?.(this, healAmount);\n        this._eventManager.emit(FightEvent.heal, { character: this, healAmount });\n    }\n    /** * 死亡处理 */\n    die(): void {\n        if (this.isDead) return;\n        this.setState(CharacterState.DEAD);\n        this._events.onDeath?.(this);\n        this._eventManager.emit(FightEvent.death, { character: this });\n        this.onCharacterDeath();\n    }\n    /** * 设置事件监听器 */\n    setEvents(events: ICharacterEvents): void {\n        this._events = { ...this._events, ...events };\n    }\n    /**  * 设置状态  */\n    protected setState(newState: CharacterState): void {\n        if (this._state !== newState) {\n            const oldState = this._state;\n            this._state = newState;\n            this._eventManager.emit(FightEvent.stateChanged, { character: this, oldState, newState });\n        }\n    }\n    /**   * 检查是否可以移动   */\n    protected canMove(): boolean {\n        return this._state === CharacterState.IDLE || this._state === CharacterState.MOVING;\n    }\n    /** * 检查资源消耗 */\n    protected checkResourceCost(_skill: ISkill): boolean {\n        // 这里应该检查MP、耐力等资源\n        return true; // 简化实现\n    }\n    /*** 消耗资源    */\n    protected consumeResources(_skill: ISkill): void {\n        // 这里应该消耗MP、耐力等资源\n    }\n    /** * 从节点获取角色 */\n    protected getCharacterFromNode(node?: cc.Node): ICharacter | undefined {\n        return node?.getComponent(BaseCharacter) as ICharacter;\n    }\n    /** * 获取世界坐标 */\n    protected getWorldPosition(node?: cc.Node): cc.Vec3 | undefined {\n        return node?.convertToWorldSpaceAR(cc.Vec3.ZERO);\n    }\n    /** * 生成唯一ID */\n    protected generateUniqueId(): string {\n        return `char_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n    }\n    /*** 清理资源*/\n    protected cleanup(): void {\n        this._skillManager?.cleanup();\n        this._buffManager?.cleanup();\n        this._eventManager?.cleanup();\n    }\n    // 抽象方法，子类需要实现\n    protected abstract onCharacterStart(): void;\n    protected abstract onCharacterUpdate(deltaTime: number): void;\n    protected abstract onCharacterDeath(): void;\n    protected abstract performMove(direction: cc.Vec3): void;\n    /**属性变化处理 */\n    protected onAttributeChanged(_event: any): void {\n    }\n    protected onStateChanged(_event: any): void {\n        this.updateAnimation();\n    }\n    protected onDeath(_event: any): void {\n        this.playDeathAnimation();\n    }\n    /*** 更新动画*/\n    protected updateAnimation(): void {\n        if (!this._spine || !this._animationConfig) return;\n        let animationName = \"\";\n        switch (this._state) {\n            case CharacterState.IDLE:\n                animationName = this._animationConfig.idle;\n                break;\n            case CharacterState.MOVING:\n                animationName = this._animationConfig.move;\n                break;\n            case CharacterState.ATTACKING:\n                animationName = this._animationConfig.attack;\n                break;\n            case CharacterState.DEAD:\n                animationName = this._animationConfig.death;\n                break;\n        }\n        if (animationName) {\n            this._spine.setAnimation(0, animationName, this._state !== CharacterState.DEAD);\n        }\n    }\n    /** * 播放死亡动画 */\n    protected playDeathAnimation(): void {\n        if (this._spine && this._animationConfig?.death) {\n            this._spine.setAnimation(0, this._animationConfig.death, false);\n        }\n    }\n}\n"]}