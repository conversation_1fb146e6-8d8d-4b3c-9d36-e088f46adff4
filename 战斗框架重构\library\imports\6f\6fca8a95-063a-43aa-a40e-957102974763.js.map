{"version": 3, "sources": ["assets\\fight\\systems\\BulletManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,+CAA8C;AAC9C,kDAA6C;AAC7C,+CAAwC;AAExC;;;GAGG;AACH;IAQI;QAPQ,mBAAc,GAAyB,IAAI,GAAG,EAAE,CAAC;QAEjD,kBAAa,GAAW,CAAC,CAAC;QAC1B,oBAAe,GAAW,CAAC,CAAC;QAC5B,gBAAW,GAAY,KAAK,CAAC;QAC7B,qBAAgB,GAAa,EAAE,CAAC;QAGpC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,sBAAI,wCAAa;QADjB,gBAAgB;aAChB;YACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC;;;OAAA;IAED,WAAW;IACX,oCAAY,GAAZ,UAAa,MAAqB,EAAE,MAAkB,EAAE,MAAmB,EAAE,cAAwB;QACjG,IAAM,QAAQ,GAAG,YAAU,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QACvF,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,IAAM,MAAM,GAAG,IAAI,qBAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAC1F,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,eAAe;IACf,iCAAS,GAAT,UAAU,MAAe;QACrB,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACpC,OAAO,CAAC,IAAI,CAAC,YAAU,MAAM,CAAC,EAAE,+BAA4B,CAAC,CAAC;YAC9D,OAAO;SACV;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,YAAU,MAAM,CAAC,EAAE,sBAAmB,CAAC,CAAC;QACpD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,WAAW;IACX,oCAAY,GAAZ,UAAa,QAAgB;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,eAAe;YACf,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,OAAO;SACV;QACD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,wBAAwB;IAChB,6CAAqB,GAA7B,UAA8B,QAAgB,EAAE,eAAgC;QAAhC,gCAAA,EAAA,uBAAgC;QAC5E,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,MAAM,EAAE;YACR,iBAAiB;YACjB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACrB,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;YACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,YAAU,QAAQ,0BAAuB,CAAC,CAAC;YAEvD,qBAAqB;YACrB,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;aACnE;SACJ;IACL,CAAC;IAED,gBAAgB;IAChB,6CAAqB,GAArB,UAAsB,MAAkB;;QACpC,IAAM,eAAe,GAAa,EAAE,CAAC;;YACrC,KAA2B,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAArC,IAAA,KAAA,mBAAY,EAAX,EAAE,QAAA,EAAE,MAAM,QAAA;gBAClB,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC1B,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC5B;aACJ;;;;;;;;;;YACD,KAAiB,IAAA,oBAAA,SAAA,eAAe,CAAA,gDAAA,6EAAE;gBAA7B,IAAM,EAAE,4BAAA;gBACT,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;aACzB;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAC,aAAW,eAAe,CAAC,MAAM,4BAAuB,MAAM,CAAC,aAAe,CAAC,CAAC;IAChG,CAAC;IAED,aAAa;IACb,uCAAe,GAAf;;QACI,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;;YACzD,KAAiB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA,2DAAE;gBAAvB,IAAM,EAAE,sBAAA;gBACT,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;aACzB;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAC,0BAAwB,SAAS,CAAC,MAAM,cAAW,CAAC,CAAC;IACrE,CAAC;IAED,aAAa;IACb,8BAAM,GAAN,UAAO,SAAiB;;QACpB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;YAChC,OAAO;SACV;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAM,gBAAgB,GAAa,EAAE,CAAC;;YACtC,SAAS;YACT,KAA2B,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAArC,IAAA,KAAA,mBAAY,EAAX,EAAE,QAAA,EAAE,MAAM,QAAA;gBAClB,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC7C,IAAI,WAAW,EAAE;oBACb,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC7B;aACJ;;;;;;;;;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;;YACzB,0BAA0B;YAC1B,KAAiB,IAAA,qBAAA,SAAA,gBAAgB,CAAA,kDAAA,gFAAE;gBAA9B,IAAM,EAAE,6BAAA;gBACT,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;aACzC;;;;;;;;;QACD,YAAY;QACZ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;;gBAClC,KAAiB,IAAA,KAAA,SAAA,IAAI,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;oBAAnC,IAAM,EAAE,WAAA;oBACT,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBACxC;;;;;;;;;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;SACpC;IACL,CAAC;IAED,eAAe;IACf,gCAAQ,GAAR;QACI,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YACrC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,cAAc,EAAE,IAAI,CAAC,eAAe;SACvC,CAAC;IACN,CAAC;IAED,eAAe;IACf,iCAAS,GAAT,UAAU,QAAgB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IACrD,CAAC;IAED,kBAAkB;IAClB,0CAAkB,GAAlB,UAAmB,MAAkB;;QACjC,IAAM,OAAO,GAAc,EAAE,CAAC;;YAC9B,KAAqB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAA9C,IAAM,MAAM,WAAA;gBACb,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC1B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACxB;aACJ;;;;;;;;;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,cAAc;IACN,2CAAmB,GAA3B;QACI,kBAAkB;IACtB,CAAC;IAED,aAAa;IACb,oCAAY,GAAZ;;;QACI,IAAM,OAAO,GAAQ,EAAE,CAAC;;YACxB,KAA2B,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAArC,IAAA,KAAA,mBAAY,EAAX,EAAE,QAAA,EAAE,MAAM,QAAA;gBAClB,OAAO,CAAC,EAAE,CAAC,GAAG;oBACV,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa;oBACnC,MAAM,EAAE,OAAA,MAAM,CAAC,MAAM,0CAAE,aAAa,KAAI,IAAI;oBAC5C,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;iBAClC,CAAC;aACL;;;;;;;;;QACD,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,OAAO,SAAA;YACP,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM;SAChD,CAAC;IACN,CAAC;IACD,aAAa;IACb,sCAAc,GAAd;QACI,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,sBAAI,uCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,YAAY;IACZ,+BAAO,GAAP;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACL,oBAAC;AAAD,CApLA,AAoLC,IAAA;AApLY,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { IBullet, IBulletConfig, IBulletManager } from \"../types/IBullet\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { EventManager } from \"./EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { Bullet } from \"./BulletSystem\";\n\n/**\n * 子弹管理器实现\n * 负责管理所有活跃的子弹\n */\nexport class BulletManager implements IBulletManager {\n    private _activeBullets: Map<string, IBullet> = new Map();\n    private _eventManager: EventManager;\n    private _totalCreated: number = 0;\n    private _totalDestroyed: number = 0;\n    private _isUpdating: boolean = false;\n    private _pendingRemovals: string[] = [];\n\n    constructor() {\n        this._eventManager = EventManager.createLocal(\"BulletManager\");\n        this.setupEventListeners();\n    }\n    /** 获取所有活跃的子弹 */\n    get activeBullets(): ReadonlyArray<IBullet> {\n        return Array.from(this._activeBullets.values());\n    }\n\n    /** 创建子弹 */\n    createBullet(config: IBulletConfig, caster: ICharacter, target?: ICharacter, targetPosition?: cc.Vec3): IBullet {\n        const bulletId = `bullet_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        const firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const bullet = new Bullet(bulletId, config, caster, firePosition, target, targetPosition);\n        this.addBullet(bullet);\n        return bullet;\n    }\n\n    /** 添加子弹到管理器 */\n    addBullet(bullet: IBullet): void {\n        if (this._activeBullets.has(bullet.id)) {\n            console.warn(`Bullet ${bullet.id} already exists in manager`);\n            return;\n        }\n        this._activeBullets.set(bullet.id, bullet);\n        this._totalCreated++;\n        console.log(`Bullet ${bullet.id} added to manager`);\n        this._eventManager.emit(FightEvent.bulletFired, { bullet });\n    }\n\n    /** 移除子弹 */\n    removeBullet(bulletId: string): void {\n        if (this._isUpdating) {\n            // 如果正在更新中，延迟移除\n            this._pendingRemovals.push(bulletId);\n            return;\n        }\n        this._internalRemoveBullet(bulletId, true);\n    }\n\n    /** 内部移除子弹方法 - 避免事件循环 */\n    private _internalRemoveBullet(bulletId: string, shouldEmitEvent: boolean = false): void {\n        const bullet = this._activeBullets.get(bulletId);\n        if (bullet) {\n            // 确保子弹被销毁，但不重复销毁\n            if (!bullet.isDestroyed) {\n                bullet.destroy();\n            }\n            this._activeBullets.delete(bulletId);\n            this._totalDestroyed++;\n            console.log(`Bullet ${bulletId} removed from manager`);\n\n            // 只有在外部调用时才发射事件，避免循环\n            if (shouldEmitEvent) {\n                this._eventManager.emit(FightEvent.bulletDestroyed, { bullet });\n            }\n        }\n    }\n\n    /** 根据施法者移除子弹 */\n    removeBulletsByCaster(caster: ICharacter): void {\n        const bulletsToRemove: string[] = [];\n        for (const [id, bullet] of this._activeBullets) {\n            if (bullet.caster === caster) {\n                bulletsToRemove.push(id);\n            }\n        }\n        for (const id of bulletsToRemove) {\n            this.removeBullet(id);\n        }\n        console.log(`Removed ${bulletsToRemove.length} bullets for caster ${caster.characterName}`);\n    }\n\n    /** 清除所有子弹 */\n    clearAllBullets(): void {\n        const bulletIds = Array.from(this._activeBullets.keys());\n        for (const id of bulletIds) {\n            this.removeBullet(id);\n        }\n        console.log(`Cleared all bullets (${bulletIds.length} bullets)`);\n    }\n\n    /** 更新所有子弹 */\n    update(deltaTime: number): void {\n        if (this._activeBullets.size === 0) {\n            return;\n        }\n        this._isUpdating = true;\n        const destroyedBullets: string[] = [];\n        // 更新所有子弹\n        for (const [id, bullet] of this._activeBullets) {\n            const isDestroyed = bullet.update(deltaTime);\n            if (isDestroyed) {\n                destroyedBullets.push(id);\n            }\n        }\n        this._isUpdating = false;\n        // 移除已销毁的子弹 - 使用内部方法避免重复销毁\n        for (const id of destroyedBullets) {\n            this._internalRemoveBullet(id, false);\n        }\n        // 处理延迟移除的子弹\n        if (this._pendingRemovals.length > 0) {\n            for (const id of this._pendingRemovals) {\n                this._internalRemoveBullet(id, true);\n            }\n            this._pendingRemovals.length = 0;\n        }\n    }\n\n    /** 获取子弹统计信息 */\n    getStats(): { activeCount: number; totalCreated: number; totalDestroyed: number } {\n        return {\n            activeCount: this._activeBullets.size,\n            totalCreated: this._totalCreated,\n            totalDestroyed: this._totalDestroyed\n        };\n    }\n\n    /** 根据ID获取子弹 */\n    getBullet(bulletId: string): IBullet | null {\n        return this._activeBullets.get(bulletId) || null;\n    }\n\n    /** 根据施法者获取子弹列表 */\n    getBulletsByCaster(caster: ICharacter): IBullet[] {\n        const bullets: IBullet[] = [];\n        for (const bullet of this._activeBullets.values()) {\n            if (bullet.caster === caster) {\n                bullets.push(bullet);\n            }\n        }\n        return bullets;\n    }\n\n    /** 设置事件监听器 */\n    private setupEventListeners(): void {\n        // 可以在这里添加全局子弹事件监听\n    }\n\n    /** 获取调试信息 */\n    getDebugInfo() {\n        const bullets: any = {};\n        for (const [id, bullet] of this._activeBullets) {\n            bullets[id] = {\n                caster: bullet.caster.characterName,\n                target: bullet.target?.characterName || null,\n                remainingHits: bullet.remainingHits,\n                hasCollided: bullet.hasCollided,\n                isDestroyed: bullet.isDestroyed\n            };\n        }\n        return {\n            stats: this.getStats(),\n            bullets,\n            pendingRemovals: this._pendingRemovals.length\n        };\n    }\n    /** 打印调试信息 */\n    printDebugInfo(): void {\n        console.log(\"BulletManager Debug Info:\", this.getDebugInfo());\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n\n    /** 清理管理器 */\n    cleanup(): void {\n        this.clearAllBullets();\n        this._eventManager.cleanup();\n    }\n}\n"]}