{"version": 3, "sources": ["assets\\fight\\skills\\PlayerSkillFire1.ts"], "names": [], "mappings": ";;;;;;;AAAA,0DAAyD;AACzD,wDAAyD;AAEzD,iDAA8D;AAC9D,6DAAkJ;AAElJ,4CAA8D;AAE9D,4CAA8C;AAC9C,0CAAqE;AAErE,gDAA2C;AAC3C,YAAY;AACZ;IAAA;QACY,QAAG,GAAW,mBAAS,CAAC,kBAAkB,CAAC;QAC3C,UAAK,GAAW,KAAK,CAAC;QACtB,iBAAY,GAAW,kBAAkB,CAAC;QAC1C,cAAS,GAAW,GAAG,CAAC;QACxB,uBAAkB,GAAW,CAAC,CAAC;QAC/B,YAAO,GAAW,EAAE,CAAC;QACrB,iBAAY,GAAW,CAAC,CAAC;QACzB,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAc,kBAAS,CAAC,MAAM,CAAC;QACpC,gBAAW,GAAoB,wBAAe,CAAC,YAAY,CAAC;QAC5D,WAAM,GAAW,GAAG,CAAC;QACrB,cAAS,GAAqB,IAAI,CAAC;QACnC,kBAAa,GAAY,EAAE,CAAC;QACpC,UAAU;QACF,YAAO,GAAG;YACd,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,iBAAiB;YAC1B,gBAAgB,EAAE,0BAA0B;YAC5C,aAAa,EAAE,+BAA+B;YAC9C,UAAU,EAAE,gBAAgB;YAC5B,MAAM,EAAE,CAAC;YACT,UAAU,EAAE,oBAAU,CAAC,KAAK;SAC/B,CAAC;IAyKN,CAAC;IAtKG,sBAAI,gCAAE;QADN,aAAa;aACb,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,kCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,yCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,sCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,+CAAiB;aAArB,cAAkC,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;aACnE,UAAsB,KAAa,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAEnE,sBAAI,oCAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7C,sBAAI,yCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,mCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,kCAAI;aAAR,cAAwB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC5C,sBAAI,wCAAU;aAAd,cAAoC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAC9D,sBAAI,mCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,sCAAQ;aAAZ,cAA4B,OAAO,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC;;;OAAA;IACrD,sBAAI,0CAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAI,oCAAM;aAAV,cAAwB,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAE9D,sBAAsB;IACtB,oCAAS,GAAT,UAAU,MAAkB,EAAE,MAAmB;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;QACD,YAAY;QACZ,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;YAC7B,OAAO,KAAK,CAAC;SAChB;QACD,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACpI,OAAO,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;IACnC,CAAC;IACD,aAAa;IACb,+BAAI,GAAJ,UAAK,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACpF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxE,6BAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,eAAU,IAAI,CAAC,KAAK,aAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAE,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,cAAY,IAAI,CAAC,SAAS,CAAC,EAAE,8BAA2B,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,eAAe;IACf,iCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC;SACxC;IACL,CAAC;IACD,eAAe;IACf,wCAAa,GAAb;QACI,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IACD,qBAAqB;IACb,yCAAc,GAAtB,UAAuB,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACtG,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,mBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAK,IAAI,CAAC,KAAK,cAAW,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClI,cAAc;QACd,IAAM,iBAAiB,GAAG,IAAI,uBAAY,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,2CAA0B,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QACxJ,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACpC,cAAc;QACd,IAAM,aAAa,GAAG,IAAI,uBAAY,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,uCAAsB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1H,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAChC,YAAY;QACZ,IAAM,cAAc,GAAG,IAAI,uBAAY,CAAC,aAAa,EAAE,GAAG,EAAE,IAAI,wCAAuB,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACvM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACjC,sBAAsB;QACtB,IAAM,aAAa,GAAG,IAAI,uBAAY,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,wCAAuB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxJ,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAChC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,gBAAgB;IACR,+CAAoB,GAA5B,UAA6B,MAAkB;QAC3C,IAAM,YAAY,GAAG;YACjB,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,oBAAU,CAAC,QAAQ;YACzB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;YACzC,KAAK,EAAE,GAAG;YACV,QAAQ,EAAE,GAAG;YACb,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,EAAE,IAAI,EAAE,wBAAc,CAAC,MAAM,EAAE;YAC3C,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,cAAc,EAAE,EAAE,EAAE;YACjF,MAAM,EAAE;gBACJ,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;aAC3E;YACD,KAAK,EAAE;gBACH,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;aACpC;SACJ,CAAC;QACF,UAAU;QACV,OAAO,IAAI,6BAAc,CAAC,oBAAoB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED,eAAe;IACP,4CAAiB,GAAzB,UAA0B,MAAkB;QACxC,OAAO;QACP,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;YAClB,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACrC,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE;gBACrC,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,sCAAiC,IAAI,CAAC,OAAO,mBAAc,UAAU,CAAC,SAAS,MAAG,CAAC,CAAC;gBACvH,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO;QACP,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;YACvB,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACrC,IAAI,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,2CAAsC,IAAI,CAAC,YAAY,mBAAc,UAAU,CAAC,cAAc,MAAG,CAAC,CAAC;gBACtI,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,aAAa;IACL,2CAAgB,GAAxB,UAAyB,MAAkB;QACvC,OAAO;QACP,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;YAClB,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACrC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,kBAAa,IAAI,CAAC,OAAO,wBAAmB,UAAU,CAAC,SAAS,MAAG,CAAC,CAAC;SAC3G;QACD,OAAO;QACP,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;YACvB,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACrC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,kBAAa,IAAI,CAAC,YAAY,6BAAwB,UAAU,CAAC,cAAc,MAAG,CAAC,CAAC;SAC1H;IACL,CAAC;IAED,aAAa;IACb,kCAAO,GAAP;QACI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,aAAa;QACb,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS;QAC/D,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,OAAO;QAC1B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,uBAAkB,IAAI,CAAC,MAAQ,CAAC,CAAC;IAC9D,CAAC;IACD,eAAe;IACf,uCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,UAAU,EAAE,IAAI,CAAC,WAAW;SAC/B,CAAC;IACN,CAAC;IAED,gCAAgC;IAChC,gCAAK,GAAL,UAAO,OAAqB;QACxB,oBAAoB;QACpB,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,OAAO,CAAC,MAAM,aAAU,CAAC,CAAC;IACtE,CAAC;IACL,uBAAC;AAAD,CAhMA,AAgMC,IAAA;AAhMY,4CAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BattleManager } from \"../systems/BattleManager\";\nimport { <PERSON><PERSON>Launcher } from \"../systems/BulletSystem\";\nimport { TimelineManager } from \"../systems/TimelineManager\";\nimport { Timeline, TimelineNode } from \"../timeline/Timeline\";\nimport { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, FireBulletTimelineEvent, PlayEffectTimelineEvent } from \"../timeline/TimelineEvents\";\nimport { IBuff } from \"../types/IBuff\";\nimport { BulletType, TrajectoryType } from \"../types/IBullet\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { DamageType } from \"../types/IDamage\";\nimport { ISkill, SkillType, SkillTargetType } from \"../types/ISkill\";\nimport { ITimeline } from \"../types/ITimeline\";\nimport SkillName from \"../types/SkillName\";\n/*** 火球术技能*/\nexport class PlayerSkillFire1 implements ISkill {\n    private _id: string = SkillName.player_skill_fire1;\n    private _name: string = \"火球术\";\n    private _description: string = \"发射一个火球，对敌人造成魔法伤害\";\n    private _cooldown: number = 2.0;\n    private _remainingCooldown: number = 0;\n    private _mpCost: number = 20;\n    private _staminaCost: number = 0;\n    private _level: number = 1;\n    private _type: SkillType = SkillType.ACTIVE;\n    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ENEMY;\n    private _range: number = 300;\n    private _timeline: ITimeline | null = null;\n    private _passiveBuffs: IBuff[] = [];\n    /**技能配置 */\n    private _config = {\n        animationName: \"skill_fire1\",\n        soundId: \"fire_skill_cast\",\n        bulletPrefabPath: \"prefabs/bullets/FireBall\",\n        hitEffectPath: \"prefabs/effects/FireExplosion\",\n        hitSoundId: \"fire_explosion\",\n        damage: 0, // 0表示使用施法者的攻击力\n        damageType: DamageType.MAGIC\n    };\n\n    // 实现ISkill接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get cooldown(): number { return this._cooldown; }\n    get remainingCooldown(): number { return this._remainingCooldown; }\n    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }\n    get mpCost(): number { return this._mpCost; }\n    get staminaCost(): number { return this._staminaCost; }\n    get level(): number { return this._level; }\n    get type(): SkillType { return this._type; }\n    get targetType(): SkillTargetType { return this._targetType; }\n    get range(): number { return this._range; }\n    get timeline(): ITimeline { return this._timeline!; }\n    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }\n    get canUse(): boolean { return this._remainingCooldown <= 0; }\n\n    /** * 检查是否可以对目标使用技能 */\n    canCastOn(caster: ICharacter, target?: ICharacter): boolean {\n        if (!this.canUse) {\n            return false;\n        }\n        if (!target || target.isDead) {\n            return false;\n        }\n        // 检查目标是否为敌人\n        if (target.role === caster.role) {\n            return false;\n        }\n        const distance = cc.Vec3.distance(caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));\n        return distance <= this._range;\n    }\n    /** * 释放技能 */\n    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {\n        if (!this.canCastOn(caster, target)) {\n            return false;\n        }\n        if (!this.checkResourceCost(caster)) {\n            return false;\n        }\n        this.consumeResources(caster);\n        this._remainingCooldown = this._cooldown;\n        this._timeline = this.createTimeline(caster, target, targets, position);\n        BattleManager.instance.timelineManager.addTimeline(this._timeline);\n        console.log(`${caster.characterName} casts ${this._name} on ${target?.characterName}`);\n        console.log(`Timeline ${this._timeline.id} added to TimelineManager`);\n        return true;\n    }\n    /** * 更新技能状态 */\n    update(deltaTime: number): void {\n        if (this._remainingCooldown > 0) {\n            this._remainingCooldown -= deltaTime;\n        }\n    }\n    /** * 重置冷却时间 */\n    resetCooldown(): void {\n        this._remainingCooldown = 0;\n    }\n    /** * 创建技能Timeline */\n    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {\n        const timeline = new Timeline(Timeline.getTimeLineId(this._id), `${this._name}_Timeline`, 3.0, caster, target, targets, position);\n        // 0.0秒：播放施法动画\n        const castAnimationNode = new TimelineNode(\"cast_animation\", 0.0, new PlayAnimationTimelineEvent(\"cast_anim\", this._config.animationName, false, true));\n        timeline.addNode(castAnimationNode);\n        // 0.1秒：播放施法音效\n        const castSoundNode = new TimelineNode(\"cast_sound\", 0.1, new PlaySoundTimelineEvent(\"cast_sound\", this._config.soundId));\n        timeline.addNode(castSoundNode);\n        // 0.5秒：发射火球\n        const fireBulletNode = new TimelineNode(\"fire_bullet\", 0.5, new FireBulletTimelineEvent(\"fire_bullet\", this.createBulletLauncher(caster), this._config.hitEffectPath, \"hit\", this._config.hitSoundId));\n        timeline.addNode(fireBulletNode);\n        // 1.0秒：播放命中特效（如果子弹命中）\n        const hitEffectNode = new TimelineNode(\"hit_effect\", 1.0, new PlayEffectTimelineEvent(\"hit_effect\", this._config.hitEffectPath, true, cc.v3(0, 50, 0)));\n        timeline.addNode(hitEffectNode);\n        return timeline;\n    }\n\n    /** * 创建子弹发射器 */\n    private createBulletLauncher(caster: ICharacter) {\n        const bulletConfig = {\n            id: \"fire_ball\",\n            type: BulletType.STRAIGHT,\n            prefabPath: this._config.bulletPrefabPath,\n            speed: 400,\n            lifeTime: 5.0,\n            maxHits: 1,\n            trajectory: { type: TrajectoryType.LINEAR },\n            collision: { radius: 20, piercing: false, layers: [\"enemy\"], checkFrequency: 60 },\n            visual: {\n                trail: { enabled: true, length: 100, width: 10, color: cc.Color.ORANGE }\n            },\n            audio: {\n                hitSound: this._config.hitSoundId\n            }\n        };\n        // 创建发射器实例\n        return new BulletLauncher(\"fire_ball_launcher\", caster, bulletConfig);\n    }\n\n    /** * 检查资源消耗 */\n    private checkResourceCost(caster: ICharacter): boolean {\n        // 检查MP\n        if (this._mpCost > 0) {\n            const attributes = caster.attributes;\n            if (attributes.currentMp < this._mpCost) {\n                console.log(`${caster.characterName} doesn't have enough MP (need ${this._mpCost}, current: ${attributes.currentMp})`);\n                return false;\n            }\n        }\n        // 检查耐力\n        if (this._staminaCost > 0) {\n            const attributes = caster.attributes;\n            if (attributes.currentStamina < this._staminaCost) {\n                console.log(`${caster.characterName} doesn't have enough stamina (need ${this._staminaCost}, current: ${attributes.currentStamina})`);\n                return false;\n            }\n        }\n        return true;\n    }\n\n    /** * 消耗资源 */\n    private consumeResources(caster: ICharacter): void {\n        // 消耗MP\n        if (this._mpCost > 0) {\n            const attributes = caster.attributes;\n            attributes.consumeMp(this._mpCost);\n            console.log(`${caster.characterName} consumed ${this._mpCost} MP (remaining: ${attributes.currentMp})`);\n        }\n        // 消耗耐力\n        if (this._staminaCost > 0) {\n            const attributes = caster.attributes;\n            attributes.consumeStamina(this._staminaCost);\n            console.log(`${caster.characterName} consumed ${this._staminaCost} stamina (remaining: ${attributes.currentStamina})`);\n        }\n    }\n\n    /** * 升级技能 */\n    levelUp(): void {\n        this._level++;\n        // 根据等级调整技能属性\n        this._mpCost = Math.max(10, this._mpCost - 1); // MP消耗减少\n        this._cooldown = Math.max(1.0, this._cooldown - 0.1); // 冷却时间减少\n        this._range += 20; // 射程增加\n        console.log(`${this._name} leveled up to ${this._level}`);\n    }\n    /** * 获取技能信息 */\n    getSkillInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            description: this._description,\n            level: this._level,\n            cooldown: this._cooldown,\n            remainingCooldown: this._remainingCooldown,\n            mpCost: this._mpCost,\n            staminaCost: this._staminaCost,\n            range: this._range,\n            canUse: this.canUse,\n            type: this._type,\n            targetType: this._targetType\n        };\n    }\n\n    /** * 技能被添加时的回调（用于多目标技能等特殊情况） */\n    onAdd?(targets: ICharacter[]): void {\n        // 火球术是单目标技能，不需要特殊处理\n        console.log(`${this._name} added with ${targets.length} targets`);\n    }\n}\n"]}