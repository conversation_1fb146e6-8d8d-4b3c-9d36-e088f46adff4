"use strict";
cc._RF.push(module, '87ff2ah6EFJJaMUK4tTEHu0', 'EquipmentManager');
// card/cardgame/src/EquipmentManager.ts

// import Base from "./Base/Base";
// import Equipment from "./Base/Equipment";
// import { ECamp } from "./CampManager";
// import FightManager from "./FightManager";
// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }
// declare global {
// }
// /** 
//  * @features :  游戏装备管理
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class EquipmentManager extends Base {
//     /**技能数据 */
//     data: IBuffDataType;
//     /**所有装备 */
//     equipmentList: Equipment[] = []
//     addEquipment(name: string, data: { defence?: number, attack?: number }) {
//         const equipment = new Equipment()
//         equipment.durable = data.defence ?? data.attack ?? 3
//         equipment.defence = data.defence ?? 2
//         equipment.attack = data.attack ?? 2
//         equipment.orginValue = data.defence ?? data.attack ?? equipment.durable
//         equipment.equipmentName = name
//         equipment.camp = ECamp.Player
//         this.equipmentList.push(equipment);
//         FightManager.campManager.getPlayerCamp().getRole().attack += equipment.attack
//         FightManager.campManager.getPlayerCamp().getRole().defence += equipment.defence
//         return equipment
//     }
//     hasEquipment(name: string) {
//         return this.equipmentList.some(e => e.equipmentName === name);
//     }
//     getEquipment(name?: string) {
//         if (name == null) {
//             return this.equipmentList.arrayRand<Equipment>();
//         }
//         return this.equipmentList.find(e => e.equipmentName === name);
//     }
//     deletEquipment(name: string) {
//         const index = this.equipmentList.findIndex(e => e.equipmentName === name);
//         if (index >= 0) {
//             let eq = this.equipmentList.splice(index, 1)?.[0]
//             if (eq) {
//                 FightManager.campManager.getPlayerCamp().getRole().attack -= eq.attack
//                 FightManager.campManager.getPlayerCamp().getRole().defence -= eq.defence
//             }
//         }
//     }
// }

cc._RF.pop();