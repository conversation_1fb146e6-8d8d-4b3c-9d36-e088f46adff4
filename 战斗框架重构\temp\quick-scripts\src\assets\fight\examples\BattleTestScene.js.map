{"version": 3, "sources": ["assets\\fight\\examples\\BattleTestScene.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qEAAoE;AACpE,qDAAoD;AACpD,+DAA8D;AAC9D,0DAAyD;AAEzD,iDAA8D;AAC9D,6DAA+G;AAC/G,0DAA6E;AAE7E,4CAA8C;AAC9C,gDAA2C;AAGrC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C;;;GAGG;AAEH;IAAqC,mCAAY;IAAjD;QAAA,qEAslBC;QArlBG,QAAQ;QAER,YAAM,GAAY,IAAI,CAAC;QAEvB,iBAAW,GAAa,IAAI,CAAC;QAE7B,SAAS;QAET,oBAAc,GAAc,IAAI,CAAC;QAEjC,kBAAY,GAAc,IAAI,CAAC;QAE/B,kBAAY,GAAc,IAAI,CAAC;QAE/B,gBAAU,GAAc,IAAI,CAAC;QAE7B,gBAAgB;QAEhB,qBAAe,GAAc,IAAI,CAAC;QAElC,2BAAqB,GAAc,IAAI,CAAC;QAExC,yBAAmB,GAAc,IAAI,CAAC;QAEtC,cAAc;QAEd,sBAAgB,GAAc,IAAI,CAAC;QAEnC,iBAAW,GAAc,IAAI,CAAC;QAE9B,kBAAY,GAAc,IAAI,CAAC;QAE/B,SAAS;QACD,YAAM,GAAc,IAAI,CAAC;QACzB,WAAK,GAAc,IAAI,CAAC;QACxB,aAAO,GAAgB,EAAE,CAAC;QAC1B,mBAAa,GAAkB,IAAI,CAAC;QACpC,qBAAe,GAAoB,IAAI,CAAC;QAEhD,OAAO;QACC,mBAAa,GAAY,KAAK,CAAC;QAC/B,eAAS,GAAY,KAAK,CAAC;QAC3B,cAAQ,GAAW,OAAO,CAAC,CAAC,yBAAyB;;IA2iBjE,CAAC;IAziBG,gCAAM,GAAN;;QACI,SAAS;QACT,IAAI,CAAC,aAAa,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;QAE1D,aAAa;QACb,MAAA,IAAI,CAAC,cAAc,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE;QAChE,MAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;QAC5D,MAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;QAC5D,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE;QAExD,mBAAmB;QACnB,MAAA,IAAI,CAAC,eAAe,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE;QAClE,MAAA,IAAI,CAAC,qBAAqB,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAC9E,MAAA,IAAI,CAAC,mBAAmB,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE;QAE1E,iBAAiB;QACjB,MAAA,IAAI,CAAC,gBAAgB,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE;QACpE,MAAA,IAAI,CAAC,WAAW,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE;QAC1D,MAAA,IAAI,CAAC,YAAY,0CAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE;QAEhE,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC;IACD,+BAAK,GAAL;QACI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IACD,mCAAS,GAAT;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;SAChC;IACL,CAAC;IACD,gCAAM,GAAN,UAAO,EAAU;QACb,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EAAE;YAC1C,sBAAsB;YACtB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IACD,WAAW;IACH,sCAAY,GAApB;QACI,IAAM,UAAU,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAC9B,UAAU,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,qBAAS,CAAC,CAAC;QACjD,IAAM,UAAU,GAAwB;YACpC,SAAS,EAAE,QAAQ;YACnB,IAAI,EAAE,8BAAa,CAAC,IAAI;YACxB,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YAChC,iBAAiB,EAAE;gBACf,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,GAAG;gBACjB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACzC,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,aAAa;IACL,qCAAW,GAAnB;QACI,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,qBAAS,CAAC,CAAC;QAC/C,IAAM,SAAS,GAAwB;YACnC,SAAS,EAAE,OAAO;YAClB,IAAI,EAAE,8BAAa,CAAC,KAAK;YACzB,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YAC/B,iBAAiB,EAAE;gBACf,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,EAAE;gBACV,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,GAAG;gBACd,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC1D,CAAC;IACD,aAAa;IACL,uCAAa,GAArB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,sBAAsB;QACtB,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,YAAY,CAAC,IAAI,OAAjB,YAAY,WAAS,IAAI,CAAC,OAAO,GAAE;YACnC,OAAO,CAAC,GAAG,CAAC,qDAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,4BAAU,CAAC,CAAC;SAC9D;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SAClC;QAED,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,YAAY,CAAC,CAAC;QAE1E,SAAS;QACT,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7D,IAAI,IAAI,CAAC,UAAU;YAAE,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;QACzD,IAAI,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QAClE,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;IACjE,CAAC;IAED,eAAe;IACP,qCAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO;SACV;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACzC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC7B,OAAO;SACV;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrF,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,gDAAU,CAAC,CAAC;SACnE;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACzB;IACL,CAAC;IAED,eAAe;IACP,mCAAS,GAAjB;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO;SACV;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC9B,OAAO;SACV;QACD,IAAM,IAAI,GAAG,IAAI,2CAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,8CAAa,CAAC,CAAC;IAClD,CAAC;IAED,eAAe;IACP,sCAAY,GAApB;QACI,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAC9B,IAAI,MAAM,GAAG,oBAAoB,CAAC;QAElC,OAAO;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,IAAI,sCAAW,UAAU,CAAC,IAAI,OAAI,CAAC;YACzC,MAAM,IAAI,SAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,OAAI,CAAC;YACtF,MAAM,IAAI,UAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,WAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,QAAI,CAAC;YAChG,MAAM,IAAI,oBAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,QAAI,CAAC;YACxD,MAAM,IAAI,yBAAQ,UAAU,CAAC,UAAU,uBAAa,UAAU,CAAC,SAAS,SAAM,CAAC;SAClF;QAED,OAAO;QACP,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,MAAM,IAAI,gCAAU,IAAI,CAAC,OAAO,CAAC,MAAM,eAAO,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;gBAC9B,IAAM,SAAS,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3C,MAAM,IAAO,KAAK,GAAG,CAAC,UAAK,SAAS,CAAC,IAAI,UAAK,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,KAAK,CAAC,UAAU,CAAC,KAAK,UAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,QAAI,CAAC;YAC5I,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;SAClB;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAChD,MAAM,IAAI,gCAAU,SAAS,CAAC,IAAI,OAAI,CAAC;YACvC,MAAM,IAAI,SAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,OAAI,CAAC;YACpF,MAAM,IAAI,oBAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,UAAM,CAAC;SAC5D;QAED,YAAY;QACZ,MAAM,IAAI,4CAAY,IAAI,CAAC,QAAQ,OAAI,CAAC;QACxC,MAAM,IAAI,mCAAY,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,UAAM,CAAC;QAC3D,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACxD,MAAM,IAAI,6BAAS,CAAC;YACpB,MAAM,IAAI,0BAAQ,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAI,CAAC;YACzD,MAAM,IAAI,mBAAO,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,QAAK,CAAC;YACtD,MAAM,IAAI,yBAAQ,WAAW,CAAC,gBAAgB,SAAM,CAAC;YAErD,MAAM,IAAI,yBAAe,CAAC;YAC1B,MAAM,IAAI,mBAAO,WAAW,CAAC,aAAa,CAAC,WAAW,OAAI,CAAC;YAC3D,MAAM,IAAI,mBAAO,WAAW,CAAC,aAAa,CAAC,WAAW,OAAI,CAAC;YAC3D,MAAM,IAAI,yBAAQ,WAAW,CAAC,aAAa,CAAC,mBAAmB,SAAM,CAAC;YAEtE,MAAM,IAAI,6BAAS,CAAC;YACpB,MAAM,IAAI,mBAAO,WAAW,CAAC,WAAW,CAAC,WAAW,OAAI,CAAC;YACzD,MAAM,IAAI,yBAAQ,WAAW,CAAC,WAAW,CAAC,YAAY,OAAI,CAAC;YAC3D,MAAM,IAAI,yBAAQ,WAAW,CAAC,WAAW,CAAC,cAAc,OAAI,CAAC;SAChE;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;IACrC,CAAC;IAED,oDAAoD;IAEpD,WAAW;IACH,qCAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO;SACV;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,SAAS;QACT,IAAI,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC;QACjE,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9D,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9D,IAAI,IAAI,CAAC,UAAU;YAAE,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,qBAAqB;IACb,wCAAc,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CACzB,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,EAC7B,cAAc,EACd,GAAG,EACH,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,SAAS;QACT,IAAM,UAAU,GAAG,IAAI,uCAAsB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACvE,IAAM,SAAS,GAAG,IAAI,uBAAY,CAAC,YAAY,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;QAClE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5B,qBAAqB;QACrB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACxC,CAAC;IAED,mBAAmB;IACX,8CAAoB,GAA5B;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CACzB,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,EAC/B,cAAc,EACd,GAAG,EACH,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,SAAS;QACT,IAAM,WAAW,GAAG,IAAI,oCAAmB,CAAC,UAAU,EAAE,EAAE,EAAE,oBAAU,CAAC,QAAQ,CAAC,CAAC;QACjF,IAAM,UAAU,GAAG,IAAI,uBAAY,CAAC,aAAa,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QACrE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7B,qBAAqB;QACrB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAChD,CAAC;IAED,sBAAsB;IACd,4CAAkB,GAA1B;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CACzB,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,EAC7B,gBAAgB,EAChB,GAAG,EACH,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,WAAW;QACX,IAAM,SAAS,GAAG,IAAI,qCAAoB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACnE,IAAM,QAAQ,GAAG,IAAI,uBAAY,CAAC,WAAW,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;QAC/D,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3B,qBAAqB;QACrB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACvD,CAAC;IAED,aAAa;IACL,yCAAe,GAAvB;;QACI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;;YAE9B,SAAS;YACT,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAA,gBAAA,4BAAE;gBAA7B,IAAM,KAAK,WAAA;gBACZ,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;oBACrB,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;iBACxB;aACJ;;;;;;;;;QACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,SAAS;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAS,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;YAChD,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;YAC7B,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEhD,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,qBAAS,CAAC,CAAC;YAChD,IAAM,SAAS,GAAwB;gBACnC,SAAS,EAAE,OAAO;gBAClB,IAAI,EAAE,8BAAa,CAAC,KAAK;gBACzB,IAAI,EAAE,kBAAK,CAAC,GAAG,CAAC,CAAE;gBAClB,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;gBACzC,iBAAiB,EAAE;oBACf,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;oBACjB,KAAK,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;oBACpB,EAAE,EAAE,EAAE;oBACN,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE;oBACnB,OAAO,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC;oBACnB,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,EAAE;oBACb,WAAW,EAAE,GAAG;oBAChB,YAAY,EAAE,IAAI;oBAClB,cAAc,EAAE,GAAG;oBACnB,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE,IAAI;oBACf,KAAK,EAAE,CAAC,GAAG,CAAC;oBACZ,UAAU,EAAE,CAAC;iBAChB;aACJ,CAAC;YACF,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAClC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzB,OAAO,CAAC,GAAG,CAAC,uBAAM,KAAK,CAAC,IAAI,kBAAQ,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IAED,aAAa;IACL,oCAAU,GAAlB;QACI,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,UAAU;YACV,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;SACrC;aAAM;YACH,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,UAAU;YACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;YACvE,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAChE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;SACnD;IACL,CAAC;IAED,aAAa;IACL,yCAAe,GAAvB;QACI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAE5B,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;gBAC9B,OAAO,CAAC,GAAG,CAAC,kBAAK,KAAK,GAAG,CAAC,OAAG,EAAE,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,aAAa;IACL,0CAAgB,GAAxB,UAAyB,KAAgB;QAAzC,iBAiBC;QAhBG,IAAM,MAAM,GAAqB;YAC7B,OAAO,EAAE,UAAC,SAAS;gBACf,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,oCAAQ,CAAC,CAAC;gBAChD,KAAI,CAAC,YAAY,CAAC,SAAsB,CAAC,CAAC;YAC9C,CAAC;YACD,YAAY,EAAE,UAAC,SAAS,EAAE,MAAM,EAAE,QAAQ;gBACtC,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,4BAAQ,MAAM,wBAAM,CAAC,CAAC;gBAC5D,IAAI,QAAQ,EAAE;oBACV,OAAO,CAAC,GAAG,CAAC,yBAAQ,QAAQ,CAAC,aAAe,CAAC,CAAC;iBACjD;YACL,CAAC;YACD,WAAW,EAAE,UAAC,SAAS,EAAE,SAAS;gBAC9B,OAAO,CAAC,GAAG,CAAI,SAAS,CAAC,aAAa,yCAAW,SAAW,CAAC,CAAC;YAClE,CAAC;SACJ,CAAC;QACF,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,aAAa;IACL,sCAAY,GAApB,UAAqB,KAAgB;;QACjC,WAAW;QACX,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAI,KAAK,CAAC,IAAI,4DAAY,CAAC,CAAC;SAC1C;QAED,eAAe;QACf,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,MAAM,EAAT,CAAS,CAAC,CAAC;QACzD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,QAAC,IAAI,CAAC,KAAK,0CAAE,MAAM,CAAA,EAAE;YAClD,YAAY;SACf;aAAM,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChC,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;IACL,CAAC;IAED,aAAa;IACL,kCAAQ,GAAhB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC9E,OAAO;SACV;QAED,eAAe;QACf,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,YAAY;QACZ,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED,aAAa;IACL,wCAAc,GAAtB;QACI,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,UAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAM,MAAM,CAAC,IAAI,0CAAS,CAAC,CAAC;SACnE;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;YAC3E,kBAAkB;YAClB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,UAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,4BAAQ,MAAM,CAAC,IAAM,CAAC,CAAC;SAC9D;IACL,CAAC;IAED,aAAa;IACL,uCAAa,GAArB;;QACI,SAAS;QACT,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7E,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,UAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,4BAAQ,IAAI,CAAC,MAAM,CAAC,IAAM,CAAC,CAAC;SAClE;;YAED,QAAQ;YACR,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAA,gBAAA,4BAAE;gBAA7B,IAAM,KAAK,WAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACrD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,UAAQ,KAAK,CAAC,IAAI,4BAAQ,IAAI,CAAC,MAAM,CAAC,IAAM,CAAC,CAAC;iBAC7D;aACJ;;;;;;;;;IACL,CAAC;IAED,cAAc;IACN,0CAAgB,GAAxB;;QACI,IAAI,YAAY,GAAqB,IAAI,CAAC;QAC1C,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,SAAS;QACT,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClC,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvF,IAAI,QAAQ,GAAG,WAAW,EAAE;gBACxB,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;aAC7B;SACJ;;YAED,SAAS;YACT,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,OAAO,CAAA,gBAAA,4BAAE;gBAA7B,IAAM,KAAK,WAAA;gBACZ,IAAI,KAAK,CAAC,MAAM;oBAAE,SAAS;gBAC3B,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClF,IAAI,QAAQ,GAAG,WAAW,EAAE;oBACxB,WAAW,GAAG,QAAQ,CAAC;oBACvB,YAAY,GAAG,KAAK,CAAC;iBACxB;aACJ;;;;;;;;;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAllBD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;wDACU;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACa;IAEjC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;yDACW;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;yDACW;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;uDACS;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACc;IAElC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACoB;IAExC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACkB;IAItC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACe;IAEnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;wDACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;yDACW;IA/BtB,eAAe;QAD3B,OAAO;OACK,eAAe,CAslB3B;IAAD,sBAAC;CAtlBD,AAslBC,CAtlBoC,EAAE,CAAC,SAAS,GAslBhD;AAtlBY,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BuffModelBeHurtFight } from \"../buff/BuffModelBeHurtFight\";\nimport { Character } from \"../characters/Character\";\nimport { PlayerSkillFire1 } from \"../skills/PlayerSkillFire1\";\nimport { BattleManager } from \"../systems/BattleManager\";\nimport { TimelineManager } from \"../systems/TimelineManager\";\nimport { Timeline, TimelineNode } from \"../timeline/Timeline\";\nimport { DamageTimelineEvent, PlaySoundTimelineEvent, AddBuffTimelineEvent } from \"../timeline/TimelineEvents\";\nimport { CharacterCreateInfo, CharacterRole } from \"../types/CharacterTypes\";\nimport { ICharacterEvents } from \"../types/ICharacter\";\nimport { DamageType } from \"../types/IDamage\";\nimport SkillName from \"../types/SkillName\";\n\n\nconst { ccclass, property } = cc._decorator;\n\n/**\n * 综合战斗测试场景\n * 整合了基础战斗测试、Timeline测试、AI演示等功能\n */\n@ccclass\nexport class BattleTestScene extends cc.Component {\n    // UI 组件\n    @property(cc.Node)\n    uiRoot: cc.Node = null;\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    // 基础战斗按钮\n    @property(cc.Button)\n    startBattleBtn: cc.Button = null;\n    @property(cc.Button)\n    endBattleBtn: cc.Button = null;\n    @property(cc.Button)\n    castSkillBtn: cc.Button = null;\n    @property(cc.Button)\n    addBuffBtn: cc.Button = null;\n\n    // Timeline 测试按钮\n    @property(cc.Button)\n    testTimelineBtn: cc.Button = null;\n    @property(cc.Button)\n    testDamageTimelineBtn: cc.Button = null;\n    @property(cc.Button)\n    testBuffTimelineBtn: cc.Button = null;\n\n    // AI 和多敌人测试按钮\n    @property(cc.Button)\n    createEnemiesBtn: cc.Button = null;\n    @property(cc.Button)\n    toggleAIBtn: cc.Button = null;\n    @property(cc.Button)\n    debugInfoBtn: cc.Button = null;\n\n    // 角色和管理器\n    private player: Character = null;\n    private enemy: Character = null;\n    private enemies: Character[] = [];\n    private battleManager: BattleManager = null;\n    private timelineManager: TimelineManager = null;\n\n    // 状态变量\n    private battleStarted: boolean = false;\n    private aiEnabled: boolean = false;\n    private testMode: string = \"basic\"; // basic, timeline, multi\n\n    onLoad() {\n        // 初始化管理器\n        this.battleManager = BattleManager.getInstance();\n        this.timelineManager = this.battleManager.timelineManager;\n\n        // 设置基础战斗按钮事件\n        this.startBattleBtn?.node.on('click', this.onStartBattle, this);\n        this.endBattleBtn?.node.on('click', this.onEndBattle, this);\n        this.castSkillBtn?.node.on('click', this.onCastSkill, this);\n        this.addBuffBtn?.node.on('click', this.onAddBuff, this);\n\n        // 设置Timeline测试按钮事件\n        this.testTimelineBtn?.node.on('click', this.onTestTimeline, this);\n        this.testDamageTimelineBtn?.node.on('click', this.onTestDamageTimeline, this);\n        this.testBuffTimelineBtn?.node.on('click', this.onTestBuffTimeline, this);\n\n        // 设置AI和多敌人测试按钮事件\n        this.createEnemiesBtn?.node.on('click', this.onCreateEnemies, this);\n        this.toggleAIBtn?.node.on('click', this.onToggleAI, this);\n        this.debugInfoBtn?.node.on('click', this.onShowDebugInfo, this);\n\n        console.log(\"综合战斗测试场景已初始化\");\n    }\n    start() {\n        this.createPlayer();\n        this.createEnemy();\n        console.log(\"角色创建完成\");\n        this.updateStatus();\n    }\n    onDestroy() {\n        if (this.battleManager) {\n            this.battleManager.cleanup();\n        }\n    }\n    update(dt: number) {\n        if (this.battleStarted && this.battleManager) {\n            // 使用BattleManager统一更新\n            this.battleManager.update(dt);\n        }\n        this.updateStatus();\n    }\n    /*** 创建玩家*/\n    private createPlayer(): void {\n        const playerNode = new cc.Node(\"Player\");\n        playerNode.parent = this.node;\n        playerNode.position = cc.v3(-200, 0, 0);\n        this.player = playerNode.addComponent(Character);\n        const playerData: CharacterCreateInfo = {\n            prefabKey: \"player\",\n            role: CharacterRole.HERO,\n            name: \"测试玩家\",\n            worldPosition: cc.v3(-200, 0, 0),\n            initialAttributes: {\n                hp: 1000,\n                maxHp: 1000,\n                mp: 200,\n                maxMp: 200,\n                attack: 100,\n                defense: 50,\n                attackSpeed: 1.2,\n                moveSpeed: 150,\n                attackRange: 300,\n                criticalRate: 0.1,\n                criticalDamage: 1.5,\n                hitRate: 0.95,\n                dodgeRate: 0.05,\n                level: 10,\n                experience: 0\n            }\n        };\n        this.player.setCharacterData(playerData);\n        // 学习技能\n        const fireSkill = new PlayerSkillFire1();\n        this.player.learnSkill(fireSkill);\n        console.log(\"玩家创建完成:\", this.player.getCharacterInfo());\n    }\n    /** * 创建敌人 */\n    private createEnemy(): void {\n        const enemyNode = new cc.Node(\"Enemy\");\n        enemyNode.parent = this.node;\n        enemyNode.position = cc.v3(200, 0, 0);\n        this.enemy = enemyNode.addComponent(Character);\n        const enemyData: CharacterCreateInfo = {\n            prefabKey: \"enemy\",\n            role: CharacterRole.ENEMY,\n            name: \"测试敌人\",\n            worldPosition: cc.v3(200, 0, 0),\n            initialAttributes: {\n                hp: 500,\n                maxHp: 500,\n                mp: 100,\n                maxMp: 100,\n                attack: 80,\n                defense: 30,\n                attackSpeed: 1.0,\n                moveSpeed: 100,\n                attackRange: 150,\n                criticalRate: 0.05,\n                criticalDamage: 1.2,\n                hitRate: 0.9,\n                dodgeRate: 0.1,\n                level: 5,\n                experience: 0\n            }\n        };\n        this.enemy.setCharacterData(enemyData);\n        console.log(\"敌人创建完成:\", this.enemy.getCharacterInfo());\n    }\n    /** * 开始战斗 */\n    private onStartBattle(): void {\n        if (this.battleStarted) {\n            console.log(\"战斗已经开始\");\n            return;\n        }\n\n        if (!this.player || !this.enemy) {\n            console.log(\"角色未创建完成，无法开始战斗\");\n            return;\n        }\n\n        this.battleStarted = true;\n\n        // 使用BattleManager开始战斗\n        let participants = [this.player];\n        if (this.testMode === \"multi\" && this.enemies.length > 0) {\n            participants.push(...this.enemies);\n            console.log(`=== 多敌人战斗开始 (${this.enemies.length}个敌人) ===`);\n        } else if (this.enemy) {\n            participants.push(this.enemy);\n            console.log(\"=== 单敌人战斗开始 ===\");\n        }\n\n        this.battleManager.startBattle(\"test_battle_\" + Date.now(), participants);\n\n        // 更新按钮状态\n        if (this.castSkillBtn) this.castSkillBtn.interactable = true;\n        if (this.addBuffBtn) this.addBuffBtn.interactable = true;\n        if (this.startBattleBtn) this.startBattleBtn.interactable = false;\n        if (this.endBattleBtn) this.endBattleBtn.interactable = true;\n    }\n\n    /**  * 释放技能  */\n    private onCastSkill(): void {\n        if (!this.battleStarted || !this.player || !this.enemy) {\n            console.log(\"战斗未开始或角色不存在\");\n            return;\n        }\n        if (this.player.isDead || this.enemy.isDead) {\n            console.log(\"有角色已死亡，无法释放技能\");\n            return;\n        }\n        const success = this.player.castSkill(SkillName.player_skill_fire1, this.enemy.node);\n        if (success) {\n            console.log(`${this.player.name} 对 ${this.enemy.name} 释放了火球术！`);\n        } else {\n            console.log(\"技能释放失败\");\n        }\n    }\n\n    /** * 添加Buff */\n    private onAddBuff(): void {\n        if (!this.battleStarted || !this.player) {\n            console.log(\"战斗未开始或玩家不存在\");\n            return;\n        }\n        if (this.player.isDead) {\n            console.log(\"玩家已死亡，无法添加Buff\");\n            return;\n        }\n        const buff = new BuffModelBeHurtFight(this.player, this.player);\n        this.player.addBuff(buff);\n        console.log(`${this.player.name} 获得了反击Buff！`);\n    }\n\n    /** * 更新状态显示 */\n    private updateStatus(): void {\n        if (!this.statusLabel) return;\n        let status = \"=== 综合战斗系统测试 ===\\n\";\n\n        // 玩家信息\n        if (this.player) {\n            const playerInfo = this.player.getCharacterInfo();\n            status += `🛡️ 玩家: ${playerInfo.name}\\n`;\n            status += `HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}\\n`;\n            status += `MP: ${this.player.attributes.currentMp || 0}/${this.player.attributes.maxMp || 0}\\n`;\n            status += `状态: ${playerInfo.isDead ? \"💀死亡\" : \"✅存活\"}\\n`;\n            status += `技能数: ${playerInfo.skillCount} | Buff数: ${playerInfo.buffCount}\\n\\n`;\n        }\n\n        // 敌人信息\n        if (this.testMode === \"multi\" && this.enemies.length > 0) {\n            status += `👹 敌人 (${this.enemies.length}个):\\n`;\n            this.enemies.forEach((enemy, index) => {\n                const enemyInfo = enemy.getCharacterInfo();\n                status += `${index + 1}. ${enemyInfo.name}: ${enemy.attributes.currentHp}/${enemy.attributes.maxHp} ${enemyInfo.isDead ? \"💀\" : \"✅\"}\\n`;\n            });\n            status += \"\\n\";\n        } else if (this.enemy) {\n            const enemyInfo = this.enemy.getCharacterInfo();\n            status += `👹 敌人: ${enemyInfo.name}\\n`;\n            status += `HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}\\n`;\n            status += `状态: ${enemyInfo.isDead ? \"💀死亡\" : \"✅存活\"}\\n\\n`;\n        }\n\n        // 测试模式和AI状态\n        status += `🎮 测试模式: ${this.testMode}\\n`;\n        status += `🤖 AI状态: ${this.aiEnabled ? \"✅启用\" : \"❌禁用\"}\\n\\n`;\n        if (this.battleManager) {\n            const battleStats = this.battleManager.getBattleStats();\n            status += `战斗状态:\\n`;\n            status += `进行中: ${battleStats.isInBattle ? \"是\" : \"否\"}\\n`;\n            status += `时长: ${battleStats.duration.toFixed(1)}s\\n`;\n            status += `参战者: ${battleStats.participantCount}\\n\\n`;\n\n            status += `Timeline统计:\\n`;\n            status += `活跃: ${battleStats.timelineStats.activeCount}\\n`;\n            status += `暂停: ${battleStats.timelineStats.pausedCount}\\n`;\n            status += `总执行: ${battleStats.timelineStats.totalExecutedEvents}\\n\\n`;\n\n            status += `子弹统计:\\n`;\n            status += `活跃: ${battleStats.bulletStats.activeCount}\\n`;\n            status += `总创建: ${battleStats.bulletStats.totalCreated}\\n`;\n            status += `总销毁: ${battleStats.bulletStats.totalDestroyed}\\n`;\n        }\n        this.statusLabel.string = status;\n    }\n\n    // ==================== 新增的测试方法 ====================\n\n    /** 结束战斗 */\n    private onEndBattle(): void {\n        if (!this.battleStarted) {\n            console.log(\"战斗未开始\");\n            return;\n        }\n\n        this.battleManager.endBattle(\"manual_stop\");\n        this.battleStarted = false;\n        this.aiEnabled = false;\n\n        // 重置按钮状态\n        if (this.startBattleBtn) this.startBattleBtn.interactable = true;\n        if (this.endBattleBtn) this.endBattleBtn.interactable = false;\n        if (this.castSkillBtn) this.castSkillBtn.interactable = false;\n        if (this.addBuffBtn) this.addBuffBtn.interactable = false;\n\n        console.log(\"战斗已结束\");\n    }\n\n    /** 测试基础Timeline功能 */\n    private onTestTimeline(): void {\n        if (!this.player || !this.enemy) {\n            console.log(\"角色未准备好\");\n            return;\n        }\n\n        console.log(\"=== 测试基础Timeline ===\");\n\n        // 创建一个简单的Timeline\n        const timeline = new Timeline(\n            \"test_timeline_\" + Date.now(),\n            \"基础测试Timeline\",\n            3.0,\n            this.player,\n            this.enemy\n        );\n\n        // 添加音效事件\n        const soundEvent = new PlaySoundTimelineEvent(\"sound_1\", \"test_sound\");\n        const soundNode = new TimelineNode(\"sound_node\", 0.5, soundEvent);\n        timeline.addNode(soundNode);\n\n        // 添加到TimelineManager\n        this.timelineManager.addTimeline(timeline);\n\n        console.log(\"基础Timeline已创建并添加到管理器\");\n    }\n\n    /** 测试伤害Timeline */\n    private onTestDamageTimeline(): void {\n        if (!this.player || !this.enemy) {\n            console.log(\"角色未准备好\");\n            return;\n        }\n\n        console.log(\"=== 测试伤害Timeline ===\");\n\n        // 创建伤害Timeline\n        const timeline = new Timeline(\n            \"damage_timeline_\" + Date.now(),\n            \"伤害测试Timeline\",\n            2.0,\n            this.player,\n            this.enemy\n        );\n\n        // 添加伤害事件\n        const damageEvent = new DamageTimelineEvent(\"damage_1\", 80, DamageType.PHYSICAL);\n        const damageNode = new TimelineNode(\"damage_node\", 1.0, damageEvent);\n        timeline.addNode(damageNode);\n\n        // 添加到TimelineManager\n        this.timelineManager.addTimeline(timeline);\n\n        console.log(\"伤害Timeline已创建，将在1秒后造成80点物理伤害\");\n    }\n\n    /** 测试Buff Timeline */\n    private onTestBuffTimeline(): void {\n        if (!this.player || !this.enemy) {\n            console.log(\"角色未准备好\");\n            return;\n        }\n\n        console.log(\"=== 测试Buff Timeline ===\");\n\n        // 创建Buff Timeline\n        const timeline = new Timeline(\n            \"buff_timeline_\" + Date.now(),\n            \"Buff测试Timeline\",\n            1.5,\n            this.player,\n            this.enemy\n        );\n\n        // 添加Buff事件\n        const buffEvent = new AddBuffTimelineEvent(\"buff_1\", \"hurt_fight\");\n        const buffNode = new TimelineNode(\"buff_node\", 0.8, buffEvent);\n        timeline.addNode(buffNode);\n\n        // 添加到TimelineManager\n        this.timelineManager.addTimeline(timeline);\n\n        console.log(\"Buff Timeline已创建，将在0.8秒后给敌人添加反击Buff\");\n    }\n\n    /** 创建多个敌人 */\n    private onCreateEnemies(): void {\n        console.log(\"=== 创建多个敌人 ===\");\n\n        // 清理现有敌人\n        for (const enemy of this.enemies) {\n            if (enemy && enemy.node) {\n                enemy.node.destroy();\n            }\n        }\n        this.enemies = [];\n\n        // 创建3个敌人\n        for (let i = 0; i < 3; i++) {\n            const enemyNode = new cc.Node(`Enemy_${i + 1}`);\n            enemyNode.parent = this.node;\n            enemyNode.position = cc.v3(200 + i * 150, 0, 0);\n\n            const enemy = enemyNode.addComponent(Character);\n            const enemyData: CharacterCreateInfo = {\n                prefabKey: \"enemy\",\n                role: CharacterRole.ENEMY,\n                name: `敌人${i + 1}`,\n                worldPosition: cc.v3(200 + i * 150, 0, 0),\n                initialAttributes: {\n                    hp: 300 + i * 100,\n                    maxHp: 300 + i * 100,\n                    mp: 50,\n                    maxMp: 50,\n                    attack: 60 + i * 10,\n                    defense: 20 + i * 5,\n                    attackSpeed: 1.0,\n                    moveSpeed: 80,\n                    attackRange: 120,\n                    criticalRate: 0.05,\n                    criticalDamage: 1.2,\n                    hitRate: 0.9,\n                    dodgeRate: 0.05,\n                    level: 3 + i,\n                    experience: 0\n                }\n            };\n            enemy.setCharacterData(enemyData);\n            this.setupEnemyEvents(enemy);\n            this.enemies.push(enemy);\n\n            console.log(`创建了${enemy.name}，HP: ${enemy.attributes.maxHp}`);\n        }\n\n        this.testMode = \"multi\";\n        console.log(\"多敌人模式已激活\");\n    }\n\n    /** 切换AI模式 */\n    private onToggleAI(): void {\n        this.aiEnabled = !this.aiEnabled;\n\n        if (this.aiEnabled) {\n            console.log(\"=== AI模式已启用 ===\");\n            // 启动AI定时器\n            this.schedule(this.updateAI, 1.0);\n        } else {\n            console.log(\"=== AI模式已禁用 ===\");\n            // 停止AI定时器\n            this.unschedule(this.updateAI);\n        }\n\n        // 更新按钮文本（如果需要的话）\n        if (this.toggleAIBtn && this.toggleAIBtn.getComponentInChildren(cc.Label)) {\n            const label = this.toggleAIBtn.getComponentInChildren(cc.Label);\n            label.string = this.aiEnabled ? \"关闭AI\" : \"开启AI\";\n        }\n    }\n\n    /** 显示调试信息 */\n    private onShowDebugInfo(): void {\n        console.log(\"=== 调试信息 ===\");\n\n        if (this.battleManager) {\n            console.log(\"BattleManager状态:\");\n            this.battleManager.printDebugInfo();\n        }\n\n        if (this.timelineManager) {\n            console.log(\"TimelineManager状态:\");\n            this.timelineManager.printDebugInfo();\n        }\n\n        if (this.player) {\n            console.log(\"玩家信息:\", this.player.getCharacterInfo());\n        }\n\n        if (this.enemy) {\n            console.log(\"敌人信息:\", this.enemy.getCharacterInfo());\n        }\n\n        if (this.enemies.length > 0) {\n            console.log(\"多敌人信息:\");\n            this.enemies.forEach((enemy, index) => {\n                console.log(`敌人${index + 1}:`, enemy.getCharacterInfo());\n            });\n        }\n\n        console.log(\"当前测试模式:\", this.testMode);\n        console.log(\"AI状态:\", this.aiEnabled ? \"启用\" : \"禁用\");\n    }\n\n    /** 设置敌人事件 */\n    private setupEnemyEvents(enemy: Character): void {\n        const events: ICharacterEvents = {\n            onDeath: (character) => {\n                console.log(`${character.characterName} 被击败了！`);\n                this.onEnemyDeath(character as Character);\n            },\n            onTakeDamage: (character, damage, attacker) => {\n                console.log(`${character.characterName} 受到了 ${damage} 点伤害`);\n                if (attacker) {\n                    console.log(`攻击者: ${attacker.characterName}`);\n                }\n            },\n            onSkillCast: (character, skillName) => {\n                console.log(`${character.characterName} 释放了技能: ${skillName}`);\n            }\n        };\n        enemy.setEvents(events);\n    }\n\n    /** 敌人死亡处理 */\n    private onEnemyDeath(enemy: Character): void {\n        // 从敌人列表中移除\n        const index = this.enemies.indexOf(enemy);\n        if (index >= 0) {\n            this.enemies.splice(index, 1);\n            console.log(`${enemy.name} 已从敌人列表中移除`);\n        }\n\n        // 检查是否所有敌人都被击败\n        const aliveEnemies = this.enemies.filter(e => !e.isDead);\n        if (aliveEnemies.length === 0 && !this.enemy?.isDead) {\n            // 只有单个敌人的情况\n        } else if (aliveEnemies.length === 0) {\n            console.log(\"🎉 胜利！所有敌人都被击败了！\");\n            this.onEndBattle();\n        }\n    }\n\n    /** AI更新逻辑 */\n    private updateAI(): void {\n        if (!this.aiEnabled || !this.battleStarted || !this.player || this.player.isDead) {\n            return;\n        }\n\n        // 玩家AI：攻击最近的敌人\n        this.updatePlayerAI();\n\n        // 敌人AI：攻击玩家\n        this.updateEnemyAI();\n    }\n\n    /** 更新玩家AI */\n    private updatePlayerAI(): void {\n        const target = this.findNearestEnemy();\n        if (!target) return;\n\n        // 尝试释放技能\n        if (this.player.castSkill(SkillName.player_skill_fire1, target.node)) {\n            console.log(`[AI] ${this.player.name} 对 ${target.name} 释放了火球术`);\n        } else if (this.player.isInAttackRange && this.player.isInAttackRange(target)) {\n            // 如果技能释放失败，使用普通攻击\n            this.player.attack(target);\n            console.log(`[AI] ${this.player.name} 攻击了 ${target.name}`);\n        }\n    }\n\n    /** 更新敌人AI */\n    private updateEnemyAI(): void {\n        // 单个敌人AI\n        if (this.enemy && !this.enemy.isDead && this.enemy.isInAttackRange(this.player)) {\n            this.enemy.attack(this.player);\n            console.log(`[AI] ${this.enemy.name} 攻击了 ${this.player.name}`);\n        }\n\n        // 多敌人AI\n        for (const enemy of this.enemies) {\n            if (!enemy.isDead && enemy.isInAttackRange(this.player)) {\n                enemy.attack(this.player);\n                console.log(`[AI] ${enemy.name} 攻击了 ${this.player.name}`);\n            }\n        }\n    }\n\n    /** 寻找最近的敌人 */\n    private findNearestEnemy(): Character | null {\n        let nearestEnemy: Character | null = null;\n        let minDistance = Infinity;\n\n        // 检查单个敌人\n        if (this.enemy && !this.enemy.isDead) {\n            const distance = cc.Vec3.distance(this.player.node.position, this.enemy.node.position);\n            if (distance < minDistance) {\n                minDistance = distance;\n                nearestEnemy = this.enemy;\n            }\n        }\n\n        // 检查多个敌人\n        for (const enemy of this.enemies) {\n            if (enemy.isDead) continue;\n            const distance = cc.Vec3.distance(this.player.node.position, enemy.node.position);\n            if (distance < minDistance) {\n                minDistance = distance;\n                nearestEnemy = enemy;\n            }\n        }\n\n        return nearestEnemy;\n    }\n}\n"]}