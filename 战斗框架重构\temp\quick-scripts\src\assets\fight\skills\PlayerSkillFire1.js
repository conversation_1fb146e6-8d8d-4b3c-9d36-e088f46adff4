"use strict";
cc._RF.push(module, '888b9/7EhpHL4i218sQZyuB', 'PlayerSkillFire1');
// fight/skills/PlayerSkillFire1.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerSkillFire1 = void 0;
var BattleManager_1 = require("../systems/BattleManager");
var BulletSystem_1 = require("../systems/BulletSystem");
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IBullet_1 = require("../types/IBullet");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var SkillName_1 = require("../types/SkillName");
/*** 火球术技能*/
var PlayerSkillFire1 = /** @class */ (function () {
    function PlayerSkillFire1() {
        this._id = SkillName_1.default.player_skill_fire1;
        this._name = "火球术";
        this._description = "发射一个火球，对敌人造成魔法伤害";
        this._cooldown = 2.0;
        this._remainingCooldown = 0;
        this._mpCost = 20;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ENEMY;
        this._range = 300;
        this._timeline = null;
        this._passiveBuffs = [];
        /**技能配置 */
        this._config = {
            animationName: "skill_fire1",
            soundId: "fire_skill_cast",
            bulletPrefabPath: "prefabs/bullets/FireBall",
            hitEffectPath: "prefabs/effects/FireExplosion",
            hitSoundId: "fire_explosion",
            damage: 0,
            damageType: IDamage_1.DamageType.MAGIC
        };
    }
    Object.defineProperty(PlayerSkillFire1.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "canUse", {
        get: function () { return this._remainingCooldown <= 0; },
        enumerable: false,
        configurable: true
    });
    /** * 检查是否可以对目标使用技能 */
    PlayerSkillFire1.prototype.canCastOn = function (caster, target) {
        if (!this.canUse) {
            return false;
        }
        if (!target || target.isDead) {
            return false;
        }
        // 检查目标是否为敌人
        if (target.role === caster.role) {
            return false;
        }
        var distance = cc.Vec3.distance(caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        return distance <= this._range;
    };
    /** * 释放技能 */
    PlayerSkillFire1.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " on " + (target === null || target === void 0 ? void 0 : target.characterName));
        console.log("Timeline " + this._timeline.id + " added to TimelineManager");
        return true;
    };
    /** * 更新技能状态 */
    PlayerSkillFire1.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** * 重置冷却时间 */
    PlayerSkillFire1.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** * 创建技能Timeline */
    PlayerSkillFire1.prototype.createTimeline = function (caster, target, targets, position) {
        var timeline = new Timeline_1.Timeline(Timeline_1.Timeline.getTimeLineId(this._id), this._name + "_Timeline", 3.0, caster, target, targets, position);
        // 0.0秒：播放施法动画
        var castAnimationNode = new Timeline_1.TimelineNode("cast_animation", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_anim", this._config.animationName, false, true));
        timeline.addNode(castAnimationNode);
        // 0.1秒：播放施法音效
        var castSoundNode = new Timeline_1.TimelineNode("cast_sound", 0.1, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId));
        timeline.addNode(castSoundNode);
        // 0.5秒：发射火球
        var fireBulletNode = new Timeline_1.TimelineNode("fire_bullet", 0.5, new TimelineEvents_1.FireBulletTimelineEvent("fire_bullet", this.createBulletLauncher(caster), this._config.hitEffectPath, "hit", this._config.hitSoundId));
        timeline.addNode(fireBulletNode);
        // 1.0秒：播放命中特效（如果子弹命中）
        var hitEffectNode = new Timeline_1.TimelineNode("hit_effect", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, cc.v3(0, 50, 0)));
        timeline.addNode(hitEffectNode);
        return timeline;
    };
    /** * 创建子弹发射器 */
    PlayerSkillFire1.prototype.createBulletLauncher = function (caster) {
        var bulletConfig = {
            id: "fire_ball",
            type: IBullet_1.BulletType.STRAIGHT,
            prefabPath: this._config.bulletPrefabPath,
            speed: 400,
            lifeTime: 5.0,
            maxHits: 1,
            trajectory: { type: IBullet_1.TrajectoryType.LINEAR },
            collision: { radius: 20, piercing: false, layers: ["enemy"], checkFrequency: 60 },
            visual: {
                trail: { enabled: true, length: 100, width: 10, color: cc.Color.ORANGE }
            },
            audio: {
                hitSound: this._config.hitSoundId
            }
        };
        // 创建发射器实例
        return new BulletSystem_1.BulletLauncher("fire_ball_launcher", caster, bulletConfig);
    };
    /** * 检查资源消耗 */
    PlayerSkillFire1.prototype.checkResourceCost = function (caster) {
        // 检查MP
        if (this._mpCost > 0) {
            var attributes = caster.attributes;
            if (attributes.currentMp < this._mpCost) {
                console.log(caster.characterName + " doesn't have enough MP (need " + this._mpCost + ", current: " + attributes.currentMp + ")");
                return false;
            }
        }
        // 检查耐力
        if (this._staminaCost > 0) {
            var attributes = caster.attributes;
            if (attributes.currentStamina < this._staminaCost) {
                console.log(caster.characterName + " doesn't have enough stamina (need " + this._staminaCost + ", current: " + attributes.currentStamina + ")");
                return false;
            }
        }
        return true;
    };
    /** * 消耗资源 */
    PlayerSkillFire1.prototype.consumeResources = function (caster) {
        // 消耗MP
        if (this._mpCost > 0) {
            var attributes = caster.attributes;
            attributes.consumeMp(this._mpCost);
            console.log(caster.characterName + " consumed " + this._mpCost + " MP (remaining: " + attributes.currentMp + ")");
        }
        // 消耗耐力
        if (this._staminaCost > 0) {
            var attributes = caster.attributes;
            attributes.consumeStamina(this._staminaCost);
            console.log(caster.characterName + " consumed " + this._staminaCost + " stamina (remaining: " + attributes.currentStamina + ")");
        }
    };
    /** * 升级技能 */
    PlayerSkillFire1.prototype.levelUp = function () {
        this._level++;
        // 根据等级调整技能属性
        this._mpCost = Math.max(10, this._mpCost - 1); // MP消耗减少
        this._cooldown = Math.max(1.0, this._cooldown - 0.1); // 冷却时间减少
        this._range += 20; // 射程增加
        console.log(this._name + " leveled up to " + this._level);
    };
    /** * 获取技能信息 */
    PlayerSkillFire1.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType
        };
    };
    /** * 技能被添加时的回调（用于多目标技能等特殊情况） */
    PlayerSkillFire1.prototype.onAdd = function (targets) {
        // 火球术是单目标技能，不需要特殊处理
        console.log(this._name + " added with " + targets.length + " targets");
    };
    return PlayerSkillFire1;
}());
exports.PlayerSkillFire1 = PlayerSkillFire1;

cc._RF.pop();