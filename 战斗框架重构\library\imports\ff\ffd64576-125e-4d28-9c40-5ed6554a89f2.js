"use strict";
cc._RF.push(module, 'ffd64V2El5NKJxAXtZVSony', 'FightEvent');
// fight/types/FightEvent.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var FightEvent = /** @class */ (function () {
    function FightEvent() {
    }
    /**攻击状态改变 */
    FightEvent.attackStateChanged = 'attackStateChanged';
    /**Buff被添加时触发事件 */
    FightEvent.buffApplied = 'buffApplied';
    /**Buff被移除时触发 */
    FightEvent.buffRemoved = 'buffRemoved';
    /**刷新Buff持续时间 */
    FightEvent.buffRefreshed = 'buffRefreshed';
    /**增加叠加层数 */
    FightEvent.buffStackChanged = 'buffStackChanged';
    /**执行反击 */
    FightEvent.counterAttack = 'counterAttack';
    /**移动状态改变 */
    FightEvent.moveStateChanged = 'moveStateChanged';
    /**释放技能 */
    FightEvent.skillCast = 'skillCast';
    /**添加Buff */
    FightEvent.buffAdded = 'buffAdded';
    /**受到伤害 */
    FightEvent.takeDamage = 'takeDamage';
    /**治疗 */
    FightEvent.heal = 'heal';
    /**死亡 */
    FightEvent.death = 'death';
    /**设置buff状态 */
    FightEvent.stateChanged = 'stateChanged';
    /**移除角色 */
    FightEvent.characterRemoved = 'characterRemoved';
    /**修改生命值 */
    FightEvent.hpChanged = 'hpChanged';
    /**修改魔法值 */
    FightEvent.mpChanged = 'mpChanged';
    /**修改耐力 */
    FightEvent.staminaChanged = 'staminaChanged';
    /**直接设置属性值（用于修改器） */
    FightEvent.attributeChanged = 'attributeChanged';
    /**buff层级叠加 */
    FightEvent.buffStacked = 'buffStacked';
    /**子弹击中 */
    FightEvent.bulletHit = 'bulletHit';
    /**子弹销毁 */
    FightEvent.bulletDestroyed = 'bulletDestroyed';
    /**子弹射击 */
    FightEvent.bulletFired = 'bulletFired';
    /**添加技能 */
    FightEvent.skillAdded = 'skillAdded';
    /**移除技能 */
    FightEvent.skillRemoved = 'skillRemoved';
    /**重置技能冷却 */
    FightEvent.skillCooldownReset = 'skillCooldownReset';
    /**重置技能冷却（多个） */
    FightEvent.allSkillsCooldownReset = 'allSkillsCooldownReset';
    /**使用技能 */
    FightEvent.skillUsed = 'skillUsed';
    /**添加Timeline */
    FightEvent.timelineAdded = 'timelineAdded';
    /**移除Timeline */
    FightEvent.timelineRemoved = 'timelineRemoved';
    /**暂停所有timeline */
    FightEvent.allTimelinesPaused = 'allTimelinesPaused';
    /**恢复所有timeline */
    FightEvent.allTimelinesResumed = 'allTimelinesResumed';
    /**清除所有Timeline */
    FightEvent.allTimelinesCleared = 'allTimelinesCleared';
    /**暂停指定Timeline */
    FightEvent.timelinePaused = 'timelinePaused';
    /**恢复指定Timeline */
    FightEvent.timelineResumed = 'timelineResumed';
    /**Timeline完成时的回调 */
    FightEvent.timelineCompleted = 'timelineCompleted';
    /**Timeline停止时的回调 */
    FightEvent.timelineStopped = 'timelineStopped';
    /**更新Timeline */
    FightEvent.completedT = 'completedT';
    /**暂停Timeline */
    FightEvent.pausedT = 'pausedT';
    /**恢复Timeline */
    FightEvent.resumedT = 'resumedT';
    /**停止Timeline */
    FightEvent.stoppedT = 'stoppedT';
    /**重置Timeline */
    FightEvent.resetT = 'resetT';
    /***跳转到指定时间点timeline */
    FightEvent.seekedT = 'seekedT';
    // 战斗管理器事件
    /**战斗开始 */
    FightEvent.battleStarted = 'battleStarted';
    /**战斗结束 */
    FightEvent.battleEnded = 'battleEnded';
    /**战斗暂停 */
    FightEvent.battlePaused = 'battlePaused';
    /**战斗恢复 */
    FightEvent.battleResumed = 'battleResumed';
    /**参战者加入 */
    FightEvent.participantAdded = 'participantAdded';
    /**参战者离开 */
    FightEvent.participantRemoved = 'participantRemoved';
    /**角色死亡 */
    FightEvent.characterDied = 'characterDied';
    /**角色被眩晕 */
    FightEvent.characterStunned = 'characterStunned';
    /**角色眩晕结束 */
    FightEvent.characterStunEnded = 'characterStunEnded';
    /**角色中毒 */
    FightEvent.characterPoisoned = 'characterPoisoned';
    /**毒素伤害 */
    FightEvent.poisonDamageDealt = 'poisonDamageDealt';
    /**角色被治疗 */
    FightEvent.characterHealed = 'characterHealed';
    return FightEvent;
}());
exports.default = FightEvent;

cc._RF.pop();