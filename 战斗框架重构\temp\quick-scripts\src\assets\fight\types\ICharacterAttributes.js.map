{"version": 3, "sources": ["assets\\fight\\types\\ICharacterAttributes.ts"], "names": [], "mappings": ";;;;;;;AAoLA,cAAc;AACd,IAAY,qBASX;AATD,WAAY,qBAAqB;IAC7B,WAAW;IACX,oCAAW,CAAA;IACX,WAAW;IACX,8CAAqB,CAAA;IACrB,YAAY;IACZ,kDAAyB,CAAA;IACzB,WAAW;IACX,8CAAqB,CAAA;AACzB,CAAC,EATW,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAShC", "file": "", "sourceRoot": "/", "sourcesContent": ["\n/*** 角色基础属性接口*/\nexport interface ICharacterAttributes {\n    /** 当前生命值 */\n    readonly currentHp: number;\n    /** 最大生命值 */\n    readonly maxHp: number;\n    /** 当前魔法值 */\n    readonly currentMp: number;\n    /** 最大魔法值 */\n    readonly maxMp: number;\n    /** 当前耐力 */\n    readonly currentStamina: number;\n    /** 最大耐力 */\n    readonly maxStamina: number;\n    /** 攻击力 */\n    readonly attack: number;\n    /** 魔法攻击力 */\n    readonly magicAttack: number;\n    /** 防御力 */\n    readonly defense: number;\n    /** 魔法防御力 */\n    readonly magicDefense: number;\n    /** 攻击速度 */\n    readonly attackSpeed: number;\n    /** 移动速度 */\n    readonly moveSpeed: number;\n    /** 攻击范围 */\n    readonly attackRange: number;\n    /** 暴击率 */\n    readonly criticalRate: number;\n    /** 暴击伤害 */\n    readonly criticalDamage: number;\n    /** 命中率 */\n    readonly accuracy: number;\n    /** 闪避率 */\n    readonly evasion: number;\n    /** 速度 */\n    readonly speed: number;\n    /** 等级 */\n    readonly level: number;\n    /**\n     * 修改生命值\n     * @param amount 修改量（正数为治疗，负数为伤害）\n     */\n    modifyHp(amount: number): void;\n    /**\n     * 获取生命值百分比\n     * @returns 生命值百分比 (0-1)\n     */\n    getHpPercentage(): number;\n    /**\n     * 是否死亡\n     * @returns 是否死亡\n     */\n    isDead(): boolean;\n    /*** 重置到满血状态*/\n    resetToFull(): void;\n    /**\n     * 添加属性修改器\n     * @param modifier 属性修改器\n     */\n    addModifier(modifier: IAttributeModifier): void;\n    /**\n     * 移除属性修改器\n     * @param modifierId 修改器ID\n     */\n    removeModifier(modifierId: string): void;\n    /**\n     * 获取当前属性值\n     * @param attributeName 属性名称\n     * @returns 属性值\n     */\n    getCurrentAttributeValue(attributeName: string): number;\n    /**\n     * 设置属性值（用于修改器）\n     * @param attributeName 属性名称\n     * @param value 属性值\n     */\n    setAttributeValue(attributeName: string, value: number): void;\n    /**\n     * 获取属性数据的副本\n     * @returns 属性数据副本\n     */\n    getAttributeData(): any;\n    /**\n     * 消耗魔法值\n     * @param amount 消耗量\n     */\n    consumeMp(amount: number): void;\n    /**\n     * 消耗耐力\n     * @param amount 消耗量\n     */\n    consumeStamina(amount: number): void;\n    /**\n     * 造成伤害\n     * @param amount 伤害量\n     */\n    takeDamage(amount: number): void;\n}\n\n/*** 角色资源接口（MP、耐力等）*/\nexport interface ICharacterResource {\n    /** 当前魔法值 */\n    readonly currentMp: number;\n    /** 最大魔法值 */\n    readonly maxMp: number;\n    /** 当前耐力 */\n    readonly currentStamina: number;\n    /** 最大耐力 */\n    readonly maxStamina: number;\n    /**\n     * 修改魔法值\n     * @param amount 修改量\n     */\n    modifyMp(amount: number): void;\n    /**\n     * 修改耐力\n     * @param amount 修改量\n     */\n    modifyStamina(amount: number): void;\n    /**\n     * 检查是否有足够的魔法值\n     * @param amount 需要的魔法值\n     * @returns 是否足够\n     */\n    hasEnoughMp(amount: number): boolean;\n    /**\n     * 检查是否有足够的耐力\n     * @param amount 需要的耐力\n     * @returns 是否足够\n     */\n    hasEnoughStamina(amount: number): boolean;\n    /**\n     * 获取魔法值百分比\n     * @returns 魔法值百分比 (0-1)\n     */\n    getMpPercentage(): number;\n    /**\n     * 获取耐力百分比\n     * @returns 耐力百分比 (0-1)\n     */\n    getStaminaPercentage(): number;\n    /**  * 重置资源到满值状态  */\n    resetResourcesToFull(): void;\n}\n\n/** * 角色属性修改器接口 */\nexport interface IAttributeModifier {\n    /** 修改器ID */\n    readonly id: string;\n    /** 修改器名称 */\n    readonly name: string;\n    /** 修改器类型 */\n    readonly type: AttributeModifierType;\n    /** 修改值 */\n    readonly value: number;\n    /** 持续时间（秒，-1表示永久） */\n    readonly duration: number;\n    /** 剩余时间 */\n    remainingTime: number;\n    /**\n     * 应用修改器到属性\n     * @param attributes 目标属性\n     */\n    apply(attributes: ICharacterAttributes): void;\n    /**\n     * 移除修改器效果\n     * @param attributes 目标属性\n     */\n    remove(attributes: ICharacterAttributes): void;\n    /**\n     * 更新修改器\n     * @param deltaTime 时间间隔\n     * @returns 是否已过期\n     */\n    update(deltaTime: number): boolean;\n}\n\n/*** 属性修改器类型*/\nexport enum AttributeModifierType {\n    /** 加法修改 */\n    ADD = \"add\",\n    /** 乘法修改 */\n    MULTIPLY = \"multiply\",\n    /** 百分比修改 */\n    PERCENTAGE = \"percentage\",\n    /** 覆盖修改 */\n    OVERRIDE = \"override\"\n}\n\n/*** 属性变化事件*/\nexport interface IAttributeChangeEvent {\n    /** 属性名称 */\n    attributeName: string;\n    /** 旧值 */\n    oldValue: number;\n    /** 新值 */\n    newValue: number;\n    /** 变化量 */\n    delta: number;\n    /** 变化原因 */\n    reason?: string;\n}\n"]}