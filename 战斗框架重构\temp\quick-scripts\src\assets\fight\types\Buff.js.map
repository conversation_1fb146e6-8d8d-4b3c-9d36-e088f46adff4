{"version": 3, "sources": ["assets\\fight\\types\\Buff.ts"], "names": [], "mappings": ";;;;;;;AACA;;;GAGG;AACH;IAAA;IA8CA,CAAC;IA7CG,qCAAqC;IAC9B,mCAAmB,GAAG,qBAAqB,CAAA;IAElD,0BAA0B;IACnB,6CAA6B,GAAG,+BAA+B,CAAA;IAEtE,yBAAyB;IAClB,2BAAW,GAAG,aAAa,CAAA;IAElC,oCAAoC;IAC7B,iCAAiB,GAAG,mBAAmB,CAAA;IAE9C,2BAA2B;IACpB,iCAAiB,GAAG,mBAAmB,CAAA;IAE9C,2BAA2B;IACpB,uCAAuB,GAAG,yBAAyB,CAAA;IAE1D,+BAA+B;IACxB,uCAAuB,GAAG,yBAAyB,CAAA;IAE1D,8BAA8B;IACvB,+BAAe,GAAG,iBAAiB,CAAA;IAE1C,wBAAwB;IACjB,0BAAU,GAAG,YAAY,CAAA;IAEhC,oCAAoC;IAC7B,iCAAiB,GAAG,mBAAmB,CAAA;IAE9C,4BAA4B;IACrB,sCAAsB,GAAG,wBAAwB,CAAA;IAExD,qCAAqC;IAC9B,iCAAiB,GAAG,mBAAmB,CAAA;IAE9C,+BAA+B;IACxB,gCAAgB,GAAG,kBAAkB,CAAA;IAE5C,+BAA+B;IACxB,8BAAc,GAAG,gBAAgB,CAAA;IAExC,iCAAiC;IAC1B,oCAAoB,GAAG,sBAAsB,CAAA;IAExD,sBAAC;CA9CD,AA8CC,IAAA;AA9CY,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n/**\r\n * Buff效果类型枚举\r\n * 定义了游戏中所有可能的Buff效果类型常量\r\n */\r\nexport class EBuffEffectType {\r\n    /** 反击几率 - 受到攻击时触发反击的概率 (0-1之间的小数) */\r\n    static counterAttackChance = 'counterAttackChance'\r\n\r\n    /** 反击伤害倍数 - 反击时造成伤害的倍数 */\r\n    static counterAttackDamageMultiplier = 'counterAttackDamageMultiplier'\r\n\r\n    /** 攻击力加成 - 固定数值的攻击力增加 */\r\n    static attackBonus = 'attackBonus'\r\n\r\n    /** 暴击率加成 - 增加暴击几率的百分比 (0-1之间的小数) */\r\n    static criticalRateBonus = 'criticalRateBonus'\r\n\r\n    /** 物理攻击倍数 - 物理攻击伤害的乘数效果 */\r\n    static attack_multiplier = 'attack_multiplier'\r\n\r\n    /** 魔法攻击倍数 - 魔法攻击伤害的乘数效果 */\r\n    static magic_attack_multiplier = 'magic_attack_multiplier'\r\n\r\n    /** 攻击力百分比加成 - 基于基础攻击力的百分比增加 */\r\n    static attack_bonus_percentage = 'attack_bonus_percentage'\r\n\r\n    /** 每秒治疗量 - 持续治疗效果，每秒恢复的生命值 */\r\n    static heal_per_second = 'heal_per_second'\r\n\r\n    /** 总治疗量 - 一次性治疗效果的总量 */\r\n    static total_heal = 'total_heal'\r\n\r\n    /** 每秒伤害 - 持续伤害效果，每秒造成的伤害 (毒、燃烧等) */\r\n    static damage_per_second = 'damage_per_second'\r\n\r\n    /** 剩余总伤害 - 持续伤害效果的剩余总伤害量 */\r\n    static total_damage_remaining = 'total_damage_remaining'\r\n\r\n    /** 治疗减免 - 降低受到治疗效果的百分比 (0-1之间的小数) */\r\n    static healing_reduction = 'healing_reduction'\r\n\r\n    /** 移动封锁 - 阻止单位移动的控制效果 (布尔值) */\r\n    static movement_blocked = 'movement_blocked'\r\n\r\n    /** 技能封锁 - 阻止使用技能的控制效果 (布尔值) */\r\n    static skills_blocked = 'skills_blocked'\r\n\r\n    /** 普攻封锁 - 阻止使用普通攻击的控制效果 (布尔值) */\r\n    static basic_attack_blocked = 'basic_attack_blocked'\r\n\r\n}"]}