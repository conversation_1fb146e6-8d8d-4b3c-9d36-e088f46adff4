
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/Buff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5af459gVBZLV45FWMDYX2vt', 'Buff');
// fight/types/Buff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EBuffEffectType = void 0;
/**
 * Buff效果类型枚举
 * 定义了游戏中所有可能的Buff效果类型常量
 */
var EBuffEffectType = /** @class */ (function () {
    function EBuffEffectType() {
    }
    /** 反击几率 - 受到攻击时触发反击的概率 (0-1之间的小数) */
    EBuffEffectType.counterAttackChance = 'counterAttackChance';
    /** 反击伤害倍数 - 反击时造成伤害的倍数 */
    EBuffEffectType.counterAttackDamageMultiplier = 'counterAttackDamageMultiplier';
    /** 攻击力加成 - 固定数值的攻击力增加 */
    EBuffEffectType.attackBonus = 'attackBonus';
    /** 暴击率加成 - 增加暴击几率的百分比 (0-1之间的小数) */
    EBuffEffectType.criticalRateBonus = 'criticalRateBonus';
    /** 物理攻击倍数 - 物理攻击伤害的乘数效果 */
    EBuffEffectType.attack_multiplier = 'attack_multiplier';
    /** 魔法攻击倍数 - 魔法攻击伤害的乘数效果 */
    EBuffEffectType.magic_attack_multiplier = 'magic_attack_multiplier';
    /** 攻击力百分比加成 - 基于基础攻击力的百分比增加 */
    EBuffEffectType.attack_bonus_percentage = 'attack_bonus_percentage';
    /** 每秒治疗量 - 持续治疗效果，每秒恢复的生命值 */
    EBuffEffectType.heal_per_second = 'heal_per_second';
    /** 总治疗量 - 一次性治疗效果的总量 */
    EBuffEffectType.total_heal = 'total_heal';
    /** 每秒伤害 - 持续伤害效果，每秒造成的伤害 (毒、燃烧等) */
    EBuffEffectType.damage_per_second = 'damage_per_second';
    /** 剩余总伤害 - 持续伤害效果的剩余总伤害量 */
    EBuffEffectType.total_damage_remaining = 'total_damage_remaining';
    /** 治疗减免 - 降低受到治疗效果的百分比 (0-1之间的小数) */
    EBuffEffectType.healing_reduction = 'healing_reduction';
    /** 移动封锁 - 阻止单位移动的控制效果 (布尔值) */
    EBuffEffectType.movement_blocked = 'movement_blocked';
    /** 技能封锁 - 阻止使用技能的控制效果 (布尔值) */
    EBuffEffectType.skills_blocked = 'skills_blocked';
    /** 普攻封锁 - 阻止使用普通攻击的控制效果 (布尔值) */
    EBuffEffectType.basic_attack_blocked = 'basic_attack_blocked';
    return EBuffEffectType;
}());
exports.EBuffEffectType = EBuffEffectType;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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