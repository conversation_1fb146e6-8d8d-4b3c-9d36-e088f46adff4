"use strict";
cc._RF.push(module, 'a8ca9HIeOBKr6U/ra2jDluH', 'TimelineEvents');
// fight/timeline/TimelineEvents.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionalTimelineEvent = exports.MultiTargetDamageTimelineEvent = exports.CustomTimelineEvent = exports.MoveTimelineEvent = exports.PlayEffectTimelineEvent = exports.PlaySoundTimelineEvent = exports.PlayAnimationTimelineEvent = exports.AddBuffTimelineEvent = exports.FireBulletTimelineEvent = exports.HealTimelineEvent = exports.DamageTimelineEvent = void 0;
var IDamage_1 = require("../types/IDamage");
var ITimeline_1 = require("../types/ITimeline");
var Timeline_1 = require("./Timeline");
var BuffModelBeHurtFight_1 = require("../buff/BuffModelBeHurtFight");
var BattleManager_1 = require("../systems/BattleManager");
/*** 伤害事件*/
var DamageTimelineEvent = /** @class */ (function (_super) {
    __extends(DamageTimelineEvent, _super);
    function DamageTimelineEvent(id, damageAmount, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.DAMAGE) || this;
        _this.damageAmount = damageAmount;
        _this.damageType = damageType;
        _this.tags = tags;
        return _this;
    }
    DamageTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("DamageTimelineEvent: No valid target found");
            return;
        }
        // 使用伤害管理器处理复杂的伤害计算
        var damageManager = BattleManager_1.BattleManager.instance.damageManager;
        var baseDamage = this.damageAmount === 0 ? caster.attributes.attack : this.damageAmount;
        var damageInfo = damageManager.dealDamage(caster, target, baseDamage, this.damageType, this.tags);
        console.log("[Timeline-Event] " + caster.characterName + " deals " + damageInfo.finalDamage + " " + this.damageType + " damage to " + target.characterName + (damageInfo.isCritical ? ' (Critical!)' : ''));
    };
    return DamageTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.DamageTimelineEvent = DamageTimelineEvent;
/*** 治疗事件*/
var HealTimelineEvent = /** @class */ (function (_super) {
    __extends(HealTimelineEvent, _super);
    function HealTimelineEvent(id, healAmount) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.HEAL) || this;
        _this.healAmount = healAmount;
        return _this;
    }
    HealTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex) || caster; // 默认治疗自己
        console.log("[Timeline-Event] " + caster.characterName + " heals " + target.characterName + " for " + this.healAmount);
        target.heal(this.healAmount);
    };
    return HealTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.HealTimelineEvent = HealTimelineEvent;
/** * 子弹发射事件 */
var FireBulletTimelineEvent = /** @class */ (function (_super) {
    __extends(FireBulletTimelineEvent, _super);
    function FireBulletTimelineEvent(id, bulletLauncher, effectPrefabPath, hitAnimationName, soundId) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.FIRE_BULLET) || this;
        _this.bulletLauncher = bulletLauncher;
        _this.effectPrefabPath = effectPrefabPath;
        _this.hitAnimationName = hitAnimationName;
        _this.soundId = soundId;
        return _this;
    }
    FireBulletTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("FireBulletTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] " + caster.characterName + " fires bullet at " + target.characterName);
        if (this.soundId) {
            this.playSound(this.soundId);
        }
        this.bulletLauncher.firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var bullet = this.bulletLauncher.fire(target);
        // 将子弹添加到子弹管理器
        if (bullet) {
            var bulletManager = BattleManager_1.BattleManager.instance.bulletManager;
            bulletManager.addBullet(bullet);
            console.log("[Timeline-Event] Bullet " + bullet.id + " added to BulletManager");
        }
        if (this.effectPrefabPath) {
            this.playEffect(this.effectPrefabPath, target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        }
    };
    return FireBulletTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.FireBulletTimelineEvent = FireBulletTimelineEvent;
/*** Buff添加事件*/
var AddBuffTimelineEvent = /** @class */ (function (_super) {
    __extends(AddBuffTimelineEvent, _super);
    function AddBuffTimelineEvent(id, buffId, targetSelf) {
        if (targetSelf === void 0) { targetSelf = false; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.ADD_BUFF) || this;
        _this.buffId = buffId;
        _this.targetSelf = targetSelf;
        return _this;
    }
    AddBuffTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("AddBuffTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] Adding buff " + this.buffId + " to " + target.characterName);
        // 简单的 Buff 创建逻辑（可以后续扩展为 BuffFactory）
        var buff = this.createBuff(this.buffId, caster, target);
        if (buff) {
            target.addBuff(buff);
            console.log("[Timeline-Event] Successfully added buff " + this.buffId + " to " + target.characterName);
        }
        else {
            console.warn("[Timeline-Event] Failed to create buff " + this.buffId);
        }
    };
    /** 创建 Buff 实例（简单工厂模式） */
    AddBuffTimelineEvent.prototype.createBuff = function (buffId, caster, target) {
        switch (buffId) {
            case "counter_attack":
            case "hurt_fight":
                return new BuffModelBeHurtFight_1.BuffModelBeHurtFight(caster, target);
            // 可以在这里添加更多 Buff 类型
            default:
                console.warn("Unknown buff ID: " + buffId);
                return null;
        }
    };
    return AddBuffTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.AddBuffTimelineEvent = AddBuffTimelineEvent;
/*** 动画播放事件*/
var PlayAnimationTimelineEvent = /** @class */ (function (_super) {
    __extends(PlayAnimationTimelineEvent, _super);
    function PlayAnimationTimelineEvent(id, animationName, loop, targetSelf) {
        if (loop === void 0) { loop = false; }
        if (targetSelf === void 0) { targetSelf = true; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_ANIMATION) || this;
        _this.animationName = animationName;
        _this.loop = loop;
        _this.targetSelf = targetSelf;
        return _this;
    }
    PlayAnimationTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        var target = this.targetSelf ? caster : this.getValidTarget(timeline, nodeIndex);
        if (!target) {
            console.warn("PlayAnimationTimelineEvent: No valid target found");
            return;
        }
        console.log("[Timeline-Event] Playing animation " + this.animationName + " on " + target.characterName);
        var spine = target.node.getComponent(sp.Skeleton);
        if (spine) {
            spine.setAnimation(0, this.animationName, this.loop);
        }
    };
    return PlayAnimationTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlayAnimationTimelineEvent = PlayAnimationTimelineEvent;
/*** 音效播放事件*/
var PlaySoundTimelineEvent = /** @class */ (function (_super) {
    __extends(PlaySoundTimelineEvent, _super);
    function PlaySoundTimelineEvent(id, soundId, volume) {
        if (volume === void 0) { volume = 1.0; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_SOUND) || this;
        _this.soundId = soundId;
        _this.volume = volume;
        return _this;
    }
    PlaySoundTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        console.log("[Timeline-Event] Playing sound " + this.soundId);
        this.playSound(this.soundId);
    };
    return PlaySoundTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlaySoundTimelineEvent = PlaySoundTimelineEvent;
/*** 特效播放事件*/
var PlayEffectTimelineEvent = /** @class */ (function (_super) {
    __extends(PlayEffectTimelineEvent, _super);
    function PlayEffectTimelineEvent(id, effectId, targetPosition, offset) {
        if (targetPosition === void 0) { targetPosition = true; }
        if (offset === void 0) { offset = cc.Vec3.ZERO; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.PLAY_EFFECT) || this;
        _this.effectId = effectId;
        _this.targetPosition = targetPosition;
        _this.offset = offset;
        return _this;
    }
    PlayEffectTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var position;
        if (this.targetPosition) {
            var target = this.getValidTarget(timeline, nodeIndex);
            if (target) {
                position = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            }
            else {
                position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
            }
        }
        else {
            position = timeline.caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO).add(this.offset);
        }
        console.log("[Timeline-Event] Playing effect " + this.effectId + " at position", position);
        this.playEffect(this.effectId, position);
    };
    return PlayEffectTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.PlayEffectTimelineEvent = PlayEffectTimelineEvent;
/** * 移动事件 */
var MoveTimelineEvent = /** @class */ (function (_super) {
    __extends(MoveTimelineEvent, _super);
    function MoveTimelineEvent(id, targetPosition, duration, easing) {
        if (duration === void 0) { duration = 1.0; }
        if (easing === void 0) { easing = "linear"; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.MOVE) || this;
        _this.targetPosition = targetPosition;
        _this.duration = duration;
        _this.easing = easing;
        return _this;
    }
    MoveTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var caster = timeline.caster;
        console.log("[Timeline-Event] Moving " + caster.characterName + " to position", this.targetPosition);
        cc.tween(caster.node)
            .to(this.duration, { position: this.targetPosition })
            .start();
    };
    return MoveTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.MoveTimelineEvent = MoveTimelineEvent;
/*** 自定义事件*/
var CustomTimelineEvent = /** @class */ (function (_super) {
    __extends(CustomTimelineEvent, _super);
    function CustomTimelineEvent(id, customFunction) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.CUSTOM) || this;
        _this.customFunction = customFunction;
        return _this;
    }
    CustomTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        try {
            this.customFunction(timeline, nodeIndex);
        }
        catch (error) {
            console.error("Error executing custom timeline event " + this.id + ":", error);
        }
    };
    return CustomTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.CustomTimelineEvent = CustomTimelineEvent;
/*** 多目标伤害事件（用于AOE技能）*/
var MultiTargetDamageTimelineEvent = /** @class */ (function (_super) {
    __extends(MultiTargetDamageTimelineEvent, _super);
    function MultiTargetDamageTimelineEvent(id, damageAmount, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.DAMAGE) || this;
        _this.damageAmount = damageAmount;
        _this.damageType = damageType;
        _this.tags = tags;
        return _this;
    }
    MultiTargetDamageTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        var e_1, _a;
        var caster = timeline.caster;
        var targets = this.getTargets(timeline);
        if (targets.length === 0) {
            console.warn("MultiTargetDamageTimelineEvent: No valid targets found");
            return;
        }
        console.log("[Timeline-Event] " + caster.characterName + " deals AOE damage to " + targets.length + " targets");
        try {
            // 对所有目标造成伤害
            for (var targets_1 = __values(targets), targets_1_1 = targets_1.next(); !targets_1_1.done; targets_1_1 = targets_1.next()) {
                var target = targets_1_1.value;
                var finalDamage = this.calculateDamage(caster, target);
                target.takeDamage(finalDamage, caster);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (targets_1_1 && !targets_1_1.done && (_a = targets_1.return)) _a.call(targets_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    MultiTargetDamageTimelineEvent.prototype.calculateDamage = function (caster, target) {
        var baseAttack = caster.attributes.attack;
        var defense = target.attributes.defense;
        var finalDamage = this.damageAmount;
        if (this.damageAmount === 0) {
            finalDamage = baseAttack;
        }
        if (this.damageType === IDamage_1.DamageType.PHYSICAL) {
            finalDamage = Math.max(1, finalDamage - defense);
        }
        return Math.floor(finalDamage);
    };
    return MultiTargetDamageTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.MultiTargetDamageTimelineEvent = MultiTargetDamageTimelineEvent;
/** * 条件事件（只有满足条件时才执行） */
var ConditionalTimelineEvent = /** @class */ (function (_super) {
    __extends(ConditionalTimelineEvent, _super);
    function ConditionalTimelineEvent(id, condition, wrappedEvent) {
        var _this = _super.call(this, id, ITimeline_1.TimelineEventType.CUSTOM) || this;
        _this.condition = condition;
        _this.wrappedEvent = wrappedEvent;
        return _this;
    }
    ConditionalTimelineEvent.prototype.execute = function (timeline, nodeIndex) {
        if (this.condition(timeline)) {
            this.wrappedEvent.execute(timeline, nodeIndex);
        }
        else {
            console.log("[Timeline-Event] Condition not met for event " + this.id);
        }
    };
    return ConditionalTimelineEvent;
}(Timeline_1.TimelineEvent));
exports.ConditionalTimelineEvent = ConditionalTimelineEvent;

cc._RF.pop();