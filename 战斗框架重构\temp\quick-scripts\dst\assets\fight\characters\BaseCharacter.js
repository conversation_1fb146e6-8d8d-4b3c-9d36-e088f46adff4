
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/characters/BaseCharacter.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ddc9d9kjuBCSJy51CA4sS9f', 'BaseCharacter');
// fight/characters/BaseCharacter.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCharacter = void 0;
var BuffManager_1 = require("../systems/BuffManager");
var EventManager_1 = require("../systems/EventManager");
var SkillManager_1 = require("../systems/SkillManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var IDamage_1 = require("../types/IDamage");
var CharacterAttributes_1 = require("./CharacterAttributes");
/*** 角色基类*/
var BaseCharacter = /** @class */ (function (_super) {
    __extends(BaseCharacter, _super);
    function BaseCharacter() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._state = CharacterTypes_1.CharacterState.IDLE;
        _this._selectTag = CharacterTypes_1.CharacterSelectTag.NONE;
        // 事件回调
        _this._events = {};
        return _this;
    }
    Object.defineProperty(BaseCharacter.prototype, "id", {
        // 实现ICharacter接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "characterName", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "role", {
        get: function () { return this._role; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "attributes", {
        get: function () { return this._attributes; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "isDead", {
        get: function () { return this._state === CharacterTypes_1.CharacterState.DEAD; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "skills", {
        get: function () { return this._skillManager.getAllSkills(); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "buffs", {
        get: function () { return this._buffManager.buffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "skillManager", {
        get: function () { return this._skillManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "buffManager", {
        get: function () { return this._buffManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "state", {
        // 额外属性
        get: function () { return this._state; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "selectTag", {
        get: function () { return this._selectTag; },
        set: function (value) { this._selectTag = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "spine", {
        get: function () { return this._spine; },
        enumerable: false,
        configurable: true
    });
    /*** 初始化角色*/
    BaseCharacter.prototype.onLoad = function () {
        this.initializeId();
        this.initializeComponents();
        this.initializeNodes();
        this.setupEventListeners();
    };
    /** * 启动角色 */
    BaseCharacter.prototype.start = function () {
        this.onCharacterStart();
    };
    /** * 更新角色状态 */
    BaseCharacter.prototype.update = function (deltaTime) {
        if (this.isDead)
            return;
        this._skillManager.update(deltaTime);
        this._buffManager.update(deltaTime);
        this.onCharacterUpdate(deltaTime);
    };
    /** * 销毁角色 */
    BaseCharacter.prototype.onDestroy = function () {
        this.cleanup();
    };
    /** * 初始化ID */
    BaseCharacter.prototype.initializeId = function () {
        this._id = this.generateUniqueId();
    };
    /** * 初始化组件 */
    BaseCharacter.prototype.initializeComponents = function () {
        this._attributes = new CharacterAttributes_1.CharacterAttributes();
        this._skillManager = new SkillManager_1.SkillManager(this);
        this._buffManager = new BuffManager_1.BuffManager(this);
        this._eventManager = EventManager_1.EventManager.createLocal("Character_" + (this._name || this._id));
    };
    /** * 初始化节点引用 */
    BaseCharacter.prototype.initializeNodes = function () {
        this._spine = this.getComponent(sp.Skeleton);
        this._fireNode = this.node.getChildByName("fireNode");
        this._fireBaseNode = this.node.getChildByName("fireBaseNode");
    };
    /** * 设置事件监听器 */
    BaseCharacter.prototype.setupEventListeners = function () {
        this._eventManager.on(FightEvent_1.default.attributeChanged, this.onAttributeChanged.bind(this));
        this._eventManager.on(FightEvent_1.default.stateChanged, this.onStateChanged.bind(this));
        this._eventManager.on(FightEvent_1.default.death, this.onDeath.bind(this));
    };
    /** * 移动角色 */
    BaseCharacter.prototype.move = function (direction) {
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.STUNNED) {
            return false;
        }
        if (this.canMove()) {
            this.setState(CharacterTypes_1.CharacterState.MOVING);
            this.performMove(direction);
            return true;
        }
        return false;
    };
    /** * 攻击目标 */
    BaseCharacter.prototype.attack = function (target) {
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.STUNNED) {
            return false;
        }
        var attackSkill = this._skillManager.getBasicAttackSkill();
        if (attackSkill) {
            return this.castSkill(attackSkill.id, target === null || target === void 0 ? void 0 : target.node);
        }
        return false;
    };
    /** * 释放技能 */
    BaseCharacter.prototype.castSkill = function (skillName, target) {
        var _a, _b;
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.SILENCED) {
            return false;
        }
        var skill = this._skillManager.getSkill(skillName);
        if (!skill || !skill.canUse) {
            return false;
        }
        if (!this.checkResourceCost(skill)) {
            return false;
        }
        this.consumeResources(skill);
        this.setState(CharacterTypes_1.CharacterState.CASTING);
        var success = skill.cast(this, this.getCharacterFromNode(target), undefined, this.getWorldPosition(target));
        if (success) {
            (_b = (_a = this._events).onSkillCast) === null || _b === void 0 ? void 0 : _b.call(_a, this, skillName);
            this._eventManager.emit(FightEvent_1.default.skillCast, { character: this, skillName: skillName });
        }
        return success;
    };
    /** * 学习技能 */
    BaseCharacter.prototype.learnSkill = function (skill) {
        this._skillManager.addSkill(skill);
    };
    /** * 添加Buff */
    BaseCharacter.prototype.addBuff = function (buff) {
        var _a, _b;
        this._buffManager.addBuff(buff);
        (_b = (_a = this._events).onBuffAdded) === null || _b === void 0 ? void 0 : _b.call(_a, this, buff);
        this._eventManager.emit(FightEvent_1.default.buffAdded, { character: this, buff: buff });
    };
    /*** 移除Buff*/
    BaseCharacter.prototype.removeBuff = function (buffId) {
        var _a, _b;
        var removed = this._buffManager.removeBuff(buffId);
        if (removed) {
            (_b = (_a = this._events).onBuffRemoved) === null || _b === void 0 ? void 0 : _b.call(_a, this, buffId);
            this._eventManager.emit(FightEvent_1.default.buffRemoved, { character: this, buffId: buffId });
        }
    };
    /*** 受到伤害（新版本，使用伤害信息对象）*/
    BaseCharacter.prototype.takeDamage = function (damageInfo) {
        var _this = this;
        var _a, _b, _c;
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.INVINCIBLE) {
            return;
        }
        // 检查是否闪避
        if (damageInfo.isDodged) {
            console.log(this.characterName + " dodged the attack!");
            this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
            return;
        }
        // 应用伤害
        var finalDamage = damageInfo.finalDamage;
        this._attributes.modifyHp(-finalDamage);
        // 根据伤害类型处理特殊效果
        this.handleDamageTypeEffects(damageInfo);
        // 处理附加的Buff
        if (damageInfo.attachedBuffs && damageInfo.attachedBuffs.length > 0) {
            damageInfo.attachedBuffs.forEach(function (buff) {
                _this.addBuff(buff);
            });
        }
        // 触发事件
        (_b = (_a = this._events).onTakeDamage) === null || _b === void 0 ? void 0 : _b.call(_a, this, finalDamage, damageInfo.attacker);
        this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
        // 检查死亡
        if (this._attributes.isDead()) {
            this.die();
        }
        // 日志输出
        var damageTypeStr = damageInfo.isCritical ? "CRITICAL " + damageInfo.damageType : damageInfo.damageType;
        console.log(this.characterName + " takes " + finalDamage + " " + damageTypeStr + " damage from " + (((_c = damageInfo.attacker) === null || _c === void 0 ? void 0 : _c.characterName) || 'unknown'));
    };
    /*** 受到伤害（简化版本，向后兼容）*/
    BaseCharacter.prototype.takeDamageSimple = function (damage, attacker) {
        // 创建简单的伤害信息对象
        var simpleDamageInfo = {
            id: "simple_damage_" + Date.now(),
            attacker: attacker || null,
            target: this,
            baseDamage: damage,
            finalDamage: damage,
            damageType: IDamage_1.DamageType.PHYSICAL,
            tags: [IDamage_1.DamageTag.DIRECT],
            isCritical: false,
            criticalMultiplier: 1.0,
            damageReduction: 0,
            damageAmplification: 0,
            attachedBuffs: [],
            isDodged: false,
            source: 'simple_attack',
            calculateFinalDamage: function () { return damage; },
            addTag: function (tag) {
                if (!simpleDamageInfo.tags.includes(tag)) {
                    simpleDamageInfo.tags.push(tag);
                }
            },
            hasTag: function (tag) { return simpleDamageInfo.tags.includes(tag); },
            addAttachedBuff: function (buff) {
                simpleDamageInfo.attachedBuffs.push(buff);
            }
        };
        this.takeDamage(simpleDamageInfo);
    };
    /** 处理不同伤害类型的特殊效果 */
    BaseCharacter.prototype.handleDamageTypeEffects = function (damageInfo) {
        switch (damageInfo.damageType) {
            case IDamage_1.DamageType.POISON:
                // 毒素伤害可能有持续效果
                console.log(this.characterName + " is poisoned!");
                break;
            case IDamage_1.DamageType.MAGIC:
                // 魔法伤害可能有特殊效果
                break;
            case IDamage_1.DamageType.TRUE:
                // 真实伤害无视防御
                console.log(this.characterName + " takes true damage!");
                break;
            default:
                break;
        }
        // 处理伤害标签
        if (damageInfo.hasTag(IDamage_1.DamageTag.CRITICAL)) {
            console.log("Critical hit on " + this.characterName + "!");
        }
        if (damageInfo.hasTag(IDamage_1.DamageTag.PENETRATING)) {
            console.log("Penetrating damage to " + this.characterName + "!");
        }
    };
    /** * 治疗 */
    BaseCharacter.prototype.heal = function (healAmount) {
        var _a, _b;
        if (this.isDead)
            return;
        this._attributes.modifyHp(healAmount);
        (_b = (_a = this._events).onHeal) === null || _b === void 0 ? void 0 : _b.call(_a, this, healAmount);
        this._eventManager.emit(FightEvent_1.default.heal, { character: this, healAmount: healAmount });
    };
    /** * 死亡处理 */
    BaseCharacter.prototype.die = function () {
        var _a, _b;
        if (this.isDead)
            return;
        this.setState(CharacterTypes_1.CharacterState.DEAD);
        (_b = (_a = this._events).onDeath) === null || _b === void 0 ? void 0 : _b.call(_a, this);
        this._eventManager.emit(FightEvent_1.default.death, { character: this });
        this.onCharacterDeath();
    };
    /** * 设置事件监听器 */
    BaseCharacter.prototype.setEvents = function (events) {
        this._events = __assign(__assign({}, this._events), events);
    };
    /**  * 设置状态  */
    BaseCharacter.prototype.setState = function (newState) {
        if (this._state !== newState) {
            var oldState = this._state;
            this._state = newState;
            this._eventManager.emit(FightEvent_1.default.stateChanged, { character: this, oldState: oldState, newState: newState });
        }
    };
    /**   * 检查是否可以移动   */
    BaseCharacter.prototype.canMove = function () {
        return this._state === CharacterTypes_1.CharacterState.IDLE || this._state === CharacterTypes_1.CharacterState.MOVING;
    };
    /** * 检查资源消耗 */
    BaseCharacter.prototype.checkResourceCost = function (_skill) {
        // 这里应该检查MP、耐力等资源
        return true; // 简化实现
    };
    /*** 消耗资源    */
    BaseCharacter.prototype.consumeResources = function (_skill) {
        // 这里应该消耗MP、耐力等资源
    };
    /** * 从节点获取角色 */
    BaseCharacter.prototype.getCharacterFromNode = function (node) {
        return node === null || node === void 0 ? void 0 : node.getComponent(BaseCharacter);
    };
    /** * 获取世界坐标 */
    BaseCharacter.prototype.getWorldPosition = function (node) {
        return node === null || node === void 0 ? void 0 : node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    };
    /** * 生成唯一ID */
    BaseCharacter.prototype.generateUniqueId = function () {
        return "char_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
    };
    /*** 清理资源*/
    BaseCharacter.prototype.cleanup = function () {
        var _a, _b, _c;
        (_a = this._skillManager) === null || _a === void 0 ? void 0 : _a.cleanup();
        (_b = this._buffManager) === null || _b === void 0 ? void 0 : _b.cleanup();
        (_c = this._eventManager) === null || _c === void 0 ? void 0 : _c.cleanup();
    };
    /**属性变化处理 */
    BaseCharacter.prototype.onAttributeChanged = function (_event) {
    };
    BaseCharacter.prototype.onStateChanged = function (_event) {
        this.updateAnimation();
    };
    BaseCharacter.prototype.onDeath = function (_event) {
        this.playDeathAnimation();
    };
    /*** 更新动画*/
    BaseCharacter.prototype.updateAnimation = function () {
        if (!this._spine || !this._animationConfig)
            return;
        var animationName = "";
        switch (this._state) {
            case CharacterTypes_1.CharacterState.IDLE:
                animationName = this._animationConfig.idle;
                break;
            case CharacterTypes_1.CharacterState.MOVING:
                animationName = this._animationConfig.move;
                break;
            case CharacterTypes_1.CharacterState.ATTACKING:
                animationName = this._animationConfig.attack;
                break;
            case CharacterTypes_1.CharacterState.DEAD:
                animationName = this._animationConfig.death;
                break;
        }
        if (animationName) {
            this._spine.setAnimation(0, animationName, this._state !== CharacterTypes_1.CharacterState.DEAD);
        }
    };
    /** * 播放死亡动画 */
    BaseCharacter.prototype.playDeathAnimation = function () {
        var _a;
        if (this._spine && ((_a = this._animationConfig) === null || _a === void 0 ? void 0 : _a.death)) {
            this._spine.setAnimation(0, this._animationConfig.death, false);
        }
    };
    return BaseCharacter;
}(cc.Component));
exports.BaseCharacter = BaseCharacter;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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