
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/timeline/Timeline.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd0704wfIcNG5p+J854MfOUW', 'Timeline');
// fight/timeline/Timeline.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineEvent = exports.TimelineNode = exports.Timeline = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
/*** Timeline实现类*/
var Timeline = /** @class */ (function () {
    function Timeline(id, name, duration, caster, target, targets, targetPosition) {
        this._timeElapsed = 0;
        this._nodes = [];
        this._isCompleted = false;
        this._isPaused = false;
        this._id = id;
        this._name = name;
        this._duration = duration;
        this._caster = caster;
        this._target = target;
        this._targets = targets;
        this._targetPosition = targetPosition;
        this._eventManager = EventManager_1.EventManager.createLocal("Timeline_" + id);
    }
    Timeline.getTimeLineId = function (id) { return id + "_" + Date.now(); };
    Object.defineProperty(Timeline.prototype, "id", {
        // 实现ITimeline接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "timeElapsed", {
        get: function () { return this._timeElapsed; },
        set: function (value) { this._timeElapsed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "nodes", {
        get: function () { return this._nodes; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "targets", {
        get: function () { return this._targets; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "targetPosition", {
        get: function () { return this._targetPosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "isCompleted", {
        get: function () { return this._isCompleted; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Timeline.prototype, "isPaused", {
        get: function () { return this._isPaused; },
        set: function (value) { this._isPaused = value; },
        enumerable: false,
        configurable: true
    });
    /** * 添加Timeline节点 */
    Timeline.prototype.addNode = function (node) {
        this._nodes.push(node);
        // 按触发时间排序
        this._nodes.sort(function (a, b) { return a.triggerTime - b.triggerTime; });
    };
    /** * 更新Timeline */
    Timeline.prototype.update = function (deltaTime) {
        var e_1, _a;
        if (this._isPaused || this._isCompleted) {
            return this._isCompleted;
        }
        // const previousTime = this._timeElapsed;
        this._timeElapsed += deltaTime;
        try {
            // 检查并触发节点事件
            for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var node = _c.value;
                if (node.shouldTrigger(this._timeElapsed, deltaTime)) {
                    node.trigger(this, this._nodes.indexOf(node));
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 检查是否完成
        if (this._timeElapsed >= this._duration) {
            this._isCompleted = true;
            this._eventManager.emit(FightEvent_1.default.completedT, { timeline: this });
        }
        return this._isCompleted;
    };
    /*** 暂停Timeline*/
    Timeline.prototype.pause = function () {
        this._isPaused = true;
        this._eventManager.emit(FightEvent_1.default.pausedT, { timeline: this });
    };
    /*** 恢复Timeline*/
    Timeline.prototype.resume = function () {
        this._isPaused = false;
        this._eventManager.emit(FightEvent_1.default.resumedT, { timeline: this });
    };
    /*** 停止Timeline*/
    Timeline.prototype.stop = function () {
        this._isCompleted = true;
        this._eventManager.emit(FightEvent_1.default.stoppedT, { timeline: this });
    };
    /** * 重置Timeline */
    Timeline.prototype.reset = function () {
        var e_2, _a;
        this._timeElapsed = 0;
        this._isCompleted = false;
        this._isPaused = false;
        try {
            for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var node = _c.value;
                node.reset();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        this._eventManager.emit(FightEvent_1.default.resetT, { timeline: this });
    };
    /** * 跳转到指定时间点 */
    Timeline.prototype.seekTo = function (time) {
        var e_3, _a;
        var oldTime = this._timeElapsed;
        this._timeElapsed = Math.max(0, Math.min(time, this._duration));
        // 如果是向前跳转，需要触发中间的事件
        if (time > oldTime) {
            try {
                for (var _b = __values(this._nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var node = _c.value;
                    if (node.triggerTime > oldTime && node.triggerTime <= time) {
                        if (!node.isTriggered || node.repeatable) {
                            node.trigger(this, this._nodes.indexOf(node));
                        }
                    }
                }
            }
            catch (e_3_1) { e_3 = { error: e_3_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_3) throw e_3.error; }
            }
        }
        this._eventManager.emit(FightEvent_1.default.seekedT, { timeline: this, oldTime: oldTime, newTime: time });
    };
    Object.defineProperty(Timeline.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理资源 */
    Timeline.prototype.cleanup = function () {
        this._eventManager.cleanup();
        this._nodes.length = 0;
    };
    return Timeline;
}());
exports.Timeline = Timeline;
/** * Timeline节点实现类 */
var TimelineNode = /** @class */ (function () {
    function TimelineNode(id, triggerTime, event, repeatable, repeatInterval, maxRepeats) {
        if (repeatable === void 0) { repeatable = false; }
        this._isTriggered = false;
        this._currentRepeats = 0;
        this._lastTriggerTime = -1;
        this._id = id;
        this._triggerTime = triggerTime;
        this._event = event;
        this._repeatable = repeatable;
        this._repeatInterval = repeatInterval;
        this._maxRepeats = maxRepeats;
    }
    Object.defineProperty(TimelineNode.prototype, "id", {
        // 实现ITimelineNode接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "triggerTime", {
        get: function () { return this._triggerTime; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "event", {
        get: function () { return this._event; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "isTriggered", {
        get: function () { return this._isTriggered; },
        set: function (value) { this._isTriggered = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "repeatable", {
        get: function () { return this._repeatable; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "repeatInterval", {
        get: function () { return this._repeatInterval; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "maxRepeats", {
        get: function () { return this._maxRepeats; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineNode.prototype, "currentRepeats", {
        get: function () { return this._currentRepeats; },
        set: function (value) { this._currentRepeats = value; },
        enumerable: false,
        configurable: true
    });
    /** * 检查是否应该触发 */
    TimelineNode.prototype.shouldTrigger = function (currentTime, _deltaTime) {
        // 首次触发检查
        if (!this._isTriggered && currentTime >= this._triggerTime) {
            return true;
        }
        // 重复触发检查
        if (this._repeatable && this._isTriggered && this._repeatInterval) {
            // 检查是否达到最大重复次数
            if (this._maxRepeats !== undefined && this._currentRepeats >= this._maxRepeats) {
                return false;
            }
            // 检查是否到了下一次触发时间
            var nextTriggerTime = this._lastTriggerTime + this._repeatInterval;
            if (currentTime >= nextTriggerTime) {
                return true;
            }
        }
        return false;
    };
    /** * 触发节点事件 */
    TimelineNode.prototype.trigger = function (timeline, nodeIndex) {
        try {
            this._event.execute(timeline, nodeIndex);
            if (!this._isTriggered) {
                this._isTriggered = true;
            }
            if (this._repeatable) {
                this._currentRepeats++;
                this._lastTriggerTime = timeline.timeElapsed;
            }
        }
        catch (error) {
            console.error("Error executing timeline event " + this._event.id + ":", error);
        }
    };
    /** * 重置节点状态 */
    TimelineNode.prototype.reset = function () {
        this._isTriggered = false;
        this._currentRepeats = 0;
        this._lastTriggerTime = -1;
    };
    return TimelineNode;
}());
exports.TimelineNode = TimelineNode;
/*** Timeline事件基类*/
var TimelineEvent = /** @class */ (function () {
    function TimelineEvent(id, type) {
        this._id = id;
        this._type = type;
    }
    Object.defineProperty(TimelineEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TimelineEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    /*** 播放音效*/
    TimelineEvent.prototype.playSound = function (soundId) {
        // 这里应该调用音效管理器
        // AudioManager.getInstance().playEffect(soundId);
        console.log("Playing sound: " + soundId);
    };
    /** * 播放特效 */
    TimelineEvent.prototype.playEffect = function (effectId, position) {
        // 这里应该调用特效管理器
        // EffectManager.getInstance().playEffect(effectId, position);
        console.log("Playing effect: " + effectId + " at position:", position);
    };
    /** * 获取目标列表 */
    TimelineEvent.prototype.getTargets = function (timeline, _nodeIndex) {
        var targets = [];
        if (timeline.target) {
            targets.push(timeline.target);
        }
        if (timeline.targets) {
            targets.push.apply(targets, __spread(timeline.targets));
        }
        return targets.filter(function (target) { return target && !target.isDead; });
    };
    /** * 获取有效的目标（排除已死亡的） */
    TimelineEvent.prototype.getValidTarget = function (timeline, nodeIndex) {
        var e_4, _a;
        // 如果指定了节点索引，尝试获取对应的目标
        if (nodeIndex !== undefined && timeline.targets && timeline.targets[nodeIndex]) {
            var target = timeline.targets[nodeIndex];
            return target && !target.isDead ? target : null;
        }
        // 否则返回单个目标
        if (timeline.target && !timeline.target.isDead) {
            return timeline.target;
        }
        // 或者返回第一个有效的多目标
        if (timeline.targets) {
            try {
                for (var _b = __values(timeline.targets), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var target = _c.value;
                    if (target && !target.isDead) {
                        return target;
                    }
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_4) throw e_4.error; }
            }
        }
        return null;
    };
    return TimelineEvent;
}());
exports.TimelineEvent = TimelineEvent;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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