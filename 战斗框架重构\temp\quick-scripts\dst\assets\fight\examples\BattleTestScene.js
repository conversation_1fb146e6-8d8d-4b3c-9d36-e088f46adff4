
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/examples/BattleTestScene.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '29a96ZvIcBF6pT7Lr37bHrM', 'BattleTestScene');
// fight/examples/BattleTestScene.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattleTestScene = void 0;
var BuffModelBeHurtFight_1 = require("../buff/BuffModelBeHurtFight");
var Character_1 = require("../characters/Character");
var PlayerSkillFire1_1 = require("../skills/PlayerSkillFire1");
var BattleManager_1 = require("../systems/BattleManager");
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var CharacterTypes_1 = require("../types/CharacterTypes");
var IDamage_1 = require("../types/IDamage");
var SkillName_1 = require("../types/SkillName");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 综合战斗测试场景
 * 整合了基础战斗测试、Timeline测试、AI演示等功能
 */
var BattleTestScene = /** @class */ (function (_super) {
    __extends(BattleTestScene, _super);
    function BattleTestScene() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // UI 组件
        _this.uiRoot = null;
        _this.statusLabel = null;
        // 基础战斗按钮
        _this.startBattleBtn = null;
        _this.endBattleBtn = null;
        _this.castSkillBtn = null;
        _this.addBuffBtn = null;
        // Timeline 测试按钮
        _this.testTimelineBtn = null;
        _this.testDamageTimelineBtn = null;
        _this.testBuffTimelineBtn = null;
        // AI 和多敌人测试按钮
        _this.createEnemiesBtn = null;
        _this.toggleAIBtn = null;
        _this.debugInfoBtn = null;
        // 角色和管理器
        _this.player = null;
        _this.enemy = null;
        _this.enemies = [];
        _this.battleManager = null;
        _this.timelineManager = null;
        // 状态变量
        _this.battleStarted = false;
        _this.aiEnabled = false;
        _this.testMode = "basic"; // basic, timeline, multi
        return _this;
    }
    BattleTestScene.prototype.onLoad = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        // 初始化管理器
        this.battleManager = BattleManager_1.BattleManager.getInstance();
        this.timelineManager = this.battleManager.timelineManager;
        // 设置基础战斗按钮事件
        (_a = this.startBattleBtn) === null || _a === void 0 ? void 0 : _a.node.on('click', this.onStartBattle, this);
        (_b = this.endBattleBtn) === null || _b === void 0 ? void 0 : _b.node.on('click', this.onEndBattle, this);
        (_c = this.castSkillBtn) === null || _c === void 0 ? void 0 : _c.node.on('click', this.onCastSkill, this);
        (_d = this.addBuffBtn) === null || _d === void 0 ? void 0 : _d.node.on('click', this.onAddBuff, this);
        // 设置Timeline测试按钮事件
        (_e = this.testTimelineBtn) === null || _e === void 0 ? void 0 : _e.node.on('click', this.onTestTimeline, this);
        (_f = this.testDamageTimelineBtn) === null || _f === void 0 ? void 0 : _f.node.on('click', this.onTestDamageTimeline, this);
        (_g = this.testBuffTimelineBtn) === null || _g === void 0 ? void 0 : _g.node.on('click', this.onTestBuffTimeline, this);
        // 设置AI和多敌人测试按钮事件
        (_h = this.createEnemiesBtn) === null || _h === void 0 ? void 0 : _h.node.on('click', this.onCreateEnemies, this);
        (_j = this.toggleAIBtn) === null || _j === void 0 ? void 0 : _j.node.on('click', this.onToggleAI, this);
        (_k = this.debugInfoBtn) === null || _k === void 0 ? void 0 : _k.node.on('click', this.onShowDebugInfo, this);
        console.log("综合战斗测试场景已初始化");
    };
    BattleTestScene.prototype.start = function () {
        this.createPlayer();
        this.createEnemy();
        console.log("角色创建完成");
        this.updateStatus();
    };
    BattleTestScene.prototype.onDestroy = function () {
        if (this.battleManager) {
            this.battleManager.cleanup();
        }
    };
    BattleTestScene.prototype.update = function (dt) {
        if (this.battleStarted && this.battleManager) {
            // 使用BattleManager统一更新
            this.battleManager.update(dt);
        }
        this.updateStatus();
    };
    /*** 创建玩家*/
    BattleTestScene.prototype.createPlayer = function () {
        var playerNode = new cc.Node("Player");
        playerNode.parent = this.node;
        playerNode.position = cc.v3(-200, 0, 0);
        this.player = playerNode.addComponent(Character_1.Character);
        var playerData = {
            prefabKey: "player",
            role: CharacterTypes_1.CharacterRole.HERO,
            name: "测试玩家",
            worldPosition: cc.v3(-200, 0, 0),
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.2,
                moveSpeed: 150,
                attackRange: 300,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);
        // 学习技能
        var fireSkill = new PlayerSkillFire1_1.PlayerSkillFire1();
        this.player.learnSkill(fireSkill);
        console.log("玩家创建完成:", this.player.getCharacterInfo());
    };
    /** * 创建敌人 */
    BattleTestScene.prototype.createEnemy = function () {
        var enemyNode = new cc.Node("Enemy");
        enemyNode.parent = this.node;
        enemyNode.position = cc.v3(200, 0, 0);
        this.enemy = enemyNode.addComponent(Character_1.Character);
        var enemyData = {
            prefabKey: "enemy",
            role: CharacterTypes_1.CharacterRole.ENEMY,
            name: "测试敌人",
            worldPosition: cc.v3(200, 0, 0),
            initialAttributes: {
                hp: 500,
                maxHp: 500,
                mp: 100,
                maxMp: 100,
                attack: 80,
                defense: 30,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 150,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.9,
                dodgeRate: 0.1,
                level: 5,
                experience: 0
            }
        };
        this.enemy.setCharacterData(enemyData);
        console.log("敌人创建完成:", this.enemy.getCharacterInfo());
    };
    /** * 开始战斗 */
    BattleTestScene.prototype.onStartBattle = function () {
        if (this.battleStarted) {
            console.log("战斗已经开始");
            return;
        }
        if (!this.player || !this.enemy) {
            console.log("角色未创建完成，无法开始战斗");
            return;
        }
        this.battleStarted = true;
        // 使用BattleManager开始战斗
        var participants = [this.player];
        if (this.testMode === "multi" && this.enemies.length > 0) {
            participants.push.apply(participants, __spread(this.enemies));
            console.log("=== \u591A\u654C\u4EBA\u6218\u6597\u5F00\u59CB (" + this.enemies.length + "\u4E2A\u654C\u4EBA) ===");
        }
        else if (this.enemy) {
            participants.push(this.enemy);
            console.log("=== 单敌人战斗开始 ===");
        }
        this.battleManager.startBattle("test_battle_" + Date.now(), participants);
        // 更新按钮状态
        if (this.castSkillBtn)
            this.castSkillBtn.interactable = true;
        if (this.addBuffBtn)
            this.addBuffBtn.interactable = true;
        if (this.startBattleBtn)
            this.startBattleBtn.interactable = false;
        if (this.endBattleBtn)
            this.endBattleBtn.interactable = true;
    };
    /**  * 释放技能  */
    BattleTestScene.prototype.onCastSkill = function () {
        if (!this.battleStarted || !this.player || !this.enemy) {
            console.log("战斗未开始或角色不存在");
            return;
        }
        if (this.player.isDead || this.enemy.isDead) {
            console.log("有角色已死亡，无法释放技能");
            return;
        }
        var success = this.player.castSkill(SkillName_1.default.player_skill_fire1, this.enemy.node);
        if (success) {
            console.log(this.player.name + " \u5BF9 " + this.enemy.name + " \u91CA\u653E\u4E86\u706B\u7403\u672F\uFF01");
        }
        else {
            console.log("技能释放失败");
        }
    };
    /** * 添加Buff */
    BattleTestScene.prototype.onAddBuff = function () {
        if (!this.battleStarted || !this.player) {
            console.log("战斗未开始或玩家不存在");
            return;
        }
        if (this.player.isDead) {
            console.log("玩家已死亡，无法添加Buff");
            return;
        }
        var buff = new BuffModelBeHurtFight_1.BuffModelBeHurtFight(this.player, this.player);
        this.player.addBuff(buff);
        console.log(this.player.name + " \u83B7\u5F97\u4E86\u53CD\u51FBBuff\uFF01");
    };
    /** * 更新状态显示 */
    BattleTestScene.prototype.updateStatus = function () {
        if (!this.statusLabel)
            return;
        var status = "=== 综合战斗系统测试 ===\n";
        // 玩家信息
        if (this.player) {
            var playerInfo = this.player.getCharacterInfo();
            status += "\uD83D\uDEE1\uFE0F \u73A9\u5BB6: " + playerInfo.name + "\n";
            status += "HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp + "\n";
            status += "MP: " + (this.player.attributes.currentMp || 0) + "/" + (this.player.attributes.maxMp || 0) + "\n";
            status += "\u72B6\u6001: " + (playerInfo.isDead ? "💀死亡" : "✅存活") + "\n";
            status += "\u6280\u80FD\u6570: " + playerInfo.skillCount + " | Buff\u6570: " + playerInfo.buffCount + "\n\n";
        }
        // 敌人信息
        if (this.testMode === "multi" && this.enemies.length > 0) {
            status += "\uD83D\uDC79 \u654C\u4EBA (" + this.enemies.length + "\u4E2A):\n";
            this.enemies.forEach(function (enemy, index) {
                var enemyInfo = enemy.getCharacterInfo();
                status += index + 1 + ". " + enemyInfo.name + ": " + enemy.attributes.currentHp + "/" + enemy.attributes.maxHp + " " + (enemyInfo.isDead ? "💀" : "✅") + "\n";
            });
            status += "\n";
        }
        else if (this.enemy) {
            var enemyInfo = this.enemy.getCharacterInfo();
            status += "\uD83D\uDC79 \u654C\u4EBA: " + enemyInfo.name + "\n";
            status += "HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp + "\n";
            status += "\u72B6\u6001: " + (enemyInfo.isDead ? "💀死亡" : "✅存活") + "\n\n";
        }
        // 测试模式和AI状态
        status += "\uD83C\uDFAE \u6D4B\u8BD5\u6A21\u5F0F: " + this.testMode + "\n";
        status += "\uD83E\uDD16 AI\u72B6\u6001: " + (this.aiEnabled ? "✅启用" : "❌禁用") + "\n\n";
        if (this.battleManager) {
            var battleStats = this.battleManager.getBattleStats();
            status += "\u6218\u6597\u72B6\u6001:\n";
            status += "\u8FDB\u884C\u4E2D: " + (battleStats.isInBattle ? "是" : "否") + "\n";
            status += "\u65F6\u957F: " + battleStats.duration.toFixed(1) + "s\n";
            status += "\u53C2\u6218\u8005: " + battleStats.participantCount + "\n\n";
            status += "Timeline\u7EDF\u8BA1:\n";
            status += "\u6D3B\u8DC3: " + battleStats.timelineStats.activeCount + "\n";
            status += "\u6682\u505C: " + battleStats.timelineStats.pausedCount + "\n";
            status += "\u603B\u6267\u884C: " + battleStats.timelineStats.totalExecutedEvents + "\n\n";
            status += "\u5B50\u5F39\u7EDF\u8BA1:\n";
            status += "\u6D3B\u8DC3: " + battleStats.bulletStats.activeCount + "\n";
            status += "\u603B\u521B\u5EFA: " + battleStats.bulletStats.totalCreated + "\n";
            status += "\u603B\u9500\u6BC1: " + battleStats.bulletStats.totalDestroyed + "\n";
        }
        this.statusLabel.string = status;
    };
    // ==================== 新增的测试方法 ====================
    /** 结束战斗 */
    BattleTestScene.prototype.onEndBattle = function () {
        if (!this.battleStarted) {
            console.log("战斗未开始");
            return;
        }
        this.battleManager.endBattle("manual_stop");
        this.battleStarted = false;
        this.aiEnabled = false;
        // 重置按钮状态
        if (this.startBattleBtn)
            this.startBattleBtn.interactable = true;
        if (this.endBattleBtn)
            this.endBattleBtn.interactable = false;
        if (this.castSkillBtn)
            this.castSkillBtn.interactable = false;
        if (this.addBuffBtn)
            this.addBuffBtn.interactable = false;
        console.log("战斗已结束");
    };
    /** 测试基础Timeline功能 */
    BattleTestScene.prototype.onTestTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试基础Timeline ===");
        // 创建一个简单的Timeline
        var timeline = new Timeline_1.Timeline("test_timeline_" + Date.now(), "基础测试Timeline", 3.0, this.player, this.enemy);
        // 添加音效事件
        var soundEvent = new TimelineEvents_1.PlaySoundTimelineEvent("sound_1", "test_sound");
        var soundNode = new Timeline_1.TimelineNode("sound_node", 0.5, soundEvent);
        timeline.addNode(soundNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("基础Timeline已创建并添加到管理器");
    };
    /** 测试伤害Timeline */
    BattleTestScene.prototype.onTestDamageTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试伤害Timeline ===");
        // 创建伤害Timeline
        var timeline = new Timeline_1.Timeline("damage_timeline_" + Date.now(), "伤害测试Timeline", 2.0, this.player, this.enemy);
        // 添加伤害事件
        var damageEvent = new TimelineEvents_1.DamageTimelineEvent("damage_1", 80, IDamage_1.DamageType.PHYSICAL);
        var damageNode = new Timeline_1.TimelineNode("damage_node", 1.0, damageEvent);
        timeline.addNode(damageNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("伤害Timeline已创建，将在1秒后造成80点物理伤害");
    };
    /** 测试Buff Timeline */
    BattleTestScene.prototype.onTestBuffTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试Buff Timeline ===");
        // 创建Buff Timeline
        var timeline = new Timeline_1.Timeline("buff_timeline_" + Date.now(), "Buff测试Timeline", 1.5, this.player, this.enemy);
        // 添加Buff事件
        var buffEvent = new TimelineEvents_1.AddBuffTimelineEvent("buff_1", "hurt_fight");
        var buffNode = new Timeline_1.TimelineNode("buff_node", 0.8, buffEvent);
        timeline.addNode(buffNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("Buff Timeline已创建，将在0.8秒后给敌人添加反击Buff");
    };
    /** 创建多个敌人 */
    BattleTestScene.prototype.onCreateEnemies = function () {
        var e_1, _a;
        console.log("=== 创建多个敌人 ===");
        try {
            // 清理现有敌人
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (enemy && enemy.node) {
                    enemy.node.destroy();
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        this.enemies = [];
        // 创建3个敌人
        for (var i = 0; i < 3; i++) {
            var enemyNode = new cc.Node("Enemy_" + (i + 1));
            enemyNode.parent = this.node;
            enemyNode.position = cc.v3(200 + i * 150, 0, 0);
            var enemy = enemyNode.addComponent(Character_1.Character);
            var enemyData = {
                prefabKey: "enemy",
                role: CharacterTypes_1.CharacterRole.ENEMY,
                name: "\u654C\u4EBA" + (i + 1),
                worldPosition: cc.v3(200 + i * 150, 0, 0),
                initialAttributes: {
                    hp: 300 + i * 100,
                    maxHp: 300 + i * 100,
                    mp: 50,
                    maxMp: 50,
                    attack: 60 + i * 10,
                    defense: 20 + i * 5,
                    attackSpeed: 1.0,
                    moveSpeed: 80,
                    attackRange: 120,
                    criticalRate: 0.05,
                    criticalDamage: 1.2,
                    hitRate: 0.9,
                    dodgeRate: 0.05,
                    level: 3 + i,
                    experience: 0
                }
            };
            enemy.setCharacterData(enemyData);
            this.setupEnemyEvents(enemy);
            this.enemies.push(enemy);
            console.log("\u521B\u5EFA\u4E86" + enemy.name + "\uFF0CHP: " + enemy.attributes.maxHp);
        }
        this.testMode = "multi";
        console.log("多敌人模式已激活");
    };
    /** 切换AI模式 */
    BattleTestScene.prototype.onToggleAI = function () {
        this.aiEnabled = !this.aiEnabled;
        if (this.aiEnabled) {
            console.log("=== AI模式已启用 ===");
            // 启动AI定时器
            this.schedule(this.updateAI, 1.0);
        }
        else {
            console.log("=== AI模式已禁用 ===");
            // 停止AI定时器
            this.unschedule(this.updateAI);
        }
        // 更新按钮文本（如果需要的话）
        if (this.toggleAIBtn && this.toggleAIBtn.getComponentInChildren(cc.Label)) {
            var label = this.toggleAIBtn.getComponentInChildren(cc.Label);
            label.string = this.aiEnabled ? "关闭AI" : "开启AI";
        }
    };
    /** 显示调试信息 */
    BattleTestScene.prototype.onShowDebugInfo = function () {
        console.log("=== 调试信息 ===");
        if (this.battleManager) {
            console.log("BattleManager状态:");
            this.battleManager.printDebugInfo();
        }
        if (this.timelineManager) {
            console.log("TimelineManager状态:");
            this.timelineManager.printDebugInfo();
        }
        if (this.player) {
            console.log("玩家信息:", this.player.getCharacterInfo());
        }
        if (this.enemy) {
            console.log("敌人信息:", this.enemy.getCharacterInfo());
        }
        if (this.enemies.length > 0) {
            console.log("多敌人信息:");
            this.enemies.forEach(function (enemy, index) {
                console.log("\u654C\u4EBA" + (index + 1) + ":", enemy.getCharacterInfo());
            });
        }
        console.log("当前测试模式:", this.testMode);
        console.log("AI状态:", this.aiEnabled ? "启用" : "禁用");
    };
    /** 设置敌人事件 */
    BattleTestScene.prototype.setupEnemyEvents = function (enemy) {
        var _this = this;
        var events = {
            onDeath: function (character) {
                console.log(character.characterName + " \u88AB\u51FB\u8D25\u4E86\uFF01");
                _this.onEnemyDeath(character);
            },
            onTakeDamage: function (character, damage, attacker) {
                console.log(character.characterName + " \u53D7\u5230\u4E86 " + damage + " \u70B9\u4F24\u5BB3");
                if (attacker) {
                    console.log("\u653B\u51FB\u8005: " + attacker.characterName);
                }
            },
            onSkillCast: function (character, skillName) {
                console.log(character.characterName + " \u91CA\u653E\u4E86\u6280\u80FD: " + skillName);
            }
        };
        enemy.setEvents(events);
    };
    /** 敌人死亡处理 */
    BattleTestScene.prototype.onEnemyDeath = function (enemy) {
        var _a;
        // 从敌人列表中移除
        var index = this.enemies.indexOf(enemy);
        if (index >= 0) {
            this.enemies.splice(index, 1);
            console.log(enemy.name + " \u5DF2\u4ECE\u654C\u4EBA\u5217\u8868\u4E2D\u79FB\u9664");
        }
        // 检查是否所有敌人都被击败
        var aliveEnemies = this.enemies.filter(function (e) { return !e.isDead; });
        if (aliveEnemies.length === 0 && !((_a = this.enemy) === null || _a === void 0 ? void 0 : _a.isDead)) {
            // 只有单个敌人的情况
        }
        else if (aliveEnemies.length === 0) {
            console.log("🎉 胜利！所有敌人都被击败了！");
            this.onEndBattle();
        }
    };
    /** AI更新逻辑 */
    BattleTestScene.prototype.updateAI = function () {
        if (!this.aiEnabled || !this.battleStarted || !this.player || this.player.isDead) {
            return;
        }
        // 玩家AI：攻击最近的敌人
        this.updatePlayerAI();
        // 敌人AI：攻击玩家
        this.updateEnemyAI();
    };
    /** 更新玩家AI */
    BattleTestScene.prototype.updatePlayerAI = function () {
        var target = this.findNearestEnemy();
        if (!target)
            return;
        // 尝试释放技能
        if (this.player.castSkill(SkillName_1.default.player_skill_fire1, target.node)) {
            console.log("[AI] " + this.player.name + " \u5BF9 " + target.name + " \u91CA\u653E\u4E86\u706B\u7403\u672F");
        }
        else if (this.player.isInAttackRange && this.player.isInAttackRange(target)) {
            // 如果技能释放失败，使用普通攻击
            this.player.attack(target);
            console.log("[AI] " + this.player.name + " \u653B\u51FB\u4E86 " + target.name);
        }
    };
    /** 更新敌人AI */
    BattleTestScene.prototype.updateEnemyAI = function () {
        var e_2, _a;
        // 单个敌人AI
        if (this.enemy && !this.enemy.isDead && this.enemy.isInAttackRange(this.player)) {
            this.enemy.attack(this.player);
            console.log("[AI] " + this.enemy.name + " \u653B\u51FB\u4E86 " + this.player.name);
        }
        try {
            // 多敌人AI
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (!enemy.isDead && enemy.isInAttackRange(this.player)) {
                    enemy.attack(this.player);
                    console.log("[AI] " + enemy.name + " \u653B\u51FB\u4E86 " + this.player.name);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
    };
    /** 寻找最近的敌人 */
    BattleTestScene.prototype.findNearestEnemy = function () {
        var e_3, _a;
        var nearestEnemy = null;
        var minDistance = Infinity;
        // 检查单个敌人
        if (this.enemy && !this.enemy.isDead) {
            var distance = cc.Vec3.distance(this.player.node.position, this.enemy.node.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = this.enemy;
            }
        }
        try {
            // 检查多个敌人
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (enemy.isDead)
                    continue;
                var distance = cc.Vec3.distance(this.player.node.position, enemy.node.position);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemy;
                }
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return nearestEnemy;
    };
    __decorate([
        property(cc.Node)
    ], BattleTestScene.prototype, "uiRoot", void 0);
    __decorate([
        property(cc.Label)
    ], BattleTestScene.prototype, "statusLabel", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "startBattleBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "endBattleBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "castSkillBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "addBuffBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testDamageTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testBuffTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "createEnemiesBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "toggleAIBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "debugInfoBtn", void 0);
    BattleTestScene = __decorate([
        ccclass
    ], BattleTestScene);
    return BattleTestScene;
}(cc.Component));
exports.BattleTestScene = BattleTestScene;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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