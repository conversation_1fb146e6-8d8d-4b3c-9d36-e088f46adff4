"use strict";
cc._RF.push(module, '55fa3ixozBPQI7/pHu4XR6K', 'StunBuff');
// fight/buff/StunBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StunBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 眩晕Debuff
 * 使目标无法行动，无法释放技能和移动
 */
var StunBuff = /** @class */ (function () {
    function StunBuff(caster, target, duration) {
        this._name = "眩晕";
        this._description = "无法行动、释放技能或移动";
        this._type = IBuff_1.BuffType.DEBUFF;
        this._stackCount = 1;
        this._maxStack = 1; // 眩晕不可叠加
        this._isExpired = false;
        this._attributeModifiers = [];
        // 眩晕配置
        this._preventMovement = true;
        this._preventSkills = true;
        this._preventBasicAttack = true;
        // 视觉效果
        this._iconPath = "icons/buffs/stun";
        this._effectPrefabPath = "prefabs/effects/StunEffect";
        this._id = "stun_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u65E0\u6CD5\u884C\u52A8\uFF0C\u6301\u7EED" + duration + "\u79D2";
        // 创建属性修改器（降低移动速度到0）
        this.createAttributeModifiers();
    }
    Object.defineProperty(StunBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (_value) { this._stackCount = 1; } // 眩晕不可叠加
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StunBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    StunBuff.prototype.createAttributeModifiers = function () {
        // 移动速度修改器（设为0）
        var moveSpeedModifier = new AttributeModifier_1.AttributeModifier(this._id + "_move_speed", "眩晕移动限制", "moveSpeed", ICharacterAttributes_1.AttributeModifierType.OVERRIDE, 0, this._duration);
        this._attributeModifiers = [moveSpeedModifier];
    };
    /** Buff被添加时触发 */
    StunBuff.prototype.onApply = function () {
        var e_1, _a;
        console.log(this._name + " applied to " + this._target.characterName + " for " + this._duration + " seconds");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放眩晕特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 中断目标当前的行动
        this.interruptCurrentActions();
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterStunned, { target: this._target, caster: this._caster, duration: this._duration });
    };
    /** Buff每帧更新时触发 */
    StunBuff.prototype.onTick = function (deltaTime) {
        var e_2, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 持续检查并阻止行动
        this.preventActions();
    };
    /** Buff被移除时触发 */
    StunBuff.prototype.onRemove = function () {
        var e_3, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            // 移除属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterStunEnded, { target: this._target });
    };
    /** 释放技能时触发 - 阻止技能释放 */
    StunBuff.prototype.onSkillCast = function (skill, timeline) {
        if (this._preventSkills) {
            console.log(this._target.characterName + " cannot cast " + skill.name + " while stunned");
            // 返回null或空timeline来阻止技能释放
            return null;
        }
        return timeline;
    };
    /** 中断当前行动 */
    StunBuff.prototype.interruptCurrentActions = function () {
        // 中断当前的移动
        if (this._target.node) {
            this._target.node.stopAllActions();
        }
        // 中断当前的技能释放（如果有的话）
        // 这里需要与SkillManager配合
        console.log("Interrupting all actions for " + this._target.characterName);
    };
    /** 阻止行动 */
    StunBuff.prototype.preventActions = function () {
        // 这里可以添加持续的行动阻止逻辑
        // 比如重置移动输入、阻止技能队列等
    };
    /** 更新Buff状态 */
    StunBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    StunBuff.prototype.refresh = function () {
        var e_4, _a;
        this._remainingTime = this._duration;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 - 眩晕不可叠加，但可以刷新时间 */
    StunBuff.prototype.addStack = function (_count) {
        if (_count === void 0) { _count = 1; }
        // 眩晕不叠加层数，但刷新持续时间
        this.refresh();
        console.log(this._name + " duration refreshed on " + this._target.characterName);
    };
    /** 减少叠加层数 - 眩晕直接移除 */
    StunBuff.prototype.removeStack = function (_count) {
        if (_count === void 0) { _count = 1; }
        this._stackCount = 0;
        this._isExpired = true;
        console.log(this._name + " removed from " + this._target.characterName);
    };
    /** 获取Buff的当前效果值 */
    StunBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.movement_blocked:
                return this._preventMovement ? 1 : 0;
            case Buff_1.EBuffEffectType.skills_blocked:
                return this._preventSkills ? 1 : 0;
            case Buff_1.EBuffEffectType.basic_attack_blocked:
                return this._preventBasicAttack ? 1 : 0;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    StunBuff.prototype.conflictsWith = function (otherBuff) {
        // 眩晕与其他控制类debuff可能冲突
        if (otherBuff.type === IBuff_1.BuffType.DEBUFF && otherBuff.name.includes("眩晕")) {
            return true;
        }
        return false;
    };
    /** 检查是否可以被净化 */
    StunBuff.prototype.canBeDispelled = function () {
        return true; // 眩晕可以被净化技能移除
    };
    /** 播放应用特效 */
    StunBuff.prototype.playApplyEffect = function () {
        console.log("Playing stun effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的眩晕特效播放逻辑
        // 比如在目标头顶显示眩晕星星特效
    };
    /** 停止特效 */
    StunBuff.prototype.stopEffect = function () {
        console.log("Stopping stun effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    StunBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            preventMovement: this._preventMovement,
            preventSkills: this._preventSkills,
            preventBasicAttack: this._preventBasicAttack,
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return StunBuff;
}());
exports.StunBuff = StunBuff;

cc._RF.pop();