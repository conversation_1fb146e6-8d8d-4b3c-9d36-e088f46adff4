"use strict";
cc._RF.push(module, '6af80gH3+JN3qm3GDBvWswx', 'SkillAndBuffExample');
// fight/examples/SkillAndBuffExample.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillAndBuffExample = void 0;
var BattleManager_1 = require("../systems/BattleManager");
var Character_1 = require("../characters/Character");
var CharacterTypes_1 = require("../types/CharacterTypes");
// 导入技能
var PlayerSkillFire1_1 = require("../skills/PlayerSkillFire1");
var HealingLightSkill_1 = require("../skills/HealingLightSkill");
var ThunderStormSkill_1 = require("../skills/ThunderStormSkill");
var ChargeAttackSkill_1 = require("../skills/ChargeAttackSkill");
// 导入buff
var AttackBoostBuff_1 = require("../buff/AttackBoostBuff");
var HealOverTimeBuff_1 = require("../buff/HealOverTimeBuff");
var StunBuff_1 = require("../buff/StunBuff");
var PoisonBuff_1 = require("../buff/PoisonBuff");
/**
 * 技能和Buff使用示例
 * 展示如何使用新的技能系统和buff系统
 */
var SkillAndBuffExample = /** @class */ (function () {
    function SkillAndBuffExample() {
        this.battleManager = BattleManager_1.BattleManager.getInstance();
        this.initializeCharacters();
        this.setupSkills();
        this.runExample();
    }
    /** 初始化角色 */
    SkillAndBuffExample.prototype.initializeCharacters = function () {
        // 创建玩家角色（战士）
        this.player = new Character_1.Character();
        this.player.setCharacterData({
            prefabKey: "player_warrior",
            name: "勇敢的战士",
            role: CharacterTypes_1.CharacterRole.HERO,
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                maxStamina: 150,
                attack: 120,
                defense: 80,
                attackSpeed: 1.0,
                moveSpeed: 200,
                attackRange: 150,
                criticalRate: 0.15,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.1,
                level: 1,
                experience: 0
            }
        });
        // 创建敌人角色
        this.enemy = new Character_1.Character();
        this.enemy.setCharacterData({
            prefabKey: "enemy_orc",
            name: "兽人战士",
            role: CharacterTypes_1.CharacterRole.ENEMY,
            initialAttributes: {
                hp: 800,
                maxHp: 800,
                mp: 100,
                maxMp: 100,
                maxStamina: 120,
                attack: 100,
                defense: 60,
                attackSpeed: 1.0,
                moveSpeed: 180,
                attackRange: 120,
                criticalRate: 0.1,
                criticalDamage: 1.3,
                hitRate: 0.9,
                dodgeRate: 0.05,
                level: 1,
                experience: 0
            }
        });
        // 创建治疗师角色
        this.healer = new Character_1.Character();
        this.healer.setCharacterData({
            prefabKey: "healer_priest",
            name: "神圣牧师",
            role: CharacterTypes_1.CharacterRole.HERO,
            initialAttributes: {
                hp: 600,
                maxHp: 600,
                mp: 300,
                maxMp: 300,
                maxStamina: 80,
                attack: 50,
                defense: 40,
                attackSpeed: 1.0,
                moveSpeed: 160,
                attackRange: 200,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.98,
                dodgeRate: 0.15,
                level: 1,
                experience: 0
            }
        });
        console.log("Characters initialized:");
        console.log("Player: " + this.player.characterName + " - HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        console.log("Enemy: " + this.enemy.characterName + " - HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        console.log("Healer: " + this.healer.characterName + " - HP: " + this.healer.attributes.currentHp + "/" + this.healer.attributes.maxHp);
    };
    /** 设置技能 */
    SkillAndBuffExample.prototype.setupSkills = function () {
        // 为玩家添加技能
        var fireballSkill = new PlayerSkillFire1_1.PlayerSkillFire1();
        var chargeSkill = new ChargeAttackSkill_1.ChargeAttackSkill();
        this.player.skillManager.addSkill(fireballSkill);
        this.player.skillManager.addSkill(chargeSkill);
        // 为治疗师添加技能
        var healSkill = new HealingLightSkill_1.HealingLightSkill();
        var thunderSkill = new ThunderStormSkill_1.ThunderStormSkill();
        this.healer.skillManager.addSkill(healSkill);
        this.healer.skillManager.addSkill(thunderSkill);
        console.log("Skills added to characters");
    };
    /** 运行示例 */
    SkillAndBuffExample.prototype.runExample = function () {
        console.log("\n=== 技能和Buff系统演示开始 ===\n");
        // 开始战斗
        this.battleManager.startBattle("skill_demo_battle", [this.player, this.enemy, this.healer]);
        // 演示1: 攻击力提升buff
        this.demonstrateAttackBoostBuff();
        // 演示2: 火球术攻击
        this.demonstrateFireballAttack();
        // 演示3: 毒素debuff
        this.demonstratePoisonDebuff();
        // 演示4: 治疗技能和持续治疗buff
        this.demonstrateHealingSkill();
        // 演示5: 眩晕debuff
        this.demonstrateStunDebuff();
        // 演示6: 冲锋攻击技能
        this.demonstrateChargeAttack();
        // 演示7: 雷暴术范围攻击
        this.demonstrateThunderStorm();
        // 模拟战斗更新
        this.simulateBattleUpdates();
        console.log("\n=== 技能和Buff系统演示结束 ===\n");
    };
    /** 演示攻击力提升buff */
    SkillAndBuffExample.prototype.demonstrateAttackBoostBuff = function () {
        console.log("\n--- 演示攻击力提升Buff ---");
        console.log(this.player.name + " \u5F53\u524D\u653B\u51FB\u529B: " + this.player.attributes.attack);
        var attackBuff = new AttackBoostBuff_1.AttackBoostBuff(this.player, this.player, 10.0, 1.5);
        this.player.buffManager.addBuff(attackBuff);
        console.log(this.player.name + " \u83B7\u5F97\u653B\u51FB\u529B\u63D0\u5347buff\u540E\u653B\u51FB\u529B: " + this.player.attributes.attack);
        console.log("Buff\u4FE1\u606F:", attackBuff.getDebugInfo());
    };
    /** 演示火球术攻击 */
    SkillAndBuffExample.prototype.demonstrateFireballAttack = function () {
        console.log("\n--- 演示火球术攻击 ---");
        var fireballSkill = this.player.skillManager.getSkill("player_skill_fire1");
        if (fireballSkill) {
            console.log(this.player.name + " \u91CA\u653E " + fireballSkill.name);
            console.log("\u654C\u4EBA\u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
            var success = fireballSkill.cast(this.player, this.enemy);
            console.log("\u6280\u80FD\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 演示毒素debuff */
    SkillAndBuffExample.prototype.demonstratePoisonDebuff = function () {
        console.log("\n--- 演示毒素Debuff ---");
        console.log(this.enemy.name + " \u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        var poisonBuff = new PoisonBuff_1.PoisonBuff(this.player, this.enemy, 8.0, 25);
        this.enemy.buffManager.addBuff(poisonBuff);
        console.log(this.enemy.name + " \u4E2D\u6BD2\u4E86\uFF01");
        console.log("\u6BD2\u7D20\u4FE1\u606F:", poisonBuff.getDebugInfo());
    };
    /** 演示治疗技能 */
    SkillAndBuffExample.prototype.demonstrateHealingSkill = function () {
        console.log("\n--- 演示治疗技能 ---");
        // 先让玩家受伤
        this.player.attributes.takeDamage(300);
        console.log(this.player.name + " \u53D7\u4F24\u540EHP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        var healSkill = this.healer.skillManager.getSkill("healing_light");
        if (healSkill) {
            console.log(this.healer.name + " \u5BF9 " + this.player.name + " \u91CA\u653E " + healSkill.name);
            var success = healSkill.cast(this.healer, this.player);
            console.log("\u6CBB\u7597\u6280\u80FD\u91CA\u653E" + (success ? '成功' : '失败'));
        }
        // 添加持续治疗buff
        var healOverTimeBuff = new HealOverTimeBuff_1.HealOverTimeBuff(this.healer, this.player, 6.0, 20);
        this.player.buffManager.addBuff(healOverTimeBuff);
        console.log(this.player.name + " \u83B7\u5F97\u6301\u7EED\u6CBB\u7597\u6548\u679C");
    };
    /** 演示眩晕debuff */
    SkillAndBuffExample.prototype.demonstrateStunDebuff = function () {
        console.log("\n--- 演示眩晕Debuff ---");
        console.log(this.enemy.name + " \u5F53\u524D\u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        var stunBuff = new StunBuff_1.StunBuff(this.player, this.enemy, 3.0);
        this.enemy.buffManager.addBuff(stunBuff);
        console.log(this.enemy.name + " \u88AB\u7729\u6655\u4E86\uFF01");
        console.log(this.enemy.name + " \u7729\u6655\u540E\u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        console.log("\u7729\u6655\u4FE1\u606F:", stunBuff.getDebugInfo());
    };
    /** 演示冲锋攻击技能 */
    SkillAndBuffExample.prototype.demonstrateChargeAttack = function () {
        console.log("\n--- 演示冲锋攻击技能 ---");
        var chargeSkill = this.player.skillManager.getSkill("charge_attack");
        if (chargeSkill) {
            console.log(this.player.name + " \u5BF9 " + this.enemy.name + " \u91CA\u653E " + chargeSkill.name);
            console.log("\u654C\u4EBA\u5F53\u524DHP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
            var success = chargeSkill.cast(this.player, this.enemy);
            console.log("\u51B2\u950B\u653B\u51FB\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 演示雷暴术范围攻击 */
    SkillAndBuffExample.prototype.demonstrateThunderStorm = function () {
        console.log("\n--- 演示雷暴术范围攻击 ---");
        var thunderSkill = this.healer.skillManager.getSkill("thunder_storm");
        if (thunderSkill) {
            var targetPosition = cc.v3(100, 100, 0);
            console.log(this.healer.name + " \u5728\u4F4D\u7F6E (" + targetPosition.x + ", " + targetPosition.y + ") \u91CA\u653E " + thunderSkill.name);
            var success = thunderSkill.cast(this.healer, undefined, undefined, targetPosition);
            console.log("\u96F7\u66B4\u672F\u91CA\u653E" + (success ? '成功' : '失败'));
        }
    };
    /** 模拟战斗更新 */
    SkillAndBuffExample.prototype.simulateBattleUpdates = function () {
        var _this = this;
        console.log("\n--- 模拟战斗更新 (5秒) ---");
        var updateInterval = 0.1; // 100ms更新间隔
        var totalTime = 5.0; // 总共5秒
        var currentTime = 0;
        var updateTimer = setInterval(function () {
            currentTime += updateInterval;
            // 更新所有角色的buff
            _this.player.buffManager.update(updateInterval);
            _this.enemy.buffManager.update(updateInterval);
            _this.healer.buffManager.update(updateInterval);
            // 更新技能冷却
            _this.player.skillManager.update(updateInterval);
            _this.enemy.skillManager.update(updateInterval);
            _this.healer.skillManager.update(updateInterval);
            // 更新战斗管理器
            _this.battleManager.update(updateInterval);
            // 每秒输出一次状态
            if (Math.floor(currentTime) !== Math.floor(currentTime - updateInterval)) {
                console.log("\n\u65F6\u95F4: " + Math.floor(currentTime) + "s");
                console.log(_this.player.name + " HP: " + _this.player.attributes.currentHp + "/" + _this.player.attributes.maxHp + ", Buffs: " + _this.player.buffManager.getAllBuffs().length);
                console.log(_this.enemy.name + " HP: " + _this.enemy.attributes.currentHp + "/" + _this.enemy.attributes.maxHp + ", Buffs: " + _this.enemy.buffManager.getAllBuffs().length);
                console.log(_this.healer.name + " HP: " + _this.healer.attributes.currentHp + "/" + _this.healer.attributes.maxHp + ", Buffs: " + _this.healer.buffManager.getAllBuffs().length);
            }
            if (currentTime >= totalTime) {
                clearInterval(updateTimer);
                _this.printFinalStatus();
            }
        }, updateInterval * 1000);
    };
    /** 打印最终状态 */
    SkillAndBuffExample.prototype.printFinalStatus = function () {
        console.log("\n--- 最终状态 ---");
        console.log(this.player.name + ":");
        console.log("  HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp);
        console.log("  \u653B\u51FB\u529B: " + this.player.attributes.attack);
        console.log("  \u6D3B\u8DC3Buffs: " + this.player.buffManager.getAllBuffs().length);
        console.log(this.enemy.name + ":");
        console.log("  HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp);
        console.log("  \u79FB\u52A8\u901F\u5EA6: " + this.enemy.attributes.moveSpeed);
        console.log("  \u6D3B\u8DC3Buffs: " + this.enemy.buffManager.getAllBuffs().length);
        console.log(this.healer.name + ":");
        console.log("  HP: " + this.healer.attributes.currentHp + "/" + this.healer.attributes.maxHp);
        console.log("  MP: " + this.healer.attributes.currentMp + "/" + this.healer.attributes.maxMp);
        console.log("  \u6D3B\u8DC3Buffs: " + this.healer.buffManager.getAllBuffs().length);
        // 打印战斗统计
        console.log("\n战斗统计:", this.battleManager.getBattleStats());
    };
    return SkillAndBuffExample;
}());
exports.SkillAndBuffExample = SkillAndBuffExample;

cc._RF.pop();