[{"__type__": "cc.Prefab", "_name": "tiledmap", "_objFlags": 0, "data": {"__id__": 1}, "optimizationPolicy": 1, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "New TiledMap", "_objFlags": 0, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_cascadeOpacityEnabled": true, "_parent": null, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_children": [], "_skewX": 0, "_skewY": 0, "_localZOrder": 0, "_globalZOrder": 0, "_tag": -1, "_opacityModifyRGB": false, "_reorderChildDirty": false, "_id": "", "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1]}}, {"__type__": "cc.TiledMap", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_tmxFile": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": null, "fileId": "6d9cbIJlHhJ/4JAwDJiN9SB"}]