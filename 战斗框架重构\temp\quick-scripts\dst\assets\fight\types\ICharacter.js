
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/ICharacter.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b4e08ErQ7ZPrrpwvxe2GRez', 'ICharacter');
// fight/types/ICharacter.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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