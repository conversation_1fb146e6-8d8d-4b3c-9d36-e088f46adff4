"use strict";
cc._RF.push(module, '31570J6F7hBKKZhVOpmBzsr', 'HealingLightSkill');
// fight/skills/HealingLightSkill.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealingLightSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var ISkill_1 = require("../types/ISkill");
var SkillName_1 = require("../types/SkillName");
/**
 * 治疗之光技能
 * 单体治疗技能，恢复目标生命值并提供短暂的生命恢复buff
 */
var HealingLightSkill = /** @class */ (function () {
    function HealingLightSkill() {
        this._id = SkillName_1.default.healing_light;
        this._name = "治疗之光";
        this._description = "释放治疗之光，恢复友军生命值并提供持续治疗效果";
        this._cooldown = 3.0;
        this._remainingCooldown = 0;
        this._mpCost = 30;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ALLY;
        this._range = 200;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_heal",
            soundId: "heal_cast",
            effectPath: "prefabs/effects/HealingLight",
            healAmount: 0,
            healMultiplier: 1.5,
            buffDuration: 5.0,
            buffHealPerSecond: 10 // 每秒治疗量
        };
    }
    Object.defineProperty(HealingLightSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealingLightSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    HealingLightSkill.prototype.canCastOn = function (caster, target) {
        if (!target)
            return false;
        if (target.isDead)
            return false;
        if (target.role === caster.role)
            return true; // 同阵营
        return false;
    };
    /** 检查资源消耗 */
    HealingLightSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    HealingLightSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    HealingLightSkill.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " on " + (target === null || target === void 0 ? void 0 : target.characterName));
        return true;
    };
    /** 创建技能Timeline */
    HealingLightSkill.prototype.createTimeline = function (caster, target, targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 2.0, caster);
        // 0.0s: 播放施法动画和音效
        var castNode = new Timeline_1.TimelineNode(timelineId + "_cast", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_animation", this._config.animationName), false);
        timeline.addNode(castNode);
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.1, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        // 0.8s: 执行治疗效果
        var healNode = new Timeline_1.TimelineNode(timelineId + "_heal", 0.8, new HealTimelineEvent("heal_effect", target, caster, this.calculateHealAmount(caster)), false);
        timeline.addNode(healNode);
        // 1.0s: 播放治疗特效
        var effectNode = new Timeline_1.TimelineNode(timelineId + "_effect", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("heal_effect", this._config.effectPath, true, target === null || target === void 0 ? void 0 : target.node.position), false);
        timeline.addNode(effectNode);
        return timeline;
    };
    /** 计算治疗量 */
    HealingLightSkill.prototype.calculateHealAmount = function (caster) {
        if (this._config.healAmount > 0) {
            return this._config.healAmount;
        }
        var baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.healMultiplier);
    };
    /** 更新技能冷却 */
    HealingLightSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    HealingLightSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    HealingLightSkill.prototype.levelUp = function () {
        this._level++;
        this._mpCost = Math.max(15, this._mpCost - 2);
        this._cooldown = Math.max(1.5, this._cooldown - 0.2);
        this._range += 15;
        this._config.healMultiplier += 0.1;
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    HealingLightSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            healMultiplier: this._config.healMultiplier
        };
    };
    return HealingLightSkill;
}());
exports.HealingLightSkill = HealingLightSkill;
/**
 * 治疗Timeline事件
 */
var HealTimelineEvent = /** @class */ (function () {
    function HealTimelineEvent(id, target, caster, healAmount) {
        this._type = ITimeline_1.TimelineEventType.HEAL;
        this._id = id;
        this._target = target;
        this._caster = caster;
        this._healAmount = healAmount;
    }
    Object.defineProperty(HealTimelineEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HealTimelineEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    HealTimelineEvent.prototype.execute = function (timeline, nodeIndex, context) {
        if (this._target && !this._target.isDead) {
            // 执行治疗
            this._target.heal(this._healAmount);
            console.log(this._caster.characterName + " heals " + this._target.characterName + " for " + this._healAmount + " HP");
            // 添加持续治疗buff（这里需要实现HealOverTimeBuff）
            // const healBuff = new HealOverTimeBuff(this._caster, this._target, 5.0, 10);
            // this._target.buffManager.addBuff(healBuff);
        }
    };
    return HealTimelineEvent;
}());
// 需要导入TimelineEventType
var ITimeline_1 = require("../types/ITimeline");
var BattleManager_1 = require("../systems/BattleManager");

cc._RF.pop();