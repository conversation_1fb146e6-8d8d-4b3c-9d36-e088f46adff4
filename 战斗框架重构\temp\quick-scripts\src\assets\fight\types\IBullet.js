"use strict";
cc._RF.push(module, '5614bBH2MRKu5sSaQoS4jIQ', 'IBullet');
// fight/types/IBullet.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrajectoryType = exports.BulletType = void 0;
/** * 子弹类型枚举 */
var BulletType;
(function (BulletType) {
    /** 直线子弹 */
    BulletType["STRAIGHT"] = "straight";
    /** 追踪子弹 */
    BulletType["HOMING"] = "homing";
    /** 抛物线子弹 */
    BulletType["PARABOLIC"] = "parabolic";
    /** 激光 */
    BulletType["LASER"] = "laser";
    /** 范围爆炸 */
    BulletType["AREA_EXPLOSION"] = "area_explosion";
    /** 穿透子弹 */
    BulletType["PIERCING"] = "piercing";
    /** 反弹子弹 */
    BulletType["BOUNCING"] = "bouncing";
    /** 分裂子弹 */
    BulletType["SPLITTING"] = "splitting";
})(BulletType = exports.BulletType || (exports.BulletType = {}));
/*** 轨迹类型枚举*/
var TrajectoryType;
(function (TrajectoryType) {
    /** 直线轨迹 */
    TrajectoryType["LINEAR"] = "linear";
    /** 追踪轨迹 */
    TrajectoryType["HOMING"] = "homing";
    /** 抛物线轨迹 */
    TrajectoryType["PARABOLIC"] = "parabolic";
    /** 贝塞尔曲线轨迹 */
    TrajectoryType["BEZIER"] = "bezier";
    /** 螺旋轨迹 */
    TrajectoryType["SPIRAL"] = "spiral";
    /** 正弦波轨迹 */
    TrajectoryType["SINE_WAVE"] = "sine_wave";
})(TrajectoryType = exports.TrajectoryType || (exports.TrajectoryType = {}));

cc._RF.pop();