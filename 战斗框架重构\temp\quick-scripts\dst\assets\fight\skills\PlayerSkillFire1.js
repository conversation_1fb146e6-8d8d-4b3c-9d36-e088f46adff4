
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/skills/PlayerSkillFire1.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '888b9/7EhpHL4i218sQZyuB', 'PlayerSkillFire1');
// fight/skills/PlayerSkillFire1.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerSkillFire1 = void 0;
var BattleManager_1 = require("../systems/BattleManager");
var BulletSystem_1 = require("../systems/BulletSystem");
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IBullet_1 = require("../types/IBullet");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var SkillName_1 = require("../types/SkillName");
/*** 火球术技能*/
var PlayerSkillFire1 = /** @class */ (function () {
    function PlayerSkillFire1() {
        this._id = SkillName_1.default.player_skill_fire1;
        this._name = "火球术";
        this._description = "发射一个火球，对敌人造成魔法伤害";
        this._cooldown = 2.0;
        this._remainingCooldown = 0;
        this._mpCost = 20;
        this._staminaCost = 0;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ENEMY;
        this._range = 300;
        this._timeline = null;
        this._passiveBuffs = [];
        /**技能配置 */
        this._config = {
            animationName: "skill_fire1",
            soundId: "fire_skill_cast",
            bulletPrefabPath: "prefabs/bullets/FireBall",
            hitEffectPath: "prefabs/effects/FireExplosion",
            hitSoundId: "fire_explosion",
            damage: 0,
            damageType: IDamage_1.DamageType.MAGIC
        };
    }
    Object.defineProperty(PlayerSkillFire1.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PlayerSkillFire1.prototype, "canUse", {
        get: function () { return this._remainingCooldown <= 0; },
        enumerable: false,
        configurable: true
    });
    /** * 检查是否可以对目标使用技能 */
    PlayerSkillFire1.prototype.canCastOn = function (caster, target) {
        if (!this.canUse) {
            return false;
        }
        if (!target || target.isDead) {
            return false;
        }
        // 检查目标是否为敌人
        if (target.role === caster.role) {
            return false;
        }
        var distance = cc.Vec3.distance(caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        return distance <= this._range;
    };
    /** * 释放技能 */
    PlayerSkillFire1.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " casts " + this._name + " on " + (target === null || target === void 0 ? void 0 : target.characterName));
        console.log("Timeline " + this._timeline.id + " added to TimelineManager");
        return true;
    };
    /** * 更新技能状态 */
    PlayerSkillFire1.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** * 重置冷却时间 */
    PlayerSkillFire1.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** * 创建技能Timeline */
    PlayerSkillFire1.prototype.createTimeline = function (caster, target, targets, position) {
        var timeline = new Timeline_1.Timeline(Timeline_1.Timeline.getTimeLineId(this._id), this._name + "_Timeline", 3.0, caster, target, targets, position);
        // 0.0秒：播放施法动画
        var castAnimationNode = new Timeline_1.TimelineNode("cast_animation", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("cast_anim", this._config.animationName, false, true));
        timeline.addNode(castAnimationNode);
        // 0.1秒：播放施法音效
        var castSoundNode = new Timeline_1.TimelineNode("cast_sound", 0.1, new TimelineEvents_1.PlaySoundTimelineEvent("cast_sound", this._config.soundId));
        timeline.addNode(castSoundNode);
        // 0.5秒：发射火球
        var fireBulletNode = new Timeline_1.TimelineNode("fire_bullet", 0.5, new TimelineEvents_1.FireBulletTimelineEvent("fire_bullet", this.createBulletLauncher(caster), this._config.hitEffectPath, "hit", this._config.hitSoundId));
        timeline.addNode(fireBulletNode);
        // 1.0秒：播放命中特效（如果子弹命中）
        var hitEffectNode = new Timeline_1.TimelineNode("hit_effect", 1.0, new TimelineEvents_1.PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, cc.v3(0, 50, 0)));
        timeline.addNode(hitEffectNode);
        return timeline;
    };
    /** * 创建子弹发射器 */
    PlayerSkillFire1.prototype.createBulletLauncher = function (caster) {
        var bulletConfig = {
            id: "fire_ball",
            type: IBullet_1.BulletType.STRAIGHT,
            prefabPath: this._config.bulletPrefabPath,
            speed: 400,
            lifeTime: 5.0,
            maxHits: 1,
            trajectory: { type: IBullet_1.TrajectoryType.LINEAR },
            collision: { radius: 20, piercing: false, layers: ["enemy"], checkFrequency: 60 },
            visual: {
                trail: { enabled: true, length: 100, width: 10, color: cc.Color.ORANGE }
            },
            audio: {
                hitSound: this._config.hitSoundId
            }
        };
        // 创建发射器实例
        return new BulletSystem_1.BulletLauncher("fire_ball_launcher", caster, bulletConfig);
    };
    /** * 检查资源消耗 */
    PlayerSkillFire1.prototype.checkResourceCost = function (caster) {
        // 检查MP
        if (this._mpCost > 0) {
            var attributes = caster.attributes;
            if (attributes.currentMp < this._mpCost) {
                console.log(caster.characterName + " doesn't have enough MP (need " + this._mpCost + ", current: " + attributes.currentMp + ")");
                return false;
            }
        }
        // 检查耐力
        if (this._staminaCost > 0) {
            var attributes = caster.attributes;
            if (attributes.currentStamina < this._staminaCost) {
                console.log(caster.characterName + " doesn't have enough stamina (need " + this._staminaCost + ", current: " + attributes.currentStamina + ")");
                return false;
            }
        }
        return true;
    };
    /** * 消耗资源 */
    PlayerSkillFire1.prototype.consumeResources = function (caster) {
        // 消耗MP
        if (this._mpCost > 0) {
            var attributes = caster.attributes;
            attributes.consumeMp(this._mpCost);
            console.log(caster.characterName + " consumed " + this._mpCost + " MP (remaining: " + attributes.currentMp + ")");
        }
        // 消耗耐力
        if (this._staminaCost > 0) {
            var attributes = caster.attributes;
            attributes.consumeStamina(this._staminaCost);
            console.log(caster.characterName + " consumed " + this._staminaCost + " stamina (remaining: " + attributes.currentStamina + ")");
        }
    };
    /** * 升级技能 */
    PlayerSkillFire1.prototype.levelUp = function () {
        this._level++;
        // 根据等级调整技能属性
        this._mpCost = Math.max(10, this._mpCost - 1); // MP消耗减少
        this._cooldown = Math.max(1.0, this._cooldown - 0.1); // 冷却时间减少
        this._range += 20; // 射程增加
        console.log(this._name + " leveled up to " + this._level);
    };
    /** * 获取技能信息 */
    PlayerSkillFire1.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType
        };
    };
    /** * 技能被添加时的回调（用于多目标技能等特殊情况） */
    PlayerSkillFire1.prototype.onAdd = function (targets) {
        // 火球术是单目标技能，不需要特殊处理
        console.log(this._name + " added with " + targets.length + " targets");
    };
    return PlayerSkillFire1;
}());
exports.PlayerSkillFire1 = PlayerSkillFire1;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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