
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/buff/BuffModelBeHurtFight.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '967330sa5BD5IpPTaRK8JvY', 'BuffModelBeHurtFight');
// fight/buff/BuffModelBeHurtFight.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffModelBeHurtFight = void 0;
var EventManager_1 = require("../systems/EventManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var IDamage_1 = require("../types/IDamage");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/*** 受伤反击Buff实现*/
var BuffModelBeHurtFight = /** @class */ (function () {
    function BuffModelBeHurtFight(caster, target) {
        this._id = "buff_be_hurt_fight";
        this._name = "受伤反击";
        this._description = "受到伤害时有概率反击攻击者";
        this._type = IBuff_1.BuffType.BUFF;
        /**持续时间，以s计算 */
        this._duration = 30.0;
        this._stackCount = 1;
        this._maxStack = 3;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._iconPath = "textures/buffs/be_hurt_fight";
        this._effectPrefabPath = "prefabs/effects/CounterAttackAura";
        /**Buff特有属性  - 默认 30%反击概率 */
        this._counterAttackChance = 0.3; //
        /**反击伤害倍率， 默认 1.5*/
        this._counterAttackDamageMultiplier = 1.5;
        this._caster = caster;
        this._target = target;
        this._remainingTime = this._duration;
        this._eventManager = new EventManager_1.EventManager();
        this.initializeAttributeModifiers();
    }
    Object.defineProperty(BuffModelBeHurtFight.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "periodicEffect", {
        get: function () { return this._periodicEffect; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffModelBeHurtFight.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); },
        enumerable: false,
        configurable: true
    });
    /*** 初始化属性修改器*/
    BuffModelBeHurtFight.prototype.initializeAttributeModifiers = function () {
        // 增加攻击力（每层增加10%）
        var attackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_attack_modifier", "反击攻击力加成", "attack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, 0.1 * this._stackCount, // 每层10%
        this._duration);
        // 增加暴击率（每层增加5%）
        var criticalRateModifier = new AttributeModifier_1.AttributeModifier(this._id + "_critical_rate_modifier", "反击暴击率加成", CharacterTypes_1.CharacterAttributeName.criticalRate, ICharacterAttributes_1.AttributeModifierType.ADD, 0.05 * this._stackCount, // 每层5%
        this._duration);
        this._attributeModifiers = [attackModifier, criticalRateModifier];
    };
    /*** Buff被添加时触发*/
    BuffModelBeHurtFight.prototype.onApply = function () {
        var e_1, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /*** Buff每帧更新时触发*/
    BuffModelBeHurtFight.prototype.onTick = function (deltaTime) {
        var e_2, _a;
        try {
            // 更新属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 这里可以添加其他每帧更新的逻辑
        // 比如粒子特效的更新等
    };
    /** * Buff被移除时触发 */
    BuffModelBeHurtFight.prototype.onRemove = function () {
        var e_3, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** * 受到伤害时触发（核心功能） */
    BuffModelBeHurtFight.prototype.onTakeDamage = function (damageInfo, attacker) {
        // 检查是否触发反击
        if (this.shouldCounterAttack(damageInfo, attacker)) {
            this.performCounterAttack(attacker);
        }
        // 返回原始伤害信息（不修改伤害）
        return damageInfo;
    };
    /** * 更新Buff状态 */
    BuffModelBeHurtFight.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        // 检查是否过期
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** * 刷新Buff持续时间 */
    BuffModelBeHurtFight.prototype.refresh = function () {
        this._remainingTime = this._duration;
        this._isExpired = false;
        console.log(this._name + " refreshed on " + this._target.characterName);
        this._eventManager.emit(FightEvent_1.default.buffRefreshed, { buff: this, target: this._target });
    };
    /** * 增加叠加层数 */
    BuffModelBeHurtFight.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._maxStack, this._stackCount + count);
        if (this._stackCount !== oldStack) {
            // 重新初始化属性修改器以反映新的层数
            this.updateAttributeModifiersForStack();
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target, oldStack: oldStack, newStack: this._stackCount });
        }
    };
    /** * 减少叠加层数 */
    BuffModelBeHurtFight.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(0, this._stackCount - count);
        if (this._stackCount !== oldStack) {
            if (this._stackCount === 0) {
                this._isExpired = true;
            }
            else {
                // 重新初始化属性修改器以反映新的层数
                this.updateAttributeModifiersForStack();
            }
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target, oldStack: oldStack, newStack: this._stackCount });
        }
    };
    /*** 获取Buff的当前效果值*/
    BuffModelBeHurtFight.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.counterAttackChance: return this._counterAttackChance;
            case Buff_1.EBuffEffectType.counterAttackDamageMultiplier: return this._counterAttackDamageMultiplier;
            case Buff_1.EBuffEffectType.attackBonus: return 0.1 * this._stackCount;
            case Buff_1.EBuffEffectType.criticalRateBonus: return 0.05 * this._stackCount;
            default: return 0;
        }
    };
    /*** 检查Buff是否与另一个Buff冲突*/
    BuffModelBeHurtFight.prototype.conflictsWith = function (otherBuff) {
        // 同类型的反击Buff冲突
        return otherBuff.id === this._id || otherBuff.id.includes("counter_attack");
    };
    /** * 检查是否应该反击 */
    BuffModelBeHurtFight.prototype.shouldCounterAttack = function (damageInfo, attacker) {
        // 检查攻击者是否有效
        if (!attacker || attacker.isDead || attacker === this._target) {
            return false;
        }
        // 检查伤害类型（只对直接伤害反击）
        if (damageInfo.damageType === IDamage_1.DamageType.TRUE) {
            return false; // 真实伤害不触发反击
        }
        // 概率检查
        var random = Math.random();
        var chance = this._counterAttackChance * this._stackCount; // 每层增加反击概率
        return random < chance;
    };
    /** * 执行反击 */
    BuffModelBeHurtFight.prototype.performCounterAttack = function (attacker) {
        console.log(this._target.characterName + " counter attacks " + attacker.characterName + "!");
        // 计算反击伤害
        var baseDamage = this._target.attributes.attack;
        var counterDamage = Math.floor(baseDamage * this._counterAttackDamageMultiplier);
        // 造成反击伤害
        attacker.takeDamage(counterDamage, this._target);
        this.playCounterAttackEffect();
        this._eventManager.emit(FightEvent_1.default.counterAttack, { buff: this, attacker: this._target, victim: attacker, damage: counterDamage });
    };
    /** * 更新属性修改器以反映新的层数 */
    BuffModelBeHurtFight.prototype.updateAttributeModifiersForStack = function () {
        var e_4, _a, e_5, _b;
        try {
            for (var _c = __values(this._attributeModifiers), _d = _c.next(); !_d.done; _d = _c.next()) {
                var modifier = _d.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        // 重新初始化修改器
        this._attributeModifiers.length = 0;
        this.initializeAttributeModifiers();
        try {
            for (var _e = __values(this._attributeModifiers), _f = _e.next(); !_f.done; _f = _e.next()) {
                var modifier = _f.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
    };
    /** * 播放应用特效 */
    BuffModelBeHurtFight.prototype.playApplyEffect = function () {
        // 这里应该调用特效管理器
        console.log("Playing apply effect for " + this._name);
    };
    /** * 停止特效 */
    BuffModelBeHurtFight.prototype.stopEffect = function () {
        // 这里应该停止特效
        console.log("Stopping effect for " + this._name);
    };
    /** * 播放反击特效 */
    BuffModelBeHurtFight.prototype.playCounterAttackEffect = function () {
        // 这里应该播放反击特效
        console.log("Playing counter attack effect for " + this._name);
    };
    /**  * 清理资源  */
    BuffModelBeHurtFight.prototype.cleanup = function () {
        this._eventManager.cleanup();
        this._attributeModifiers.length = 0;
    };
    return BuffModelBeHurtFight;
}());
exports.BuffModelBeHurtFight = BuffModelBeHurtFight;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXGJ1ZmZcXEJ1ZmZNb2RlbEJlSHVydEZpZ2h0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsd0RBQXVEO0FBQ3ZELDBEQUFpRTtBQUNqRSxrREFBNkM7QUFDN0Msd0NBQXNFO0FBRXRFLHNFQUEwRjtBQUMxRiw0Q0FBMkQ7QUFDM0QscUVBQW9FO0FBQ3BFLHNDQUFnRDtBQUdoRCxpQkFBaUI7QUFDakI7SUF1QkksOEJBQVksTUFBa0IsRUFBRSxNQUFrQjtRQXRCMUMsUUFBRyxHQUFXLG9CQUFvQixDQUFDO1FBQ25DLFVBQUssR0FBVyxNQUFNLENBQUM7UUFDdkIsaUJBQVksR0FBVyxlQUFlLENBQUM7UUFDdkMsVUFBSyxHQUFhLGdCQUFRLENBQUMsSUFBSSxDQUFDO1FBQ3hDLGVBQWU7UUFDUCxjQUFTLEdBQVcsSUFBSSxDQUFDO1FBRXpCLGdCQUFXLEdBQVcsQ0FBQyxDQUFDO1FBQ3hCLGNBQVMsR0FBVyxDQUFDLENBQUM7UUFHdEIsZUFBVSxHQUFZLEtBQUssQ0FBQztRQUM1Qix3QkFBbUIsR0FBeUIsRUFBRSxDQUFDO1FBRS9DLGNBQVMsR0FBVyw4QkFBOEIsQ0FBQztRQUNuRCxzQkFBaUIsR0FBVyxtQ0FBbUMsQ0FBQztRQUV4RSw0QkFBNEI7UUFDcEIseUJBQW9CLEdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRTtRQUM5QyxtQkFBbUI7UUFDWCxtQ0FBOEIsR0FBVyxHQUFHLENBQUM7UUFHakQsSUFBSSxDQUFDLE9BQU8sR0FBRyxNQUFNLENBQUM7UUFDdEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxNQUFNLENBQUM7UUFDdEIsSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSwyQkFBWSxFQUFFLENBQUM7UUFDeEMsSUFBSSxDQUFDLDRCQUE0QixFQUFFLENBQUM7SUFDeEMsQ0FBQztJQUdELHNCQUFJLG9DQUFFO1FBRE4sWUFBWTthQUNaLGNBQW1CLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7OztPQUFBO0lBQ3JDLHNCQUFJLHNDQUFJO2FBQVIsY0FBcUIsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQzs7O09BQUE7SUFDekMsc0JBQUksNkNBQVc7YUFBZixjQUE0QixPQUFPLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDOzs7T0FBQTtJQUN2RCxzQkFBSSxzQ0FBSTthQUFSLGNBQXVCLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7OztPQUFBO0lBQzNDLHNCQUFJLDBDQUFRO2FBQVosY0FBeUIsT0FBTyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQzs7O09BQUE7SUFDakQsc0JBQUksMENBQVE7YUFBWixjQUF5QixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDOzs7T0FBQTtJQUNqRCxzQkFBSSx3Q0FBTTthQUFWLGNBQTJCLE9BQU8sSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUM7OztPQUFBO0lBQ2pELHNCQUFJLHdDQUFNO2FBQVYsY0FBMkIsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQzs7O09BQUE7SUFDakQsc0JBQUksMkNBQVM7YUFBYixjQUEyQixPQUFPLElBQUksQ0FBQyxVQUFVLElBQUksSUFBSSxDQUFDLGNBQWMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDOzs7T0FBQTtJQUNoRixzQkFBSSxvREFBa0I7YUFBdEIsY0FBOEQsT0FBTyxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDOzs7T0FBQTtJQUNoRyxzQkFBSSxnREFBYzthQUFsQixjQUF3RCxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDOzs7T0FBQTtJQUN0RixzQkFBSSwwQ0FBUTthQUFaLGNBQXFDLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUM7OztPQUFBO0lBQzdELHNCQUFJLGtEQUFnQjthQUFwQixjQUE2QyxPQUFPLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUM7OztPQUFBO0lBQzdFLHNCQUFJLCtDQUFhO2FBQWpCLGNBQThCLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUM7YUFDM0QsVUFBa0IsS0FBYSxJQUFJLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDOzs7T0FEbkI7SUFFM0Qsc0JBQUksNENBQVU7YUFBZCxjQUEyQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDO2FBQ3JELFVBQWUsS0FBYSxJQUFJLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDOzs7T0FEN0M7SUFHckQsZUFBZTtJQUNMLDJEQUE0QixHQUF0QztRQUNJLGlCQUFpQjtRQUNqQixJQUFNLGNBQWMsR0FBRyxJQUFJLHFDQUFpQixDQUNyQyxJQUFJLENBQUMsR0FBRyxxQkFBa0IsRUFDN0IsU0FBUyxFQUNULFFBQVEsRUFDUiw0Q0FBcUIsQ0FBQyxVQUFVLEVBQ2hDLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLFFBQVE7UUFDaEMsSUFBSSxDQUFDLFNBQVMsQ0FDakIsQ0FBQztRQUNGLGdCQUFnQjtRQUNoQixJQUFNLG9CQUFvQixHQUFHLElBQUkscUNBQWlCLENBQzNDLElBQUksQ0FBQyxHQUFHLDRCQUF5QixFQUNwQyxTQUFTLEVBQ1QsdUNBQXNCLENBQUMsWUFBWSxFQUNuQyw0Q0FBcUIsQ0FBQyxHQUFHLEVBQ3pCLElBQUksR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLE9BQU87UUFDaEMsSUFBSSxDQUFDLFNBQVMsQ0FDakIsQ0FBQztRQUNGLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxDQUFDLGNBQWMsRUFBRSxvQkFBb0IsQ0FBQyxDQUFDO0lBQ3RFLENBQUM7SUFFRCxpQkFBaUI7SUFDakIsc0NBQU8sR0FBUDs7UUFDSSxPQUFPLENBQUMsR0FBRyxDQUFJLElBQUksQ0FBQyxLQUFLLG9CQUFlLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBYSxpQkFBWSxJQUFJLENBQUMsV0FBVyxNQUFHLENBQUMsQ0FBQzs7WUFDbkcsYUFBYTtZQUNiLEtBQXVCLElBQUEsS0FBQSxTQUFBLElBQUksQ0FBQyxtQkFBbUIsQ0FBQSxnQkFBQSw0QkFBRTtnQkFBNUMsSUFBTSxRQUFRLFdBQUE7Z0JBQ2YsSUFBSSxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO2FBQ2pEOzs7Ozs7Ozs7UUFDRCxPQUFPO1FBQ1AsSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7WUFDeEIsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1NBQzFCO1FBQ0QsT0FBTztRQUNQLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLG9CQUFVLENBQUMsV0FBVyxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDMUYsQ0FBQztJQUVELGtCQUFrQjtJQUNsQixxQ0FBTSxHQUFOLFVBQU8sU0FBaUI7OztZQUNwQixVQUFVO1lBQ1YsS0FBdUIsSUFBQSxLQUFBLFNBQUEsSUFBSSxDQUFDLG1CQUFtQixDQUFBLGdCQUFBLDRCQUFFO2dCQUE1QyxJQUFNLFFBQVEsV0FBQTtnQkFDZixRQUFRLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDO2FBQzlCOzs7Ozs7Ozs7UUFDRCxrQkFBa0I7UUFDbEIsYUFBYTtJQUNqQixDQUFDO0lBRUQsbUJBQW1CO0lBQ25CLHVDQUFRLEdBQVI7O1FBQ0ksT0FBTyxDQUFDLEdBQUcsQ0FBSSxJQUFJLENBQUMsS0FBSyxzQkFBaUIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxhQUFlLENBQUMsQ0FBQzs7WUFDeEUsS0FBdUIsSUFBQSxLQUFBLFNBQUEsSUFBSSxDQUFDLG1CQUFtQixDQUFBLGdCQUFBLDRCQUFFO2dCQUE1QyxJQUFNLFFBQVEsV0FBQTtnQkFDZixJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxjQUFjLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDO2FBQ3ZEOzs7Ozs7Ozs7UUFDRCxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUM7UUFDbEIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQVUsQ0FBQyxXQUFXLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUMxRixDQUFDO0lBRUQsc0JBQXNCO0lBQ3RCLDJDQUFZLEdBQVosVUFBYSxVQUF1QixFQUFFLFFBQW9CO1FBQ3RELFdBQVc7UUFDWCxJQUFJLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxVQUFVLEVBQUUsUUFBUSxDQUFDLEVBQUU7WUFDaEQsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFFBQVEsQ0FBQyxDQUFDO1NBQ3ZDO1FBQ0Qsa0JBQWtCO1FBQ2xCLE9BQU8sVUFBVSxDQUFDO0lBQ3RCLENBQUM7SUFFRCxpQkFBaUI7SUFDakIscUNBQU0sR0FBTixVQUFPLFNBQWlCO1FBQ3BCLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixPQUFPLElBQUksQ0FBQztTQUNmO1FBQ0QsSUFBSSxDQUFDLGNBQWMsSUFBSSxTQUFTLENBQUM7UUFDakMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUN2QixTQUFTO1FBQ1QsSUFBSSxJQUFJLENBQUMsY0FBYyxJQUFJLENBQUMsRUFBRTtZQUMxQixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQztZQUN2QixPQUFPLElBQUksQ0FBQztTQUNmO1FBQ0QsT0FBTyxLQUFLLENBQUM7SUFDakIsQ0FBQztJQUVELG1CQUFtQjtJQUNuQixzQ0FBTyxHQUFQO1FBQ0ksSUFBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFDO1FBQ3hCLE9BQU8sQ0FBQyxHQUFHLENBQUksSUFBSSxDQUFDLEtBQUssc0JBQWlCLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBZSxDQUFDLENBQUM7UUFDeEUsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQVUsQ0FBQyxhQUFhLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUM1RixDQUFDO0lBRUQsZUFBZTtJQUNmLHVDQUFRLEdBQVIsVUFBUyxLQUFpQjtRQUFqQixzQkFBQSxFQUFBLFNBQWlCO1FBQ3RCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUM7UUFDbEMsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUN0RSxJQUFJLElBQUksQ0FBQyxXQUFXLEtBQUssUUFBUSxFQUFFO1lBQy9CLG9CQUFvQjtZQUNwQixJQUFJLENBQUMsZ0NBQWdDLEVBQUUsQ0FBQztZQUN4QyxPQUFPLENBQUMsR0FBRyxDQUFJLElBQUksQ0FBQyxLQUFLLDRCQUF1QixJQUFJLENBQUMsV0FBVyxZQUFPLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBZSxDQUFDLENBQUM7WUFDckcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQVUsQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsUUFBUSxVQUFBLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDO1NBQ3BJO0lBQ0wsQ0FBQztJQUNELGVBQWU7SUFDZiwwQ0FBVyxHQUFYLFVBQVksS0FBaUI7UUFBakIsc0JBQUEsRUFBQSxTQUFpQjtRQUN6QixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO1FBQ2xDLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUMsQ0FBQztRQUN6RCxJQUFJLElBQUksQ0FBQyxXQUFXLEtBQUssUUFBUSxFQUFFO1lBQy9CLElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxDQUFDLEVBQUU7Z0JBQ3hCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO2FBQzFCO2lCQUFNO2dCQUNILG9CQUFvQjtnQkFDcEIsSUFBSSxDQUFDLGdDQUFnQyxFQUFFLENBQUM7YUFDM0M7WUFDRCxPQUFPLENBQUMsR0FBRyxDQUFJLElBQUksQ0FBQyxLQUFLLDRCQUF1QixJQUFJLENBQUMsV0FBVyxZQUFPLElBQUksQ0FBQyxPQUFPLENBQUMsYUFBZSxDQUFDLENBQUM7WUFDckcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsb0JBQVUsQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsUUFBUSxVQUFBLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDO1NBQ3BJO0lBQ0wsQ0FBQztJQUNELG1CQUFtQjtJQUNuQiw2Q0FBYyxHQUFkLFVBQWUsVUFBa0I7UUFDN0IsUUFBUSxVQUFVLEVBQUU7WUFDaEIsS0FBSyxzQkFBZSxDQUFDLG1CQUFtQixDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsb0JBQW9CLENBQUM7WUFDM0UsS0FBSyxzQkFBZSxDQUFDLDZCQUE2QixDQUFDLENBQUMsT0FBTyxJQUFJLENBQUMsOEJBQThCLENBQUM7WUFDL0YsS0FBSyxzQkFBZSxDQUFDLFdBQVcsQ0FBQyxDQUFDLE9BQU8sR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUM7WUFDaEUsS0FBSyxzQkFBZSxDQUFDLGlCQUFpQixDQUFDLENBQUMsT0FBTyxJQUFJLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztZQUN2RSxPQUFPLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQztTQUNyQjtJQUNMLENBQUM7SUFDRCx5QkFBeUI7SUFDekIsNENBQWEsR0FBYixVQUFjLFNBQWdCO1FBQzFCLGVBQWU7UUFDZixPQUFPLFNBQVMsQ0FBQyxFQUFFLEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxTQUFTLENBQUMsRUFBRSxDQUFDLFFBQVEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQ2hGLENBQUM7SUFFRCxpQkFBaUI7SUFDVCxrREFBbUIsR0FBM0IsVUFBNEIsVUFBdUIsRUFBRSxRQUFvQjtRQUNyRSxZQUFZO1FBQ1osSUFBSSxDQUFDLFFBQVEsSUFBSSxRQUFRLENBQUMsTUFBTSxJQUFJLFFBQVEsS0FBSyxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQzNELE9BQU8sS0FBSyxDQUFDO1NBQ2hCO1FBQ0QsbUJBQW1CO1FBQ25CLElBQUksVUFBVSxDQUFDLFVBQVUsS0FBSyxvQkFBVSxDQUFDLElBQUksRUFBRTtZQUMzQyxPQUFPLEtBQUssQ0FBQyxDQUFDLFlBQVk7U0FDN0I7UUFDRCxPQUFPO1FBQ1AsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQzdCLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsV0FBVztRQUN4RSxPQUFPLE1BQU0sR0FBRyxNQUFNLENBQUM7SUFDM0IsQ0FBQztJQUVELGFBQWE7SUFDTCxtREFBb0IsR0FBNUIsVUFBNkIsUUFBb0I7UUFDN0MsT0FBTyxDQUFDLEdBQUcsQ0FBSSxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEseUJBQW9CLFFBQVEsQ0FBQyxhQUFhLE1BQUcsQ0FBQyxDQUFDO1FBQ3hGLFNBQVM7UUFDVCxJQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUM7UUFDbEQsSUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLDhCQUE4QixDQUFDLENBQUM7UUFDbkYsU0FBUztRQUNULFFBQVEsQ0FBQyxVQUFVLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNqRCxJQUFJLENBQUMsdUJBQXVCLEVBQUUsQ0FBQztRQUMvQixJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxvQkFBVSxDQUFDLGFBQWEsRUFBRSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxNQUFNLEVBQUUsYUFBYSxFQUFFLENBQUMsQ0FBQztJQUN2SSxDQUFDO0lBRUQsdUJBQXVCO0lBQ2YsK0RBQWdDLEdBQXhDOzs7WUFDSSxLQUF1QixJQUFBLEtBQUEsU0FBQSxJQUFJLENBQUMsbUJBQW1CLENBQUEsZ0JBQUEsNEJBQUU7Z0JBQTVDLElBQU0sUUFBUSxXQUFBO2dCQUNmLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7YUFDdkQ7Ozs7Ozs7OztRQUNELFdBQVc7UUFDWCxJQUFJLENBQUMsbUJBQW1CLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQztRQUNwQyxJQUFJLENBQUMsNEJBQTRCLEVBQUUsQ0FBQzs7WUFDcEMsS0FBdUIsSUFBQSxLQUFBLFNBQUEsSUFBSSxDQUFDLG1CQUFtQixDQUFBLGdCQUFBLDRCQUFFO2dCQUE1QyxJQUFNLFFBQVEsV0FBQTtnQkFDZixJQUFJLENBQUMsT0FBTyxDQUFDLFVBQVUsQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUM7YUFDakQ7Ozs7Ozs7OztJQUNMLENBQUM7SUFFRCxlQUFlO0lBQ0wsOENBQWUsR0FBekI7UUFDSSxjQUFjO1FBQ2QsT0FBTyxDQUFDLEdBQUcsQ0FBQyw4QkFBNEIsSUFBSSxDQUFDLEtBQU8sQ0FBQyxDQUFDO0lBQzFELENBQUM7SUFDRCxhQUFhO0lBQ0gseUNBQVUsR0FBcEI7UUFDSSxXQUFXO1FBQ1gsT0FBTyxDQUFDLEdBQUcsQ0FBQyx5QkFBdUIsSUFBSSxDQUFDLEtBQU8sQ0FBQyxDQUFDO0lBQ3JELENBQUM7SUFDRCxlQUFlO0lBQ0wsc0RBQXVCLEdBQWpDO1FBQ0ksYUFBYTtRQUNiLE9BQU8sQ0FBQyxHQUFHLENBQUMsdUNBQXFDLElBQUksQ0FBQyxLQUFPLENBQUMsQ0FBQztJQUNuRSxDQUFDO0lBQ0QsZUFBZTtJQUNmLHNDQUFPLEdBQVA7UUFDSSxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQzdCLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDO0lBQ3hDLENBQUM7SUFDTCwyQkFBQztBQUFELENBcFBBLEFBb1BDLElBQUE7QUFwUFksb0RBQW9CIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRXZlbnRNYW5hZ2VyIH0gZnJvbSBcIi4uL3N5c3RlbXMvRXZlbnRNYW5hZ2VyXCI7XG5pbXBvcnQgeyBDaGFyYWN0ZXJBdHRyaWJ1dGVOYW1lIH0gZnJvbSBcIi4uL3R5cGVzL0NoYXJhY3RlclR5cGVzXCI7XG5pbXBvcnQgRmlnaHRFdmVudCBmcm9tIFwiLi4vdHlwZXMvRmlnaHRFdmVudFwiO1xuaW1wb3J0IHsgSUJ1ZmYsIEJ1ZmZUeXBlLCBJQnVmZlBlcmlvZGljRWZmZWN0IH0gZnJvbSBcIi4uL3R5cGVzL0lCdWZmXCI7XG5pbXBvcnQgeyBJQ2hhcmFjdGVyIH0gZnJvbSBcIi4uL3R5cGVzL0lDaGFyYWN0ZXJcIjtcbmltcG9ydCB7IElBdHRyaWJ1dGVNb2RpZmllciwgQXR0cmlidXRlTW9kaWZpZXJUeXBlIH0gZnJvbSBcIi4uL3R5cGVzL0lDaGFyYWN0ZXJBdHRyaWJ1dGVzXCI7XG5pbXBvcnQgeyBJRGFtYWdlSW5mbywgRGFtYWdlVHlwZSB9IGZyb20gXCIuLi90eXBlcy9JRGFtYWdlXCI7XG5pbXBvcnQgeyBBdHRyaWJ1dGVNb2RpZmllciB9IGZyb20gXCIuLi9jaGFyYWN0ZXJzL0F0dHJpYnV0ZU1vZGlmaWVyXCI7XG5pbXBvcnQgeyBFQnVmZkVmZmVjdFR5cGUgfSBmcm9tIFwiLi4vdHlwZXMvQnVmZlwiO1xuXG5cbi8qKiog5Y+X5Lyk5Y+N5Ye7QnVmZuWunueOsCovXG5leHBvcnQgY2xhc3MgQnVmZk1vZGVsQmVIdXJ0RmlnaHQgaW1wbGVtZW50cyBJQnVmZiB7XG4gICAgcHJpdmF0ZSBfaWQ6IHN0cmluZyA9IFwiYnVmZl9iZV9odXJ0X2ZpZ2h0XCI7XG4gICAgcHJpdmF0ZSBfbmFtZTogc3RyaW5nID0gXCLlj5fkvKTlj43lh7tcIjtcbiAgICBwcml2YXRlIF9kZXNjcmlwdGlvbjogc3RyaW5nID0gXCLlj5fliLDkvKTlrrPml7bmnInmpoLnjoflj43lh7vmlLvlh7vogIVcIjtcbiAgICBwcml2YXRlIF90eXBlOiBCdWZmVHlwZSA9IEJ1ZmZUeXBlLkJVRkY7XG4gICAgLyoq5oyB57ut5pe26Ze077yM5Lulc+iuoeeulyAqL1xuICAgIHByaXZhdGUgX2R1cmF0aW9uOiBudW1iZXIgPSAzMC4wO1xuICAgIHByaXZhdGUgX3JlbWFpbmluZ1RpbWU6IG51bWJlcjtcbiAgICBwcml2YXRlIF9zdGFja0NvdW50OiBudW1iZXIgPSAxO1xuICAgIHByaXZhdGUgX21heFN0YWNrOiBudW1iZXIgPSAzO1xuICAgIHByaXZhdGUgX2Nhc3RlcjogSUNoYXJhY3RlcjtcbiAgICBwcml2YXRlIF90YXJnZXQ6IElDaGFyYWN0ZXI7XG4gICAgcHJpdmF0ZSBfaXNFeHBpcmVkOiBib29sZWFuID0gZmFsc2U7XG4gICAgcHJpdmF0ZSBfYXR0cmlidXRlTW9kaWZpZXJzOiBJQXR0cmlidXRlTW9kaWZpZXJbXSA9IFtdO1xuICAgIHByaXZhdGUgX3BlcmlvZGljRWZmZWN0PzogSUJ1ZmZQZXJpb2RpY0VmZmVjdDtcbiAgICBwcml2YXRlIF9pY29uUGF0aDogc3RyaW5nID0gXCJ0ZXh0dXJlcy9idWZmcy9iZV9odXJ0X2ZpZ2h0XCI7XG4gICAgcHJpdmF0ZSBfZWZmZWN0UHJlZmFiUGF0aDogc3RyaW5nID0gXCJwcmVmYWJzL2VmZmVjdHMvQ291bnRlckF0dGFja0F1cmFcIjtcbiAgICBwcml2YXRlIF9ldmVudE1hbmFnZXI6IEV2ZW50TWFuYWdlcjtcbiAgICAvKipCdWZm54m55pyJ5bGe5oCnICAtIOm7mOiupCAzMCXlj43lh7vmpoLnjocgKi9cbiAgICBwcml2YXRlIF9jb3VudGVyQXR0YWNrQ2hhbmNlOiBudW1iZXIgPSAwLjM7IC8vXG4gICAgLyoq5Y+N5Ye75Lyk5a6z5YCN546H77yMIOm7mOiupCAxLjUqL1xuICAgIHByaXZhdGUgX2NvdW50ZXJBdHRhY2tEYW1hZ2VNdWx0aXBsaWVyOiBudW1iZXIgPSAxLjU7XG5cbiAgICBjb25zdHJ1Y3RvcihjYXN0ZXI6IElDaGFyYWN0ZXIsIHRhcmdldDogSUNoYXJhY3Rlcikge1xuICAgICAgICB0aGlzLl9jYXN0ZXIgPSBjYXN0ZXI7XG4gICAgICAgIHRoaXMuX3RhcmdldCA9IHRhcmdldDtcbiAgICAgICAgdGhpcy5fcmVtYWluaW5nVGltZSA9IHRoaXMuX2R1cmF0aW9uO1xuICAgICAgICB0aGlzLl9ldmVudE1hbmFnZXIgPSBuZXcgRXZlbnRNYW5hZ2VyKCk7XG4gICAgICAgIHRoaXMuaW5pdGlhbGl6ZUF0dHJpYnV0ZU1vZGlmaWVycygpO1xuICAgIH1cblxuICAgIC8vIOWunueOsElCdWZm5o6l5Y+jXG4gICAgZ2V0IGlkKCk6IHN0cmluZyB7IHJldHVybiB0aGlzLl9pZDsgfVxuICAgIGdldCBuYW1lKCk6IHN0cmluZyB7IHJldHVybiB0aGlzLl9uYW1lOyB9XG4gICAgZ2V0IGRlc2NyaXB0aW9uKCk6IHN0cmluZyB7IHJldHVybiB0aGlzLl9kZXNjcmlwdGlvbjsgfVxuICAgIGdldCB0eXBlKCk6IEJ1ZmZUeXBlIHsgcmV0dXJuIHRoaXMuX3R5cGU7IH1cbiAgICBnZXQgZHVyYXRpb24oKTogbnVtYmVyIHsgcmV0dXJuIHRoaXMuX2R1cmF0aW9uOyB9XG4gICAgZ2V0IG1heFN0YWNrKCk6IG51bWJlciB7IHJldHVybiB0aGlzLl9tYXhTdGFjazsgfVxuICAgIGdldCBjYXN0ZXIoKTogSUNoYXJhY3RlciB7IHJldHVybiB0aGlzLl9jYXN0ZXI7IH1cbiAgICBnZXQgdGFyZ2V0KCk6IElDaGFyYWN0ZXIgeyByZXR1cm4gdGhpcy5fdGFyZ2V0OyB9XG4gICAgZ2V0IGlzRXhwaXJlZCgpOiBib29sZWFuIHsgcmV0dXJuIHRoaXMuX2lzRXhwaXJlZCB8fCB0aGlzLl9yZW1haW5pbmdUaW1lIDw9IDA7IH1cbiAgICBnZXQgYXR0cmlidXRlTW9kaWZpZXJzKCk6IFJlYWRvbmx5QXJyYXk8SUF0dHJpYnV0ZU1vZGlmaWVyPiB7IHJldHVybiB0aGlzLl9hdHRyaWJ1dGVNb2RpZmllcnM7IH1cbiAgICBnZXQgcGVyaW9kaWNFZmZlY3QoKTogSUJ1ZmZQZXJpb2RpY0VmZmVjdCB8IHVuZGVmaW5lZCB7IHJldHVybiB0aGlzLl9wZXJpb2RpY0VmZmVjdDsgfVxuICAgIGdldCBpY29uUGF0aCgpOiBzdHJpbmcgfCB1bmRlZmluZWQgeyByZXR1cm4gdGhpcy5faWNvblBhdGg7IH1cbiAgICBnZXQgZWZmZWN0UHJlZmFiUGF0aCgpOiBzdHJpbmcgfCB1bmRlZmluZWQgeyByZXR1cm4gdGhpcy5fZWZmZWN0UHJlZmFiUGF0aDsgfVxuICAgIGdldCByZW1haW5pbmdUaW1lKCk6IG51bWJlciB7IHJldHVybiB0aGlzLl9yZW1haW5pbmdUaW1lOyB9XG4gICAgc2V0IHJlbWFpbmluZ1RpbWUodmFsdWU6IG51bWJlcikgeyB0aGlzLl9yZW1haW5pbmdUaW1lID0gTWF0aC5tYXgoMCwgdmFsdWUpOyB9XG4gICAgZ2V0IHN0YWNrQ291bnQoKTogbnVtYmVyIHsgcmV0dXJuIHRoaXMuX3N0YWNrQ291bnQ7IH1cbiAgICBzZXQgc3RhY2tDb3VudCh2YWx1ZTogbnVtYmVyKSB7IHRoaXMuX3N0YWNrQ291bnQgPSBNYXRoLm1heCgwLCBNYXRoLm1pbih2YWx1ZSwgdGhpcy5fbWF4U3RhY2spKTsgfVxuXG4gICAgLyoqKiDliJ3lp4vljJblsZ7mgKfkv67mlLnlmagqL1xuICAgIHByb3RlY3RlZCBpbml0aWFsaXplQXR0cmlidXRlTW9kaWZpZXJzKCk6IHZvaWQge1xuICAgICAgICAvLyDlop7liqDmlLvlh7vlipvvvIjmr4/lsYLlop7liqAxMCXvvIlcbiAgICAgICAgY29uc3QgYXR0YWNrTW9kaWZpZXIgPSBuZXcgQXR0cmlidXRlTW9kaWZpZXIoXG4gICAgICAgICAgICBgJHt0aGlzLl9pZH1fYXR0YWNrX21vZGlmaWVyYCxcbiAgICAgICAgICAgIFwi5Y+N5Ye75pS75Ye75Yqb5Yqg5oiQXCIsXG4gICAgICAgICAgICBcImF0dGFja1wiLFxuICAgICAgICAgICAgQXR0cmlidXRlTW9kaWZpZXJUeXBlLlBFUkNFTlRBR0UsXG4gICAgICAgICAgICAwLjEgKiB0aGlzLl9zdGFja0NvdW50LCAvLyDmr4/lsYIxMCVcbiAgICAgICAgICAgIHRoaXMuX2R1cmF0aW9uXG4gICAgICAgICk7XG4gICAgICAgIC8vIOWinuWKoOaatOWHu+eOh++8iOavj+WxguWinuWKoDUl77yJXG4gICAgICAgIGNvbnN0IGNyaXRpY2FsUmF0ZU1vZGlmaWVyID0gbmV3IEF0dHJpYnV0ZU1vZGlmaWVyKFxuICAgICAgICAgICAgYCR7dGhpcy5faWR9X2NyaXRpY2FsX3JhdGVfbW9kaWZpZXJgLFxuICAgICAgICAgICAgXCLlj43lh7vmmrTlh7vnjofliqDmiJBcIixcbiAgICAgICAgICAgIENoYXJhY3RlckF0dHJpYnV0ZU5hbWUuY3JpdGljYWxSYXRlLFxuICAgICAgICAgICAgQXR0cmlidXRlTW9kaWZpZXJUeXBlLkFERCxcbiAgICAgICAgICAgIDAuMDUgKiB0aGlzLl9zdGFja0NvdW50LCAvLyDmr4/lsYI1JVxuICAgICAgICAgICAgdGhpcy5fZHVyYXRpb25cbiAgICAgICAgKTtcbiAgICAgICAgdGhpcy5fYXR0cmlidXRlTW9kaWZpZXJzID0gW2F0dGFja01vZGlmaWVyLCBjcml0aWNhbFJhdGVNb2RpZmllcl07XG4gICAgfVxuXG4gICAgLyoqKiBCdWZm6KKr5re75Yqg5pe26Kem5Y+RKi9cbiAgICBvbkFwcGx5KCk6IHZvaWQge1xuICAgICAgICBjb25zb2xlLmxvZyhgJHt0aGlzLl9uYW1lfSBhcHBsaWVkIHRvICR7dGhpcy5fdGFyZ2V0LmNoYXJhY3Rlck5hbWV9IChTdGFjazogJHt0aGlzLl9zdGFja0NvdW50fSlgKTtcbiAgICAgICAgLy8g5bqU55So5bGe5oCn5L+u5pS55Zmo5Yiw55uu5qCHXG4gICAgICAgIGZvciAoY29uc3QgbW9kaWZpZXIgb2YgdGhpcy5fYXR0cmlidXRlTW9kaWZpZXJzKSB7XG4gICAgICAgICAgICB0aGlzLl90YXJnZXQuYXR0cmlidXRlcy5hZGRNb2RpZmllcihtb2RpZmllcik7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5pKt5pS+54m55pWIXG4gICAgICAgIGlmICh0aGlzLl9lZmZlY3RQcmVmYWJQYXRoKSB7XG4gICAgICAgICAgICB0aGlzLnBsYXlBcHBseUVmZmVjdCgpO1xuICAgICAgICB9XG4gICAgICAgIC8vIOinpuWPkeS6i+S7tlxuICAgICAgICB0aGlzLl9ldmVudE1hbmFnZXIuZW1pdChGaWdodEV2ZW50LmJ1ZmZBcHBsaWVkLCB7IGJ1ZmY6IHRoaXMsIHRhcmdldDogdGhpcy5fdGFyZ2V0IH0pO1xuICAgIH1cblxuICAgIC8qKiogQnVmZuavj+W4p+abtOaWsOaXtuinpuWPkSovXG4gICAgb25UaWNrKGRlbHRhVGltZTogbnVtYmVyKTogdm9pZCB7XG4gICAgICAgIC8vIOabtOaWsOWxnuaAp+S/ruaUueWZqFxuICAgICAgICBmb3IgKGNvbnN0IG1vZGlmaWVyIG9mIHRoaXMuX2F0dHJpYnV0ZU1vZGlmaWVycykge1xuICAgICAgICAgICAgbW9kaWZpZXIudXBkYXRlKGRlbHRhVGltZSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5YW25LuW5q+P5bin5pu05paw55qE6YC76L6RXG4gICAgICAgIC8vIOavlOWmgueykuWtkOeJueaViOeahOabtOaWsOetiVxuICAgIH1cblxuICAgIC8qKiAqIEJ1Zmbooqvnp7vpmaTml7bop6blj5EgKi9cbiAgICBvblJlbW92ZSgpOiB2b2lkIHtcbiAgICAgICAgY29uc29sZS5sb2coYCR7dGhpcy5fbmFtZX0gcmVtb3ZlZCBmcm9tICR7dGhpcy5fdGFyZ2V0LmNoYXJhY3Rlck5hbWV9YCk7XG4gICAgICAgIGZvciAoY29uc3QgbW9kaWZpZXIgb2YgdGhpcy5fYXR0cmlidXRlTW9kaWZpZXJzKSB7XG4gICAgICAgICAgICB0aGlzLl90YXJnZXQuYXR0cmlidXRlcy5yZW1vdmVNb2RpZmllcihtb2RpZmllci5pZCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdG9wRWZmZWN0KCk7XG4gICAgICAgIHRoaXMuX2V2ZW50TWFuYWdlci5lbWl0KEZpZ2h0RXZlbnQuYnVmZlJlbW92ZWQsIHsgYnVmZjogdGhpcywgdGFyZ2V0OiB0aGlzLl90YXJnZXQgfSk7XG4gICAgfVxuXG4gICAgLyoqICog5Y+X5Yiw5Lyk5a6z5pe26Kem5Y+R77yI5qC45b+D5Yqf6IO977yJICovXG4gICAgb25UYWtlRGFtYWdlKGRhbWFnZUluZm86IElEYW1hZ2VJbmZvLCBhdHRhY2tlcjogSUNoYXJhY3Rlcik6IElEYW1hZ2VJbmZvIHtcbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm6Kem5Y+R5Y+N5Ye7XG4gICAgICAgIGlmICh0aGlzLnNob3VsZENvdW50ZXJBdHRhY2soZGFtYWdlSW5mbywgYXR0YWNrZXIpKSB7XG4gICAgICAgICAgICB0aGlzLnBlcmZvcm1Db3VudGVyQXR0YWNrKGF0dGFja2VyKTtcbiAgICAgICAgfVxuICAgICAgICAvLyDov5Tlm57ljp/lp4vkvKTlrrPkv6Hmga/vvIjkuI3kv67mlLnkvKTlrrPvvIlcbiAgICAgICAgcmV0dXJuIGRhbWFnZUluZm87XG4gICAgfVxuXG4gICAgLyoqICog5pu05pawQnVmZueKtuaAgSAqL1xuICAgIHVwZGF0ZShkZWx0YVRpbWU6IG51bWJlcik6IGJvb2xlYW4ge1xuICAgICAgICBpZiAodGhpcy5faXNFeHBpcmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9yZW1haW5pbmdUaW1lIC09IGRlbHRhVGltZTtcbiAgICAgICAgdGhpcy5vblRpY2soZGVsdGFUaW1lKTtcbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm6L+H5pyfXG4gICAgICAgIGlmICh0aGlzLl9yZW1haW5pbmdUaW1lIDw9IDApIHtcbiAgICAgICAgICAgIHRoaXMuX2lzRXhwaXJlZCA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLyoqICog5Yi35pawQnVmZuaMgee7reaXtumXtCAqL1xuICAgIHJlZnJlc2goKTogdm9pZCB7XG4gICAgICAgIHRoaXMuX3JlbWFpbmluZ1RpbWUgPSB0aGlzLl9kdXJhdGlvbjtcbiAgICAgICAgdGhpcy5faXNFeHBpcmVkID0gZmFsc2U7XG4gICAgICAgIGNvbnNvbGUubG9nKGAke3RoaXMuX25hbWV9IHJlZnJlc2hlZCBvbiAke3RoaXMuX3RhcmdldC5jaGFyYWN0ZXJOYW1lfWApO1xuICAgICAgICB0aGlzLl9ldmVudE1hbmFnZXIuZW1pdChGaWdodEV2ZW50LmJ1ZmZSZWZyZXNoZWQsIHsgYnVmZjogdGhpcywgdGFyZ2V0OiB0aGlzLl90YXJnZXQgfSk7XG4gICAgfVxuXG4gICAgLyoqICog5aKe5Yqg5Y+g5Yqg5bGC5pWwICovXG4gICAgYWRkU3RhY2soY291bnQ6IG51bWJlciA9IDEpOiB2b2lkIHtcbiAgICAgICAgY29uc3Qgb2xkU3RhY2sgPSB0aGlzLl9zdGFja0NvdW50O1xuICAgICAgICB0aGlzLl9zdGFja0NvdW50ID0gTWF0aC5taW4odGhpcy5fbWF4U3RhY2ssIHRoaXMuX3N0YWNrQ291bnQgKyBjb3VudCk7XG4gICAgICAgIGlmICh0aGlzLl9zdGFja0NvdW50ICE9PSBvbGRTdGFjaykge1xuICAgICAgICAgICAgLy8g6YeN5paw5Yid5aeL5YyW5bGe5oCn5L+u5pS55Zmo5Lul5Y+N5pig5paw55qE5bGC5pWwXG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUF0dHJpYnV0ZU1vZGlmaWVyc0ZvclN0YWNrKCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgJHt0aGlzLl9uYW1lfSBzdGFjayBpbmNyZWFzZWQgdG8gJHt0aGlzLl9zdGFja0NvdW50fSBvbiAke3RoaXMuX3RhcmdldC5jaGFyYWN0ZXJOYW1lfWApO1xuICAgICAgICAgICAgdGhpcy5fZXZlbnRNYW5hZ2VyLmVtaXQoRmlnaHRFdmVudC5idWZmU3RhY2tDaGFuZ2VkLCB7IGJ1ZmY6IHRoaXMsIHRhcmdldDogdGhpcy5fdGFyZ2V0LCBvbGRTdGFjaywgbmV3U3RhY2s6IHRoaXMuX3N0YWNrQ291bnQgfSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqICog5YeP5bCR5Y+g5Yqg5bGC5pWwICovXG4gICAgcmVtb3ZlU3RhY2soY291bnQ6IG51bWJlciA9IDEpOiB2b2lkIHtcbiAgICAgICAgY29uc3Qgb2xkU3RhY2sgPSB0aGlzLl9zdGFja0NvdW50O1xuICAgICAgICB0aGlzLl9zdGFja0NvdW50ID0gTWF0aC5tYXgoMCwgdGhpcy5fc3RhY2tDb3VudCAtIGNvdW50KTtcbiAgICAgICAgaWYgKHRoaXMuX3N0YWNrQ291bnQgIT09IG9sZFN0YWNrKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5fc3RhY2tDb3VudCA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHRoaXMuX2lzRXhwaXJlZCA9IHRydWU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMluWxnuaAp+S/ruaUueWZqOS7peWPjeaYoOaWsOeahOWxguaVsFxuICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlQXR0cmlidXRlTW9kaWZpZXJzRm9yU3RhY2soKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGAke3RoaXMuX25hbWV9IHN0YWNrIGRlY3JlYXNlZCB0byAke3RoaXMuX3N0YWNrQ291bnR9IG9uICR7dGhpcy5fdGFyZ2V0LmNoYXJhY3Rlck5hbWV9YCk7XG4gICAgICAgICAgICB0aGlzLl9ldmVudE1hbmFnZXIuZW1pdChGaWdodEV2ZW50LmJ1ZmZTdGFja0NoYW5nZWQsIHsgYnVmZjogdGhpcywgdGFyZ2V0OiB0aGlzLl90YXJnZXQsIG9sZFN0YWNrLCBuZXdTdGFjazogdGhpcy5fc3RhY2tDb3VudCB9KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKioqIOiOt+WPlkJ1ZmbnmoTlvZPliY3mlYjmnpzlgLwqL1xuICAgIGdldEVmZmVjdFZhbHVlKGVmZmVjdFR5cGU6IHN0cmluZyk6IG51bWJlciB7XG4gICAgICAgIHN3aXRjaCAoZWZmZWN0VHlwZSkge1xuICAgICAgICAgICAgY2FzZSBFQnVmZkVmZmVjdFR5cGUuY291bnRlckF0dGFja0NoYW5jZTogcmV0dXJuIHRoaXMuX2NvdW50ZXJBdHRhY2tDaGFuY2U7XG4gICAgICAgICAgICBjYXNlIEVCdWZmRWZmZWN0VHlwZS5jb3VudGVyQXR0YWNrRGFtYWdlTXVsdGlwbGllcjogcmV0dXJuIHRoaXMuX2NvdW50ZXJBdHRhY2tEYW1hZ2VNdWx0aXBsaWVyO1xuICAgICAgICAgICAgY2FzZSBFQnVmZkVmZmVjdFR5cGUuYXR0YWNrQm9udXM6IHJldHVybiAwLjEgKiB0aGlzLl9zdGFja0NvdW50O1xuICAgICAgICAgICAgY2FzZSBFQnVmZkVmZmVjdFR5cGUuY3JpdGljYWxSYXRlQm9udXM6IHJldHVybiAwLjA1ICogdGhpcy5fc3RhY2tDb3VudDtcbiAgICAgICAgICAgIGRlZmF1bHQ6IHJldHVybiAwO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKiog5qOA5p+lQnVmZuaYr+WQpuS4juWPpuS4gOS4qkJ1ZmblhrLnqoEqL1xuICAgIGNvbmZsaWN0c1dpdGgob3RoZXJCdWZmOiBJQnVmZik6IGJvb2xlYW4ge1xuICAgICAgICAvLyDlkIznsbvlnovnmoTlj43lh7tCdWZm5Yay56qBXG4gICAgICAgIHJldHVybiBvdGhlckJ1ZmYuaWQgPT09IHRoaXMuX2lkIHx8IG90aGVyQnVmZi5pZC5pbmNsdWRlcyhcImNvdW50ZXJfYXR0YWNrXCIpO1xuICAgIH1cblxuICAgIC8qKiAqIOajgOafpeaYr+WQpuW6lOivpeWPjeWHuyAqL1xuICAgIHByaXZhdGUgc2hvdWxkQ291bnRlckF0dGFjayhkYW1hZ2VJbmZvOiBJRGFtYWdlSW5mbywgYXR0YWNrZXI6IElDaGFyYWN0ZXIpOiBib29sZWFuIHtcbiAgICAgICAgLy8g5qOA5p+l5pS75Ye76ICF5piv5ZCm5pyJ5pWIXG4gICAgICAgIGlmICghYXR0YWNrZXIgfHwgYXR0YWNrZXIuaXNEZWFkIHx8IGF0dGFja2VyID09PSB0aGlzLl90YXJnZXQpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICAvLyDmo4Dmn6XkvKTlrrPnsbvlnovvvIjlj6rlr7nnm7TmjqXkvKTlrrPlj43lh7vvvIlcbiAgICAgICAgaWYgKGRhbWFnZUluZm8uZGFtYWdlVHlwZSA9PT0gRGFtYWdlVHlwZS5UUlVFKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7IC8vIOecn+WunuS8pOWus+S4jeinpuWPkeWPjeWHu1xuICAgICAgICB9XG4gICAgICAgIC8vIOamgueOh+ajgOafpVxuICAgICAgICBjb25zdCByYW5kb20gPSBNYXRoLnJhbmRvbSgpO1xuICAgICAgICBjb25zdCBjaGFuY2UgPSB0aGlzLl9jb3VudGVyQXR0YWNrQ2hhbmNlICogdGhpcy5fc3RhY2tDb3VudDsgLy8g5q+P5bGC5aKe5Yqg5Y+N5Ye75qaC546HXG4gICAgICAgIHJldHVybiByYW5kb20gPCBjaGFuY2U7XG4gICAgfVxuXG4gICAgLyoqICog5omn6KGM5Y+N5Ye7ICovXG4gICAgcHJpdmF0ZSBwZXJmb3JtQ291bnRlckF0dGFjayhhdHRhY2tlcjogSUNoYXJhY3Rlcik6IHZvaWQge1xuICAgICAgICBjb25zb2xlLmxvZyhgJHt0aGlzLl90YXJnZXQuY2hhcmFjdGVyTmFtZX0gY291bnRlciBhdHRhY2tzICR7YXR0YWNrZXIuY2hhcmFjdGVyTmFtZX0hYCk7XG4gICAgICAgIC8vIOiuoeeul+WPjeWHu+S8pOWus1xuICAgICAgICBjb25zdCBiYXNlRGFtYWdlID0gdGhpcy5fdGFyZ2V0LmF0dHJpYnV0ZXMuYXR0YWNrO1xuICAgICAgICBjb25zdCBjb3VudGVyRGFtYWdlID0gTWF0aC5mbG9vcihiYXNlRGFtYWdlICogdGhpcy5fY291bnRlckF0dGFja0RhbWFnZU11bHRpcGxpZXIpO1xuICAgICAgICAvLyDpgKDmiJDlj43lh7vkvKTlrrNcbiAgICAgICAgYXR0YWNrZXIudGFrZURhbWFnZShjb3VudGVyRGFtYWdlLCB0aGlzLl90YXJnZXQpO1xuICAgICAgICB0aGlzLnBsYXlDb3VudGVyQXR0YWNrRWZmZWN0KCk7XG4gICAgICAgIHRoaXMuX2V2ZW50TWFuYWdlci5lbWl0KEZpZ2h0RXZlbnQuY291bnRlckF0dGFjaywgeyBidWZmOiB0aGlzLCBhdHRhY2tlcjogdGhpcy5fdGFyZ2V0LCB2aWN0aW06IGF0dGFja2VyLCBkYW1hZ2U6IGNvdW50ZXJEYW1hZ2UgfSk7XG4gICAgfVxuXG4gICAgLyoqICog5pu05paw5bGe5oCn5L+u5pS55Zmo5Lul5Y+N5pig5paw55qE5bGC5pWwICovXG4gICAgcHJpdmF0ZSB1cGRhdGVBdHRyaWJ1dGVNb2RpZmllcnNGb3JTdGFjaygpOiB2b2lkIHtcbiAgICAgICAgZm9yIChjb25zdCBtb2RpZmllciBvZiB0aGlzLl9hdHRyaWJ1dGVNb2RpZmllcnMpIHtcbiAgICAgICAgICAgIHRoaXMuX3RhcmdldC5hdHRyaWJ1dGVzLnJlbW92ZU1vZGlmaWVyKG1vZGlmaWVyLmlkKTtcbiAgICAgICAgfVxuICAgICAgICAvLyDph43mlrDliJ3lp4vljJbkv67mlLnlmahcbiAgICAgICAgdGhpcy5fYXR0cmlidXRlTW9kaWZpZXJzLmxlbmd0aCA9IDA7XG4gICAgICAgIHRoaXMuaW5pdGlhbGl6ZUF0dHJpYnV0ZU1vZGlmaWVycygpO1xuICAgICAgICBmb3IgKGNvbnN0IG1vZGlmaWVyIG9mIHRoaXMuX2F0dHJpYnV0ZU1vZGlmaWVycykge1xuICAgICAgICAgICAgdGhpcy5fdGFyZ2V0LmF0dHJpYnV0ZXMuYWRkTW9kaWZpZXIobW9kaWZpZXIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqICog5pKt5pS+5bqU55So54m55pWIICovXG4gICAgcHJvdGVjdGVkIHBsYXlBcHBseUVmZmVjdCgpOiB2b2lkIHtcbiAgICAgICAgLy8g6L+Z6YeM5bqU6K+l6LCD55So54m55pWI566h55CG5ZmoXG4gICAgICAgIGNvbnNvbGUubG9nKGBQbGF5aW5nIGFwcGx5IGVmZmVjdCBmb3IgJHt0aGlzLl9uYW1lfWApO1xuICAgIH1cbiAgICAvKiogKiDlgZzmraLnibnmlYggKi9cbiAgICBwcm90ZWN0ZWQgc3RvcEVmZmVjdCgpOiB2b2lkIHtcbiAgICAgICAgLy8g6L+Z6YeM5bqU6K+l5YGc5q2i54m55pWIXG4gICAgICAgIGNvbnNvbGUubG9nKGBTdG9wcGluZyBlZmZlY3QgZm9yICR7dGhpcy5fbmFtZX1gKTtcbiAgICB9XG4gICAgLyoqICog5pKt5pS+5Y+N5Ye754m55pWIICovXG4gICAgcHJvdGVjdGVkIHBsYXlDb3VudGVyQXR0YWNrRWZmZWN0KCk6IHZvaWQge1xuICAgICAgICAvLyDov5nph4zlupTor6Xmkq3mlL7lj43lh7vnibnmlYhcbiAgICAgICAgY29uc29sZS5sb2coYFBsYXlpbmcgY291bnRlciBhdHRhY2sgZWZmZWN0IGZvciAke3RoaXMuX25hbWV9YCk7XG4gICAgfVxuICAgIC8qKiAgKiDmuIXnkIbotYTmupAgICovXG4gICAgY2xlYW51cCgpOiB2b2lkIHtcbiAgICAgICAgdGhpcy5fZXZlbnRNYW5hZ2VyLmNsZWFudXAoKTtcbiAgICAgICAgdGhpcy5fYXR0cmlidXRlTW9kaWZpZXJzLmxlbmd0aCA9IDA7XG4gICAgfVxufVxuIl19