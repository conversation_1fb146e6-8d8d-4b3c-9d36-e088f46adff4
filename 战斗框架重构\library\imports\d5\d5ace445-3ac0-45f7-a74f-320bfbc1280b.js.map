{"version": 3, "sources": ["assets\\fight\\types\\IDamage.ts"], "names": [], "mappings": ";;;;;;;AAsDA,aAAa;AACb,IAAY,UAWX;AAXD,WAAY,UAAU;IAClB,WAAW;IACX,mCAAqB,CAAA;IACrB,WAAW;IACX,6BAAe,CAAA;IACf,WAAW;IACX,2BAAa,CAAA;IACb,SAAS;IACT,2BAAa,CAAA;IACb,WAAW;IACX,+BAAiB,CAAA;AACrB,CAAC,EAXW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAWrB;AACD,eAAe;AACf,IAAY,SAiBX;AAjBD,WAAY,SAAS;IACjB,WAAW;IACX,8BAAiB,CAAA;IACjB,WAAW;IACX,oCAAuB,CAAA;IACvB,WAAW;IACX,gCAAmB,CAAA;IACnB,WAAW;IACX,8BAAiB,CAAA;IACjB,WAAW;IACX,wCAA2B,CAAA;IAC3B,WAAW;IACX,kCAAqB,CAAA;IACrB,WAAW;IACX,4BAAe,CAAA;IACf,WAAW;IACX,0CAA6B,CAAA;AACjC,CAAC,EAjBW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAiBpB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ICharacter } from \"./ICharacter\";\nimport { IBuff } from \"./IBuff\";\n/*** 伤害信息接口*/\nexport interface IDamageInfo {\n    /** 伤害ID */\n    readonly id: string;\n    /** 攻击者 */\n    readonly attacker: ICharacter | null;\n    /** 受害者 */\n    readonly target: ICharacter;\n    /** 基础伤害 */\n    baseDamage: number;\n    /** 最终伤害 */\n    readonly finalDamage: number;\n    /** 伤害类型 */\n    readonly damageType: DamageType;\n    /** 伤害标签 */\n    readonly tags: DamageTag[];\n    /** 是否暴击 */\n    isCritical: boolean;\n    /** 暴击倍率 */\n    criticalMultiplier: number;\n    /** 伤害减免 */\n    damageReduction: number;\n    /** 伤害增幅 */\n    damageAmplification: number;\n    /** 附加的Buff */\n    attachedBuffs: IBuff[];\n    /** 伤害来源（技能、普攻等） */\n    source?: string;\n    /** 是否闪避 */\n    isDodged: boolean;\n    /**\n     * 计算最终伤害\n     * @returns 最终伤害值\n     */\n    calculateFinalDamage(): number;\n    /**\n     * 添加伤害标签\n     * @param tag 伤害标签\n     */\n    addTag(tag: DamageTag): void;\n    /**\n     * 检查是否包含标签\n     * @param tag 伤害标签\n     * @returns 是否包含\n     */\n    hasTag(tag: DamageTag): boolean;\n    /**\n     * 添加附加Buff\n     * @param buff Buff\n     */\n    addAttachedBuff(buff: IBuff): void;\n}\n/*** 伤害类型枚举*/\nexport enum DamageType {\n    /** 物理伤害 */\n    PHYSICAL = \"physical\",\n    /** 魔法伤害 */\n    MAGIC = \"magic\",\n    /** 真实伤害 */\n    TRUE = \"true\",\n    /** 治疗 */\n    HEAL = \"heal\",\n    /** 毒素伤害 */\n    POISON = \"poison\"\n}\n/** * 伤害标签枚举 */\nexport enum DamageTag {\n    /** 直接伤害 */\n    DIRECT = \"direct\",\n    /** 持续伤害 */\n    OVER_TIME = \"over_time\",\n    /** 反射伤害 */\n    REFLECT = \"reflect\",\n    /** 溅射伤害 */\n    SPLASH = \"splash\",\n    /** 穿透伤害 */\n    PENETRATING = \"penetrating\",\n    /** 暴击伤害 */\n    CRITICAL = \"critical\",\n    /** 技能伤害 */\n    SKILL = \"skill\",\n    /** 普攻伤害 */\n    BASIC_ATTACK = \"basic_attack\"\n}\n\n/** * 伤害计算器接口 */\nexport interface IDamageCalculator {\n    /**\n     * 计算伤害\n     * @param attacker 攻击者\n     * @param target 目标\n     * @param baseDamage 基础伤害\n     * @param damageType 伤害类型\n     * @param tags 伤害标签\n     * @returns 伤害信息\n     */\n    calculateDamage(attacker: ICharacter | null, target: ICharacter, baseDamage: number, damageType: DamageType, tags?: DamageTag[]): IDamageInfo;\n    /**\n     * 计算暴击\n     * @param attacker 攻击者\n     * @param target 目标\n     * @returns 是否暴击和暴击倍率\n     */\n    calculateCritical(attacker: ICharacter, target: ICharacter): { isCritical: boolean; multiplier: number };\n    /**\n     * 计算伤害减免\n     * @param target 目标\n     * @param damageType 伤害类型\n     * @param baseDamage 基础伤害\n     * @returns 伤害减免值\n     */\n    calculateDamageReduction(target: ICharacter, damageType: DamageType, baseDamage: number): number;\n}\n\n/** * 伤害处理器接口 */\nexport interface IDamageProcessor {\n    /**\n     * 处理伤害\n     * @param damageInfo 伤害信息\n     */\n    processDamage(damageInfo: IDamageInfo): void;\n    /**\n     * 应用伤害前的处理\n     * @param damageInfo 伤害信息\n     * @returns 修改后的伤害信息\n     */\n    beforeDamage(damageInfo: IDamageInfo): IDamageInfo;\n    /**\n     * 应用伤害后的处理\n     * @param damageInfo 伤害信息\n     */\n    afterDamage(damageInfo: IDamageInfo): void;\n}\n\n/*** 伤害事件接口*/\nexport interface IDamageEvents {\n    /** 伤害计算前事件 */\n    onBeforeDamageCalculation?: (damageInfo: IDamageInfo) => IDamageInfo;\n    /** 伤害应用前事件 */\n    onBeforeDamageApplication?: (damageInfo: IDamageInfo) => IDamageInfo;\n    /** 伤害应用后事件 */\n    onAfterDamageApplication?: (damageInfo: IDamageInfo) => void;\n    /** 暴击事件 */\n    onCriticalHit?: (damageInfo: IDamageInfo) => void;\n    /** 死亡事件 */\n    onDeath?: (damageInfo: IDamageInfo) => void;\n}\n\n/*** 伤害管理器接口*/\nexport interface IDamageManager {\n    /** 伤害计算器 */\n    readonly calculator: IDamageCalculator;\n    /** 伤害处理器 */\n    readonly processor: IDamageProcessor;\n    /**\n     * 造成伤害\n     * @param attacker 攻击者\n     * @param target 目标\n     * @param baseDamage 基础伤害\n     * @param damageType 伤害类型\n     * @param tags 伤害标签\n     * @param source 伤害来源\n     */\n    dealDamage(attacker: ICharacter | null, target: ICharacter, baseDamage: number, damageType: DamageType, tags?: DamageTag[], source?: string): void;\n    /**\n     * 治疗\n     * @param healer 治疗者\n     * @param target 目标\n     * @param healAmount 治疗量\n     * @param source 治疗来源\n     */\n    heal(healer: ICharacter | null, target: ICharacter, healAmount: number, source?: string): void;\n    /**\n     * 注册伤害事件监听器\n     * @param events 事件监听器\n     */\n    registerEvents(events: IDamageEvents): void;\n}\n"]}