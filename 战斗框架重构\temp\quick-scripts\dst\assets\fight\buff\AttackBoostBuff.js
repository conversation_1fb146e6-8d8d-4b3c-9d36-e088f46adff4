
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/buff/AttackBoostBuff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a2765IdhidGqKzOEGN4aCaG', 'AttackBoostBuff');
// fight/buff/AttackBoostBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackBoostBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 攻击力提升Buff
 * 提升角色的攻击力和魔法攻击力
 */
var AttackBoostBuff = /** @class */ (function () {
    function AttackBoostBuff(caster, target, duration, attackMultiplier, magicAttackMultiplier) {
        this._name = "攻击强化";
        this._description = "提升攻击力和魔法攻击力";
        this._type = IBuff_1.BuffType.BUFF;
        this._stackCount = 1;
        this._maxStack = 5;
        this._isExpired = false;
        this._attributeModifiers = [];
        // 视觉效果
        this._iconPath = "icons/buffs/attack_boost";
        this._effectPrefabPath = "prefabs/effects/AttackAura";
        this._id = "attack_boost_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._attackMultiplier = attackMultiplier;
        this._magicAttackMultiplier = magicAttackMultiplier || attackMultiplier;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u653B\u51FB\u529B\u63D0\u5347" + Math.round((attackMultiplier - 1) * 100) + "%\uFF0C\u6301\u7EED" + duration + "\u79D2";
        this.createAttributeModifiers();
    }
    Object.defineProperty(AttackBoostBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackBoostBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) {
            var oldStack = this._stackCount;
            this._stackCount = Math.max(0, Math.min(value, this._maxStack));
            if (oldStack !== this._stackCount) {
                this.updateAttributeModifiers();
            }
        },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    AttackBoostBuff.prototype.createAttributeModifiers = function () {
        // 攻击力修改器
        var attackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_attack", "攻击力提升", "attack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, (this._attackMultiplier - 1) * this._stackCount, this._duration);
        // 魔法攻击力修改器
        var magicAttackModifier = new AttributeModifier_1.AttributeModifier(this._id + "_magic_attack", "魔法攻击力提升", "magicAttack", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, (this._magicAttackMultiplier - 1) * this._stackCount, this._duration);
        this._attributeModifiers = [attackModifier, magicAttackModifier];
    };
    /** 更新属性修改器 */
    AttackBoostBuff.prototype.updateAttributeModifiers = function () {
        var e_1, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                var attributeModifier = modifier;
                if (attributeModifier.attributeName === "attack") {
                    attributeModifier.setValue((this._attackMultiplier - 1) * this._stackCount);
                }
                else if (attributeModifier.attributeName === "magicAttack") {
                    attributeModifier.setValue((this._magicAttackMultiplier - 1) * this._stackCount);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** Buff被添加时触发 */
    AttackBoostBuff.prototype.onApply = function () {
        var e_2, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            // 应用属性修改器到目标
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        // 播放特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 触发事件
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
    };
    /** Buff每帧更新时触发 */
    AttackBoostBuff.prototype.onTick = function (deltaTime) {
        var e_3, _a;
        try {
            // 更新属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /** Buff被移除时触发 */
    AttackBoostBuff.prototype.onRemove = function () {
        var e_4, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            // 移除属性修改器
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 造成伤害时触发 */
    AttackBoostBuff.prototype.onDealDamage = function (damageInfo, _target) {
        // 攻击力提升buff可以在这里添加额外的伤害加成
        var bonusDamagePercentage = 0.1 * this._stackCount; // 每层额外10%伤害
        var bonusDamage = Math.floor(damageInfo.baseDamage * bonusDamagePercentage);
        console.log(this._name + " adds " + bonusDamage + " bonus damage (" + bonusDamagePercentage * 100 + "% of base damage, Stack: " + this._stackCount + ")");
        // 正确的方式：通过damageAmplification来增加伤害
        // 这样伤害计算系统会重新计算finalDamage
        damageInfo.damageAmplification += bonusDamage;
        return damageInfo;
    };
    /** 更新Buff状态 */
    AttackBoostBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    AttackBoostBuff.prototype.refresh = function () {
        var e_5, _a;
        this._remainingTime = this._duration;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    AttackBoostBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    AttackBoostBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    AttackBoostBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.attack_multiplier:
                return this._attackMultiplier * this._stackCount;
            case Buff_1.EBuffEffectType.magic_attack_multiplier:
                return this._magicAttackMultiplier * this._stackCount;
            case Buff_1.EBuffEffectType.attack_bonus_percentage:
                return (this._attackMultiplier - 1) * this._stackCount * 100;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    AttackBoostBuff.prototype.conflictsWith = function (_otherBuff) {
        // 同类型的攻击力提升buff不冲突，可以叠加
        return false;
    };
    /** 播放应用特效 */
    AttackBoostBuff.prototype.playApplyEffect = function () {
        console.log("Playing apply effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效播放逻辑
    };
    /** 停止特效 */
    AttackBoostBuff.prototype.stopEffect = function () {
        console.log("Stopping effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    AttackBoostBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            attackMultiplier: this._attackMultiplier,
            magicAttackMultiplier: this._magicAttackMultiplier,
            currentAttackBonus: this.getEffectValue(Buff_1.EBuffEffectType.attack_bonus_percentage),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return AttackBoostBuff;
}());
exports.AttackBoostBuff = AttackBoostBuff;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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