
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/IBuff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8b303G5rv5OHpSF/JPdV3mo', 'IBuff');
// fight/types/IBuff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffPeriodicEffectType = exports.BuffModifierType = exports.BuffType = void 0;
/*** Buff类型枚举*/
var BuffType;
(function (BuffType) {
    /** 增益 */
    BuffType["BUFF"] = "buff";
    /** 减益 */
    BuffType["DEBUFF"] = "debuff";
    /** 中性 */
    BuffType["NEUTRAL"] = "neutral";
})(BuffType = exports.BuffType || (exports.BuffType = {}));
/*** Buff修改器类型枚举*/
var BuffModifierType;
(function (BuffModifierType) {
    /** 加法 */
    BuffModifierType["ADD"] = "add";
    /** 乘法 */
    BuffModifierType["MULTIPLY"] = "multiply";
    /** 百分比 */
    BuffModifierType["PERCENTAGE"] = "percentage";
})(BuffModifierType = exports.BuffModifierType || (exports.BuffModifierType = {}));
/*** Buff周期性效果类型枚举*/
var BuffPeriodicEffectType;
(function (BuffPeriodicEffectType) {
    /** 持续伤害 */
    BuffPeriodicEffectType["DAMAGE_OVER_TIME"] = "damage_over_time";
    /** 持续治疗 */
    BuffPeriodicEffectType["HEAL_OVER_TIME"] = "heal_over_time";
    /** 魔法恢复 */
    BuffPeriodicEffectType["MP_RECOVERY"] = "mp_recovery";
    /** 耐力恢复 */
    BuffPeriodicEffectType["STAMINA_RECOVERY"] = "stamina_recovery";
})(BuffPeriodicEffectType = exports.BuffPeriodicEffectType || (exports.BuffPeriodicEffectType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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