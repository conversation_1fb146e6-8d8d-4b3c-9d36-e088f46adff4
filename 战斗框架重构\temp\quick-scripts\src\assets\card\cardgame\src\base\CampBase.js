"use strict";
cc._RF.push(module, 'd646dLNTdxJ95Dtx6lS3GOz', 'CampBase');
// card/cardgame/src/base/CampBase.ts

// import BuffManager from '../../Controller/BuffManager';
// import { ECamp } from '../CampManager';
// import RoleManager from '../RoleManager';
// import Base from './Base';
// import RoleBase from './RoleBase';
// const { ccclass, property } = cc._decorator;
// declare global {
//     /**阵营数据 */
//     export interface ICampDataType {
//         /**阵营名字 */
//         campName: ECamp;
//     }
//     interface IDealInteractive {
//         addHp?: number
//         reduceHp?: number
//         attack?: number
//     }
// }
// /**
//  * @features : 阵营控制基类
//  * @description: 游戏中多个角色的阵营控制基类
//  * @Date : 2020-08-12 23:28:52
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:57:04
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class CampBase extends Base {
//     /**卡牌阵营信息 */
//     data: ICampDataType;
//     /**阵营角色管理 */
//     roleMgr = new RoleManager
//     /**对阵营使用Buff的管理 */
//     buffMgr = new BuffManager;
//     /**阵营名字*/
//     @Base.ViewLinked
//     campName: ECamp;
//     get isAllDeath() { return this.roleMgr.checkCardAllDeath() }
//     /**
//      * 初始化阵营 
//      * @param initData 要初始化的卡牌数据
//      * @param campData 要初始化的阵营数据
//      *  ! 组件查找顺序 -> 节点挂载 -> 节点的子节点查找
//      */
//     initCamp(initData: ICampMgrDataType) {
//         //初始化卡牌组件
//         // let cardList: CardBase[] = [];
//         // if (this.cardComList.length != 0) {
//         //     cardList = this.cardComList;
//         // } else if (this.node.children.length > 0) {
//         //     //尝试从节点下面或取卡牌
//         //     for (let cardNode of this.node.children) {
//         //         let card = cardNode.getComponent(CardBase);
//         //         if (card) cardList.push(card);
//         //     }
//         // } else cc.error(`[${this.className}]获取卡牌组件失败，请检查是否需要初始化or检查卡牌组件列表`);
//         this.initCampData = initData
//         this.roleMgr.initRoles(initData)
//         if (initData.campData) {
//             this.data = initData.campData;
//         } else cc.error(`[${this.className}]没有设置阵营数据！`);
//         this.restChose()
//     }
//     restChose() {
//         if (this.curRole == null || this.curRole.hp == 0) {
//             this.curRole = this.roleMgr.roles.find(r => r.hp != 0)
//         }
//     }
//     initCampData: ICampMgrDataType
//     restCamp() {
//         if (this.roleMgr.roles.length == 0) {
//             this.roleMgr.roles = []
//             this.roleMgr.initRoles(this.initCampData)
//         } else {
//             this.restChose()
//             // this.curRole.name = this.curRole.name
//         }
//     }
//     async attackCamp(attackCamp: CampBase, roles?: RoleBase[]) {
//         cc.log(`阵营[${this.campName}]开始准备攻击`);
//         let card = this.roleMgr.getRoleCard(1)
//         if (card.length != 0) {
//             await card[0].attackCamp(attackCamp);
//             cc.log(`阵营[${attackCamp.campName}]攻击完成`);
//         }
//     }
//     addHp(addValue: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             role.hp += addValue
//             return { addHp: addValue }
//         }
//     }
//     addHpByPercent(addPercent: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let complete = role.hp * (addPercent / 100)
//             role.hp += complete
//             return { addHp: complete }
//         }
//     }
//     reduceHp(addValue: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             role.hp -= addValue
//             return { reduceHp: addValue }
//         }
//     }
//     reduceHpByPercent(addPercent: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let complete = role.hp * (addPercent / 100)
//             role.hp -= complete
//             return { reduceHp: complete }
//         }
//     }
//     /**攻击指定角色 */
//     attackRole(attackRole: RoleBase): IDealInteractive {
//         let selfRole = this.getRole()
//         if (selfRole && attackRole) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let dodge = Boolean.randomBoolean(80)
//             dodge = true
//             if (dodge && attackRole.hp > 0) {
//                 let v = selfRole.attack * (1 - attackRole.defence / (attackRole.defence + 150))
//                 attackRole.hp -= parseInt(v.toString());
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);
//                 return { attack: selfRole.attack }
//             } else {
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发闪避`);
//                 return { attack: 0 }
//             }
//         }
//     }
//     /** 卡片攻击指定角色*/
//     attackRoleByCard(attackRole: RoleBase, aggressivity: number): IDealInteractive {
//         let selfRole = this.getRole()
//         if (selfRole && attackRole) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             // return role.attackRoleByCard(attackRole, aggressivity);
//             if (attackRole.hp > 0) {
//                 attackRole.hp -= aggressivity;
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);
//                 return { attack: aggressivity }
//             }
//         }
//     }
//     curRole: RoleBase
//     getRole(): RoleBase | null {
//         return this.curRole
//     }
//     getRoleEquipment() {
//         return this.getRole()?.equipMgr
//     }
// }

cc._RF.pop();