{"version": 3, "sources": ["assets\\fight\\characters\\Character.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,oDAAmD;AACnD,0DAA8E;AAC9E,kDAA6C;AAE7C,iDAAgD;AAChD,6DAA4D;AAEtD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,UAAU;AAEV;IAA+B,6BAAa;IAA5C;QAAA,qEAmOC;QA/NG,UAAU;QAEV,eAAS,GAAW,EAAE,CAAC;QAEvB,cAAQ,GAAW,EAAE,CAAC;QAEtB,qBAAe,GAAW,EAAE,CAAC;QAC7B,OAAO;QAEP,uBAAiB,GAAW,MAAM,CAAC;QAEnC,uBAAiB,GAAW,MAAM,CAAC;QAEnC,yBAAmB,GAAW,QAAQ,CAAC;QAEvC,wBAAkB,GAAW,OAAO,CAAC;QACrC,OAAO;QAEP,mBAAa,GAAW,EAAE,CAAC;QAE3B,iBAAW,GAAW,EAAE,CAAC;QAEzB,kBAAY,GAAW,EAAE,CAAC;;IAyM9B,CAAC;IAvMG,YAAY;IACF,oCAAgB,GAA1B;QACI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IACD,eAAe;IACL,qCAAiB,GAA3B,UAA4B,SAAiB;QACzC,qCAAqC;QACrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IACD,eAAe;IACL,oCAAgB,GAA1B;QAAA,iBAQC;QAPG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,SAAS;QACT,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IACD,aAAa;IACH,+BAAW,GAArB,UAAsB,SAAkB;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC/E,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACpC,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IACD,gBAAgB;IACR,qCAAiB,GAAzB;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,2BAAY,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,2BAAY,CAAC,CAAC;QAClG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAU,CAAC,CAAC;IAChG,CAAC;IACD,gBAAgB;IACR,6CAAyB,GAAjC;QACI,IAAI,CAAC,gBAAgB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,MAAM,EAAE,IAAI,CAAC,mBAAmB;YAChC,KAAK,EAAE,IAAI,CAAC,kBAAkB;SACjC,CAAC;IACN,CAAC;IACD,gBAAgB;IACR,yCAAqB,GAA7B;QACI,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,IAAI,EAAE,IAAI,CAAC,WAAW;YACtB,KAAK,EAAE,IAAI,CAAC,YAAY;SAC3B,CAAC;IACN,CAAC;IACD,aAAa;IACL,kCAAc,GAAtB;QACI,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IACD,aAAa;IACL,oCAAgB,GAAxB;QACI,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;SAC5B;IACL,CAAC;IACD,eAAe;IACL,iCAAa,GAAvB,UAAwB,UAAkB;QACtC,wBAAwB;IAC5B,CAAC;IACD,aAAa;IACL,qCAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACjE;IACL,CAAC;IACD,iBAAiB;IACjB,uCAAmB,GAAnB,UAAoB,IAAqB;QAArB,qBAAA,EAAA,YAAqB;QACrC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACnE;IACL,CAAC;IACD,iBAAiB;IACjB,qCAAiB,GAAjB;QACI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACjE;IACL,CAAC;IACD,eAAe;IACP,kCAAc,GAAtB;QACI,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YACzB,kBAAkB;YAClB,kEAAkE;SACrE;IACL,CAAC;IACD,aAAa;IACL,mCAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IACD,eAAe;IACf,iCAAa,GAAb,UAAc,MAAkB;QAC5B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC1B,OAAO,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IACD,eAAe;IACf,iCAAa,GAAb,UAAc,OAAiB,EAAE,aAA0B;QAA3D,iBAoBC;QAnBG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,QAAQ,CAAC,+BAAc,CAAC,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,SAAS;QACT,IAAM,WAAW,GAAG;YAChB,eAAe,EAAE,GAAG;YACpB,aAAa,EAAE,GAAG;YAClB,WAAW,EAAE;gBACT,aAAa,aAAb,aAAa,uBAAb,aAAa,GAAK;gBAClB,eAAe;YACnB,CAAC;YACD,SAAS,EAAE;gBACP,KAAI,CAAC,QAAQ,CAAC,+BAAc,CAAC,IAAI,CAAC,CAAC;gBACnC,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC;SACJ,CAAC;QACF,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IACxD,CAAC;IACD,eAAe;IACf,oCAAgB,GAAhB,UAAiB,IAAyB;QACtC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1E,SAAS;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,kBAAkB;YAClB,IAAI,CAAC,WAAW,GAAG,IAAI,yCAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnE,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,qCAAS,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;SAC5E;QAED,OAAO;QACP,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;SAChH;QAED,QAAQ;QACR,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SAClC;IACL,CAAC;IAED,kBAAkB;IAClB,wCAAoB,GAApB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7D;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IACD,eAAe;IACf,oCAAgB,GAAhB,UAAiB,MAAe;QAC5B,IAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;IAChD,CAAC;IACD,iBAAiB;IACjB,mCAAe,GAAf,UAAgB,MAAiB;QAC7B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;QACD,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClI,OAAO,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IACnD,CAAC;IACD,aAAa;IACb,8BAAU,GAAV,UAAW,MAAe;QACtB,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,IAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAClD;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACjD;IACL,CAAC;IACD,eAAe;IACf,oCAAgB,GAAhB;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;YAC9C,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACvD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC9B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;SAC/B,CAAC;IACN,CAAC;IA5ND;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gDACG;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+CACE;IAEtB;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sDACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;wDACe;IAEnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;wDACe;IAEnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;0DACmB;IAEvC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;yDACiB;IAGrC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;oDACO;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kDACK;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mDACM;IA1BjB,SAAS;QADrB,OAAO;OACK,SAAS,CAmOrB;IAAD,gBAAC;CAnOD,AAmOC,CAnO8B,6BAAa,GAmO3C;AAnOY,8BAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AttackAction } from \"../actions/AttackAction\";\nimport { MoveAction } from \"../actions/MoveAction\";\nimport { CharacterState, CharacterCreateInfo } from \"../types/CharacterTypes\";\nimport FightEvent from \"../types/FightEvent\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { BaseCharacter } from \"./BaseCharacter\";\nimport { CharacterAttributes } from \"./CharacterAttributes\";\n\nconst { ccclass, property } = cc._decorator;\n\n/*** 角色类*/\n@ccclass\nexport class Character extends BaseCharacter {\n    /**动作组件 */\n    private _attackAction: AttackAction;\n    private _moveAction: MoveAction;\n    /**角色配置 */\n    @property(cc.String)\n    prefabKey: string = \"\";\n    @property(cc.String)\n    roleName: string = \"\";\n    @property(cc.String)\n    attackSkillName: string = \"\";\n    // 动画配置\n    @property(cc.String)\n    idleAnimationName: string = \"idle\";\n    @property(cc.String)\n    moveAnimationName: string = \"move\";\n    @property(cc.String)\n    attackAnimationName: string = \"attack\";\n    @property(cc.String)\n    deathAnimationName: string = \"death\";\n    // 音效配置\n    @property(cc.String)\n    attackSoundId: string = \"\";\n    @property(cc.String)\n    hurtSoundId: string = \"\";\n    @property(cc.String)\n    deathSoundId: string = \"\";\n\n    /*** 初始化角色*/\n    protected onCharacterStart(): void {\n        this.initializeActions();\n        this.initializeAnimationConfig();\n        this.initializeSoundConfig();\n        this.setupCollision();\n    }\n    /**  * 更新角色  */\n    protected onCharacterUpdate(deltaTime: number): void {\n        // 基类已经处理了技能和Buff的更新， 这里可以添加角色特定的更新逻辑\n        this.updateActions(deltaTime);\n    }\n    /** * 角色死亡处理 */\n    protected onCharacterDeath(): void {\n        this.playDeathAnimation();\n        this.playDeathSound();\n        this.disableCollision();\n        // 延迟移除角色\n        this.scheduleOnce(() => {\n            this.removeCharacter();\n        }, 1.0);\n    }\n    /** * 执行移动 */\n    protected performMove(direction: cc.Vec3): void {\n        if (this._moveAction) {\n            const moveVector = direction.clone().multiplyScalar(this.attributes.moveSpeed);\n            this._moveAction.moveBy(moveVector);\n            this.playMoveAnimation();\n        }\n    }\n    /** * 初始化动作组件 */\n    private initializeActions(): void {\n        this._attackAction = this.node.getComponent(AttackAction) || this.node.addComponent(AttackAction);\n        this._moveAction = this.node.getComponent(MoveAction) || this.node.addComponent(MoveAction);\n    }\n    /** * 初始化动画配置 */\n    private initializeAnimationConfig(): void {\n        this._animationConfig = {\n            idle: this.idleAnimationName,\n            move: this.moveAnimationName,\n            attack: this.attackAnimationName,\n            death: this.deathAnimationName\n        };\n    }\n    /** * 初始化音效配置 */\n    private initializeSoundConfig(): void {\n        this._soundConfig = {\n            attack: this.attackSoundId,\n            hurt: this.hurtSoundId,\n            death: this.deathSoundId\n        };\n    }\n    /** * 设置碰撞 */\n    private setupCollision(): void {\n        const collider = this.node.getComponent(cc.Collider);\n        if (collider) {\n            collider.enabled = true;\n        }\n    }\n    /** * 禁用碰撞 */\n    private disableCollision(): void {\n        const collider = this.node.getComponent(cc.Collider);\n        if (collider) {\n            collider.enabled = false;\n        }\n    }\n    /** * 更新动作组件 */\n    protected updateActions(_deltaTime: number): void {\n        // 动作组件会自己更新，这里可以添加额外的逻辑\n    }\n    /*** 播放移动动画*/\n    private playMoveAnimation(): void {\n        if (this._spine && this._animationConfig.move) {\n            this._spine.setAnimation(0, this._animationConfig.move, true);\n        }\n    }\n    /**  * 播放攻击动画  */\n    playAttackAnimation(loop: boolean = false): void {\n        if (this._spine && this._animationConfig.attack) {\n            this._spine.setAnimation(0, this._animationConfig.attack, loop);\n        }\n    }\n    /**  * 播放空闲动画  */\n    playIdleAnimation(): void {\n        if (this._spine && this._animationConfig.idle) {\n            this._spine.setAnimation(0, this._animationConfig.idle, true);\n        }\n    }\n    /** * 播放死亡音效 */\n    private playDeathSound(): void {\n        if (this._soundConfig.death) {\n            // 这里应该调用音效管理器播放音效\n            // AudioManager.getInstance().playEffect(this._soundConfig.death);\n        }\n    }\n    /** * 移除角色 */\n    private removeCharacter(): void {\n        this.node.emit(FightEvent.characterRemoved, this);\n        this.node.destroy();\n    }\n    /** * 强制攻击目标 */\n    forceAttackTo(target: ICharacter): boolean {\n        if (!target || target.isDead) {\n            console.error('forceAttackTo but target is null or dead');\n            return false;\n        }\n        return this.attack(target);\n    }\n    /** * 执行攻击动作 */\n    performAttack(_target?: cc.Node, onHitCallback?: () => void): boolean {\n        if (!this._attackAction) {\n            return false;\n        }\n        this.setState(CharacterState.ATTACKING);\n        this.playAttackAnimation();\n        // 配置攻击动作\n        const attackProps = {\n            hurtStartTimeMs: 300, // 300ms后开始造成伤害\n            hurtEndTimeMs: 600,   // 600ms后攻击结束\n            onHurtStart: () => {\n                onHitCallback?.();\n                // 这里可以添加伤害计算逻辑\n            },\n            onHurtEnd: () => {\n                this.setState(CharacterState.IDLE);\n                this.playIdleAnimation();\n            }\n        };\n        return this._attackAction.doAttackOnce(attackProps);\n    }\n    /** * 设置角色数据 */\n    setCharacterData(data: CharacterCreateInfo): void {\n        this._name = data.name;\n        this._role = data.role;\n        this.roleName = data.name;\n        this.prefabKey = typeof data.prefabKey === 'string' ? data.prefabKey : '';\n\n        // 设置初始属性\n        if (data.initialAttributes) {\n            // 重新创建属性组件以应用初始数据\n            this._attributes = new CharacterAttributes(data.initialAttributes);\n            console.log(`${this._name} 属性已设置:`, this._attributes.getAttributeData());\n        }\n\n        // 设置位置\n        if (data.worldPosition) {\n            this.node.position = data.parent ? data.parent.convertToNodeSpaceAR(data.worldPosition) : data.worldPosition;\n        }\n\n        // 设置父节点\n        if (data.parent) {\n            this.node.parent = data.parent;\n        }\n    }\n\n    /** * 获取开火点世界坐标 */\n    getFireWorldPosition(): cc.Vec3 {\n        if (this._fireNode) {\n            return this._fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        }\n        return this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n    }\n    /** * 获取开火方向 */\n    getFireDirection(target: cc.Vec3): cc.Vec3 {\n        const firePos = this.getFireWorldPosition();\n        return target.subtract(firePos).normalize();\n    }\n    /*** 检查是否在攻击范围内*/\n    isInAttackRange(target: Character): boolean {\n        if (!target || target.isDead) {\n            return false;\n        }\n        const distance = cc.Vec3.distance(this.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));\n        return distance <= this.attributes.attackRange;\n    }\n    /** * 面向目标 */\n    faceTarget(target: cc.Vec3): void {\n        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const direction = target.subtract(currentPos);\n        if (direction.x < 0) {\n            this.node.scaleX = -Math.abs(this.node.scaleX);\n        } else {\n            this.node.scaleX = Math.abs(this.node.scaleX);\n        }\n    }\n    /** * 获取角色信息 */\n    getCharacterInfo() {\n        return {\n            id: this.id,\n            name: this.name,\n            role: this.role,\n            state: this.state,\n            isDead: this.isDead,\n            attributes: this.attributes.getAttributeData(),\n            position: this.node.convertToWorldSpaceAR(cc.Vec3.ZERO),\n            skillCount: this.skills.length,\n            buffCount: this.buffs.length\n        };\n    }\n}\n"]}