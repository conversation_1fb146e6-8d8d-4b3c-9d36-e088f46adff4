{"version": 3, "sources": ["assets\\fight\\actions\\AttackAction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAsD;AACtD,kDAA6C;AAGvC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAC5C,YAAY;AAEZ;IAAkC,gCAAY;IAA9C;QAAA,qEA2MC;QA1MG,YAAY;QACZ,kBAAY,GAAgB,4BAAW,CAAC,IAAI,CAAC;QAC7C,WAAW;QACH,kBAAY,GAAW,CAAC,CAAC;QACjC,YAAY;QACJ,mBAAa,GAA8B,IAAI,CAAC;QACxD,UAAU;QACF,yBAAmB,GAAY,KAAK,CAAC;QACrC,uBAAiB,GAAY,KAAK,CAAC;QAC3C,UAAU;QACF,kBAAY,GAAyB,EAAE,CAAC;QAChD,cAAc;QAEd,gBAAU,GAAY,KAAK,CAAC;QAC5B,YAAY;QAEZ,oBAAc,GAAW,CAAC,CAAC;;IA0L/B,CAAC;IAvLG,sBAAI,qCAAW;QADf,aAAa;aACb;YACI,OAAO,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,CAAC;QAClD,CAAC;;;OAAA;IAED,sBAAI,2CAAiB;QADrB,mBAAmB;aACnB;YACI,OAAO,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QACzH,CAAC;;;OAAA;IAED,sBAAI,wCAAc;QADlB,qBAAqB;aACrB;YACI,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,EAAE;gBAC/D,OAAO,CAAC,CAAC;aACZ;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC;;;OAAA;IACD,aAAa;IACH,6BAAM,GAAhB,UAAiB,EAAU;QACvB,IAAI,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,EAAE;YACxC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IACD,gBAAgB;IACN,gCAAS,GAAnB;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,mCAAY,GAAZ,UAAa,KAAyB;QAClC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YAClC,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,EAAE;YACxC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;SACf;aAAM,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;YAC1E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,aAAa;IACb,sCAAe,GAAf;QACI,IAAI,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,IAAI,EAAE;YACxC,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,4BAAW,CAAC,IAAI,CAAC,CAAC;SACnC;IACL,CAAC;IACD,aAAa;IACb,iCAAU,GAAV;QACI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,mBAAmB;IACnB,qCAAc,GAAd,cAA2B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IAE7D,eAAe;IACP,kCAAW,GAAnB,UAAoB,KAAyB;QACzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,4BAAW,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IACD,cAAc;IACN,wCAAiB,GAAzB,UAA0B,EAAU;QAChC,IAAI,CAAC,YAAY,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ;IAC5C,CAAC;IACD,eAAe;IACP,wCAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACK,IAAA,KAAqC,IAAI,CAAC,aAAa,EAArD,eAAe,qBAAA,EAAE,aAAa,mBAAuB,CAAC;QAC9D,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,EAAE;YACpC,IAAI,CAAC,QAAQ,CAAC,4BAAW,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;aAAM,IAAI,IAAI,CAAC,YAAY,IAAI,eAAe,IAAI,IAAI,CAAC,YAAY,KAAK,4BAAW,CAAC,MAAM,EAAE;YACzF,IAAI,CAAC,QAAQ,CAAC,4BAAW,CAAC,MAAM,CAAC,CAAC;SACrC;IACL,CAAC;IAED,WAAW;IACH,uCAAgB,GAAxB;QACI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACK,IAAA,KAA+E,IAAI,CAAC,aAAa,EAA/F,eAAe,qBAAA,EAAE,aAAa,mBAAA,EAAE,WAAW,iBAAA,EAAE,SAAS,eAAA,EAAE,gBAAgB,sBAAuB,CAAC;QACxG,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,IAAI,eAAe,EAAE;YACnE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,WAAW,aAAX,WAAW,uBAAX,WAAW,GAAK;SACnB;QACD,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,EAAE;YAC/D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,SAAS,aAAT,SAAS,uBAAT,SAAS,GAAK;SACjB;IACL,CAAC;IAED,aAAa;IACL,+BAAQ,GAAhB,UAAiB,QAAqB;QAClC,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;YAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC3C;IACL,CAAC;IACD,iBAAiB;IACT,qCAAc,GAAtB,UAAuB,QAAqB,EAAE,QAAqB;QAC/D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAU,CAAC,kBAAkB,EAAE,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACvF,QAAQ,QAAQ,EAAE;YACd,KAAK,4BAAW,CAAC,IAAI;gBAAE,IAAI,CAAC,WAAW,EAAE,CAAC;gBAAC,MAAM;YACjD,KAAK,4BAAW,CAAC,MAAM;gBAAE,IAAI,CAAC,aAAa,EAAE,CAAC;gBAAC,MAAM;YACrD,KAAK,4BAAW,CAAC,MAAM;gBAAE,IAAI,CAAC,aAAa,EAAE,CAAC;gBAAC,MAAM;YACrD,KAAK,4BAAW,CAAC,QAAQ;gBAAE,IAAI,CAAC,eAAe,EAAE,CAAC;gBAAC,MAAM;SAC5D;IACL,CAAC;IACD,eAAe;IACP,kCAAW,GAAnB;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IACD,gBAAgB;IACR,wCAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAG,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;SAChC;IACL,CAAC;IACD,eAAe;IACP,0CAAmB,GAA3B,UAA4B,KAAyB;QACjD,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,KAAK,CAAC,eAAe,GAAG,CAAC,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,aAAa,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,aAAa;IACH,kCAAW,GAArB;QACI,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IACD,eAAe;IACL,oCAAa,GAAvB;QACI,cAAc;IAClB,CAAC;IACD,eAAe;IACL,oCAAa,GAAvB;QACI,cAAc;IAClB,CAAC;IACD,aAAa;IACH,sCAAe,GAAzB;QACI,cAAc;IAClB,CAAC;IAED,eAAe;IACf,oCAAa,GAAb;QACI,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAC/B,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,eAAe;gBACnD,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa;aAClD,CAAC,CAAC,CAAC,IAAI;SACX,CAAC;IACN,CAAC;IA5LD;QADC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;oDACO;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;wDACM;IAjBlB,YAAY;QADxB,OAAO;OACK,YAAY,CA2MxB;IAAD,mBAAC;CA3MD,AA2MC,CA3MiC,EAAE,CAAC,SAAS,GA2M7C;AA3MY,oCAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AttackState } from \"../types/CharacterTypes\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IAttackActionProps } from \"../types/ISkill\";\n\nconst { ccclass, property } = cc._decorator;\n/*** 攻击动作类*/\n@ccclass\nexport class AttackAction extends cc.Component {\n    /**当前攻击状态 */\n    currentState: AttackState = AttackState.IDLE;\n    /**攻击计时器 */\n    private _attackTimer: number = 0;\n    /**当前攻击属性 */\n    private _currentProps: IAttackActionProps | null = null;\n    /**回调标记 */\n    private _hurtStartTriggered: boolean = false;\n    private _hurtEndTriggered: boolean = false;\n    /**攻击队列 */\n    private _attackQueue: IAttackActionProps[] = [];\n    /**是否允许攻击队列 */\n    @property(cc.Boolean)\n    allowQueue: boolean = false;\n    /**最大队列长度 */\n    @property(cc.Integer)\n    maxQueueLength: number = 3;\n\n    /*** 是否正在攻击*/\n    get isAttacking(): boolean {\n        return this.currentState !== AttackState.IDLE;\n    }\n    /** * 是否可以开始新的攻击 */\n    get canStartNewAttack(): boolean {\n        return this.currentState === AttackState.IDLE || (this.allowQueue && this._attackQueue.length < this.maxQueueLength);\n    }\n    /** * 获取攻击进度 (0-1) */\n    get attackProgress(): number {\n        if (!this._currentProps || this.currentState === AttackState.IDLE) {\n            return 0;\n        }\n        return Math.min(1, this._attackTimer / this._currentProps.hurtEndTimeMs);\n    }\n    /*** 更新攻击状态*/\n    protected update(dt: number): void {\n        if (this.currentState === AttackState.IDLE) {\n            return;\n        }\n        this.updateAttackTimer(dt);\n        this.updateAttackState();\n        this.triggerCallbacks();\n    }\n    /** * 组件销毁时清理 */\n    protected onDestroy(): void {\n        this.interruptAttack();\n        this.clearQueue();\n    }\n\n    /**\n     * 执行一次攻击\n     * @param props 攻击属性\n     * @returns 是否成功开始攻击\n     */\n    doAttackOnce(props: IAttackActionProps): boolean {\n        if (!this.validateAttackProps(props)) {\n            console.error(\"Invalid attack properties\");\n            return false;\n        }\n        if (this.currentState === AttackState.IDLE) {\n            this.startAttack(props);\n            return true;\n        } else if (this.allowQueue && this._attackQueue.length < this.maxQueueLength) {\n            this._attackQueue.push(props);\n            return true;\n        }\n        return false;\n    }\n    /*** 中断当前攻击*/\n    interruptAttack(): void {\n        if (this.currentState !== AttackState.IDLE) {\n            this.resetAttack();\n            this.setState(AttackState.IDLE);\n        }\n    }\n    /*** 清空攻击队列*/\n    clearQueue(): void {\n        this._attackQueue.length = 0;\n    }\n    /** * 获取队列中的攻击数量 */\n    getQueueLength(): number { return this._attackQueue.length; }\n\n    /**  * 开始攻击  */\n    private startAttack(props: IAttackActionProps): void {\n        this._currentProps = props;\n        this._attackTimer = 0;\n        this._hurtStartTriggered = false;\n        this._hurtEndTriggered = false;\n        this.setState(AttackState.WINDUP);\n    }\n    /*** 更新攻击计时器*/\n    private updateAttackTimer(dt: number): void {\n        this._attackTimer += dt * 1000; // 转换为毫秒\n    }\n    /** * 更新攻击状态 */\n    private updateAttackState(): void {\n        if (!this._currentProps) {\n            return;\n        }\n        const { hurtStartTimeMs, hurtEndTimeMs } = this._currentProps;\n        if (this._attackTimer >= hurtEndTimeMs) {\n            this.setState(AttackState.IDLE);\n            this.processNextAttack();\n        } else if (this._attackTimer >= hurtStartTimeMs && this.currentState === AttackState.WINDUP) {\n            this.setState(AttackState.DAMAGE);\n        }\n    }\n\n    /*** 触发回调*/\n    private triggerCallbacks(): void {\n        if (!this._currentProps) {\n            return;\n        }\n        const { hurtStartTimeMs, hurtEndTimeMs, onHurtStart, onHurtEnd, onAttackProgress } = this._currentProps;\n        if (!this._hurtStartTriggered && this._attackTimer >= hurtStartTimeMs) {\n            this._hurtStartTriggered = true;\n            onHurtStart?.();\n        }\n        if (onAttackProgress) {\n            onAttackProgress(this.attackProgress);\n        }\n        if (!this._hurtEndTriggered && this._attackTimer >= hurtEndTimeMs) {\n            this._hurtEndTriggered = true;\n            onHurtEnd?.();\n        }\n    }\n\n    /*** 设置攻击状态*/\n    private setState(newState: AttackState): void {\n        if (this.currentState !== newState) {\n            const oldState = this.currentState;\n            this.currentState = newState;\n            this.onStateChanged(oldState, newState);\n        }\n    }\n    /** * 状态改变时的处理 */\n    private onStateChanged(oldState: AttackState, newState: AttackState): void {\n        this.node.emit(FightEvent.attackStateChanged, { oldState, newState, component: this });\n        switch (newState) {\n            case AttackState.IDLE: this.onEnterIdle(); break;\n            case AttackState.WINDUP: this.onEnterWindup(); break;\n            case AttackState.DAMAGE: this.onEnterDamage(); break;\n            case AttackState.RECOVERY: this.onEnterRecovery(); break;\n        }\n    }\n    /** * 重置攻击状态 */\n    private resetAttack(): void {\n        this._currentProps = null;\n        this._attackTimer = 0;\n        this._hurtStartTriggered = false;\n        this._hurtEndTriggered = false;\n    }\n    /** * 处理下一个攻击 */\n    private processNextAttack(): void {\n        if (this._attackQueue.length > 0) {\n            const nextAttack = this._attackQueue.shift()!;\n            this.startAttack(nextAttack);\n        }\n    }\n    /** * 验证攻击属性 */\n    private validateAttackProps(props: IAttackActionProps): boolean {\n        if (!props) {\n            return false;\n        }\n        if (props.hurtStartTimeMs < 0 || props.hurtEndTimeMs < 0) {\n            return false;\n        }\n        if (props.hurtStartTimeMs > props.hurtEndTimeMs) {\n            return false;\n        }\n        return true;\n    }\n    /*** 进入空闲状态*/\n    protected onEnterIdle(): void {\n        this.resetAttack();\n    }\n    /** * 进入前摇状态 */\n    protected onEnterWindup(): void {\n        // 可以在这里添加前摇特效\n    }\n    /** * 进入伤害状态 */\n    protected onEnterDamage(): void {\n        // 可以在这里添加伤害特效\n    }\n    /*** 进入后摇状态*/\n    protected onEnterRecovery(): void {\n        // 可以在这里添加后摇特效\n    }\n\n    /** * 获取攻击信息 */\n    getAttackInfo() {\n        return {\n            currentState: this.currentState,\n            isAttacking: this.isAttacking,\n            attackProgress: this.attackProgress,\n            queueLength: this._attackQueue.length,\n            canStartNewAttack: this.canStartNewAttack,\n            currentProps: this._currentProps ? {\n                hurtStartTimeMs: this._currentProps.hurtStartTimeMs,\n                hurtEndTimeMs: this._currentProps.hurtEndTimeMs\n            } : null\n        };\n    }\n}\n"]}