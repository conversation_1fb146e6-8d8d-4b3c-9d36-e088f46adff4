{"version": 3, "sources": ["assets\\fight\\buff\\PoisonBuff.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,kDAA6C;AAC7C,wCAA8F;AAE9F,sEAA0F;AAE1F,qEAAoE;AACpE,sCAAgD;AAEhD;;;GAGG;AACH;IAyBI,oBAAY,MAAkB,EAAE,MAAkB,EAAE,QAAgB,EAAE,eAAuB;QAvBrF,UAAK,GAAW,IAAI,CAAC;QACrB,iBAAY,GAAW,mBAAmB,CAAC;QAC3C,UAAK,GAAa,gBAAQ,CAAC,MAAM,CAAC;QAGlC,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,EAAE,CAAC;QAGvB,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAyB,EAAE,CAAC;QAK/C,oBAAe,GAAW,GAAG,CAAC,CAAC,SAAS;QACxC,oBAAe,GAAW,CAAC,CAAC;QAC5B,sBAAiB,GAAW,GAAG,CAAC,CAAC,YAAY;QAErD,OAAO;QACC,cAAS,GAAW,oBAAoB,CAAC;QACzC,sBAAiB,GAAW,4BAA4B,CAAC;QAG7D,IAAI,CAAC,GAAG,GAAG,YAAU,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QACjF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,UAAQ,IAAI,CAAC,GAAK,CAAC,CAAC;QAElE,IAAI,CAAC,YAAY,GAAG,6BAAO,eAAe,gFAAe,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC,2BAAO,QAAQ,WAAG,CAAC;QAEpH,UAAU;QACV,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAGD,sBAAI,0BAAE;QADN,YAAY;aACZ,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,4BAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,mCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,4BAAI;aAAR,cAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,gCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,gCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,8BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,8BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,iCAAS;aAAb,cAA2B,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChF,sBAAI,0CAAkB;aAAtB,cAA8D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;;;OAAA;IAChG,sBAAI,gCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,wCAAgB;aAApB,cAAiC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,qCAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAE3D,sBAAI,kCAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACrD,UAAe,KAAa;YACxB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAChE,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,EAAE;gBAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;QACL,CAAC;;;OAPoD;IAQrD,sBAAI,sCAAc;aAAlB;YACI,OAAO;gBACH,QAAQ,EAAE,IAAI,CAAC,eAAe;gBAC9B,UAAU,EAAE,8BAAsB,CAAC,gBAAgB;gBACnD,KAAK,EAAE,IAAI,CAAC,gBAAgB;gBAC5B,SAAS,EAAE,IAAI;aAClB,CAAC;QACN,CAAC;;;OAAA;IAED,cAAc;IACN,6CAAwB,GAAhC;QACI,cAAc;QACd,IAAM,wBAAwB,GAAG,IAAI,qCAAiB,CAC/C,IAAI,CAAC,GAAG,uBAAoB,EAC/B,QAAQ,EACR,iBAAiB,EACjB,4CAAqB,CAAC,UAAU,EAChC,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAC1C,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1D,CAAC;IAED,cAAc;IACN,6CAAwB,GAAhC;;;YACI,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAM,iBAAiB,GAAG,QAA6B,CAAC;gBACxD,IAAI,iBAAiB,CAAC,aAAa,KAAK,iBAAiB,EAAE;oBACvD,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC1E;aACJ;;;;;;;;;IACL,CAAC;IAED,iBAAiB;IACjB,4BAAO,GAAP;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,IAAI,CAAC,OAAO,CAAC,aAAa,iBAAY,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;;YACnG,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACjD;;;;;;;;;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1G,CAAC;IAED,kBAAkB;IAClB,2BAAM,GAAN,UAAO,SAAiB;;QACpB,IAAI,CAAC,eAAe,IAAI,SAAS,CAAC;QAClC,gBAAgB;QAChB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;SAC5B;;YACD,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC9B;;;;;;;;;IACL,CAAC;IAED,iBAAiB;IACjB,6BAAQ,GAAR;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;;YACxE,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvD;;;;;;;;;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,aAAa;IACL,qCAAgB,GAAxB;QACI,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC7D,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,eAAU,WAAW,4BAAuB,IAAI,CAAC,KAAK,iBAAY,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;YAChI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,WAAW;YACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,iBAAiB,EAAE;gBAClD,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,MAAM,EAAE,WAAW;gBACnB,MAAM,EAAE,IAAI;aACf,CAAC,CAAC;SACN;IACL,CAAC;IAED,eAAe;IACf,2BAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,iBAAiB;IACjB,4BAAO,GAAP;;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;;YACzB,YAAY;YACZ,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;aAC3C;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC5E,CAAC;IAED,aAAa;IACb,6BAAQ,GAAR,UAAS,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,aAAa;IACb,gCAAW,GAAX,UAAY,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,mBAAmB;IACnB,mCAAc,GAAd,UAAe,UAAkB;QAC7B,QAAQ,UAAU,EAAE;YAChB,KAAK,sBAAe,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YACpD,KAAK,sBAAe,CAAC,sBAAsB;gBACvC,OAAO,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YAC1E,KAAK,sBAAe,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;YACrD;gBACI,OAAO,CAAC,CAAC;SAChB;IACL,CAAC;IAED,yBAAyB;IACzB,kCAAa,GAAb,UAAc,UAAiB;QAC3B,sBAAsB;QACtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IAChB,mCAAc,GAAd;QACI,OAAO,IAAI,CAAC,CAAC,cAAc;IAC/B,CAAC;IAED,aAAa;IACL,oCAAe,GAAvB;QACI,OAAO,CAAC,GAAG,CAAC,+BAA6B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACxF,oBAAoB;IACxB,CAAC;IAED,aAAa;IACL,qCAAgB,GAAxB;QACI,OAAO,CAAC,GAAG,CAAC,qCAAmC,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QAC7E,kBAAkB;IACtB,CAAC;IAED,WAAW;IACH,+BAAU,GAAlB;QACI,OAAO,CAAC,GAAG,CAAC,gCAA8B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACzF,kBAAkB;IACtB,CAAC;IAED,aAAa;IACb,iCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAe,CAAC,sBAAsB,CAAC;YACjF,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAe,CAAC,iBAAiB,CAAC;YACxE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,SAAS,EAAE,IAAI,CAAC,UAAU;SAC7B,CAAC;IACN,CAAC;IACL,iBAAC;AAAD,CAhQA,AAgQC,IAAA;AAhQY,gCAAU", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff, BuffType, IBuffPeriodicEffect, BuffPeriodicEffectType } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { IAttributeModifier, AttributeModifierType } from \"../types/ICharacterAttributes\";\nimport { DamageType } from \"../types/IDamage\";\nimport { AttributeModifier } from \"../characters/AttributeModifier\";\nimport { EBuffEffectType } from \"../types/Buff\";\n\n/**\n * 毒素Debuff\n * 持续造成毒素伤害并降低目标的生命恢复效果\n */\nexport class PoisonBuff implements IBuff {\n    private _id: string;\n    private _name: string = \"中毒\";\n    private _description: string = \"持续受到毒素伤害，生命恢复效果降低\";\n    private _type: BuffType = BuffType.DEBUFF;\n    private _duration: number;\n    private _remainingTime: number;\n    private _stackCount: number = 1;\n    private _maxStack: number = 10;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _isExpired: boolean = false;\n    private _attributeModifiers: IAttributeModifier[] = [];\n    private _eventManager: EventManager;\n\n    // 毒素配置\n    private _damagePerSecond: number;\n    private _damageInterval: number = 1.0; // 每秒触发一次\n    private _lastDamageTime: number = 0;\n    private _healingReduction: number = 0.5; // 治疗效果减少50%\n\n    // 视觉效果\n    private _iconPath: string = \"icons/buffs/poison\";\n    private _effectPrefabPath: string = \"prefabs/effects/PoisonAura\";\n\n    constructor(caster: ICharacter, target: ICharacter, duration: number, damagePerSecond: number) {\n        this._id = `poison_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        this._caster = caster;\n        this._target = target;\n        this._duration = duration;\n        this._remainingTime = duration;\n        this._damagePerSecond = damagePerSecond;\n        this._eventManager = EventManager.createLocal(`buff_${this._id}`);\n\n        this._description = `每秒受到${damagePerSecond}点毒素伤害，治疗效果降低${Math.round(this._healingReduction * 100)}%，持续${duration}秒`;\n\n        // 创建属性修改器\n        this.createAttributeModifiers();\n    }\n\n    // 实现IBuff接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get type(): BuffType { return this._type; }\n    get duration(): number { return this._duration; }\n    get maxStack(): number { return this._maxStack; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter { return this._target; }\n    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }\n    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }\n    get iconPath(): string { return this._iconPath; }\n    get effectPrefabPath(): string { return this._effectPrefabPath; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }\n    get stackCount(): number { return this._stackCount; }\n    set stackCount(value: number) {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(0, Math.min(value, this._maxStack));\n        if (oldStack !== this._stackCount) {\n            this.updateAttributeModifiers();\n        }\n    }\n    get periodicEffect() {\n        return {\n            interval: this._damageInterval,\n            effectType: BuffPeriodicEffectType.DAMAGE_OVER_TIME,\n            value: this._damagePerSecond,\n            stackable: true\n        };\n    }\n\n    /** 创建属性修改器 */\n    private createAttributeModifiers(): void {\n        // 生命恢复效果减少修改器\n        const healingReductionModifier = new AttributeModifier(\n            `${this._id}_healing_reduction`,\n            \"毒素治疗减少\",\n            \"healingReceived\",\n            AttributeModifierType.PERCENTAGE,\n            -this._healingReduction * this._stackCount,\n            this._duration\n        );\n        this._attributeModifiers = [healingReductionModifier];\n    }\n\n    /** 更新属性修改器 */\n    private updateAttributeModifiers(): void {\n        for (const modifier of this._attributeModifiers) {\n            const attributeModifier = modifier as AttributeModifier;\n            if (attributeModifier.attributeName === \"healingReceived\") {\n                attributeModifier.setValue(-this._healingReduction * this._stackCount);\n            }\n        }\n    }\n\n    /** Buff被添加时触发 */\n    onApply(): void {\n        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.addModifier(modifier);\n        }\n        if (this._effectPrefabPath) {\n            this.playApplyEffect();\n        }\n        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });\n        this._eventManager.emit(FightEvent.characterPoisoned, { target: this._target, caster: this._caster });\n    }\n\n    /** Buff每帧更新时触发 */\n    onTick(deltaTime: number): void {\n        this._lastDamageTime += deltaTime;\n        // 检查是否到了造成伤害的时间\n        if (this._lastDamageTime >= this._damageInterval) {\n            this.dealPoisonDamage();\n            this._lastDamageTime = 0;\n        }\n        for (const modifier of this._attributeModifiers) {\n            modifier.update(deltaTime);\n        }\n    }\n\n    /** Buff被移除时触发 */\n    onRemove(): void {\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.removeModifier(modifier.id);\n        }\n        this.stopEffect();\n        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });\n    }\n\n    /** 造成毒素伤害 */\n    private dealPoisonDamage(): void {\n        if (this._target && !this._target.isDead) {\n            const totalDamage = this._damagePerSecond * this._stackCount;\n            // 直接造成毒素伤害\n            this._target.takeDamage(totalDamage, this._caster);\n            console.log(`${this._target.characterName} takes ${totalDamage} poison damage from ${this._name} (Stack: ${this._stackCount})`);\n            this.playDamageEffect();\n            // 触发毒素伤害事件\n            this._eventManager.emit(FightEvent.poisonDamageDealt, {\n                target: this._target,\n                caster: this._caster,\n                damage: totalDamage,\n                source: this\n            });\n        }\n    }\n\n    /** 更新Buff状态 */\n    update(deltaTime: number): boolean {\n        if (this._isExpired) {\n            return true;\n        }\n        this._remainingTime -= deltaTime;\n        this.onTick(deltaTime);\n        if (this._remainingTime <= 0) {\n            this._isExpired = true;\n            return true;\n        }\n        return false;\n    }\n\n    /** 刷新Buff持续时间 */\n    refresh(): void {\n        this._remainingTime = this._duration;\n        this._lastDamageTime = 0;\n        // 刷新属性修改器时间\n        for (const modifier of this._attributeModifiers) {\n            modifier.remainingTime = this._duration;\n        }\n        console.log(`${this._name} refreshed on ${this._target.characterName}`);\n    }\n\n    /** 增加叠加层数 */\n    addStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.min(this._stackCount + count, this._maxStack);\n        if (this._stackCount > oldStack) {\n            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 减少叠加层数 */\n    removeStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(this._stackCount - count, 0);\n        if (this._stackCount < oldStack) {\n            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 获取Buff的当前效果值 */\n    getEffectValue(effectType: string): number {\n        switch (effectType) {\n            case EBuffEffectType.damage_per_second:\n                return this._damagePerSecond * this._stackCount;\n            case EBuffEffectType.total_damage_remaining:\n                return this._damagePerSecond * this._stackCount * this._remainingTime;\n            case EBuffEffectType.healing_reduction:\n                return this._healingReduction * this._stackCount;\n            default:\n                return 0;\n        }\n    }\n\n    /** 检查Buff是否与另一个Buff冲突 */\n    conflictsWith(_otherBuff: IBuff): boolean {\n        // 毒素buff可以与其他毒素叠加，不冲突\n        return false;\n    }\n\n    /** 检查是否可以被净化 */\n    canBeDispelled(): boolean {\n        return true; // 毒素可以被净化技能移除\n    }\n\n    /** 播放应用特效 */\n    private playApplyEffect(): void {\n        console.log(`Playing poison effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的中毒特效播放逻辑\n    }\n\n    /** 播放伤害特效 */\n    private playDamageEffect(): void {\n        console.log(`Playing poison damage effect on ${this._target.characterName}`);\n        // 这里应该实现毒素伤害的视觉特效\n    }\n\n    /** 停止特效 */\n    private stopEffect(): void {\n        console.log(`Stopping poison effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效停止逻辑\n    }\n\n    /** 获取调试信息 */\n    getDebugInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            type: this._type,\n            duration: this._duration,\n            remainingTime: this._remainingTime,\n            stackCount: this._stackCount,\n            maxStack: this._maxStack,\n            damagePerSecond: this._damagePerSecond,\n            totalDamageRemaining: this.getEffectValue(EBuffEffectType.total_damage_remaining),\n            healingReduction: this.getEffectValue(EBuffEffectType.healing_reduction),\n            caster: this._caster.characterName,\n            target: this._target.characterName,\n            isExpired: this._isExpired\n        };\n    }\n}\n"]}