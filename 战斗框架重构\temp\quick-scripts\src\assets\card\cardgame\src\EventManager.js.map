{"version": 3, "sources": ["assets\\card\\cardgame\\src\\EventManager.ts"], "names": [], "mappings": ";;;;AACA,qDAAqD;AAErD,aAAa;AACb,iDAAiD;AACjD,iBAAiB;AACjB,iBAAiB;AACjB,gCAAgC;AAChC,iBAAiB;AACjB,8BAA8B;AAC9B,iBAAiB;AACjB,gCAAgC;AAChC,iBAAiB;AACjB,8BAA8B;AAE9B,iBAAiB;AACjB,0BAA0B;AAC1B,iBAAiB;AACjB,kCAAkC;AAClC,mBAAmB;AACnB,kCAAkC;AAClC,mBAAmB;AACnB,0CAA0C;AAC1C,mBAAmB;AACnB,sCAAsC;AACtC,iBAAiB;AACjB,kCAAkC;AAElC,mBAAmB;AACnB,qBAAqB;AACrB,oCAAoC;AACpC,mBAAmB;AACnB,kCAAkC;AAElC,iBAAiB;AACjB,mBAAmB;AACnB,8BAA8B;AAC9B,uBAAuB;AACvB,wCAAwC;AACxC,mBAAmB;AACnB,0CAA0C;AAC1C,mBAAmB;AACnB,wDAAwD;AAExD,yBAAyB;AACzB,4CAA4C;AAC5C,mBAAmB;AACnB,8CAA8C;AAC9C,mBAAmB;AACnB,4DAA4D;AAE5D,oBAAoB;AACpB,IAAI;AACJ,eAAe;AACf,6DAA6D;AAC7D,iBAAiB;AACjB,iBAAiB;AACjB,uBAAuB;AACvB,iBAAiB;AACjB,sBAAsB;AACtB,iBAAiB;AACjB,uBAAuB;AACvB,iBAAiB;AACjB,sBAAsB;AAEtB,mBAAmB;AACnB,mBAAmB;AACnB,yBAAyB;AACzB,mBAAmB;AACnB,wBAAwB;AAExB,iBAAiB;AACjB,uBAAuB;AACvB,2BAA2B;AAC3B,mBAAmB;AACnB,4BAA4B;AAC5B,mBAAmB;AACnB,mCAAmC;AAEnC,yBAAyB;AACzB,6BAA6B;AAC7B,mBAAmB;AACnB,8BAA8B;AAC9B,mBAAmB;AACnB,qCAAqC;AAErC,oBAAoB;AACpB,IAAI;AACJ,gBAAgB;AAChB,8DAA8D;AAC9D,IAAI;AAEJ,aAAa;AACb,iFAAiF", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n// import { Broadcast } from \"@judu233/cc-broadcast\";\r\n\r\n// /**消息名字 */\r\n// interface ITestKey extends broadcast.IMsgKey {\r\n//     /**流程部分 */\r\n//     /**战斗开始 */\r\n//     FightStart: `FightStart`,\r\n//     /**战斗结束 */\r\n//     FightOver: `FightOver`,\r\n//     /**回合开始 */\r\n//     RoundStart: `RoundStart`,\r\n//     /**回合结束 */\r\n//     RoundOver: `RoundOver`,\r\n    \r\n//     /**使用卡牌 */\r\n//     UseCard: `UseCard`,\r\n//     /**丢弃卡牌 */\r\n//     DiscardCard: `DiscardCard`,\r\n//     /**显示使用卡片 */\r\n//     ShowUseCard: `ShowUseCard`,\r\n//     /**显示丢弃卡片 */\r\n//     ShowDiscardCard: `ShowDiscardCard`,\r\n//     /**取消显示所有 */\r\n//     CancelShowAll: `CancelShowAll`,\r\n//     /**污染卡片 */\r\n//     PolluteCard: `PolluteCard`,\r\n\r\n//     /**战斗控制部分 */\r\n//     /**触发玩家攻击角色 */\r\n//     PlayerAttack: `PlayerAttack`,\r\n//     /**触发玩家技能 */\r\n//     PlayerSkill: `PlayerSkill`,\r\n\r\n//     /**属性监听 */\r\n//     /**刷新UI界面 */\r\n//     RefreshUI: `RefreshUI`,\r\n//     /**触发玩家卡牌血量增减 */\r\n//     PlayerHpChange: `PlayerHpChange`,\r\n//     /**玩家卡牌死亡 */\r\n//     PlayerCardDeath: `PlayerCardDeath`,\r\n//     /**玩家卡牌复活 */\r\n//     PlayerCardResurrection: `PlayerCardResurrection`,\r\n\r\n//     /**触发电脑敌人卡牌血量增减 */\r\n//     ComputerHpChange: `ComputerHpChange`,\r\n//     /**敌方卡牌死亡 */\r\n//     ComputerCardDeath: `ComputerCardDeath`,\r\n//     /**敌方卡牌复活 */\r\n//     ComputerCardResurrection: `ComputerCardResurrection`,\r\n\r\n//     /**技能·buff */\r\n// }\r\n// /**消息广播传参值*/\r\n// interface ITestValueType extends broadcast.IMsgValueType {\r\n//     /**流程部分 */\r\n//     /**战斗开始 */\r\n//     FightStart: any,\r\n//     /**战斗结束 */\r\n//     FightOver: any,\r\n//     /**回合开始 */\r\n//     RoundStart: any,\r\n//     /**回合结束 */\r\n//     RoundOver: any,\r\n\r\n//     /**战斗控制部分 */\r\n//     /**触发玩家战斗 */\r\n//     PlayerAttack: any,\r\n//     /**触发玩家技能 */\r\n//     PlayerSkill: any,\r\n\r\n//     /**属性监听 */\r\n//     /**触发玩家卡牌血量增减 */\r\n//     PlayerHpChange: any,\r\n//     /**玩家卡牌死亡 */\r\n//     PlayerCardDeath: any,\r\n//     /**玩家卡牌复活 */\r\n//     PlayerCardResurrection: any,\r\n\r\n//     /**触发电脑敌人卡牌血量增减 */\r\n//     ComputerHpChange: any,\r\n//     /**敌方卡牌死亡 */\r\n//     ComputerCardDeath: any,\r\n//     /**敌方卡牌复活 */\r\n//     ComputerCardResurrection: any,\r\n\r\n//     /**技能·buff */\r\n// }\r\n// /**消息回传传参值 */\r\n// interface ITestResultType extends broadcast.IMsgValueType {\r\n// }\r\n\r\n// /**事件管理 */\r\n// export const BRO = new Broadcast<ITestKey, ITestValueType, ITestResultType>();"]}