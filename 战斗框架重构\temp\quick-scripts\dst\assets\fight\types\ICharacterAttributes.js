
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/ICharacterAttributes.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b8929RbYgdEjoHxhSzw5Jhg', 'ICharacterAttributes');
// fight/types/ICharacterAttributes.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeModifierType = void 0;
/*** 属性修改器类型*/
var AttributeModifierType;
(function (AttributeModifierType) {
    /** 加法修改 */
    AttributeModifierType["ADD"] = "add";
    /** 乘法修改 */
    AttributeModifierType["MULTIPLY"] = "multiply";
    /** 百分比修改 */
    AttributeModifierType["PERCENTAGE"] = "percentage";
    /** 覆盖修改 */
    AttributeModifierType["OVERRIDE"] = "override";
})(AttributeModifierType = exports.AttributeModifierType || (exports.AttributeModifierType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXHR5cGVzXFxJQ2hhcmFjdGVyQXR0cmlidXRlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFvTEEsY0FBYztBQUNkLElBQVkscUJBU1g7QUFURCxXQUFZLHFCQUFxQjtJQUM3QixXQUFXO0lBQ1gsb0NBQVcsQ0FBQTtJQUNYLFdBQVc7SUFDWCw4Q0FBcUIsQ0FBQTtJQUNyQixZQUFZO0lBQ1osa0RBQXlCLENBQUE7SUFDekIsV0FBVztJQUNYLDhDQUFxQixDQUFBO0FBQ3pCLENBQUMsRUFUVyxxQkFBcUIsR0FBckIsNkJBQXFCLEtBQXJCLDZCQUFxQixRQVNoQyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxuLyoqKiDop5LoibLln7rnoYDlsZ7mgKfmjqXlj6MqL1xuZXhwb3J0IGludGVyZmFjZSBJQ2hhcmFjdGVyQXR0cmlidXRlcyB7XG4gICAgLyoqIOW9k+WJjeeUn+WRveWAvCAqL1xuICAgIHJlYWRvbmx5IGN1cnJlbnRIcDogbnVtYmVyO1xuICAgIC8qKiDmnIDlpKfnlJ/lkb3lgLwgKi9cbiAgICByZWFkb25seSBtYXhIcDogbnVtYmVyO1xuICAgIC8qKiDlvZPliY3prZTms5XlgLwgKi9cbiAgICByZWFkb25seSBjdXJyZW50TXA6IG51bWJlcjtcbiAgICAvKiog5pyA5aSn6a2U5rOV5YC8ICovXG4gICAgcmVhZG9ubHkgbWF4TXA6IG51bWJlcjtcbiAgICAvKiog5b2T5YmN6ICQ5YqbICovXG4gICAgcmVhZG9ubHkgY3VycmVudFN0YW1pbmE6IG51bWJlcjtcbiAgICAvKiog5pyA5aSn6ICQ5YqbICovXG4gICAgcmVhZG9ubHkgbWF4U3RhbWluYTogbnVtYmVyO1xuICAgIC8qKiDmlLvlh7vlipsgKi9cbiAgICByZWFkb25seSBhdHRhY2s6IG51bWJlcjtcbiAgICAvKiog6a2U5rOV5pS75Ye75YqbICovXG4gICAgcmVhZG9ubHkgbWFnaWNBdHRhY2s6IG51bWJlcjtcbiAgICAvKiog6Ziy5b6h5YqbICovXG4gICAgcmVhZG9ubHkgZGVmZW5zZTogbnVtYmVyO1xuICAgIC8qKiDprZTms5XpmLLlvqHlipsgKi9cbiAgICByZWFkb25seSBtYWdpY0RlZmVuc2U6IG51bWJlcjtcbiAgICAvKiog5pS75Ye76YCf5bqmICovXG4gICAgcmVhZG9ubHkgYXR0YWNrU3BlZWQ6IG51bWJlcjtcbiAgICAvKiog56e75Yqo6YCf5bqmICovXG4gICAgcmVhZG9ubHkgbW92ZVNwZWVkOiBudW1iZXI7XG4gICAgLyoqIOaUu+WHu+iMg+WbtCAqL1xuICAgIHJlYWRvbmx5IGF0dGFja1JhbmdlOiBudW1iZXI7XG4gICAgLyoqIOaatOWHu+eOhyAqL1xuICAgIHJlYWRvbmx5IGNyaXRpY2FsUmF0ZTogbnVtYmVyO1xuICAgIC8qKiDmmrTlh7vkvKTlrrMgKi9cbiAgICByZWFkb25seSBjcml0aWNhbERhbWFnZTogbnVtYmVyO1xuICAgIC8qKiDlkb3kuK3njocgKi9cbiAgICByZWFkb25seSBhY2N1cmFjeTogbnVtYmVyO1xuICAgIC8qKiDpl6rpgb/njocgKi9cbiAgICByZWFkb25seSBldmFzaW9uOiBudW1iZXI7XG4gICAgLyoqIOmAn+W6piAqL1xuICAgIHJlYWRvbmx5IHNwZWVkOiBudW1iZXI7XG4gICAgLyoqIOetiee6pyAqL1xuICAgIHJlYWRvbmx5IGxldmVsOiBudW1iZXI7XG4gICAgLyoqXG4gICAgICog5L+u5pS555Sf5ZG95YC8XG4gICAgICogQHBhcmFtIGFtb3VudCDkv67mlLnph4/vvIjmraPmlbDkuLrmsrvnlpfvvIzotJ/mlbDkuLrkvKTlrrPvvIlcbiAgICAgKi9cbiAgICBtb2RpZnlIcChhbW91bnQ6IG51bWJlcik6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog6I635Y+W55Sf5ZG95YC855m+5YiG5q+UXG4gICAgICogQHJldHVybnMg55Sf5ZG95YC855m+5YiG5q+UICgwLTEpXG4gICAgICovXG4gICAgZ2V0SHBQZXJjZW50YWdlKCk6IG51bWJlcjtcbiAgICAvKipcbiAgICAgKiDmmK/lkKbmrbvkuqFcbiAgICAgKiBAcmV0dXJucyDmmK/lkKbmrbvkuqFcbiAgICAgKi9cbiAgICBpc0RlYWQoKTogYm9vbGVhbjtcbiAgICAvKioqIOmHjee9ruWIsOa7oeihgOeKtuaAgSovXG4gICAgcmVzZXRUb0Z1bGwoKTogdm9pZDtcbiAgICAvKipcbiAgICAgKiDmt7vliqDlsZ7mgKfkv67mlLnlmahcbiAgICAgKiBAcGFyYW0gbW9kaWZpZXIg5bGe5oCn5L+u5pS55ZmoXG4gICAgICovXG4gICAgYWRkTW9kaWZpZXIobW9kaWZpZXI6IElBdHRyaWJ1dGVNb2RpZmllcik6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog56e76Zmk5bGe5oCn5L+u5pS55ZmoXG4gICAgICogQHBhcmFtIG1vZGlmaWVySWQg5L+u5pS55ZmoSURcbiAgICAgKi9cbiAgICByZW1vdmVNb2RpZmllcihtb2RpZmllcklkOiBzdHJpbmcpOiB2b2lkO1xuICAgIC8qKlxuICAgICAqIOiOt+WPluW9k+WJjeWxnuaAp+WAvFxuICAgICAqIEBwYXJhbSBhdHRyaWJ1dGVOYW1lIOWxnuaAp+WQjeensFxuICAgICAqIEByZXR1cm5zIOWxnuaAp+WAvFxuICAgICAqL1xuICAgIGdldEN1cnJlbnRBdHRyaWJ1dGVWYWx1ZShhdHRyaWJ1dGVOYW1lOiBzdHJpbmcpOiBudW1iZXI7XG4gICAgLyoqXG4gICAgICog6K6+572u5bGe5oCn5YC877yI55So5LqO5L+u5pS55Zmo77yJXG4gICAgICogQHBhcmFtIGF0dHJpYnV0ZU5hbWUg5bGe5oCn5ZCN56ewXG4gICAgICogQHBhcmFtIHZhbHVlIOWxnuaAp+WAvFxuICAgICAqL1xuICAgIHNldEF0dHJpYnV0ZVZhbHVlKGF0dHJpYnV0ZU5hbWU6IHN0cmluZywgdmFsdWU6IG51bWJlcik6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog6I635Y+W5bGe5oCn5pWw5o2u55qE5Ymv5pysXG4gICAgICogQHJldHVybnMg5bGe5oCn5pWw5o2u5Ymv5pysXG4gICAgICovXG4gICAgZ2V0QXR0cmlidXRlRGF0YSgpOiBhbnk7XG4gICAgLyoqXG4gICAgICog5raI6ICX6a2U5rOV5YC8XG4gICAgICogQHBhcmFtIGFtb3VudCDmtojogJfph49cbiAgICAgKi9cbiAgICBjb25zdW1lTXAoYW1vdW50OiBudW1iZXIpOiB2b2lkO1xuICAgIC8qKlxuICAgICAqIOa2iOiAl+iAkOWKm1xuICAgICAqIEBwYXJhbSBhbW91bnQg5raI6ICX6YePXG4gICAgICovXG4gICAgY29uc3VtZVN0YW1pbmEoYW1vdW50OiBudW1iZXIpOiB2b2lkO1xuICAgIC8qKlxuICAgICAqIOmAoOaIkOS8pOWus1xuICAgICAqIEBwYXJhbSBhbW91bnQg5Lyk5a6z6YePXG4gICAgICovXG4gICAgdGFrZURhbWFnZShhbW91bnQ6IG51bWJlcik6IHZvaWQ7XG59XG5cbi8qKiog6KeS6Imy6LWE5rqQ5o6l5Y+j77yITVDjgIHogJDlipvnrYnvvIkqL1xuZXhwb3J0IGludGVyZmFjZSBJQ2hhcmFjdGVyUmVzb3VyY2Uge1xuICAgIC8qKiDlvZPliY3prZTms5XlgLwgKi9cbiAgICByZWFkb25seSBjdXJyZW50TXA6IG51bWJlcjtcbiAgICAvKiog5pyA5aSn6a2U5rOV5YC8ICovXG4gICAgcmVhZG9ubHkgbWF4TXA6IG51bWJlcjtcbiAgICAvKiog5b2T5YmN6ICQ5YqbICovXG4gICAgcmVhZG9ubHkgY3VycmVudFN0YW1pbmE6IG51bWJlcjtcbiAgICAvKiog5pyA5aSn6ICQ5YqbICovXG4gICAgcmVhZG9ubHkgbWF4U3RhbWluYTogbnVtYmVyO1xuICAgIC8qKlxuICAgICAqIOS/ruaUuemtlOazleWAvFxuICAgICAqIEBwYXJhbSBhbW91bnQg5L+u5pS56YePXG4gICAgICovXG4gICAgbW9kaWZ5TXAoYW1vdW50OiBudW1iZXIpOiB2b2lkO1xuICAgIC8qKlxuICAgICAqIOS/ruaUueiAkOWKm1xuICAgICAqIEBwYXJhbSBhbW91bnQg5L+u5pS56YePXG4gICAgICovXG4gICAgbW9kaWZ5U3RhbWluYShhbW91bnQ6IG51bWJlcik6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog5qOA5p+l5piv5ZCm5pyJ6Laz5aSf55qE6a2U5rOV5YC8XG4gICAgICogQHBhcmFtIGFtb3VudCDpnIDopoHnmoTprZTms5XlgLxcbiAgICAgKiBAcmV0dXJucyDmmK/lkKbotrPlpJ9cbiAgICAgKi9cbiAgICBoYXNFbm91Z2hNcChhbW91bnQ6IG51bWJlcik6IGJvb2xlYW47XG4gICAgLyoqXG4gICAgICog5qOA5p+l5piv5ZCm5pyJ6Laz5aSf55qE6ICQ5YqbXG4gICAgICogQHBhcmFtIGFtb3VudCDpnIDopoHnmoTogJDliptcbiAgICAgKiBAcmV0dXJucyDmmK/lkKbotrPlpJ9cbiAgICAgKi9cbiAgICBoYXNFbm91Z2hTdGFtaW5hKGFtb3VudDogbnVtYmVyKTogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiDojrflj5bprZTms5XlgLznmb7liIbmr5RcbiAgICAgKiBAcmV0dXJucyDprZTms5XlgLznmb7liIbmr5QgKDAtMSlcbiAgICAgKi9cbiAgICBnZXRNcFBlcmNlbnRhZ2UoKTogbnVtYmVyO1xuICAgIC8qKlxuICAgICAqIOiOt+WPluiAkOWKm+eZvuWIhuavlFxuICAgICAqIEByZXR1cm5zIOiAkOWKm+eZvuWIhuavlCAoMC0xKVxuICAgICAqL1xuICAgIGdldFN0YW1pbmFQZXJjZW50YWdlKCk6IG51bWJlcjtcbiAgICAvKiogICog6YeN572u6LWE5rqQ5Yiw5ruh5YC854q25oCBICAqL1xuICAgIHJlc2V0UmVzb3VyY2VzVG9GdWxsKCk6IHZvaWQ7XG59XG5cbi8qKiAqIOinkuiJsuWxnuaAp+S/ruaUueWZqOaOpeWPoyAqL1xuZXhwb3J0IGludGVyZmFjZSBJQXR0cmlidXRlTW9kaWZpZXIge1xuICAgIC8qKiDkv67mlLnlmahJRCAqL1xuICAgIHJlYWRvbmx5IGlkOiBzdHJpbmc7XG4gICAgLyoqIOS/ruaUueWZqOWQjeensCAqL1xuICAgIHJlYWRvbmx5IG5hbWU6IHN0cmluZztcbiAgICAvKiog5L+u5pS55Zmo57G75Z6LICovXG4gICAgcmVhZG9ubHkgdHlwZTogQXR0cmlidXRlTW9kaWZpZXJUeXBlO1xuICAgIC8qKiDkv67mlLnlgLwgKi9cbiAgICByZWFkb25seSB2YWx1ZTogbnVtYmVyO1xuICAgIC8qKiDmjIHnu63ml7bpl7TvvIjnp5LvvIwtMeihqOekuuawuOS5he+8iSAqL1xuICAgIHJlYWRvbmx5IGR1cmF0aW9uOiBudW1iZXI7XG4gICAgLyoqIOWJqeS9meaXtumXtCAqL1xuICAgIHJlbWFpbmluZ1RpbWU6IG51bWJlcjtcbiAgICAvKipcbiAgICAgKiDlupTnlKjkv67mlLnlmajliLDlsZ7mgKdcbiAgICAgKiBAcGFyYW0gYXR0cmlidXRlcyDnm67moIflsZ7mgKdcbiAgICAgKi9cbiAgICBhcHBseShhdHRyaWJ1dGVzOiBJQ2hhcmFjdGVyQXR0cmlidXRlcyk6IHZvaWQ7XG4gICAgLyoqXG4gICAgICog56e76Zmk5L+u5pS55Zmo5pWI5p6cXG4gICAgICogQHBhcmFtIGF0dHJpYnV0ZXMg55uu5qCH5bGe5oCnXG4gICAgICovXG4gICAgcmVtb3ZlKGF0dHJpYnV0ZXM6IElDaGFyYWN0ZXJBdHRyaWJ1dGVzKTogdm9pZDtcbiAgICAvKipcbiAgICAgKiDmm7TmlrDkv67mlLnlmahcbiAgICAgKiBAcGFyYW0gZGVsdGFUaW1lIOaXtumXtOmXtOmalFxuICAgICAqIEByZXR1cm5zIOaYr+WQpuW3sui/h+acn1xuICAgICAqL1xuICAgIHVwZGF0ZShkZWx0YVRpbWU6IG51bWJlcik6IGJvb2xlYW47XG59XG5cbi8qKiog5bGe5oCn5L+u5pS55Zmo57G75Z6LKi9cbmV4cG9ydCBlbnVtIEF0dHJpYnV0ZU1vZGlmaWVyVHlwZSB7XG4gICAgLyoqIOWKoOazleS/ruaUuSAqL1xuICAgIEFERCA9IFwiYWRkXCIsXG4gICAgLyoqIOS5mOazleS/ruaUuSAqL1xuICAgIE1VTFRJUExZID0gXCJtdWx0aXBseVwiLFxuICAgIC8qKiDnmb7liIbmr5Tkv67mlLkgKi9cbiAgICBQRVJDRU5UQUdFID0gXCJwZXJjZW50YWdlXCIsXG4gICAgLyoqIOimhuebluS/ruaUuSAqL1xuICAgIE9WRVJSSURFID0gXCJvdmVycmlkZVwiXG59XG5cbi8qKiog5bGe5oCn5Y+Y5YyW5LqL5Lu2Ki9cbmV4cG9ydCBpbnRlcmZhY2UgSUF0dHJpYnV0ZUNoYW5nZUV2ZW50IHtcbiAgICAvKiog5bGe5oCn5ZCN56ewICovXG4gICAgYXR0cmlidXRlTmFtZTogc3RyaW5nO1xuICAgIC8qKiDml6flgLwgKi9cbiAgICBvbGRWYWx1ZTogbnVtYmVyO1xuICAgIC8qKiDmlrDlgLwgKi9cbiAgICBuZXdWYWx1ZTogbnVtYmVyO1xuICAgIC8qKiDlj5jljJbph48gKi9cbiAgICBkZWx0YTogbnVtYmVyO1xuICAgIC8qKiDlj5jljJbljp/lm6AgKi9cbiAgICByZWFzb24/OiBzdHJpbmc7XG59XG4iXX0=