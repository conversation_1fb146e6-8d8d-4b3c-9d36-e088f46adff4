{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\CardModel.ts"], "names": [], "mappings": ";;;;AAAA,0DAA0D;AAE1D,mBAAmB;AACnB,6BAA6B;AAC7B,mBAAmB;AACnB,wBAAwB;AACxB,mBAAmB;AACnB,yBAAyB;AACzB,mBAAmB;AACnB,4BAA4B;AAC5B,mBAAmB;AACnB,wBAAwB;AACxB,QAAQ;AACR,IAAI;AACJ,8CAA8C;AAC9C,2BAA2B;AAC3B,uBAAuB;AACvB,wBAAwB;AACxB,2BAA2B;AAC3B,sBAAsB;AACtB,uCAAuC;AACvC,yBAAyB;AACzB,yBAAyB;AACzB,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["// import { desLoadDataProxy } from \"@judu233/cc-vm-core\";\r\n\r\n// declare global {\r\n//     interface ICardModel {\r\n//         /**心动 */\r\n//         hert: number;\r\n//         /**金钱 */\r\n//         money: number;\r\n//         /**体质 */\r\n//         physical: number;\r\n//         /**颜值 */\r\n//         face: number;\r\n//     }\r\n// }\r\n// @desLoadDataProxy('CardModel', 'CardModel')\r\n// export class CardModel {\r\n//     static hert: 10;\r\n//     static money: 10;\r\n//     static physical: 10;\r\n//     static face: 1;\r\n//     static data: IEventBar[][] = [];\r\n//     static curDay = 1;\r\n//     static curTab = 0;\r\n// }"]}