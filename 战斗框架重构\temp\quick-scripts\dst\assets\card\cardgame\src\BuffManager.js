
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/BuffManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0aad5w41glC14IryJJXS7wZ', 'BuffManager');
// card/cardgame/src/BuffManager.ts

// import Base from "./Base/Base";
// import BuffBase from "./Base/BuffBase";
// /**  
//     * @Title : buff管理器
//     * @Description : 该类又技能类管理 - 关系： 技能 <-> buff  =  一对多
//  **/
// export default class BuffManager extends Base {
//     /**所有Buff */
//     buff: Map<string, BuffBase> = new Map();
//     /**正在释放的buff */
//     releaseBuffMap: Map<string, BuffBase> = new Map();
//     /**已失效Buff */
//     failureBuffMap: Map<string, BuffBase> = new Map();
//     /**初始化buff管理 */
//     initBuffManager(buffList: BuffBase[]) {
//         buffList.forEach(buff => {
//             this.buff.set(buff.data.id, buff);
//         });
//     }
//     /**使用buff */
//     useBuff(buff: BuffBase) {
//         buff.useBuff();
//         this.releaseBuffMap.set(buff.data.id, buff);
//     }
//     /**使用完毕，移除Buff */
//     removeBuff(buff: BuffBase) {
//         if (this.releaseBuffMap.delete(buff.data.id))
//             this.failureBuffMap.set(buff.data.id, buff);
//         else
//             cc.warn(`${this.className}移除Buff失败，未找到id:${buff.data.id}, name:${buff.data.name}的buff`);
//     }
//     /**叠加buff */
//     overlayBuff(buff: BuffBase) { }
//     /**释放buff */
//     releaseBuff(buff: BuffBase) {
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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