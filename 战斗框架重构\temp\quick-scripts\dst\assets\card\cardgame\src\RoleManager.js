
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/RoleManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1a7deAaKZRK5qoCCTbr9YWd', 'RoleManager');
// card/cardgame/src/RoleManager.ts

// import Base from "./Base/Base";
// import RoleBase from "./Base/RoleBase";
// const { ccclass, property } = cc._decorator;
// declare global {
//     export interface IRoleManager {
//     }
// }
// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class RoleManager extends Base {
//     data: IRoleManager;
//     /**所有的角色列表 */
//     roles: RoleBase[] = [];
//     initRoles(datas: ICampMgrDataType) {
//         for (let roleData of datas.roleData) {
//             let role = new RoleBase();
//             role.initRole(roleData);
//             role.cardMgr.initCardList(datas)
//             this.roles.push(role);
//         }
//     }
//     deleateRole(role: RoleBase) {
//         if (role && cc.isValid(role)) {
//             cc.log(`删除:roleName:${role.roleName}, gui:${role.GID}`)
//             this.roles.delete(role)
//         }
//     }
//     /**检查所有角色是否死亡 */
//     checkCardAllDeath() {
//         return this.roles.every(role => role.isDeath)
//     }
//     /**检查所有角色是否活着 */
//     checkCardAllSurvive() {
//         return this.roles.every(role => !role.isDeath)
//     }
//     /**获取1位角色的随机卡片 */
//     getRoleCard(count: number) {
//         return this.roles[0]?.cardMgr.getSurviveCardForRandom(count)
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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