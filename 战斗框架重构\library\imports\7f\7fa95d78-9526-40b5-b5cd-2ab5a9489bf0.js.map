{"version": 3, "sources": ["assets\\fight\\types\\CharacterTypes.ts"], "names": [], "mappings": ";;;;;;;AACA,aAAa;AACb,IAAY,aAaX;AAbD,WAAY,aAAa;IACrB,aAAa;IACb,wDAAY,CAAA;IACZ,YAAY;IACZ,iDAAQ,CAAA;IACR,SAAS;IACT,mDAAS,CAAA;IACT,aAAa;IACb,qDAAU,CAAA;IACV,YAAY;IACZ,uDAAW,CAAA;IACX,SAAS;IACT,uDAAW,CAAA;AACf,CAAC,EAbW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAaxB;AACD,aAAa;AACb,IAAY,cAmBX;AAnBD,WAAY,cAAc;IACtB,SAAS;IACT,+BAAa,CAAA;IACb,SAAS;IACT,mCAAiB,CAAA;IACjB,SAAS;IACT,yCAAuB,CAAA;IACvB,WAAW;IACX,qCAAmB,CAAA;IACnB,SAAS;IACT,+BAAa,CAAA;IACb,SAAS;IACT,+BAAa,CAAA;IACb,SAAS;IACT,qCAAmB,CAAA;IACnB,SAAS;IACT,uCAAqB,CAAA;IACrB,SAAS;IACT,2CAAyB,CAAA;AAC7B,CAAC,EAnBW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAmBzB;AACD,eAAe;AACf,IAAY,kBAOX;AAPD,WAAY,kBAAkB;IAC1B,UAAU;IACV,2DAAQ,CAAA;IACR,WAAW;IACX,+DAAU,CAAA;IACV,WAAW;IACX,iEAAW,CAAA;AACf,CAAC,EAPW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAO7B;AAuDD;IAAA;IAuCA,CAAC;IAtCG,UAAU;IACH,yBAAE,GAAG,IAAI,CAAA;IAChB,YAAY;IACL,4BAAK,GAAG,OAAO,CAAA;IACtB,UAAU;IACH,yBAAE,GAAG,IAAI,CAAA;IAChB,YAAY;IACL,4BAAK,GAAG,OAAO,CAAA;IACtB,WAAW;IACJ,iCAAU,GAAG,YAAY,CAAA;IAChC,UAAU;IACH,6BAAM,GAAG,QAAQ,CAAA;IACxB,UAAU;IACH,8BAAO,GAAG,SAAS,CAAA;IAC1B,WAAW;IACJ,kCAAW,GAAG,aAAa,CAAA;IAClC,WAAW;IACJ,gCAAS,GAAG,WAAW,CAAA;IAC9B,WAAW;IACJ,kCAAW,GAAG,aAAa,CAAA;IAClC,UAAU;IACH,mCAAY,GAAG,cAAc,CAAA;IACpC,WAAW;IACJ,qCAAc,GAAG,gBAAgB,CAAA;IACxC,UAAU;IACH,8BAAO,GAAG,SAAS,CAAA;IAC1B,UAAU;IACH,gCAAS,GAAG,WAAW,CAAA;IAC9B,SAAS;IACF,4BAAK,GAAG,OAAO,CAAA;IACtB,UAAU;IACH,iCAAU,GAAG,YAAY,CAAA;IAChC,UAAU;IACH,gCAAS,GAAG,WAAW,CAAA;IAC9B,UAAU;IACH,gCAAS,GAAG,WAAW,CAAA;IAC9B,SAAS;IACF,qCAAc,GAAG,gBAAgB,CAAA;IAC5C,6BAAC;CAvCD,AAuCC,IAAA;AAvCY,wDAAsB;AAmHnC,eAAe;AACf,IAAY,WASX;AATD,WAAY,WAAW;IACnB,WAAW;IACX,4BAAa,CAAA;IACb,WAAW;IACX,gCAAiB,CAAA;IACjB,WAAW;IACX,gCAAiB,CAAA;IACjB,WAAW;IACX,oCAAqB,CAAA;AACzB,CAAC,EATW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAStB", "file": "", "sourceRoot": "/", "sourcesContent": ["\n/*** 角色阵营枚举*/\nexport enum CharacterRole {\n    /** 无效/未定义 */\n    INVALID = -1,\n    /** 英雄/玩家 */\n    HERO = 0,\n    /** 敌人 */\n    ENEMY = 1,\n    /** 子弹/投射物 */\n    BULLET = 2,\n    /** 伙伴/宠物 */\n    PARTNER = 3,\n    /** 中立 */\n    NEUTRAL = 4\n}\n/*** 角色状态枚举*/\nexport enum CharacterState {\n    /** 空闲 */\n    IDLE = \"idle\",\n    /** 移动 */\n    MOVING = \"moving\",\n    /** 攻击 */\n    ATTACKING = \"attacking\",\n    /** 释放技能 */\n    CASTING = \"casting\",\n    /** 受伤 */\n    HURT = \"hurt\",\n    /** 死亡 */\n    DEAD = \"dead\",\n    /** 眩晕 */\n    STUNNED = \"stunned\",\n    /** 沉默 */\n    SILENCED = \"silenced\",\n    /** 无敌 */\n    INVINCIBLE = \"invincible\"\n}\n/*** 角色选择标签枚举*/\nexport enum CharacterSelectTag {\n    /** 未选中 */\n    NONE = 0,\n    /** 玩家选中 */\n    PLAYER = 1,\n    /** 伙伴选中 */\n    PARTNER = 2\n}\n/*** 角色创建信息*/\nexport interface CharacterCreateInfo {\n    /** 预制体键值或节点 */\n    prefabKey: string | cc.Node;\n    /** 角色阵营 */\n    role: CharacterRole;\n    /** 角色名称 */\n    name: string;\n    /** 世界坐标位置 */\n    worldPosition?: cc.Vec3;\n    /** 父节点 */\n    parent?: cc.Node;\n    /** 初始属性 */\n    initialAttributes?: Partial<CharacterAttributeData>;\n    /** 初始技能列表 */\n    initialSkills?: string[];\n    /** 初始Buff列表 */\n    initialBuffs?: string[];\n}\n/** * 角色属性数据 */\nexport interface CharacterAttributeData {\n    /** 生命值 */\n    hp: number;\n    /** 最大生命值 */\n    maxHp: number;\n    /** 魔法值 */\n    mp: number;\n    /** 最大魔法值 */\n    maxMp: number;\n    /** 最大耐力 */\n    maxStamina: number;\n    /** 攻击力 */\n    attack: number;\n    /** 防御力 */\n    defense: number;\n    /** 攻击速度 */\n    attackSpeed: number;\n    /** 移动速度 */\n    moveSpeed: number;\n    /** 攻击范围 */\n    attackRange: number;\n    /** 暴击率 */\n    criticalRate: number;\n    /** 暴击伤害 */\n    criticalDamage: number;\n    /** 命中率 */\n    hitRate: number;\n    /** 闪避率 */\n    dodgeRate: number;\n    /** 等级 */\n    level: number;\n    /** 经验值 */\n    experience: number;\n}\nexport class CharacterAttributeName {\n    /** 生命值 */\n    static hp = 'hp'\n    /** 最大生命值 */\n    static maxHp = 'maxHp'\n    /** 魔法值 */\n    static mp = 'mp'\n    /** 最大魔法值 */\n    static maxMp = 'maxMp'\n    /** 最大耐力 */\n    static maxStamina = 'maxStamina'\n    /** 攻击力 */\n    static attack = 'attack'\n    /** 防御力 */\n    static defense = 'defense'\n    /** 攻击速度 */\n    static attackSpeed = 'attackSpeed'\n    /** 移动速度 */\n    static moveSpeed = 'moveSpeed'\n    /** 攻击范围 */\n    static attackRange = 'attackRange'\n    /** 暴击率 */\n    static criticalRate = 'criticalRate'\n    /** 暴击伤害 */\n    static criticalDamage = 'criticalDamage'\n    /** 命中率 */\n    static hitRate = 'hitRate'\n    /** 闪避率 */\n    static dodgeRate = 'dodgeRate'\n    /** 等级 */\n    static level = 'level'\n    /** 经验值 */\n    static experience = 'experience'\n    /**当前hp */\n    static currentHp = 'currentHp'\n    /**当前Mp */\n    static currentMp = 'currentMp'\n    /**当前耐力*/\n    static currentStamina = 'currentStamina'\n}\n/*** 角色资源数据*/\nexport interface CharacterResourceData {\n    /** 当前生命值 */\n    currentHp: number;\n    /** 当前魔法值 */\n    currentMp: number;\n    /** 当前耐力 */\n    currentStamina: number;\n    /** 最大耐力 */\n    maxStamina: number;\n}\n/*** 角色动画配置*/\nexport interface CharacterAnimationConfig {\n    /** 空闲动画 */\n    idle: string;\n    /** 移动动画 */\n    move: string;\n    /** 攻击动画 */\n    attack: string;\n    /** 受伤动画 */\n    hurt: string;\n    /** 死亡动画 */\n    death: string;\n    /** 技能动画映射 */\n    skills?: { [skillId: string]: string };\n}\n/** 角色音效配*/\nexport interface CharacterSoundConfig {\n    /** 攻击音效 */\n    attack?: string;\n    /** 受伤音效 */\n    hurt?: string;\n    /** 死亡音效 */\n    death?: string;\n    /** 技能音效映射 */\n    skills?: { [skillId: string]: string };\n}\n/*** 角色配置*/\nexport interface CharacterConfig {\n    /** 角色ID */\n    id: string;\n    /** 角色名称 */\n    name: string;\n    /** 角色描述 */\n    description: string;\n    /** 预制体路径 */\n    prefabPath: string;\n    /** 角色阵营 */\n    role: CharacterRole;\n    /** 基础属性 */\n    baseAttributes: CharacterAttributeData;\n    /** 动画配置 */\n    animations: CharacterAnimationConfig;\n    /** 音效配置 */\n    sounds: CharacterSoundConfig;\n    /** 默认技能列表 */\n    defaultSkills: string[];\n    /** 可学习技能列表 */\n    learnableSkills: string[];\n    /** 头像路径 */\n    avatarPath?: string;\n    /** 模型缩放 */\n    scale?: number;\n}\n/*** 角色事件数据*/\nexport interface CharacterEventData {\n    /** 事件类型 */\n    type: string;\n    /** 角色 */\n    character: any;\n    /** 额外数据 */\n    data?: any;\n    /** 时间戳 */\n    timestamp: number;\n}\n/** * 攻击状态枚举 */\nexport enum AttackState {\n    /** 空闲状态 */\n    IDLE = \"idle\",\n    /** 前摇阶段 */\n    WINDUP = \"windup\",\n    /** 伤害阶段 */\n    DAMAGE = \"damage\",\n    /** 后摇阶段 */\n    RECOVERY = \"recovery\"\n}\n"]}