"use strict";
cc._RF.push(module, 'e6db1uGotJItICfY8rXl8QC', 'TimelineManager');
// fight/systems/TimelineManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineManager = void 0;
var Timeline_1 = require("../timeline/Timeline");
var FightEvent_1 = require("../types/FightEvent");
var EventManager_1 = require("./EventManager");
/*** Timeline管理器实现*/
var TimelineManager = /** @class */ (function () {
    function TimelineManager() {
        this._activeTimelines = new Map();
        this._pausedTimelines = new Set();
        this._totalExecutedEvents = 0;
        this._isUpdating = false;
        this._pendingRemovals = [];
        this._eventManager = EventManager_1.EventManager.createLocal("TimelineManager");
    }
    Object.defineProperty(TimelineManager.prototype, "activeTimelines", {
        /** * 获取所有活跃的Timeline */
        get: function () {
            return Array.from(this._activeTimelines.values());
        },
        enumerable: false,
        configurable: true
    });
    /** * 添加Timeline */
    TimelineManager.prototype.addTimeline = function (timeline) {
        if (this._activeTimelines.has(timeline.id)) {
            console.warn("Timeline " + timeline.id + " already exists, replacing...");
        }
        this._activeTimelines.set(timeline.id, timeline);
        timeline.eventManager.on(FightEvent_1.default.completedT, this.onTimelineCompleted.bind(this));
        timeline.eventManager.on(FightEvent_1.default.stoppedT, this.onTimelineStopped.bind(this));
        console.log("Timeline " + timeline.id + " added to manager");
        this._eventManager.emit(FightEvent_1.default.timelineAdded, { timeline: timeline });
    };
    /*** 根据配置创建并添加Timeline*/
    TimelineManager.prototype.createTimeline = function (config, caster, target, targets, position) {
        var e_1, _a;
        var timeline = new Timeline_1.Timeline(config.id, config.name, config.duration, caster, target, targets, position);
        try {
            for (var _b = __values(config.nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var nodeConfig = _c.value;
                // 这里应该根据nodeConfig创建具体的TimelineNode， 现在先简化处理
                console.log("Creating timeline node: " + nodeConfig.id);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        this.addTimeline(timeline);
        return timeline;
    };
    /** * 移除Timeline */
    TimelineManager.prototype.removeTimeline = function (timelineId) {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(timelineId);
            return;
        }
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline) {
            timeline.stop();
            timeline.eventManager.off(FightEvent_1.default.completedT, this.onTimelineCompleted.bind(this));
            timeline.eventManager.off(FightEvent_1.default.stoppedT, this.onTimelineStopped.bind(this));
            // 从映射中移除
            this._activeTimelines.delete(timelineId);
            this._pausedTimelines.delete(timelineId);
            console.log("Timeline " + timelineId + " removed from manager");
            this._eventManager.emit(FightEvent_1.default.timelineRemoved, { timeline: timeline });
        }
    };
    /** * 根据施法者移除Timeline */
    TimelineManager.prototype.removeTimelinesByCaster = function (caster) {
        var e_2, _a, e_3, _b;
        var timelinesToRemove = [];
        try {
            for (var _c = __values(this._activeTimelines), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], timeline = _e[1];
                if (timeline.caster === caster) {
                    timelinesToRemove.push(id);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var timelinesToRemove_1 = __values(timelinesToRemove), timelinesToRemove_1_1 = timelinesToRemove_1.next(); !timelinesToRemove_1_1.done; timelinesToRemove_1_1 = timelinesToRemove_1.next()) {
                var id = timelinesToRemove_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (timelinesToRemove_1_1 && !timelinesToRemove_1_1.done && (_b = timelinesToRemove_1.return)) _b.call(timelinesToRemove_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        console.log("Removed " + timelinesToRemove.length + " timelines for caster " + caster.characterName);
    };
    /** * 暂停所有Timeline */
    TimelineManager.prototype.pauseAll = function () {
        var e_4, _a;
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (!timeline.isPaused) {
                    timeline.pause();
                    this._pausedTimelines.add(timeline.id);
                }
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        console.log("Paused " + this._pausedTimelines.size + " timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesPaused);
    };
    /** * 恢复所有Timeline */
    TimelineManager.prototype.resumeAll = function () {
        var e_5, _a;
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (timeline.isPaused) {
                    timeline.resume();
                    this._pausedTimelines.delete(timeline.id);
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log("Resumed all timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesResumed);
    };
    /*** 清除所有Timeline*/
    TimelineManager.prototype.clearAll = function () {
        var e_6, _a;
        var timelineIds = Array.from(this._activeTimelines.keys());
        try {
            for (var timelineIds_1 = __values(timelineIds), timelineIds_1_1 = timelineIds_1.next(); !timelineIds_1_1.done; timelineIds_1_1 = timelineIds_1.next()) {
                var id = timelineIds_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (timelineIds_1_1 && !timelineIds_1_1.done && (_a = timelineIds_1.return)) _a.call(timelineIds_1);
            }
            finally { if (e_6) throw e_6.error; }
        }
        this._pausedTimelines.clear();
        this._totalExecutedEvents = 0;
        console.log("Cleared all timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesCleared);
    };
    /** * 更新所有Timeline */
    TimelineManager.prototype.update = function (deltaTime) {
        var e_7, _a, e_8, _b, e_9, _c;
        if (this._activeTimelines.size === 0) {
            return;
        }
        this._isUpdating = true;
        var completedTimelines = [];
        try {
            // 更新所有Timeline
            for (var _d = __values(this._activeTimelines), _e = _d.next(); !_e.done; _e = _d.next()) {
                var _f = __read(_e.value, 2), id = _f[0], timeline = _f[1];
                if (!timeline.isPaused) {
                    var isCompleted = timeline.update(deltaTime);
                    if (isCompleted) {
                        completedTimelines.push(id);
                    }
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_7) throw e_7.error; }
        }
        this._isUpdating = false;
        try {
            // 移除已完成的Timeline
            for (var completedTimelines_1 = __values(completedTimelines), completedTimelines_1_1 = completedTimelines_1.next(); !completedTimelines_1_1.done; completedTimelines_1_1 = completedTimelines_1.next()) {
                var id = completedTimelines_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (completedTimelines_1_1 && !completedTimelines_1_1.done && (_b = completedTimelines_1.return)) _b.call(completedTimelines_1);
            }
            finally { if (e_8) throw e_8.error; }
        }
        // 处理延迟移除的Timeline
        if (this._pendingRemovals.length > 0) {
            try {
                for (var _g = __values(this._pendingRemovals), _h = _g.next(); !_h.done; _h = _g.next()) {
                    var id = _h.value;
                    this.removeTimeline(id);
                }
            }
            catch (e_9_1) { e_9 = { error: e_9_1 }; }
            finally {
                try {
                    if (_h && !_h.done && (_c = _g.return)) _c.call(_g);
                }
                finally { if (e_9) throw e_9.error; }
            }
            this._pendingRemovals.length = 0;
        }
    };
    /** * 获取Timeline统计信息 */
    TimelineManager.prototype.getStats = function () {
        return {
            activeCount: this._activeTimelines.size,
            pausedCount: this._pausedTimelines.size,
            totalExecutedEvents: this._totalExecutedEvents
        };
    };
    /** * 根据ID获取Timeline */
    TimelineManager.prototype.getTimeline = function (timelineId) {
        return this._activeTimelines.get(timelineId) || null;
    };
    /*** 根据施法者获取Timeline列表*/
    TimelineManager.prototype.getTimelinesByCaster = function (caster) {
        var e_10, _a;
        var timelines = [];
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (timeline.caster === caster) {
                    timelines.push(timeline);
                }
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return timelines;
    };
    /** * 暂停指定Timeline */
    TimelineManager.prototype.pauseTimeline = function (timelineId) {
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline && !timeline.isPaused) {
            timeline.pause();
            this._pausedTimelines.add(timelineId);
            console.log("Timeline " + timelineId + " paused");
            this._eventManager.emit(FightEvent_1.default.timelinePaused, { timeline: timeline });
        }
    };
    /**  * 恢复指定Timeline  */
    TimelineManager.prototype.resumeTimeline = function (timelineId) {
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline && timeline.isPaused) {
            timeline.resume();
            this._pausedTimelines.delete(timelineId);
            console.log("Timeline " + timelineId + " resumed");
            this._eventManager.emit(FightEvent_1.default.timelineResumed, { timeline: timeline });
        }
    };
    /*** Timeline完成时的回调*/
    TimelineManager.prototype.onTimelineCompleted = function (event) {
        var timeline = event.timeline;
        console.log("Timeline " + timeline.id + " completed");
        this._totalExecutedEvents++;
        this._eventManager.emit(FightEvent_1.default.timelineCompleted, { timeline: timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    };
    /*** Timeline停止时的回调*/
    TimelineManager.prototype.onTimelineStopped = function (event) {
        var timeline = event.timeline;
        console.log("Timeline " + timeline.id + " stopped");
        this._eventManager.emit(FightEvent_1.default.timelineStopped, { timeline: timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    };
    /*** 安排移除Timeline*/
    TimelineManager.prototype.scheduleRemoval = function (timelineId) {
        if (this._isUpdating) {
            this._pendingRemovals.push(timelineId);
        }
        else {
            this.removeTimeline(timelineId);
        }
    };
    Object.defineProperty(TimelineManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /*** 获取调试信息*/
    TimelineManager.prototype.getDebugInfo = function () {
        var e_11, _a;
        var _b;
        var timelines = {};
        try {
            for (var _c = __values(this._activeTimelines), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], timeline = _e[1];
                timelines[id] = {
                    name: timeline.name,
                    duration: timeline.duration,
                    timeElapsed: timeline.timeElapsed,
                    isCompleted: timeline.isCompleted,
                    isPaused: timeline.isPaused,
                    nodeCount: timeline.nodes.length,
                    caster: timeline.caster.characterName,
                    target: ((_b = timeline.target) === null || _b === void 0 ? void 0 : _b.characterName) || null
                };
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return {
            stats: this.getStats(),
            timelines: timelines,
            pendingRemovals: this._pendingRemovals.length
        };
    };
    /*** 打印调试信息*/
    TimelineManager.prototype.printDebugInfo = function () {
        console.log("TimelineManager Debug Info:", this.getDebugInfo());
    };
    /*** 清理管理器*/
    TimelineManager.prototype.cleanup = function () {
        this.clearAll();
        this._eventManager.cleanup();
    };
    return TimelineManager;
}());
exports.TimelineManager = TimelineManager;

cc._RF.pop();