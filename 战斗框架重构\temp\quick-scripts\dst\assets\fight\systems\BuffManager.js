
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/BuffManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8b935gc+zVD3Jtokzwxqrup', 'BuffManager');
// fight/systems/BuffManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuffManager = void 0;
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var EventManager_1 = require("./EventManager");
/*** Buff管理器类*/
var BuffManager = /** @class */ (function () {
    function BuffManager(character) {
        var e_1, _a;
        this._buffs = new Map();
        this._buffsByType = new Map();
        this._character = character;
        this._eventManager = EventManager_1.EventManager.createLocal("BuffManager_" + character.characterName);
        try {
            // 初始化按类型分组的映射
            for (var _b = __values(Object.values(IBuff_1.BuffType)), _c = _b.next(); !_c.done; _c = _b.next()) {
                var type = _c.value;
                this._buffsByType.set(type, new Set());
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    }
    Object.defineProperty(BuffManager.prototype, "buffs", {
        /** * 获取所有Buff */
        get: function () {
            return Array.from(this._buffs.values());
        },
        enumerable: false,
        configurable: true
    });
    /**
     * 添加Buff
     * @param buff Buff实例
     */
    BuffManager.prototype.addBuff = function (buff) {
        var _a;
        var existingBuff = this._buffs.get(buff.id);
        if (existingBuff) {
            // 如果Buff已存在，处理叠加逻辑
            this.handleBuffStack(existingBuff, buff);
        }
        else {
            // 添加新Buff
            this._buffs.set(buff.id, buff);
            (_a = this._buffsByType.get(buff.type)) === null || _a === void 0 ? void 0 : _a.add(buff.id);
            // 触发Buff应用效果
            buff.onApply();
            this._eventManager.emit(FightEvent_1.default.buffAdded, { buff: buff, character: this._character });
        }
    };
    /**
     * 移除Buff
     * @param buffId Buff ID
     * @returns 是否移除成功
     */
    BuffManager.prototype.removeBuff = function (buffId) {
        var _a;
        var buff = this._buffs.get(buffId);
        if (!buff) {
            return false;
        }
        // 触发Buff移除效果
        buff.onRemove();
        // 从映射中移除
        this._buffs.delete(buffId);
        (_a = this._buffsByType.get(buff.type)) === null || _a === void 0 ? void 0 : _a.delete(buffId);
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: buff, character: this._character });
        return true;
    };
    /**
     * 根据ID获取Buff
     * @param buffId Buff ID
     * @returns Buff实例或null
     */
    BuffManager.prototype.getBuff = function (buffId) {
        return this._buffs.get(buffId) || null;
    };
    /**
     * 根据类型获取Buff列表
     * @param type Buff类型
     * @returns Buff列表
     */
    BuffManager.prototype.getBuffsByType = function (type) {
        var e_2, _a;
        var buffIds = this._buffsByType.get(type);
        if (!buffIds) {
            return [];
        }
        var buffs = [];
        try {
            for (var buffIds_1 = __values(buffIds), buffIds_1_1 = buffIds_1.next(); !buffIds_1_1.done; buffIds_1_1 = buffIds_1.next()) {
                var buffId = buffIds_1_1.value;
                var buff = this._buffs.get(buffId);
                if (buff) {
                    buffs.push(buff);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (buffIds_1_1 && !buffIds_1_1.done && (_a = buffIds_1.return)) _a.call(buffIds_1);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return buffs;
    };
    /**
     * 检查是否拥有指定Buff
     * @param buffId Buff ID
     * @returns 是否拥有
     */
    BuffManager.prototype.hasBuff = function (buffId) {
        return this._buffs.has(buffId);
    };
    /**
     * 检查是否拥有指定类型的Buff
     * @param type Buff类型
     * @returns 是否拥有
     */
    BuffManager.prototype.hasBuffOfType = function (type) {
        var buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size > 0 : false;
    };
    /**
     * 获取指定类型的Buff数量
     * @param type Buff类型
     * @returns Buff数量
     */
    BuffManager.prototype.getBuffCountByType = function (type) {
        var buffIds = this._buffsByType.get(type);
        return buffIds ? buffIds.size : 0;
    };
    /** * 清除所有Buff */
    BuffManager.prototype.clearAllBuffs = function () {
        var e_3, _a;
        var buffsToRemove = Array.from(this._buffs.keys());
        try {
            for (var buffsToRemove_1 = __values(buffsToRemove), buffsToRemove_1_1 = buffsToRemove_1.next(); !buffsToRemove_1_1.done; buffsToRemove_1_1 = buffsToRemove_1.next()) {
                var buffId = buffsToRemove_1_1.value;
                this.removeBuff(buffId);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (buffsToRemove_1_1 && !buffsToRemove_1_1.done && (_a = buffsToRemove_1.return)) _a.call(buffsToRemove_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /**
     * 清除指定类型的Buff
     * @param type Buff类型
     */
    BuffManager.prototype.clearBuffsByType = function (type) {
        var e_4, _a;
        var buffIds = this._buffsByType.get(type);
        if (buffIds) {
            var idsToRemove = Array.from(buffIds);
            try {
                for (var idsToRemove_1 = __values(idsToRemove), idsToRemove_1_1 = idsToRemove_1.next(); !idsToRemove_1_1.done; idsToRemove_1_1 = idsToRemove_1.next()) {
                    var buffId = idsToRemove_1_1.value;
                    this.removeBuff(buffId);
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (idsToRemove_1_1 && !idsToRemove_1_1.done && (_a = idsToRemove_1.return)) _a.call(idsToRemove_1);
                }
                finally { if (e_4) throw e_4.error; }
            }
        }
    };
    /**
     * 刷新Buff持续时间
     * @param buffId Buff ID
     */
    BuffManager.prototype.refreshBuff = function (buffId) {
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.refresh();
            this._eventManager.emit(FightEvent_1.default.buffRefreshed, { buff: buff, character: this._character });
        }
    };
    /**
     * 增加Buff叠加层数
     * @param buffId Buff ID
     * @param count 增加的层数
     */
    BuffManager.prototype.addBuffStack = function (buffId, count) {
        if (count === void 0) { count = 1; }
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.addStack(count);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: buff, character: this._character });
        }
    };
    /**
     * 减少Buff叠加层数
     * @param buffId Buff ID
     * @param count 减少的层数
     */
    BuffManager.prototype.removeBuffStack = function (buffId, count) {
        if (count === void 0) { count = 1; }
        var buff = this._buffs.get(buffId);
        if (buff) {
            buff.removeStack(count);
            // 如果叠加层数为0，移除Buff
            if (buff.stackCount <= 0) {
                this.removeBuff(buffId);
            }
            else {
                this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: buff, character: this._character });
            }
        }
    };
    /**
     * 更新所有Buff
     * @param deltaTime 时间间隔
     */
    BuffManager.prototype.update = function (deltaTime) {
        var e_5, _a, e_6, _b;
        var expiredBuffs = [];
        try {
            for (var _c = __values(this._buffs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), buffId = _e[0], buff = _e[1];
                // 更新Buff状态
                var isExpired = buff.update(deltaTime);
                if (isExpired) {
                    expiredBuffs.push(buffId);
                }
                else {
                    // 触发Buff每帧效果
                    buff.onTick(deltaTime);
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            // 移除过期的Buff
            for (var expiredBuffs_1 = __values(expiredBuffs), expiredBuffs_1_1 = expiredBuffs_1.next(); !expiredBuffs_1_1.done; expiredBuffs_1_1 = expiredBuffs_1.next()) {
                var buffId = expiredBuffs_1_1.value;
                this.removeBuff(buffId);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (expiredBuffs_1_1 && !expiredBuffs_1_1.done && (_b = expiredBuffs_1.return)) _b.call(expiredBuffs_1);
            }
            finally { if (e_6) throw e_6.error; }
        }
    };
    /**
     * 处理Buff叠加逻辑
     * @param existingBuff 已存在的Buff
     * @param newBuff 新的Buff
     */
    BuffManager.prototype.handleBuffStack = function (existingBuff, newBuff) {
        if (existingBuff.maxStack > 1) {
            // 可以叠加
            existingBuff.addStack(newBuff.stackCount);
            existingBuff.refresh(); // 刷新持续时间
            this._eventManager.emit(FightEvent_1.default.buffStacked, {
                buff: existingBuff,
                character: this._character,
                addedStacks: newBuff.stackCount
            });
        }
        else {
            // 不能叠加，刷新持续时间
            existingBuff.refresh();
            this._eventManager.emit(FightEvent_1.default.buffRefreshed, {
                buff: existingBuff,
                character: this._character
            });
        }
    };
    /**
     * 获取所有Buff的ID列表
     * @returns Buff ID数组
     */
    BuffManager.prototype.getAllBuffIds = function () {
        return Array.from(this._buffs.keys());
    };
    /**
     * 获取所有Buff列表
     * @returns Buff数组
     */
    BuffManager.prototype.getAllBuffs = function () {
        return Array.from(this._buffs.values());
    };
    /**
     * 获取Buff统计信息
     * @returns 统计信息
     */
    BuffManager.prototype.getBuffStats = function () {
        var e_7, _a;
        var stats = {
            totalBuffs: this._buffs.size,
            buffsByType: {},
            expiredBuffs: 0,
            stackableBuffs: 0
        };
        try {
            for (var _b = __values(this._buffs.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (!stats.buffsByType[buff.type]) {
                    stats.buffsByType[buff.type] = 0;
                }
                stats.buffsByType[buff.type]++;
                if (buff.isExpired) {
                    stats.expiredBuffs++;
                }
                if (buff.maxStack > 1) {
                    stats.stackableBuffs++;
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return stats;
    };
    /**
     * 导出Buff数据
     * @returns Buff数据
     */
    BuffManager.prototype.exportBuffData = function () {
        var e_8, _a;
        var data = {};
        try {
            for (var _b = __values(this._buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), id = _d[0], buff = _d[1];
                data[id] = {
                    id: buff.id,
                    name: buff.name,
                    type: buff.type,
                    duration: buff.duration,
                    remainingTime: buff.remainingTime,
                    stackCount: buff.stackCount,
                    maxStack: buff.maxStack,
                    isExpired: buff.isExpired
                };
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return data;
    };
    Object.defineProperty(BuffManager.prototype, "eventManager", {
        /**
         * 获取事件管理器（供外部直接使用，避免包装方法冗余）
         */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理Buff管理器 */
    BuffManager.prototype.cleanup = function () {
        this.clearAllBuffs();
        this._buffsByType.clear();
        this._eventManager.cleanup();
    };
    /** * 获取调试信息 */
    BuffManager.prototype.getDebugInfo = function () {
        return {
            characterId: this._character.id,
            buffCount: this._buffs.size,
            buffs: this.exportBuffData(),
            stats: this.getBuffStats()
        };
    };
    return BuffManager;
}());
exports.BuffManager = BuffManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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