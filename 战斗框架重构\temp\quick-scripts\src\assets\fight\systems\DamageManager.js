"use strict";
cc._RF.push(module, 'bc5fegInK5EULVPX5PRYkTd', 'DamageManager');
// fight/systems/DamageManager.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageManager = void 0;
var IDamage_1 = require("../types/IDamage");
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
/**
 * 简化的伤害信息实现
 */
var DamageInfo = /** @class */ (function () {
    function DamageInfo(attacker, target, baseDamage, damageType, tags) {
        if (tags === void 0) { tags = []; }
        this.isCritical = false;
        this.criticalMultiplier = 1.0;
        this.damageReduction = 0;
        this.damageAmplification = 0;
        this.attachedBuffs = [];
        this.isDodged = false;
        this.id = "damage_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this.attacker = attacker;
        this.target = target;
        this.baseDamage = baseDamage;
        this._finalDamage = baseDamage;
        this.damageType = damageType;
        this.tags = __spread(tags);
    }
    Object.defineProperty(DamageInfo.prototype, "finalDamage", {
        get: function () { return this._finalDamage; },
        set: function (value) { this._finalDamage = value; },
        enumerable: false,
        configurable: true
    });
    DamageInfo.prototype.calculateFinalDamage = function () {
        return this._finalDamage;
    };
    DamageInfo.prototype.addTag = function (tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    };
    DamageInfo.prototype.hasTag = function (tag) {
        return this.tags.includes(tag);
    };
    DamageInfo.prototype.addAttachedBuff = function (buff) {
        this.attachedBuffs.push(buff);
    };
    return DamageInfo;
}());
/**
 * 伤害管理器
 * 负责处理复杂的伤害计算，包括暴击、抗性、伤害修正等
 */
var DamageManager = /** @class */ (function () {
    function DamageManager() {
        this._totalDamageDealt = 0;
        this._totalDamageTaken = 0;
        this._criticalHits = 0;
        this._totalHits = 0;
        this._eventManager = new EventManager_1.EventManager();
    }
    /**
     * 处理伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @param damageType 伤害类型
     * @param tags 伤害标签
     * @returns 最终伤害信息
     */
    DamageManager.prototype.dealDamage = function (attacker, target, baseDamage, damageType, tags) {
        if (damageType === void 0) { damageType = IDamage_1.DamageType.PHYSICAL; }
        if (tags === void 0) { tags = []; }
        // 创建伤害信息
        var damageInfo = new DamageInfo(attacker, target, baseDamage, damageType, tags);
        // 检查闪避
        if (this.checkDodge(attacker, target)) {
            damageInfo.isDodged = true;
            damageInfo.finalDamage = 0;
            console.log(target.characterName + " dodged attack from " + attacker.characterName);
            this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
            return damageInfo;
        }
        // 计算暴击
        if (this.checkCritical(attacker, target)) {
            damageInfo.isCritical = true;
            var criticalDamage = attacker.attributes.criticalDamage || 1.5;
            damageInfo.finalDamage *= criticalDamage;
            this._criticalHits++;
            console.log("Critical hit! " + attacker.characterName + " deals critical damage to " + target.characterName);
        }
        // 应用防御力
        damageInfo.finalDamage = this.applyDefense(damageInfo.finalDamage, target, damageType);
        // 应用伤害减免
        damageInfo.finalDamage = this.applyDamageReduction(damageInfo, target);
        // 应用攻击者的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyAttackerModifiers(damageInfo, attacker);
        // 应用目标的伤害修正（来自Buff等）
        damageInfo.finalDamage = this.applyTargetModifiers(damageInfo, target);
        // 确保伤害不为负数
        damageInfo.finalDamage = Math.max(0, Math.floor(damageInfo.finalDamage));
        // 计算实际伤害减免
        damageInfo.damageReduction = damageInfo.baseDamage - damageInfo.finalDamage;
        // 更新统计
        this._totalDamageDealt += damageInfo.finalDamage;
        this._totalHits++;
        // 应用伤害到目标
        if (damageInfo.finalDamage > 0 || damageInfo.isDodged) {
            target.takeDamage(damageInfo);
            if (!damageInfo.isDodged) {
                this._totalDamageTaken += damageInfo.finalDamage;
            }
        }
        console.log(attacker.characterName + " deals " + damageInfo.finalDamage + " " + damageType + " damage to " + target.characterName);
        // 触发伤害事件
        this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
        return damageInfo;
    };
    /** 检查是否闪避 */
    DamageManager.prototype.checkDodge = function (attacker, target) {
        var hitRate = attacker.attributes.accuracy || 1.0;
        var dodgeRate = target.attributes.evasion || 0.0;
        var finalHitRate = Math.max(0, hitRate - dodgeRate);
        return Math.random() > finalHitRate;
    };
    /** 检查是否暴击 */
    DamageManager.prototype.checkCritical = function (attacker, _target) {
        var criticalRate = attacker.attributes.criticalRate || 0.0;
        // 这里可以添加目标的暴击抗性
        return Math.random() < criticalRate;
    };
    /** 应用防御力 */
    DamageManager.prototype.applyDefense = function (damage, target, damageType) {
        var defense = 0;
        switch (damageType) {
            case IDamage_1.DamageType.PHYSICAL:
                defense = target.attributes.defense || 0;
                break;
            case IDamage_1.DamageType.MAGIC:
                // 魔法防御力（如果有的话）
                defense = target.attributes.magicDefense || target.attributes.defense * 0.5;
                break;
            case IDamage_1.DamageType.TRUE:
                // 真实伤害无视防御
                return damage;
        }
        // 简单的防御计算：伤害 = 基础伤害 - 防御力
        // 可以改为更复杂的公式，如：伤害 = 基础伤害 * (100 / (100 + 防御力))
        return Math.max(1, damage - defense);
    };
    /** 应用伤害减免 */
    DamageManager.prototype.applyDamageReduction = function (damageInfo, _target) {
        var damage = damageInfo.finalDamage;
        // 这里可以添加基于伤害类型的减免
        // 例如：火焰抗性、冰霜抗性等
        return damage;
    };
    /** 应用攻击者的伤害修正 */
    DamageManager.prototype.applyAttackerModifiers = function (damageInfo, attacker) {
        var e_1, _a;
        var damage = damageInfo.finalDamage;
        try {
            // 遍历攻击者的所有Buff，应用伤害修正
            for (var _b = __values(attacker.buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (buff.onDealDamage) {
                    var modifiedInfo = buff.onDealDamage(damageInfo, damageInfo.target);
                    damage = modifiedInfo.finalDamage;
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return damage;
    };
    /** 应用目标的伤害修正 */
    DamageManager.prototype.applyTargetModifiers = function (damageInfo, target) {
        var e_2, _a;
        var damage = damageInfo.finalDamage;
        try {
            // 遍历目标的所有Buff，应用伤害修正
            for (var _b = __values(target.buffs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var buff = _c.value;
                if (buff.onTakeDamage) {
                    var modifiedInfo = buff.onTakeDamage(damageInfo, damageInfo.attacker);
                    damage = modifiedInfo.finalDamage;
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return damage;
    };
    /** 获取统计信息 */
    DamageManager.prototype.getStats = function () {
        return {
            totalDamageDealt: this._totalDamageDealt,
            totalDamageTaken: this._totalDamageTaken,
            totalHits: this._totalHits,
            criticalHits: this._criticalHits,
            criticalRate: this._totalHits > 0 ? this._criticalHits / this._totalHits : 0
        };
    };
    /** 重置统计信息 */
    DamageManager.prototype.resetStats = function () {
        this._totalDamageDealt = 0;
        this._totalDamageTaken = 0;
        this._criticalHits = 0;
        this._totalHits = 0;
    };
    Object.defineProperty(DamageManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    DamageManager.prototype.cleanup = function () {
        this.resetStats();
        this._eventManager.cleanup();
    };
    return DamageManager;
}());
exports.DamageManager = DamageManager;

cc._RF.pop();