"use strict";
cc._RF.push(module, '86724jQcgZEKYvZN4mQ8lO8', 'AttributeModifier');
// fight/characters/AttributeModifier.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeModifier = void 0;
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
/**
 * 属性修改器实现类
 */
var AttributeModifier = /** @class */ (function () {
    function AttributeModifier(id, name, attributeName, type, value, duration) {
        if (duration === void 0) { duration = -1; }
        this._id = id;
        this._name = name;
        this._attributeName = attributeName;
        this._type = type;
        this._value = value;
        this._duration = duration;
        this._remainingTime = duration;
    }
    Object.defineProperty(AttributeModifier.prototype, "id", {
        // 实现IAttributeModifier接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "value", {
        get: function () { return this._value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttributeModifier.prototype, "attributeName", {
        // 额外属性
        get: function () { return this._attributeName; },
        enumerable: false,
        configurable: true
    });
    /*** 应用修改器到属性*/
    AttributeModifier.prototype.apply = function (attributes) {
        var currentValue = attributes.getCurrentAttributeValue(this._attributeName);
        var newValue = currentValue;
        switch (this._type) {
            case ICharacterAttributes_1.AttributeModifierType.ADD:
                newValue = currentValue + this._value;
                break;
            case ICharacterAttributes_1.AttributeModifierType.MULTIPLY:
                newValue = currentValue * this._value;
                break;
            case ICharacterAttributes_1.AttributeModifierType.PERCENTAGE:
                newValue = currentValue * (1 + this._value);
                break;
            case ICharacterAttributes_1.AttributeModifierType.OVERRIDE:
                newValue = this._value;
                break;
        }
        attributes.setAttributeValue(this._attributeName, newValue);
        console.log("Applied " + this._name + ": " + this._attributeName + " " + currentValue + " -> " + newValue);
    };
    /*** 移除修改器效果*/
    AttributeModifier.prototype.remove = function (_attributes) {
        // 这里需要恢复原始值，但由于可能有多个修改器，
        // 实际实现中应该重新计算所有修改器
        console.log("Removed " + this._name + " from " + this._attributeName);
    };
    /** * 更新修改器 */
    AttributeModifier.prototype.update = function (deltaTime) {
        if (this._duration < 0) {
            return false; // 永久修改器
        }
        this._remainingTime -= deltaTime;
        return this._remainingTime <= 0;
    };
    /*** 设置新的值*/
    AttributeModifier.prototype.setValue = function (value) {
        this._value = value;
    };
    /** * 获取调试信息 */
    AttributeModifier.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            attributeName: this._attributeName,
            type: this._type,
            value: this._value,
            duration: this._duration,
            remainingTime: this._remainingTime
        };
    };
    return AttributeModifier;
}());
exports.AttributeModifier = AttributeModifier;

cc._RF.pop();