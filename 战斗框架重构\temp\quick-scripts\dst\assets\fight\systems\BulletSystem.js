
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/BulletSystem.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '59043TAlOpFwZob3TryXFuJ', 'BulletSystem');
// fight/systems/BulletSystem.ts

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulletLauncher = exports.Bullet = void 0;
var FightEvent_1 = require("../types/FightEvent");
var IBullet_1 = require("../types/IBullet");
var EventManager_1 = require("./EventManager");
/**
 * 子弹系统实现
 * 提供基础的子弹发射和管理功能
 */
var Bullet = /** @class */ (function () {
    function Bullet(id, config, caster, firePosition, target, targetPosition) {
        this._timeElapsed = 0;
        this._hasCollided = false;
        this._isDestroyed = false;
        this._id = id;
        this._config = config;
        this._caster = caster;
        this._target = target;
        this._targetPosition = targetPosition;
        this._firePosition = firePosition;
        this._remainingHits = config.maxHits;
        this._eventManager = EventManager_1.EventManager.createLocal("Bullet_" + id);
        // 创建轨迹实例
        this._trajectory = this.createTrajectory();
        this.createBulletNode();
    }
    Object.defineProperty(Bullet.prototype, "id", {
        // 实现IBullet接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "type", {
        get: function () { return this._config.type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "node", {
        get: function () { return this._node; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "targetPosition", {
        get: function () { return this._targetPosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "firePosition", {
        get: function () { return this._firePosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "currentPosition", {
        get: function () { return this._node ? this._node.position : cc.Vec3.ZERO; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "speed", {
        get: function () { return this._config.speed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "lifeTime", {
        get: function () { return this._config.lifeTime; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "timeElapsed", {
        get: function () { return this._timeElapsed; },
        set: function (value) { this._timeElapsed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "remainingHits", {
        get: function () { return this._remainingHits; },
        set: function (value) { this._remainingHits = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "maxHits", {
        get: function () { return this._config.maxHits; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "hasCollided", {
        get: function () { return this._hasCollided; },
        set: function (value) { this._hasCollided = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "isDestroyed", {
        get: function () { return this._isDestroyed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "trajectory", {
        get: function () { return this._trajectory; },
        enumerable: false,
        configurable: true
    });
    /*** 更新子弹*/
    Bullet.prototype.update = function (deltaTime) {
        if (this._isDestroyed) {
            return true; // 已销毁
        }
        this._timeElapsed += deltaTime;
        // 检查生命周期
        if (this._timeElapsed >= this._config.lifeTime) {
            this.destroy();
            return true;
        }
        // 更新位置
        this.updateMovement(deltaTime);
        // 检查碰撞
        this.checkCollision();
        return false;
    };
    /** * 命中目标 */
    Bullet.prototype.hit = function (target) {
        var _a, _b;
        if (this._isDestroyed || this._remainingHits <= 0) {
            return false;
        }
        // 造成伤害
        var damage = this.calculateDamage();
        target.takeDamageSimple(damage, this._caster);
        this._remainingHits--;
        this._hasCollided = true;
        console.log("Bullet " + this._id + " hit " + target.characterName + " for " + damage + " damage");
        // 播放命中音效
        if ((_a = this._config.audio) === null || _a === void 0 ? void 0 : _a.hitSound) {
            console.log("Playing hit sound: " + this._config.audio.hitSound);
        }
        // 触发命中事件
        this._eventManager.emit(FightEvent_1.default.bulletHit, { bullet: this, target: target, damage: damage });
        // 检查是否需要销毁
        if (!((_b = this._config.collision) === null || _b === void 0 ? void 0 : _b.piercing) || this._remainingHits <= 0) {
            this.destroy();
        }
        return true;
    };
    /** * 设置目标 */
    Bullet.prototype.setTarget = function (target) {
        this._target = target;
    };
    /** * 设置目标位置 */
    Bullet.prototype.setTargetPosition = function (position) {
        this._targetPosition = position;
    };
    /**  * 销毁子弹  */
    Bullet.prototype.destroy = function () {
        if (this._isDestroyed)
            return;
        this._isDestroyed = true;
        if (this._node && this._node.isValid) {
            this._node.destroy();
        }
        this._eventManager.emit(FightEvent_1.default.bulletDestroyed, { bullet: this });
        this._eventManager.cleanup();
    };
    /** * 创建轨迹实例 */
    Bullet.prototype.createTrajectory = function () {
        var _this = this;
        // 简化实现，返回一个基础轨迹 
        return {
            type: this._config.trajectory.type,
            calculateNextPosition: function (_bullet, deltaTime) {
                return _this.calculateNextPosition(deltaTime);
            },
            calculateRotation: function (_bullet) {
                return 0; // 简化实现
            },
            hasReachedTarget: function (bullet, threshold) {
                if (!_this._target)
                    return false;
                var distance = cc.Vec3.distance(bullet.currentPosition, _this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
                return distance <= threshold;
            }
        };
    };
    /** * 计算下一帧位置 */
    Bullet.prototype.calculateNextPosition = function (deltaTime) {
        if (!this._node)
            return cc.Vec3.ZERO;
        var currentPos = this._node.position;
        var targetPos;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        else if (this._targetPosition) {
            targetPos = this._targetPosition;
        }
        else {
            return currentPos; // 没有目标，保持当前位置
        }
        // 计算移动方向和距离
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = this._config.speed * deltaTime;
        return currentPos.add(direction.multiplyScalar(moveDistance));
    };
    /** * 创建子弹节点 */
    Bullet.prototype.createBulletNode = function () {
        this._node = new cc.Node("Bullet_" + this._id);
        // 添加精灵组件
        this._node.addComponent(cc.Sprite);
        // 这里应该加载子弹贴图
        // cc.resources.load(this._config.prefabPath, cc.SpriteFrame, (err, spriteFrame) => {
        //     if (!err && sprite.isValid) {
        //         sprite.spriteFrame = spriteFrame;
        //     }
        // });
        // 设置初始位置
        this._node.position = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 添加到场景
        cc.director.getScene().addChild(this._node);
    };
    /** * 更新移动 */
    Bullet.prototype.updateMovement = function (deltaTime) {
        if (!this._node || !this._node.isValid)
            return;
        var currentPos = this._node.position;
        var targetPos;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        else if (this._targetPosition) {
            targetPos = this._targetPosition;
        }
        else {
            // 没有目标，直接销毁
            this.destroy();
            return;
        }
        // 计算移动方向
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = this._config.speed * deltaTime;
        // 根据轨迹类型移动
        switch (this._config.trajectory.type) {
            case IBullet_1.TrajectoryType.LINEAR:
                this.moveLinear(direction, moveDistance);
                break;
            case IBullet_1.TrajectoryType.PARABOLIC:
                this.moveParabolic(targetPos, deltaTime);
                break;
            case IBullet_1.TrajectoryType.HOMING:
                this.moveHoming(targetPos, deltaTime);
                break;
            default:
                this.moveLinear(direction, moveDistance);
                break;
        }
        // 检查是否到达目标
        var distanceToTarget = cc.Vec3.distance(this._node.position, targetPos);
        if (distanceToTarget <= 10) { // 10像素的容错范围
            this.onHitTarget();
        }
    };
    /*** 线性移动*/
    Bullet.prototype.moveLinear = function (direction, distance) {
        var newPos = this._node.position.add(direction.multiplyScalar(distance));
        this._node.position = newPos;
    };
    /*** 抛物线移动(简化的抛物线实现)*/
    Bullet.prototype.moveParabolic = function (targetPos, _deltaTime) {
        var progress = this._timeElapsed / this._config.lifeTime;
        var startPos = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 计算抛物线轨迹
        var midPoint = startPos.add(targetPos).multiplyScalar(0.5);
        midPoint.y += 100; // 抛物线高度
        var x = cc.misc.lerp(startPos.x, targetPos.x, progress);
        var y = this.calculateParabolicY(startPos, midPoint, targetPos, progress);
        this._node.position = cc.v3(x, y, 0);
    };
    /** * 追踪移动 */
    Bullet.prototype.moveHoming = function (targetPos, deltaTime) {
        var currentPos = this._node.position;
        var direction = targetPos.subtract(currentPos).normalize();
        // 追踪子弹有更强的转向能力
        var moveDistance = this._config.speed * deltaTime;
        var newPos = currentPos.add(direction.multiplyScalar(moveDistance));
        this._node.position = newPos;
    };
    /** * 计算抛物线Y坐标 */
    Bullet.prototype.calculateParabolicY = function (start, mid, end, t) {
        // 二次贝塞尔曲线
        var oneMinusT = 1 - t;
        return oneMinusT * oneMinusT * start.y + 2 * oneMinusT * t * mid.y + t * t * end.y;
    };
    /** * 检查碰撞 */
    Bullet.prototype.checkCollision = function () {
        if (!this._config.collision)
            return;
        // 简化的碰撞检测
        var bulletPos = this._node.position;
        var collisionRadius = this._config.collision.radius;
        // 这里应该使用物理系统或碰撞检测系统
        // 现在简化为距离检测
        if (this._target && !this._target.isDead) {
            var targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
            var distance = cc.Vec3.distance(bulletPos, targetPos);
            if (distance <= collisionRadius) {
                this.onHitTarget();
            }
        }
    };
    /*** 命中目标*/
    Bullet.prototype.onHitTarget = function () {
        if (!this._target) {
            return;
        }
        // 使用hit方法处理命中逻辑
        this.hit(this._target);
    };
    /** * 计算伤害 */
    Bullet.prototype.calculateDamage = function () {
        // 简化的伤害计算，使用施法者的攻击力
        var baseDamage = this._caster.attributes.attack;
        // 这里可以添加更复杂的伤害计算逻辑
        return Math.floor(baseDamage);
    };
    Object.defineProperty(Bullet.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    return Bullet;
}());
exports.Bullet = Bullet;
/*** 子弹发射器实现类*/
var BulletLauncher = /** @class */ (function () {
    function BulletLauncher(id, caster, bulletConfig) {
        this._firePosition = cc.Vec3.ZERO;
        this._fireDirection = cc.Vec3.ZERO;
        this._fireAngle = 0;
        this._fireSpeed = 400;
        this._id = id;
        this._caster = caster;
        this._bulletConfig = bulletConfig;
        this._eventManager = EventManager_1.EventManager.createLocal("BulletLauncher_" + id);
    }
    Object.defineProperty(BulletLauncher.prototype, "id", {
        // 实现IBulletLauncher接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "bulletConfig", {
        get: function () { return this._bulletConfig; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "firePosition", {
        get: function () { return this._firePosition; },
        set: function (value) { this._firePosition = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireDirection", {
        get: function () { return this._fireDirection; },
        set: function (value) { this._fireDirection = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireAngle", {
        get: function () { return this._fireAngle; },
        set: function (value) { this._fireAngle = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireSpeed", {
        get: function () { return this._fireSpeed; },
        set: function (value) { this._fireSpeed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "bulletTimesp", {
        get: function () { return Date.now() + "_" + Math.random().toString(36).substring(2, 11); },
        enumerable: false,
        configurable: true
    });
    /*** 发射单个子弹*/
    BulletLauncher.prototype.fire = function (target, targetPosition) {
        if (!target && !targetPosition) {
            console.warn("BulletLauncher: No target or target position provided");
            return null;
        }
        var bulletId = this._id + "_bullet_" + this.bulletTimesp;
        var bullet = new Bullet(bulletId, this._bulletConfig, this._caster, this._firePosition, target, targetPosition);
        console.log("BulletLauncher " + this._id + " fired bullet " + bulletId);
        this._eventManager.emit(FightEvent_1.default.bulletFired, { launcher: this, bullet: bullet, target: target, targetPosition: targetPosition });
        return bullet;
    };
    /*** 发射多个子弹（散射）*/
    BulletLauncher.prototype.fireBurst = function (count, spread, target, targetPosition) {
        var bullets = [];
        if (count <= 0)
            return bullets;
        // 计算散射角度
        var startAngle = -spread / 2;
        var angleStep = count > 1 ? spread / (count - 1) : 0;
        for (var i = 0; i < count; i++) {
            var angle = startAngle + angleStep * i;
            // 计算散射后的目标位置
            var burstTargetPos = void 0;
            if (targetPosition) {
                burstTargetPos = this.calculateBurstPosition(targetPosition, angle);
            }
            else if (target) {
                var originalPos = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
                burstTargetPos = this.calculateBurstPosition(originalPos, angle);
            }
            else {
                continue;
            }
            var bullet = this.fire(undefined, burstTargetPos);
            if (bullet) {
                bullets.push(bullet);
            }
        }
        console.log("BulletLauncher " + this._id + " fired burst of " + bullets.length + " bullets");
        return bullets;
    };
    /** * 设置发射参数 */
    BulletLauncher.prototype.setFireParams = function (position, direction, speed) {
        this._firePosition = position;
        this._fireDirection = direction.normalize();
        if (speed !== undefined) {
            this._fireSpeed = speed;
        }
        // 计算角度
        this._fireAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
    };
    /** * 计算散射位置 */
    BulletLauncher.prototype.calculateBurstPosition = function (originalPos, angleOffset) {
        var distance = cc.Vec3.distance(this._firePosition, originalPos);
        var originalAngle = Math.atan2(originalPos.y - this._firePosition.y, originalPos.x - this._firePosition.x);
        var newAngle = originalAngle + angleOffset * Math.PI / 180;
        return cc.v3(this._firePosition.x + Math.cos(newAngle) * distance, this._firePosition.y + Math.sin(newAngle) * distance, originalPos.z);
    };
    /** * 更新子弹配置 */
    BulletLauncher.prototype.updateBulletConfig = function (config) {
        this._bulletConfig = __assign(__assign({}, this._bulletConfig), config);
    };
    Object.defineProperty(BulletLauncher.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /**  * 清理资源  */
    BulletLauncher.prototype.cleanup = function () {
        this._eventManager.cleanup();
    };
    return BulletLauncher;
}());
exports.BulletLauncher = BulletLauncher;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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