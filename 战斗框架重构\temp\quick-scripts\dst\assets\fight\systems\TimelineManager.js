
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/TimelineManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e6db1uGotJItICfY8rXl8QC', 'TimelineManager');
// fight/systems/TimelineManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimelineManager = void 0;
var Timeline_1 = require("../timeline/Timeline");
var FightEvent_1 = require("../types/FightEvent");
var EventManager_1 = require("./EventManager");
/*** Timeline管理器实现*/
var TimelineManager = /** @class */ (function () {
    function TimelineManager() {
        this._activeTimelines = new Map();
        this._pausedTimelines = new Set();
        this._totalExecutedEvents = 0;
        this._isUpdating = false;
        this._pendingRemovals = [];
        this._eventManager = EventManager_1.EventManager.createLocal("TimelineManager");
    }
    Object.defineProperty(TimelineManager.prototype, "activeTimelines", {
        /** * 获取所有活跃的Timeline */
        get: function () {
            return Array.from(this._activeTimelines.values());
        },
        enumerable: false,
        configurable: true
    });
    /** * 添加Timeline */
    TimelineManager.prototype.addTimeline = function (timeline) {
        if (this._activeTimelines.has(timeline.id)) {
            console.warn("Timeline " + timeline.id + " already exists, replacing...");
        }
        this._activeTimelines.set(timeline.id, timeline);
        timeline.eventManager.on(FightEvent_1.default.completedT, this.onTimelineCompleted.bind(this));
        timeline.eventManager.on(FightEvent_1.default.stoppedT, this.onTimelineStopped.bind(this));
        console.log("Timeline " + timeline.id + " added to manager");
        this._eventManager.emit(FightEvent_1.default.timelineAdded, { timeline: timeline });
    };
    /*** 根据配置创建并添加Timeline*/
    TimelineManager.prototype.createTimeline = function (config, caster, target, targets, position) {
        var e_1, _a;
        var timeline = new Timeline_1.Timeline(config.id, config.name, config.duration, caster, target, targets, position);
        try {
            for (var _b = __values(config.nodes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var nodeConfig = _c.value;
                // 这里应该根据nodeConfig创建具体的TimelineNode， 现在先简化处理
                console.log("Creating timeline node: " + nodeConfig.id);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        this.addTimeline(timeline);
        return timeline;
    };
    /** * 移除Timeline */
    TimelineManager.prototype.removeTimeline = function (timelineId) {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(timelineId);
            return;
        }
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline) {
            timeline.stop();
            timeline.eventManager.off(FightEvent_1.default.completedT, this.onTimelineCompleted.bind(this));
            timeline.eventManager.off(FightEvent_1.default.stoppedT, this.onTimelineStopped.bind(this));
            // 从映射中移除
            this._activeTimelines.delete(timelineId);
            this._pausedTimelines.delete(timelineId);
            console.log("Timeline " + timelineId + " removed from manager");
            this._eventManager.emit(FightEvent_1.default.timelineRemoved, { timeline: timeline });
        }
    };
    /** * 根据施法者移除Timeline */
    TimelineManager.prototype.removeTimelinesByCaster = function (caster) {
        var e_2, _a, e_3, _b;
        var timelinesToRemove = [];
        try {
            for (var _c = __values(this._activeTimelines), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], timeline = _e[1];
                if (timeline.caster === caster) {
                    timelinesToRemove.push(id);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var timelinesToRemove_1 = __values(timelinesToRemove), timelinesToRemove_1_1 = timelinesToRemove_1.next(); !timelinesToRemove_1_1.done; timelinesToRemove_1_1 = timelinesToRemove_1.next()) {
                var id = timelinesToRemove_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (timelinesToRemove_1_1 && !timelinesToRemove_1_1.done && (_b = timelinesToRemove_1.return)) _b.call(timelinesToRemove_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        console.log("Removed " + timelinesToRemove.length + " timelines for caster " + caster.characterName);
    };
    /** * 暂停所有Timeline */
    TimelineManager.prototype.pauseAll = function () {
        var e_4, _a;
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (!timeline.isPaused) {
                    timeline.pause();
                    this._pausedTimelines.add(timeline.id);
                }
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        console.log("Paused " + this._pausedTimelines.size + " timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesPaused);
    };
    /** * 恢复所有Timeline */
    TimelineManager.prototype.resumeAll = function () {
        var e_5, _a;
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (timeline.isPaused) {
                    timeline.resume();
                    this._pausedTimelines.delete(timeline.id);
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log("Resumed all timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesResumed);
    };
    /*** 清除所有Timeline*/
    TimelineManager.prototype.clearAll = function () {
        var e_6, _a;
        var timelineIds = Array.from(this._activeTimelines.keys());
        try {
            for (var timelineIds_1 = __values(timelineIds), timelineIds_1_1 = timelineIds_1.next(); !timelineIds_1_1.done; timelineIds_1_1 = timelineIds_1.next()) {
                var id = timelineIds_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (timelineIds_1_1 && !timelineIds_1_1.done && (_a = timelineIds_1.return)) _a.call(timelineIds_1);
            }
            finally { if (e_6) throw e_6.error; }
        }
        this._pausedTimelines.clear();
        this._totalExecutedEvents = 0;
        console.log("Cleared all timelines");
        this._eventManager.emit(FightEvent_1.default.allTimelinesCleared);
    };
    /** * 更新所有Timeline */
    TimelineManager.prototype.update = function (deltaTime) {
        var e_7, _a, e_8, _b, e_9, _c;
        if (this._activeTimelines.size === 0) {
            return;
        }
        this._isUpdating = true;
        var completedTimelines = [];
        try {
            // 更新所有Timeline
            for (var _d = __values(this._activeTimelines), _e = _d.next(); !_e.done; _e = _d.next()) {
                var _f = __read(_e.value, 2), id = _f[0], timeline = _f[1];
                if (!timeline.isPaused) {
                    var isCompleted = timeline.update(deltaTime);
                    if (isCompleted) {
                        completedTimelines.push(id);
                    }
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_7) throw e_7.error; }
        }
        this._isUpdating = false;
        try {
            // 移除已完成的Timeline
            for (var completedTimelines_1 = __values(completedTimelines), completedTimelines_1_1 = completedTimelines_1.next(); !completedTimelines_1_1.done; completedTimelines_1_1 = completedTimelines_1.next()) {
                var id = completedTimelines_1_1.value;
                this.removeTimeline(id);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (completedTimelines_1_1 && !completedTimelines_1_1.done && (_b = completedTimelines_1.return)) _b.call(completedTimelines_1);
            }
            finally { if (e_8) throw e_8.error; }
        }
        // 处理延迟移除的Timeline
        if (this._pendingRemovals.length > 0) {
            try {
                for (var _g = __values(this._pendingRemovals), _h = _g.next(); !_h.done; _h = _g.next()) {
                    var id = _h.value;
                    this.removeTimeline(id);
                }
            }
            catch (e_9_1) { e_9 = { error: e_9_1 }; }
            finally {
                try {
                    if (_h && !_h.done && (_c = _g.return)) _c.call(_g);
                }
                finally { if (e_9) throw e_9.error; }
            }
            this._pendingRemovals.length = 0;
        }
    };
    /** * 获取Timeline统计信息 */
    TimelineManager.prototype.getStats = function () {
        return {
            activeCount: this._activeTimelines.size,
            pausedCount: this._pausedTimelines.size,
            totalExecutedEvents: this._totalExecutedEvents
        };
    };
    /** * 根据ID获取Timeline */
    TimelineManager.prototype.getTimeline = function (timelineId) {
        return this._activeTimelines.get(timelineId) || null;
    };
    /*** 根据施法者获取Timeline列表*/
    TimelineManager.prototype.getTimelinesByCaster = function (caster) {
        var e_10, _a;
        var timelines = [];
        try {
            for (var _b = __values(this._activeTimelines.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var timeline = _c.value;
                if (timeline.caster === caster) {
                    timelines.push(timeline);
                }
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return timelines;
    };
    /** * 暂停指定Timeline */
    TimelineManager.prototype.pauseTimeline = function (timelineId) {
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline && !timeline.isPaused) {
            timeline.pause();
            this._pausedTimelines.add(timelineId);
            console.log("Timeline " + timelineId + " paused");
            this._eventManager.emit(FightEvent_1.default.timelinePaused, { timeline: timeline });
        }
    };
    /**  * 恢复指定Timeline  */
    TimelineManager.prototype.resumeTimeline = function (timelineId) {
        var timeline = this._activeTimelines.get(timelineId);
        if (timeline && timeline.isPaused) {
            timeline.resume();
            this._pausedTimelines.delete(timelineId);
            console.log("Timeline " + timelineId + " resumed");
            this._eventManager.emit(FightEvent_1.default.timelineResumed, { timeline: timeline });
        }
    };
    /*** Timeline完成时的回调*/
    TimelineManager.prototype.onTimelineCompleted = function (event) {
        var timeline = event.timeline;
        console.log("Timeline " + timeline.id + " completed");
        this._totalExecutedEvents++;
        this._eventManager.emit(FightEvent_1.default.timelineCompleted, { timeline: timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    };
    /*** Timeline停止时的回调*/
    TimelineManager.prototype.onTimelineStopped = function (event) {
        var timeline = event.timeline;
        console.log("Timeline " + timeline.id + " stopped");
        this._eventManager.emit(FightEvent_1.default.timelineStopped, { timeline: timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    };
    /*** 安排移除Timeline*/
    TimelineManager.prototype.scheduleRemoval = function (timelineId) {
        if (this._isUpdating) {
            this._pendingRemovals.push(timelineId);
        }
        else {
            this.removeTimeline(timelineId);
        }
    };
    Object.defineProperty(TimelineManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /*** 获取调试信息*/
    TimelineManager.prototype.getDebugInfo = function () {
        var e_11, _a;
        var _b;
        var timelines = {};
        try {
            for (var _c = __values(this._activeTimelines), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], timeline = _e[1];
                timelines[id] = {
                    name: timeline.name,
                    duration: timeline.duration,
                    timeElapsed: timeline.timeElapsed,
                    isCompleted: timeline.isCompleted,
                    isPaused: timeline.isPaused,
                    nodeCount: timeline.nodes.length,
                    caster: timeline.caster.characterName,
                    target: ((_b = timeline.target) === null || _b === void 0 ? void 0 : _b.characterName) || null
                };
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return {
            stats: this.getStats(),
            timelines: timelines,
            pendingRemovals: this._pendingRemovals.length
        };
    };
    /*** 打印调试信息*/
    TimelineManager.prototype.printDebugInfo = function () {
        console.log("TimelineManager Debug Info:", this.getDebugInfo());
    };
    /*** 清理管理器*/
    TimelineManager.prototype.cleanup = function () {
        this.clearAll();
        this._eventManager.cleanup();
    };
    return TimelineManager;
}());
exports.TimelineManager = TimelineManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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