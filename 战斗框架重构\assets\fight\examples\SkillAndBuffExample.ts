import { BattleManager } from "../systems/BattleManager";
import { Character } from "../characters/Character";
import { CharacterRole } from "../types/CharacterTypes";

// 导入技能
import { PlayerSkillFire1 } from "../skills/PlayerSkillFire1";
import { HealingLightSkill } from "../skills/HealingLightSkill";
import { ThunderStormSkill } from "../skills/ThunderStormSkill";
import { ChargeAttackSkill } from "../skills/ChargeAttackSkill";

// 导入buff
import { AttackBoostBuff } from "../buff/AttackBoostBuff";
import { HealOverTimeBuff } from "../buff/HealOverTimeBuff";
import { StunBuff } from "../buff/StunBuff";
import { PoisonBuff } from "../buff/PoisonBuff";

/**
 * 技能和Buff使用示例
 * 展示如何使用新的技能系统和buff系统
 */
export class SkillAndBuffExample {
    private battleManager: BattleManager;
    private player: Character;
    private enemy: Character;
    private healer: Character;

    constructor() {
        this.battleManager = BattleManager.instance
        this.initializeCharacters();
        this.setupSkills();
        this.runExample();
    }

    /** 初始化角色 */
    private initializeCharacters(): void {
        // 创建玩家角色（战士）
        this.player = new Character();
        this.player.setCharacterData({
            prefabKey: "player_warrior",
            name: "勇敢的战士",
            role: CharacterRole.HERO,
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                maxStamina: 150,
                attack: 120,
                defense: 80,
                attackSpeed: 1.0,
                moveSpeed: 200,
                attackRange: 150,
                criticalRate: 0.15,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.1,
                level: 1,
                experience: 0
            }
        });

        // 创建敌人角色
        this.enemy = new Character();
        this.enemy.setCharacterData({
            prefabKey: "enemy_orc",
            name: "兽人战士",
            role: CharacterRole.ENEMY,
            initialAttributes: {
                hp: 800,
                maxHp: 800,
                mp: 100,
                maxMp: 100,
                maxStamina: 120,
                attack: 100,
                defense: 60,
                attackSpeed: 1.0,
                moveSpeed: 180,
                attackRange: 120,
                criticalRate: 0.1,
                criticalDamage: 1.3,
                hitRate: 0.9,
                dodgeRate: 0.05,
                level: 1,
                experience: 0
            }
        });

        // 创建治疗师角色
        this.healer = new Character();
        this.healer.setCharacterData({
            prefabKey: "healer_priest",
            name: "神圣牧师",
            role: CharacterRole.HERO,
            initialAttributes: {
                hp: 600,
                maxHp: 600,
                mp: 300,
                maxMp: 300,
                maxStamina: 80,
                attack: 50,
                defense: 40,
                attackSpeed: 1.0,
                moveSpeed: 160,
                attackRange: 200,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.98,
                dodgeRate: 0.15,
                level: 1,
                experience: 0
            }
        });

        console.log("Characters initialized:");
        console.log(`Player: ${this.player.characterName} - HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);
        console.log(`Enemy: ${this.enemy.characterName} - HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);
        console.log(`Healer: ${this.healer.characterName} - HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}`);
    }

    /** 设置技能 */
    private setupSkills(): void {
        // 为玩家添加技能
        const fireballSkill = new PlayerSkillFire1();
        const chargeSkill = new ChargeAttackSkill();
        this.player.skillManager.addSkill(fireballSkill);
        this.player.skillManager.addSkill(chargeSkill);

        // 为治疗师添加技能
        const healSkill = new HealingLightSkill();
        const thunderSkill = new ThunderStormSkill();
        this.healer.skillManager.addSkill(healSkill);
        this.healer.skillManager.addSkill(thunderSkill);

        console.log("Skills added to characters");
    }

    /** 运行示例 */
    private runExample(): void {
        console.log("\n=== 技能和Buff系统演示开始 ===\n");

        // 开始战斗
        this.battleManager.startBattle("skill_demo_battle", [this.player, this.enemy, this.healer]);

        // 演示1: 攻击力提升buff
        this.demonstrateAttackBoostBuff();

        // 演示2: 火球术攻击
        this.demonstrateFireballAttack();

        // 演示3: 毒素debuff
        this.demonstratePoisonDebuff();

        // 演示4: 治疗技能和持续治疗buff
        this.demonstrateHealingSkill();

        // 演示5: 眩晕debuff
        this.demonstrateStunDebuff();

        // 演示6: 冲锋攻击技能
        this.demonstrateChargeAttack();

        // 演示7: 雷暴术范围攻击
        this.demonstrateThunderStorm();

        // 模拟战斗更新
        this.simulateBattleUpdates();

        console.log("\n=== 技能和Buff系统演示结束 ===\n");
    }

    /** 演示攻击力提升buff */
    private demonstrateAttackBoostBuff(): void {
        console.log("\n--- 演示攻击力提升Buff ---");
        console.log(`${this.player.name} 当前攻击力: ${this.player.attributes.attack}`);

        const attackBuff = new AttackBoostBuff(this.player, this.player, 10.0, 1.5);
        this.player.buffManager.addBuff(attackBuff);

        console.log(`${this.player.name} 获得攻击力提升buff后攻击力: ${this.player.attributes.attack}`);
        console.log(`Buff信息:`, attackBuff.getDebugInfo());
    }

    /** 演示火球术攻击 */
    private demonstrateFireballAttack(): void {
        console.log("\n--- 演示火球术攻击 ---");
        const fireballSkill = this.player.skillManager.getSkill("player_skill_fire1");
        if (fireballSkill) {
            console.log(`${this.player.name} 释放 ${fireballSkill.name}`);
            console.log(`敌人当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);

            const success = fireballSkill.cast(this.player, this.enemy);
            console.log(`技能释放${success ? '成功' : '失败'}`);
        }
    }

    /** 演示毒素debuff */
    private demonstratePoisonDebuff(): void {
        console.log("\n--- 演示毒素Debuff ---");
        console.log(`${this.enemy.name} 当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);

        const poisonBuff = new PoisonBuff(this.player, this.enemy, 8.0, 25);
        this.enemy.buffManager.addBuff(poisonBuff);

        console.log(`${this.enemy.name} 中毒了！`);
        console.log(`毒素信息:`, poisonBuff.getDebugInfo());
    }

    /** 演示治疗技能 */
    private demonstrateHealingSkill(): void {
        console.log("\n--- 演示治疗技能 ---");

        // 先让玩家受伤
        this.player.attributes.takeDamage(300);
        console.log(`${this.player.name} 受伤后HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);

        const healSkill = this.healer.skillManager.getSkill("healing_light");
        if (healSkill) {
            console.log(`${this.healer.name} 对 ${this.player.name} 释放 ${healSkill.name}`);
            const success = healSkill.cast(this.healer, this.player);
            console.log(`治疗技能释放${success ? '成功' : '失败'}`);
        }

        // 添加持续治疗buff
        const healOverTimeBuff = new HealOverTimeBuff(this.healer, this.player, 6.0, 20);
        this.player.buffManager.addBuff(healOverTimeBuff);
        console.log(`${this.player.name} 获得持续治疗效果`);
    }

    /** 演示眩晕debuff */
    private demonstrateStunDebuff(): void {
        console.log("\n--- 演示眩晕Debuff ---");
        console.log(`${this.enemy.name} 当前移动速度: ${this.enemy.attributes.moveSpeed}`);

        const stunBuff = new StunBuff(this.player, this.enemy, 3.0);
        this.enemy.buffManager.addBuff(stunBuff);

        console.log(`${this.enemy.name} 被眩晕了！`);
        console.log(`${this.enemy.name} 眩晕后移动速度: ${this.enemy.attributes.moveSpeed}`);
        console.log(`眩晕信息:`, stunBuff.getDebugInfo());
    }

    /** 演示冲锋攻击技能 */
    private demonstrateChargeAttack(): void {
        console.log("\n--- 演示冲锋攻击技能 ---");
        const chargeSkill = this.player.skillManager.getSkill("charge_attack");
        if (chargeSkill) {
            console.log(`${this.player.name} 对 ${this.enemy.name} 释放 ${chargeSkill.name}`);
            console.log(`敌人当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);

            const success = chargeSkill.cast(this.player, this.enemy);
            console.log(`冲锋攻击释放${success ? '成功' : '失败'}`);
        }
    }

    /** 演示雷暴术范围攻击 */
    private demonstrateThunderStorm(): void {
        console.log("\n--- 演示雷暴术范围攻击 ---");
        const thunderSkill = this.healer.skillManager.getSkill("thunder_storm");
        if (thunderSkill) {
            const targetPosition = cc.v3(100, 100, 0);
            console.log(`${this.healer.name} 在位置 (${targetPosition.x}, ${targetPosition.y}) 释放 ${thunderSkill.name}`);

            const success = thunderSkill.cast(this.healer, undefined, undefined, targetPosition);
            console.log(`雷暴术释放${success ? '成功' : '失败'}`);
        }
    }

    /** 模拟战斗更新 */
    private simulateBattleUpdates(): void {
        console.log("\n--- 模拟战斗更新 (5秒) ---");

        const updateInterval = 0.1; // 100ms更新间隔
        const totalTime = 5.0; // 总共5秒
        let currentTime = 0;

        const updateTimer = setInterval(() => {
            currentTime += updateInterval;

            // 更新所有角色的buff
            this.player.buffManager.update(updateInterval);
            this.enemy.buffManager.update(updateInterval);
            this.healer.buffManager.update(updateInterval);

            // 更新技能冷却
            this.player.skillManager.update(updateInterval);
            this.enemy.skillManager.update(updateInterval);
            this.healer.skillManager.update(updateInterval);

            // 更新战斗管理器
            this.battleManager.update(updateInterval);

            // 每秒输出一次状态
            if (Math.floor(currentTime) !== Math.floor(currentTime - updateInterval)) {
                console.log(`\n时间: ${Math.floor(currentTime)}s`);
                console.log(`${this.player.name} HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}, Buffs: ${this.player.buffManager.getAllBuffs().length}`);
                console.log(`${this.enemy.name} HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}, Buffs: ${this.enemy.buffManager.getAllBuffs().length}`);
                console.log(`${this.healer.name} HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}, Buffs: ${this.healer.buffManager.getAllBuffs().length}`);
            }

            if (currentTime >= totalTime) {
                clearInterval(updateTimer);
                this.printFinalStatus();
            }
        }, updateInterval * 1000);
    }

    /** 打印最终状态 */
    private printFinalStatus(): void {
        console.log("\n--- 最终状态 ---");
        console.log(`${this.player.name}:`);
        console.log(`  HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);
        console.log(`  攻击力: ${this.player.attributes.attack}`);
        console.log(`  活跃Buffs: ${this.player.buffManager.getAllBuffs().length}`);

        console.log(`${this.enemy.name}:`);
        console.log(`  HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);
        console.log(`  移动速度: ${this.enemy.attributes.moveSpeed}`);
        console.log(`  活跃Buffs: ${this.enemy.buffManager.getAllBuffs().length}`);

        console.log(`${this.healer.name}:`);
        console.log(`  HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}`);
        console.log(`  MP: ${this.healer.attributes.currentMp}/${this.healer.attributes.maxMp}`);
        console.log(`  活跃Buffs: ${this.healer.buffManager.getAllBuffs().length}`);

        // 打印战斗统计
        console.log("\n战斗统计:", this.battleManager.getBattleStats());
    }
}
