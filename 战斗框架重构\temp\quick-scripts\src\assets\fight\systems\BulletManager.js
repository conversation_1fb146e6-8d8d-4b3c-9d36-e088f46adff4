"use strict";
cc._RF.push(module, '6fca8qVBjpDqqQOlXECl0dj', 'BulletManager');
// fight/systems/BulletManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulletManager = void 0;
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
var BulletSystem_1 = require("./BulletSystem");
/**
 * 子弹管理器实现
 * 负责管理所有活跃的子弹
 */
var BulletManager = /** @class */ (function () {
    function BulletManager() {
        this._activeBullets = new Map();
        this._totalCreated = 0;
        this._totalDestroyed = 0;
        this._isUpdating = false;
        this._pendingRemovals = [];
        this._eventManager = EventManager_1.EventManager.createLocal("BulletManager");
        this.setupEventListeners();
    }
    Object.defineProperty(BulletManager.prototype, "activeBullets", {
        /** 获取所有活跃的子弹 */
        get: function () {
            return Array.from(this._activeBullets.values());
        },
        enumerable: false,
        configurable: true
    });
    /** 创建子弹 */
    BulletManager.prototype.createBullet = function (config, caster, target, targetPosition) {
        var bulletId = "bullet_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var bullet = new BulletSystem_1.Bullet(bulletId, config, caster, firePosition, target, targetPosition);
        this.addBullet(bullet);
        return bullet;
    };
    /** 添加子弹到管理器 */
    BulletManager.prototype.addBullet = function (bullet) {
        if (this._activeBullets.has(bullet.id)) {
            console.warn("Bullet " + bullet.id + " already exists in manager");
            return;
        }
        this._activeBullets.set(bullet.id, bullet);
        this._totalCreated++;
        console.log("Bullet " + bullet.id + " added to manager");
        this._eventManager.emit(FightEvent_1.default.bulletFired, { bullet: bullet });
    };
    /** 移除子弹 */
    BulletManager.prototype.removeBullet = function (bulletId) {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(bulletId);
            return;
        }
        this._internalRemoveBullet(bulletId, true);
    };
    /** 内部移除子弹方法 - 避免事件循环 */
    BulletManager.prototype._internalRemoveBullet = function (bulletId, shouldEmitEvent) {
        if (shouldEmitEvent === void 0) { shouldEmitEvent = false; }
        var bullet = this._activeBullets.get(bulletId);
        if (bullet) {
            // 确保子弹被销毁，但不重复销毁
            if (!bullet.isDestroyed) {
                bullet.destroy();
            }
            this._activeBullets.delete(bulletId);
            this._totalDestroyed++;
            console.log("Bullet " + bulletId + " removed from manager");
            // 只有在外部调用时才发射事件，避免循环
            if (shouldEmitEvent) {
                this._eventManager.emit(FightEvent_1.default.bulletDestroyed, { bullet: bullet });
            }
        }
    };
    /** 根据施法者移除子弹 */
    BulletManager.prototype.removeBulletsByCaster = function (caster) {
        var e_1, _a, e_2, _b;
        var bulletsToRemove = [];
        try {
            for (var _c = __values(this._activeBullets), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], bullet = _e[1];
                if (bullet.caster === caster) {
                    bulletsToRemove.push(id);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var bulletsToRemove_1 = __values(bulletsToRemove), bulletsToRemove_1_1 = bulletsToRemove_1.next(); !bulletsToRemove_1_1.done; bulletsToRemove_1_1 = bulletsToRemove_1.next()) {
                var id = bulletsToRemove_1_1.value;
                this.removeBullet(id);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (bulletsToRemove_1_1 && !bulletsToRemove_1_1.done && (_b = bulletsToRemove_1.return)) _b.call(bulletsToRemove_1);
            }
            finally { if (e_2) throw e_2.error; }
        }
        console.log("Removed " + bulletsToRemove.length + " bullets for caster " + caster.characterName);
    };
    /** 清除所有子弹 */
    BulletManager.prototype.clearAllBullets = function () {
        var e_3, _a;
        var bulletIds = Array.from(this._activeBullets.keys());
        try {
            for (var bulletIds_1 = __values(bulletIds), bulletIds_1_1 = bulletIds_1.next(); !bulletIds_1_1.done; bulletIds_1_1 = bulletIds_1.next()) {
                var id = bulletIds_1_1.value;
                this.removeBullet(id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (bulletIds_1_1 && !bulletIds_1_1.done && (_a = bulletIds_1.return)) _a.call(bulletIds_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        console.log("Cleared all bullets (" + bulletIds.length + " bullets)");
    };
    /** 更新所有子弹 */
    BulletManager.prototype.update = function (deltaTime) {
        var e_4, _a, e_5, _b, e_6, _c;
        if (this._activeBullets.size === 0) {
            return;
        }
        this._isUpdating = true;
        var destroyedBullets = [];
        try {
            // 更新所有子弹
            for (var _d = __values(this._activeBullets), _e = _d.next(); !_e.done; _e = _d.next()) {
                var _f = __read(_e.value, 2), id = _f[0], bullet = _f[1];
                var isDestroyed = bullet.update(deltaTime);
                if (isDestroyed) {
                    destroyedBullets.push(id);
                }
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this._isUpdating = false;
        try {
            // 移除已销毁的子弹 - 使用内部方法避免重复销毁
            for (var destroyedBullets_1 = __values(destroyedBullets), destroyedBullets_1_1 = destroyedBullets_1.next(); !destroyedBullets_1_1.done; destroyedBullets_1_1 = destroyedBullets_1.next()) {
                var id = destroyedBullets_1_1.value;
                this._internalRemoveBullet(id, false);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (destroyedBullets_1_1 && !destroyedBullets_1_1.done && (_b = destroyedBullets_1.return)) _b.call(destroyedBullets_1);
            }
            finally { if (e_5) throw e_5.error; }
        }
        // 处理延迟移除的子弹
        if (this._pendingRemovals.length > 0) {
            try {
                for (var _g = __values(this._pendingRemovals), _h = _g.next(); !_h.done; _h = _g.next()) {
                    var id = _h.value;
                    this._internalRemoveBullet(id, true);
                }
            }
            catch (e_6_1) { e_6 = { error: e_6_1 }; }
            finally {
                try {
                    if (_h && !_h.done && (_c = _g.return)) _c.call(_g);
                }
                finally { if (e_6) throw e_6.error; }
            }
            this._pendingRemovals.length = 0;
        }
    };
    /** 获取子弹统计信息 */
    BulletManager.prototype.getStats = function () {
        return {
            activeCount: this._activeBullets.size,
            totalCreated: this._totalCreated,
            totalDestroyed: this._totalDestroyed
        };
    };
    /** 根据ID获取子弹 */
    BulletManager.prototype.getBullet = function (bulletId) {
        return this._activeBullets.get(bulletId) || null;
    };
    /** 根据施法者获取子弹列表 */
    BulletManager.prototype.getBulletsByCaster = function (caster) {
        var e_7, _a;
        var bullets = [];
        try {
            for (var _b = __values(this._activeBullets.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var bullet = _c.value;
                if (bullet.caster === caster) {
                    bullets.push(bullet);
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return bullets;
    };
    /** 设置事件监听器 */
    BulletManager.prototype.setupEventListeners = function () {
        // 可以在这里添加全局子弹事件监听
    };
    /** 获取调试信息 */
    BulletManager.prototype.getDebugInfo = function () {
        var e_8, _a;
        var _b;
        var bullets = {};
        try {
            for (var _c = __values(this._activeBullets), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], bullet = _e[1];
                bullets[id] = {
                    caster: bullet.caster.characterName,
                    target: ((_b = bullet.target) === null || _b === void 0 ? void 0 : _b.characterName) || null,
                    remainingHits: bullet.remainingHits,
                    hasCollided: bullet.hasCollided,
                    isDestroyed: bullet.isDestroyed
                };
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return {
            stats: this.getStats(),
            bullets: bullets,
            pendingRemovals: this._pendingRemovals.length
        };
    };
    /** 打印调试信息 */
    BulletManager.prototype.printDebugInfo = function () {
        console.log("BulletManager Debug Info:", this.getDebugInfo());
    };
    Object.defineProperty(BulletManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    BulletManager.prototype.cleanup = function () {
        this.clearAllBullets();
        this._eventManager.cleanup();
    };
    return BulletManager;
}());
exports.BulletManager = BulletManager;

cc._RF.pop();