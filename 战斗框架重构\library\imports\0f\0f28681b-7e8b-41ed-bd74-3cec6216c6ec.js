"use strict";
cc._RF.push(module, '0f286gbfotB7b10POxiFsbs', 'SkillManager');
// fight/systems/SkillManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillManager = void 0;
var FightEvent_1 = require("../types/FightEvent");
var ISkill_1 = require("../types/ISkill");
var EventManager_1 = require("./EventManager");
/**
 * 技能管理器
 * 负责管理角色的技能学习、使用和冷却
 */
var SkillManager = /** @class */ (function () {
    function SkillManager(character) {
        this._skills = new Map();
        this._basicAttackSkill = null;
        this._character = character;
        this._eventManager = EventManager_1.EventManager.createLocal("SkillManager_" + character.characterName);
    }
    /**
     * 添加技能
     * @param skill 技能实例
     */
    SkillManager.prototype.addSkill = function (skill) {
        if (this._skills.has(skill.id)) {
            console.warn("Skill " + skill.id + " already exists, replacing...");
        }
        this._skills.set(skill.id, skill);
        // 如果是基础攻击技能，设置为默认攻击技能
        if (skill.type === ISkill_1.SkillType.BASIC_ATTACK) {
            this._basicAttackSkill = skill;
        }
        this._eventManager.emit(FightEvent_1.default.skillAdded, { skill: skill, character: this._character });
    };
    /**
     * 移除技能
     * @param skillId 技能ID
     * @returns 是否移除成功
     */
    SkillManager.prototype.removeSkill = function (skillId) {
        var _a;
        var skill = this._skills.get(skillId);
        if (!skill) {
            return false;
        }
        this._skills.delete(skillId);
        // 如果移除的是基础攻击技能，清空引用
        if (((_a = this._basicAttackSkill) === null || _a === void 0 ? void 0 : _a.id) === skillId) {
            this._basicAttackSkill = null;
        }
        this._eventManager.emit(FightEvent_1.default.skillRemoved, { skill: skill, character: this._character });
        return true;
    };
    /**
     * 获取技能
     * @param skillId 技能ID
     * @returns 技能实例或null
     */
    SkillManager.prototype.getSkill = function (skillId) {
        return this._skills.get(skillId) || null;
    };
    /**
     * 获取基础攻击技能
     * @returns 基础攻击技能或null
     */
    SkillManager.prototype.getBasicAttackSkill = function () {
        return this._basicAttackSkill;
    };
    /**
     * 设置基础攻击技能
     * @param skillId 技能ID
     */
    SkillManager.prototype.setBasicAttackSkill = function (skillId) {
        var skill = this._skills.get(skillId);
        if (skill) {
            this._basicAttackSkill = skill;
        }
    };
    /**
     * 获取所有技能
     * @returns 技能数组
     */
    SkillManager.prototype.getAllSkills = function () {
        return Array.from(this._skills.values());
    };
    /**
     * 根据类型获取技能
     * @param type 技能类型
     * @returns 技能数组
     */
    SkillManager.prototype.getSkillsByType = function (type) {
        return this.getAllSkills().filter(function (skill) { return skill.type === type; });
    };
    /**
     * 获取可用的技能
     * @returns 可用技能数组
     */
    SkillManager.prototype.getAvailableSkills = function () {
        return this.getAllSkills().filter(function (skill) { return skill.canUse; });
    };
    /**
     * 获取冷却中的技能
     * @returns 冷却中技能数组
     */
    SkillManager.prototype.getCooldownSkills = function () {
        return this.getAllSkills().filter(function (skill) { return !skill.canUse && skill.remainingCooldown > 0; });
    };
    /**
     * 检查是否拥有技能
     * @param skillId 技能ID
     * @returns 是否拥有
     */
    SkillManager.prototype.hasSkill = function (skillId) {
        return this._skills.has(skillId);
    };
    /**
     * 检查技能是否可用
     * @param skillId 技能ID
     * @returns 是否可用
     */
    SkillManager.prototype.isSkillAvailable = function (skillId) {
        var skill = this._skills.get(skillId);
        return skill ? skill.canUse : false;
    };
    /**
     * 获取技能冷却剩余时间
     * @param skillId 技能ID
     * @returns 剩余冷却时间（秒）
     */
    SkillManager.prototype.getSkillCooldownRemaining = function (skillId) {
        var skill = this._skills.get(skillId);
        return skill ? skill.remainingCooldown : 0;
    };
    /**
     * 重置技能冷却
     * @param skillId 技能ID，如果不指定则重置所有技能
     */
    SkillManager.prototype.resetCooldown = function (skillId) {
        var e_1, _a;
        if (skillId) {
            var skill = this._skills.get(skillId);
            if (skill) {
                skill.resetCooldown();
                this._eventManager.emit(FightEvent_1.default.skillCooldownReset, { skill: skill, character: this._character });
            }
        }
        else {
            try {
                for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var skill = _c.value;
                    skill.resetCooldown();
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_1) throw e_1.error; }
            }
            this._eventManager.emit(FightEvent_1.default.allSkillsCooldownReset, { character: this._character });
        }
    };
    /**
     * 使用技能
     * @param skillId 技能ID
     * @param target 目标角色
     * @param position 目标位置
     * @returns 是否使用成功
     */
    SkillManager.prototype.useSkill = function (skillId, target, position) {
        var skill = this._skills.get(skillId);
        if (!skill) {
            console.warn("Skill " + skillId + " not found");
            return false;
        }
        if (!skill.canCastOn(this._character, target)) {
            console.warn("Cannot cast skill " + skillId + " on target");
            return false;
        }
        var success = skill.cast(this._character, target, [], position);
        if (success) {
            this._eventManager.emit(FightEvent_1.default.skillUsed, { skill: skill, character: this._character, target: target, position: position });
        }
        return success;
    };
    /**
     * 更新所有技能状态
     * @param deltaTime 时间间隔
     */
    SkillManager.prototype.update = function (deltaTime) {
        var e_2, _a;
        try {
            for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var skill = _c.value;
                skill.update(deltaTime);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
    };
    /**
     * 获取技能统计信息
     * @returns 统计信息
     */
    SkillManager.prototype.getSkillStats = function () {
        var e_3, _a;
        var stats = {
            totalSkills: this._skills.size,
            availableSkills: 0,
            cooldownSkills: 0,
            skillsByType: {}
        };
        try {
            for (var _b = __values(this._skills.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var skill = _c.value;
                if (skill.canUse) {
                    stats.availableSkills++;
                }
                else {
                    stats.cooldownSkills++;
                }
                if (!stats.skillsByType[skill.type]) {
                    stats.skillsByType[skill.type] = 0;
                }
                stats.skillsByType[skill.type]++;
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return stats;
    };
    /**
     * 导出技能数据
     * @returns 技能数据
     */
    SkillManager.prototype.exportSkillData = function () {
        var e_4, _a;
        var data = {};
        try {
            for (var _b = __values(this._skills), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), id = _d[0], skill = _d[1];
                data[id] = {
                    id: skill.id,
                    name: skill.name,
                    type: skill.type,
                    level: skill.level,
                    cooldown: skill.cooldown,
                    remainingCooldown: skill.remainingCooldown,
                    canUse: skill.canUse
                };
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return data;
    };
    Object.defineProperty(SkillManager.prototype, "eventManager", {
        /**
         * 获取事件管理器（供外部直接使用，避免包装方法冗余）
         */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** * 清理技能管理器 */
    SkillManager.prototype.cleanup = function () {
        this._skills.clear();
        this._basicAttackSkill = null;
        this._eventManager.cleanup();
    };
    /** * 获取调试信息 */
    SkillManager.prototype.getDebugInfo = function () {
        var _a;
        return {
            characterId: this._character.id,
            skillCount: this._skills.size,
            basicAttackSkill: ((_a = this._basicAttackSkill) === null || _a === void 0 ? void 0 : _a.id) || null,
            skills: this.exportSkillData(),
            stats: this.getSkillStats()
        };
    };
    return SkillManager;
}());
exports.SkillManager = SkillManager;

cc._RF.pop();