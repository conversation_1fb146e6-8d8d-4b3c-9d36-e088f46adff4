
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/CampBase.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd646dLNTdxJ95Dtx6lS3GOz', 'CampBase');
// card/cardgame/src/base/CampBase.ts

// import BuffManager from '../../Controller/BuffManager';
// import { ECamp } from '../CampManager';
// import RoleManager from '../RoleManager';
// import Base from './Base';
// import RoleBase from './RoleBase';
// const { ccclass, property } = cc._decorator;
// declare global {
//     /**阵营数据 */
//     export interface ICampDataType {
//         /**阵营名字 */
//         campName: ECamp;
//     }
//     interface IDealInteractive {
//         addHp?: number
//         reduceHp?: number
//         attack?: number
//     }
// }
// /**
//  * @features : 阵营控制基类
//  * @description: 游戏中多个角色的阵营控制基类
//  * @Date : 2020-08-12 23:28:52
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:57:04
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class CampBase extends Base {
//     /**卡牌阵营信息 */
//     data: ICampDataType;
//     /**阵营角色管理 */
//     roleMgr = new RoleManager
//     /**对阵营使用Buff的管理 */
//     buffMgr = new BuffManager;
//     /**阵营名字*/
//     @Base.ViewLinked
//     campName: ECamp;
//     get isAllDeath() { return this.roleMgr.checkCardAllDeath() }
//     /**
//      * 初始化阵营 
//      * @param initData 要初始化的卡牌数据
//      * @param campData 要初始化的阵营数据
//      *  ! 组件查找顺序 -> 节点挂载 -> 节点的子节点查找
//      */
//     initCamp(initData: ICampMgrDataType) {
//         //初始化卡牌组件
//         // let cardList: CardBase[] = [];
//         // if (this.cardComList.length != 0) {
//         //     cardList = this.cardComList;
//         // } else if (this.node.children.length > 0) {
//         //     //尝试从节点下面或取卡牌
//         //     for (let cardNode of this.node.children) {
//         //         let card = cardNode.getComponent(CardBase);
//         //         if (card) cardList.push(card);
//         //     }
//         // } else cc.error(`[${this.className}]获取卡牌组件失败，请检查是否需要初始化or检查卡牌组件列表`);
//         this.initCampData = initData
//         this.roleMgr.initRoles(initData)
//         if (initData.campData) {
//             this.data = initData.campData;
//         } else cc.error(`[${this.className}]没有设置阵营数据！`);
//         this.restChose()
//     }
//     restChose() {
//         if (this.curRole == null || this.curRole.hp == 0) {
//             this.curRole = this.roleMgr.roles.find(r => r.hp != 0)
//         }
//     }
//     initCampData: ICampMgrDataType
//     restCamp() {
//         if (this.roleMgr.roles.length == 0) {
//             this.roleMgr.roles = []
//             this.roleMgr.initRoles(this.initCampData)
//         } else {
//             this.restChose()
//             // this.curRole.name = this.curRole.name
//         }
//     }
//     async attackCamp(attackCamp: CampBase, roles?: RoleBase[]) {
//         cc.log(`阵营[${this.campName}]开始准备攻击`);
//         let card = this.roleMgr.getRoleCard(1)
//         if (card.length != 0) {
//             await card[0].attackCamp(attackCamp);
//             cc.log(`阵营[${attackCamp.campName}]攻击完成`);
//         }
//     }
//     addHp(addValue: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             role.hp += addValue
//             return { addHp: addValue }
//         }
//     }
//     addHpByPercent(addPercent: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let complete = role.hp * (addPercent / 100)
//             role.hp += complete
//             return { addHp: complete }
//         }
//     }
//     reduceHp(addValue: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             role.hp -= addValue
//             return { reduceHp: addValue }
//         }
//     }
//     reduceHpByPercent(addPercent: number): IDealInteractive {
//         let role = this.getRole()
//         if (role) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let complete = role.hp * (addPercent / 100)
//             role.hp -= complete
//             return { reduceHp: complete }
//         }
//     }
//     /**攻击指定角色 */
//     attackRole(attackRole: RoleBase): IDealInteractive {
//         let selfRole = this.getRole()
//         if (selfRole && attackRole) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             let dodge = Boolean.randomBoolean(80)
//             dodge = true
//             if (dodge && attackRole.hp > 0) {
//                 let v = selfRole.attack * (1 - attackRole.defence / (attackRole.defence + 150))
//                 attackRole.hp -= parseInt(v.toString());
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);
//                 return { attack: selfRole.attack }
//             } else {
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发闪避`);
//                 return { attack: 0 }
//             }
//         }
//     }
//     /** 卡片攻击指定角色*/
//     attackRoleByCard(attackRole: RoleBase, aggressivity: number): IDealInteractive {
//         let selfRole = this.getRole()
//         if (selfRole && attackRole) {
//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)
//             // return role.attackRoleByCard(attackRole, aggressivity);
//             if (attackRole.hp > 0) {
//                 attackRole.hp -= aggressivity;
//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);
//                 return { attack: aggressivity }
//             }
//         }
//     }
//     curRole: RoleBase
//     getRole(): RoleBase | null {
//         return this.curRole
//     }
//     getRoleEquipment() {
//         return this.getRole()?.equipMgr
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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