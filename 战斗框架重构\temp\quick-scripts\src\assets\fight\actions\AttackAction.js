"use strict";
cc._RF.push(module, 'aa8d9i2HDBGRY3h7us0CBBt', 'AttackAction');
// fight/actions/AttackAction.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackAction = void 0;
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 攻击动作类*/
var AttackAction = /** @class */ (function (_super) {
    __extends(AttackAction, _super);
    function AttackAction() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**当前攻击状态 */
        _this.currentState = CharacterTypes_1.AttackState.IDLE;
        /**攻击计时器 */
        _this._attackTimer = 0;
        /**当前攻击属性 */
        _this._currentProps = null;
        /**回调标记 */
        _this._hurtStartTriggered = false;
        _this._hurtEndTriggered = false;
        /**攻击队列 */
        _this._attackQueue = [];
        /**是否允许攻击队列 */
        _this.allowQueue = false;
        /**最大队列长度 */
        _this.maxQueueLength = 3;
        return _this;
    }
    Object.defineProperty(AttackAction.prototype, "isAttacking", {
        /*** 是否正在攻击*/
        get: function () {
            return this.currentState !== CharacterTypes_1.AttackState.IDLE;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackAction.prototype, "canStartNewAttack", {
        /** * 是否可以开始新的攻击 */
        get: function () {
            return this.currentState === CharacterTypes_1.AttackState.IDLE || (this.allowQueue && this._attackQueue.length < this.maxQueueLength);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AttackAction.prototype, "attackProgress", {
        /** * 获取攻击进度 (0-1) */
        get: function () {
            if (!this._currentProps || this.currentState === CharacterTypes_1.AttackState.IDLE) {
                return 0;
            }
            return Math.min(1, this._attackTimer / this._currentProps.hurtEndTimeMs);
        },
        enumerable: false,
        configurable: true
    });
    /*** 更新攻击状态*/
    AttackAction.prototype.update = function (dt) {
        if (this.currentState === CharacterTypes_1.AttackState.IDLE) {
            return;
        }
        this.updateAttackTimer(dt);
        this.updateAttackState();
        this.triggerCallbacks();
    };
    /** * 组件销毁时清理 */
    AttackAction.prototype.onDestroy = function () {
        this.interruptAttack();
        this.clearQueue();
    };
    /**
     * 执行一次攻击
     * @param props 攻击属性
     * @returns 是否成功开始攻击
     */
    AttackAction.prototype.doAttackOnce = function (props) {
        if (!this.validateAttackProps(props)) {
            console.error("Invalid attack properties");
            return false;
        }
        if (this.currentState === CharacterTypes_1.AttackState.IDLE) {
            this.startAttack(props);
            return true;
        }
        else if (this.allowQueue && this._attackQueue.length < this.maxQueueLength) {
            this._attackQueue.push(props);
            return true;
        }
        return false;
    };
    /*** 中断当前攻击*/
    AttackAction.prototype.interruptAttack = function () {
        if (this.currentState !== CharacterTypes_1.AttackState.IDLE) {
            this.resetAttack();
            this.setState(CharacterTypes_1.AttackState.IDLE);
        }
    };
    /*** 清空攻击队列*/
    AttackAction.prototype.clearQueue = function () {
        this._attackQueue.length = 0;
    };
    /** * 获取队列中的攻击数量 */
    AttackAction.prototype.getQueueLength = function () { return this._attackQueue.length; };
    /**  * 开始攻击  */
    AttackAction.prototype.startAttack = function (props) {
        this._currentProps = props;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
        this.setState(CharacterTypes_1.AttackState.WINDUP);
    };
    /*** 更新攻击计时器*/
    AttackAction.prototype.updateAttackTimer = function (dt) {
        this._attackTimer += dt * 1000; // 转换为毫秒
    };
    /** * 更新攻击状态 */
    AttackAction.prototype.updateAttackState = function () {
        if (!this._currentProps) {
            return;
        }
        var _a = this._currentProps, hurtStartTimeMs = _a.hurtStartTimeMs, hurtEndTimeMs = _a.hurtEndTimeMs;
        if (this._attackTimer >= hurtEndTimeMs) {
            this.setState(CharacterTypes_1.AttackState.IDLE);
            this.processNextAttack();
        }
        else if (this._attackTimer >= hurtStartTimeMs && this.currentState === CharacterTypes_1.AttackState.WINDUP) {
            this.setState(CharacterTypes_1.AttackState.DAMAGE);
        }
    };
    /*** 触发回调*/
    AttackAction.prototype.triggerCallbacks = function () {
        if (!this._currentProps) {
            return;
        }
        var _a = this._currentProps, hurtStartTimeMs = _a.hurtStartTimeMs, hurtEndTimeMs = _a.hurtEndTimeMs, onHurtStart = _a.onHurtStart, onHurtEnd = _a.onHurtEnd, onAttackProgress = _a.onAttackProgress;
        if (!this._hurtStartTriggered && this._attackTimer >= hurtStartTimeMs) {
            this._hurtStartTriggered = true;
            onHurtStart === null || onHurtStart === void 0 ? void 0 : onHurtStart();
        }
        if (onAttackProgress) {
            onAttackProgress(this.attackProgress);
        }
        if (!this._hurtEndTriggered && this._attackTimer >= hurtEndTimeMs) {
            this._hurtEndTriggered = true;
            onHurtEnd === null || onHurtEnd === void 0 ? void 0 : onHurtEnd();
        }
    };
    /*** 设置攻击状态*/
    AttackAction.prototype.setState = function (newState) {
        if (this.currentState !== newState) {
            var oldState = this.currentState;
            this.currentState = newState;
            this.onStateChanged(oldState, newState);
        }
    };
    /** * 状态改变时的处理 */
    AttackAction.prototype.onStateChanged = function (oldState, newState) {
        this.node.emit(FightEvent_1.default.attackStateChanged, { oldState: oldState, newState: newState, component: this });
        switch (newState) {
            case CharacterTypes_1.AttackState.IDLE:
                this.onEnterIdle();
                break;
            case CharacterTypes_1.AttackState.WINDUP:
                this.onEnterWindup();
                break;
            case CharacterTypes_1.AttackState.DAMAGE:
                this.onEnterDamage();
                break;
            case CharacterTypes_1.AttackState.RECOVERY:
                this.onEnterRecovery();
                break;
        }
    };
    /** * 重置攻击状态 */
    AttackAction.prototype.resetAttack = function () {
        this._currentProps = null;
        this._attackTimer = 0;
        this._hurtStartTriggered = false;
        this._hurtEndTriggered = false;
    };
    /** * 处理下一个攻击 */
    AttackAction.prototype.processNextAttack = function () {
        if (this._attackQueue.length > 0) {
            var nextAttack = this._attackQueue.shift();
            this.startAttack(nextAttack);
        }
    };
    /** * 验证攻击属性 */
    AttackAction.prototype.validateAttackProps = function (props) {
        if (!props) {
            return false;
        }
        if (props.hurtStartTimeMs < 0 || props.hurtEndTimeMs < 0) {
            return false;
        }
        if (props.hurtStartTimeMs > props.hurtEndTimeMs) {
            return false;
        }
        return true;
    };
    /*** 进入空闲状态*/
    AttackAction.prototype.onEnterIdle = function () {
        this.resetAttack();
    };
    /** * 进入前摇状态 */
    AttackAction.prototype.onEnterWindup = function () {
        // 可以在这里添加前摇特效
    };
    /** * 进入伤害状态 */
    AttackAction.prototype.onEnterDamage = function () {
        // 可以在这里添加伤害特效
    };
    /*** 进入后摇状态*/
    AttackAction.prototype.onEnterRecovery = function () {
        // 可以在这里添加后摇特效
    };
    /** * 获取攻击信息 */
    AttackAction.prototype.getAttackInfo = function () {
        return {
            currentState: this.currentState,
            isAttacking: this.isAttacking,
            attackProgress: this.attackProgress,
            queueLength: this._attackQueue.length,
            canStartNewAttack: this.canStartNewAttack,
            currentProps: this._currentProps ? {
                hurtStartTimeMs: this._currentProps.hurtStartTimeMs,
                hurtEndTimeMs: this._currentProps.hurtEndTimeMs
            } : null
        };
    };
    __decorate([
        property(cc.Boolean)
    ], AttackAction.prototype, "allowQueue", void 0);
    __decorate([
        property(cc.Integer)
    ], AttackAction.prototype, "maxQueueLength", void 0);
    AttackAction = __decorate([
        ccclass
    ], AttackAction);
    return AttackAction;
}(cc.Component));
exports.AttackAction = AttackAction;

cc._RF.pop();