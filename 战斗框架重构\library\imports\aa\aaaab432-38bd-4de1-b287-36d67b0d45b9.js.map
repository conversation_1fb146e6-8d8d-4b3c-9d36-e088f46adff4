{"version": 3, "sources": ["assets\\fight\\types\\SkillName.ts"], "names": [], "mappings": ";;;;;;AAAA;IAAA;IAgBA,CAAC;IAfG,SAAS;IACO,4BAAkB,GAAG,oBAAoB,CAAC;IAC1D,UAAU;IACM,uBAAa,GAAG,eAAe,CAAC;IAChD,SAAS;IACO,uBAAa,GAAG,eAAe,CAAC;IAChD,UAAU;IACM,uBAAa,GAAG,eAAe,CAAC;IAEhC,gBAAM,GAAG,QAAQ,CAAC;IAClB,gBAAM,GAAG,QAAQ,CAAC;IAClB,gBAAM,GAAG,QAAQ,CAAC;IAClB,gBAAM,GAAG,QAAQ,CAAC;IAClB,gBAAM,GAAG,QAAQ,CAAC;IAClB,gBAAM,GAAG,QAAQ,CAAC;IACtC,gBAAC;CAhBD,AAgBC,IAAA;kBAhBoB,SAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["export default class SkillName {\r\n    /**火球术 */\r\n    static readonly player_skill_fire1 = \"player_skill_fire1\";\r\n    /**治疗之光 */\r\n    static readonly healing_light = \"healing_light\";\r\n    /**雷暴术 */\r\n    static readonly thunder_storm = \"thunder_storm\";\r\n    /**冲锋攻击 */\r\n    static readonly charge_attack = \"charge_attack\";\r\n\r\n    static readonly skill1 = \"skill1\";\r\n    static readonly skill2 = \"skill2\";\r\n    static readonly skill3 = \"skill3\";\r\n    static readonly skill4 = \"skill4\";\r\n    static readonly skill5 = \"skill5\";\r\n    static readonly skill6 = \"skill6\";\r\n}"]}