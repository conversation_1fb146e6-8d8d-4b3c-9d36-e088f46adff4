
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/Equipment.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'be52a2WoPFKC64tzUBQLRs/', 'Equipment');
// card/cardgame/src/base/Equipment.ts

// import BuffManager from "../BuffManager";
// import { ECamp } from "../CampManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";
// /**buff类型 */
// export enum EBuffType {
//     None = `None`,
// }
// /**卡片的状态 */
// export enum EquipmentStatus {
//     /**正常 */
//     Normal = `Normal`,
//     /**损坏 */
//     Broken = `Broken`,
//     /**已装备 */
//     Equiped = `Equiped`,
//     /**无法使用 */
//     Unable = `Unable`,
// }
// declare global {
//     type IEquipmentName = 'Equipment'
//     export interface IEquipmentData {
//         equipmentName: string;
//         /**耐久 */
//         durable: number
//         /**提供的防御力 */
//         defence: number
//         /**提供的攻击力 */
//         attack: number
//         status: EquipmentStatus
//         camp: ECamp
//         orginValue: number
//     }
//     // interface IMainUIData {
//     // }
// }
// /** 
//  * @features : 游戏装备
//  * @description : 针对游戏技能的Buff基类， 所有技能buff都将继承该类 ，并且该类及子类不可挂载到场景中
//  * @Date : 2020-08-12 23:28:43
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 14:01:01
//  * @LastEditors : judu233
//  */
// export default class Equipment extends Base implements IEquipmentData {
//     /**技能数据 */
//     data: IEquipmentData;
//     /**装备技能管理 */
//     skillMgr = new SkillManager
//     /**对装备使用buff管理 */
//     buffMgr = new BuffManager
//     /**buff的Id */
//     @Base.ViewLinked
//     id: string
//     /**buff的类型 */
//     @Base.ViewLinked
//     type: string
//     /**耐久 */
//     @Base.ViewLinked
//     durable: number
//     @Base.ViewLinked
//     defence: number
//     @Base.ViewLinked
//     attack: number
//     @Base.ViewLinked
//     status: EquipmentStatus
//     @Base.ViewLinked
//     equipmentName: string
//     @Base.ViewLinked
//     camp: ECamp
//     @Base.ViewLinked
//     orginValue: number
//     init() {
//         this.status = EquipmentStatus.Normal
//     }
//     durableChangeView(newValue: number) {
//         if (newValue <= 0) {
//             this.status = EquipmentStatus.Broken
//         }
//     }
//     /**使用buff ` 子类负责实现具体逻辑*/
//     useBuff() { }
//     /**移除Buff 子类负责实现具体逻辑*/
//     removeBuff() { }
//     /**叠加一层buff 子类负责实现具体逻辑*/
//     overlayBuff() { }
//     /**减少一层buff */
//     reduceBuff() { }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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