"use strict";
cc._RF.push(module, 'd5aceRFOsBF96dPMgv7wSgL', 'IDamage');
// fight/types/IDamage.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageTag = exports.DamageType = void 0;
/*** 伤害类型枚举*/
var DamageType;
(function (DamageType) {
    /** 物理伤害 */
    DamageType["PHYSICAL"] = "physical";
    /** 魔法伤害 */
    DamageType["MAGIC"] = "magic";
    /** 真实伤害 */
    DamageType["TRUE"] = "true";
    /** 治疗 */
    DamageType["HEAL"] = "heal";
    /** 毒素伤害 */
    DamageType["POISON"] = "poison";
})(DamageType = exports.DamageType || (exports.DamageType = {}));
/** * 伤害标签枚举 */
var DamageTag;
(function (DamageTag) {
    /** 直接伤害 */
    DamageTag["DIRECT"] = "direct";
    /** 持续伤害 */
    DamageTag["OVER_TIME"] = "over_time";
    /** 反射伤害 */
    DamageTag["REFLECT"] = "reflect";
    /** 溅射伤害 */
    DamageTag["SPLASH"] = "splash";
    /** 穿透伤害 */
    DamageTag["PENETRATING"] = "penetrating";
    /** 暴击伤害 */
    DamageTag["CRITICAL"] = "critical";
    /** 技能伤害 */
    DamageTag["SKILL"] = "skill";
    /** 普攻伤害 */
    DamageTag["BASIC_ATTACK"] = "basic_attack";
})(DamageTag = exports.DamageTag || (exports.DamageTag = {}));

cc._RF.pop();