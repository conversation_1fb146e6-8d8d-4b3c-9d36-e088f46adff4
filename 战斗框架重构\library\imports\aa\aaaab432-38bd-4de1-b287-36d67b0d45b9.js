"use strict";
cc._RF.push(module, 'aaaabQyOL1N4bKHNtZ7DUW5', 'SkillName');
// fight/types/SkillName.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var SkillName = /** @class */ (function () {
    function SkillName() {
    }
    /**火球术 */
    SkillName.player_skill_fire1 = "player_skill_fire1";
    /**治疗之光 */
    SkillName.healing_light = "healing_light";
    /**雷暴术 */
    SkillName.thunder_storm = "thunder_storm";
    /**冲锋攻击 */
    SkillName.charge_attack = "charge_attack";
    SkillName.skill1 = "skill1";
    SkillName.skill2 = "skill2";
    SkillName.skill3 = "skill3";
    SkillName.skill4 = "skill4";
    SkillName.skill5 = "skill5";
    SkillName.skill6 = "skill6";
    return SkillName;
}());
exports.default = SkillName;

cc._RF.pop();