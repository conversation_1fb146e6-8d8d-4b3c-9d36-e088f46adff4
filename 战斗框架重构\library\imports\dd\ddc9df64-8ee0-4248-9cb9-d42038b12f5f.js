"use strict";
cc._RF.push(module, 'ddc9d9kjuBCSJy51CA4sS9f', 'BaseCharacter');
// fight/characters/BaseCharacter.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCharacter = void 0;
var BuffManager_1 = require("../systems/BuffManager");
var EventManager_1 = require("../systems/EventManager");
var SkillManager_1 = require("../systems/SkillManager");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var IDamage_1 = require("../types/IDamage");
var CharacterAttributes_1 = require("./CharacterAttributes");
/*** 角色基类*/
var BaseCharacter = /** @class */ (function (_super) {
    __extends(BaseCharacter, _super);
    function BaseCharacter() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._state = CharacterTypes_1.CharacterState.IDLE;
        _this._selectTag = CharacterTypes_1.CharacterSelectTag.NONE;
        // 事件回调
        _this._events = {};
        return _this;
    }
    Object.defineProperty(BaseCharacter.prototype, "id", {
        // 实现ICharacter接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "characterName", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "role", {
        get: function () { return this._role; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "attributes", {
        get: function () { return this._attributes; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "isDead", {
        get: function () { return this._state === CharacterTypes_1.CharacterState.DEAD; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "skills", {
        get: function () { return this._skillManager.getAllSkills(); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "buffs", {
        get: function () { return this._buffManager.buffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "skillManager", {
        get: function () { return this._skillManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "buffManager", {
        get: function () { return this._buffManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "state", {
        // 额外属性
        get: function () { return this._state; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "selectTag", {
        get: function () { return this._selectTag; },
        set: function (value) { this._selectTag = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseCharacter.prototype, "spine", {
        get: function () { return this._spine; },
        enumerable: false,
        configurable: true
    });
    /*** 初始化角色*/
    BaseCharacter.prototype.onLoad = function () {
        this.initializeId();
        this.initializeComponents();
        this.initializeNodes();
        this.setupEventListeners();
    };
    /** * 启动角色 */
    BaseCharacter.prototype.start = function () {
        this.onCharacterStart();
    };
    /** * 更新角色状态 */
    BaseCharacter.prototype.update = function (deltaTime) {
        if (this.isDead)
            return;
        this._skillManager.update(deltaTime);
        this._buffManager.update(deltaTime);
        this.onCharacterUpdate(deltaTime);
    };
    /** * 销毁角色 */
    BaseCharacter.prototype.onDestroy = function () {
        this.cleanup();
    };
    /** * 初始化ID */
    BaseCharacter.prototype.initializeId = function () {
        this._id = this.generateUniqueId();
    };
    /** * 初始化组件 */
    BaseCharacter.prototype.initializeComponents = function () {
        this._attributes = new CharacterAttributes_1.CharacterAttributes();
        this._skillManager = new SkillManager_1.SkillManager(this);
        this._buffManager = new BuffManager_1.BuffManager(this);
        this._eventManager = EventManager_1.EventManager.createLocal("Character_" + (this._name || this._id));
    };
    /** * 初始化节点引用 */
    BaseCharacter.prototype.initializeNodes = function () {
        this._spine = this.getComponent(sp.Skeleton);
        this._fireNode = this.node.getChildByName("fireNode");
        this._fireBaseNode = this.node.getChildByName("fireBaseNode");
    };
    /** * 设置事件监听器 */
    BaseCharacter.prototype.setupEventListeners = function () {
        this._eventManager.on(FightEvent_1.default.attributeChanged, this.onAttributeChanged.bind(this));
        this._eventManager.on(FightEvent_1.default.stateChanged, this.onStateChanged.bind(this));
        this._eventManager.on(FightEvent_1.default.death, this.onDeath.bind(this));
    };
    /** * 移动角色 */
    BaseCharacter.prototype.move = function (direction) {
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.STUNNED) {
            return false;
        }
        if (this.canMove()) {
            this.setState(CharacterTypes_1.CharacterState.MOVING);
            this.performMove(direction);
            return true;
        }
        return false;
    };
    /** * 攻击目标 */
    BaseCharacter.prototype.attack = function (target) {
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.STUNNED) {
            return false;
        }
        var attackSkill = this._skillManager.getBasicAttackSkill();
        if (attackSkill) {
            return this.castSkill(attackSkill.id, target === null || target === void 0 ? void 0 : target.node);
        }
        return false;
    };
    /** * 释放技能 */
    BaseCharacter.prototype.castSkill = function (skillName, target) {
        var _a, _b;
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.SILENCED) {
            return false;
        }
        var skill = this._skillManager.getSkill(skillName);
        if (!skill || !skill.canUse) {
            return false;
        }
        if (!this.checkResourceCost(skill)) {
            return false;
        }
        this.consumeResources(skill);
        this.setState(CharacterTypes_1.CharacterState.CASTING);
        var success = skill.cast(this, this.getCharacterFromNode(target), undefined, this.getWorldPosition(target));
        if (success) {
            (_b = (_a = this._events).onSkillCast) === null || _b === void 0 ? void 0 : _b.call(_a, this, skillName);
            this._eventManager.emit(FightEvent_1.default.skillCast, { character: this, skillName: skillName });
        }
        return success;
    };
    /** * 学习技能 */
    BaseCharacter.prototype.learnSkill = function (skill) {
        this._skillManager.addSkill(skill);
    };
    /** * 添加Buff */
    BaseCharacter.prototype.addBuff = function (buff) {
        var _a, _b;
        this._buffManager.addBuff(buff);
        (_b = (_a = this._events).onBuffAdded) === null || _b === void 0 ? void 0 : _b.call(_a, this, buff);
        this._eventManager.emit(FightEvent_1.default.buffAdded, { character: this, buff: buff });
    };
    /*** 移除Buff*/
    BaseCharacter.prototype.removeBuff = function (buffId) {
        var _a, _b;
        var removed = this._buffManager.removeBuff(buffId);
        if (removed) {
            (_b = (_a = this._events).onBuffRemoved) === null || _b === void 0 ? void 0 : _b.call(_a, this, buffId);
            this._eventManager.emit(FightEvent_1.default.buffRemoved, { character: this, buffId: buffId });
        }
    };
    /*** 受到伤害（新版本，使用伤害信息对象）*/
    BaseCharacter.prototype.takeDamage = function (damageInfo) {
        var _this = this;
        var _a, _b, _c;
        if (this.isDead || this._state === CharacterTypes_1.CharacterState.INVINCIBLE) {
            return;
        }
        // 检查是否闪避
        if (damageInfo.isDodged) {
            console.log(this.characterName + " dodged the attack!");
            this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
            return;
        }
        // 应用伤害
        var finalDamage = damageInfo.finalDamage;
        this._attributes.modifyHp(-finalDamage);
        // 根据伤害类型处理特殊效果
        this.handleDamageTypeEffects(damageInfo);
        // 处理附加的Buff
        if (damageInfo.attachedBuffs && damageInfo.attachedBuffs.length > 0) {
            damageInfo.attachedBuffs.forEach(function (buff) {
                _this.addBuff(buff);
            });
        }
        // 触发事件
        (_b = (_a = this._events).onTakeDamage) === null || _b === void 0 ? void 0 : _b.call(_a, this, finalDamage, damageInfo.attacker);
        this._eventManager.emit(FightEvent_1.default.takeDamage, damageInfo);
        // 检查死亡
        if (this._attributes.isDead()) {
            this.die();
        }
        // 日志输出
        var damageTypeStr = damageInfo.isCritical ? "CRITICAL " + damageInfo.damageType : damageInfo.damageType;
        console.log(this.characterName + " takes " + finalDamage + " " + damageTypeStr + " damage from " + (((_c = damageInfo.attacker) === null || _c === void 0 ? void 0 : _c.characterName) || 'unknown'));
    };
    /*** 受到伤害（简化版本，向后兼容）*/
    BaseCharacter.prototype.takeDamageSimple = function (damage, attacker) {
        // 创建简单的伤害信息对象
        var simpleDamageInfo = {
            id: "simple_damage_" + Date.now(),
            attacker: attacker || null,
            target: this,
            baseDamage: damage,
            finalDamage: damage,
            damageType: IDamage_1.DamageType.PHYSICAL,
            tags: [IDamage_1.DamageTag.DIRECT],
            isCritical: false,
            criticalMultiplier: 1.0,
            damageReduction: 0,
            damageAmplification: 0,
            attachedBuffs: [],
            isDodged: false,
            source: 'simple_attack',
            calculateFinalDamage: function () { return damage; },
            addTag: function (tag) {
                if (!simpleDamageInfo.tags.includes(tag)) {
                    simpleDamageInfo.tags.push(tag);
                }
            },
            hasTag: function (tag) { return simpleDamageInfo.tags.includes(tag); },
            addAttachedBuff: function (buff) {
                simpleDamageInfo.attachedBuffs.push(buff);
            }
        };
        this.takeDamage(simpleDamageInfo);
    };
    /** 处理不同伤害类型的特殊效果 */
    BaseCharacter.prototype.handleDamageTypeEffects = function (damageInfo) {
        switch (damageInfo.damageType) {
            case IDamage_1.DamageType.POISON:
                // 毒素伤害可能有持续效果
                console.log(this.characterName + " is poisoned!");
                break;
            case IDamage_1.DamageType.MAGIC:
                // 魔法伤害可能有特殊效果
                break;
            case IDamage_1.DamageType.TRUE:
                // 真实伤害无视防御
                console.log(this.characterName + " takes true damage!");
                break;
            default:
                break;
        }
        // 处理伤害标签
        if (damageInfo.hasTag(IDamage_1.DamageTag.CRITICAL)) {
            console.log("Critical hit on " + this.characterName + "!");
        }
        if (damageInfo.hasTag(IDamage_1.DamageTag.PENETRATING)) {
            console.log("Penetrating damage to " + this.characterName + "!");
        }
    };
    /** * 治疗 */
    BaseCharacter.prototype.heal = function (healAmount) {
        var _a, _b;
        if (this.isDead)
            return;
        this._attributes.modifyHp(healAmount);
        (_b = (_a = this._events).onHeal) === null || _b === void 0 ? void 0 : _b.call(_a, this, healAmount);
        this._eventManager.emit(FightEvent_1.default.heal, { character: this, healAmount: healAmount });
    };
    /** * 死亡处理 */
    BaseCharacter.prototype.die = function () {
        var _a, _b;
        if (this.isDead)
            return;
        this.setState(CharacterTypes_1.CharacterState.DEAD);
        (_b = (_a = this._events).onDeath) === null || _b === void 0 ? void 0 : _b.call(_a, this);
        this._eventManager.emit(FightEvent_1.default.death, { character: this });
        this.onCharacterDeath();
    };
    /** * 设置事件监听器 */
    BaseCharacter.prototype.setEvents = function (events) {
        this._events = __assign(__assign({}, this._events), events);
    };
    /**  * 设置状态  */
    BaseCharacter.prototype.setState = function (newState) {
        if (this._state !== newState) {
            var oldState = this._state;
            this._state = newState;
            this._eventManager.emit(FightEvent_1.default.stateChanged, { character: this, oldState: oldState, newState: newState });
        }
    };
    /**   * 检查是否可以移动   */
    BaseCharacter.prototype.canMove = function () {
        return this._state === CharacterTypes_1.CharacterState.IDLE || this._state === CharacterTypes_1.CharacterState.MOVING;
    };
    /** * 检查资源消耗 */
    BaseCharacter.prototype.checkResourceCost = function (_skill) {
        // 这里应该检查MP、耐力等资源
        return true; // 简化实现
    };
    /*** 消耗资源    */
    BaseCharacter.prototype.consumeResources = function (_skill) {
        // 这里应该消耗MP、耐力等资源
    };
    /** * 从节点获取角色 */
    BaseCharacter.prototype.getCharacterFromNode = function (node) {
        return node === null || node === void 0 ? void 0 : node.getComponent(BaseCharacter);
    };
    /** * 获取世界坐标 */
    BaseCharacter.prototype.getWorldPosition = function (node) {
        return node === null || node === void 0 ? void 0 : node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    };
    /** * 生成唯一ID */
    BaseCharacter.prototype.generateUniqueId = function () {
        return "char_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
    };
    /*** 清理资源*/
    BaseCharacter.prototype.cleanup = function () {
        var _a, _b, _c;
        (_a = this._skillManager) === null || _a === void 0 ? void 0 : _a.cleanup();
        (_b = this._buffManager) === null || _b === void 0 ? void 0 : _b.cleanup();
        (_c = this._eventManager) === null || _c === void 0 ? void 0 : _c.cleanup();
    };
    /**属性变化处理 */
    BaseCharacter.prototype.onAttributeChanged = function (_event) {
    };
    BaseCharacter.prototype.onStateChanged = function (_event) {
        this.updateAnimation();
    };
    BaseCharacter.prototype.onDeath = function (_event) {
        this.playDeathAnimation();
    };
    /*** 更新动画*/
    BaseCharacter.prototype.updateAnimation = function () {
        if (!this._spine || !this._animationConfig)
            return;
        var animationName = "";
        switch (this._state) {
            case CharacterTypes_1.CharacterState.IDLE:
                animationName = this._animationConfig.idle;
                break;
            case CharacterTypes_1.CharacterState.MOVING:
                animationName = this._animationConfig.move;
                break;
            case CharacterTypes_1.CharacterState.ATTACKING:
                animationName = this._animationConfig.attack;
                break;
            case CharacterTypes_1.CharacterState.DEAD:
                animationName = this._animationConfig.death;
                break;
        }
        if (animationName) {
            this._spine.setAnimation(0, animationName, this._state !== CharacterTypes_1.CharacterState.DEAD);
        }
    };
    /** * 播放死亡动画 */
    BaseCharacter.prototype.playDeathAnimation = function () {
        var _a;
        if (this._spine && ((_a = this._animationConfig) === null || _a === void 0 ? void 0 : _a.death)) {
            this._spine.setAnimation(0, this._animationConfig.death, false);
        }
    };
    return BaseCharacter;
}(cc.Component));
exports.BaseCharacter = BaseCharacter;

cc._RF.pop();