import { IBullet, IBulletConfig, IBulletManager } from "../types/IBullet";
import { ICharacter } from "../types/ICharacter";
import { EventManager } from "./EventManager";
import FightEvent from "../types/FightEvent";
import { Bullet } from "./BulletSystem";

/**
 * 子弹管理器实现
 * 负责管理所有活跃的子弹
 */
export class BulletManager implements IBulletManager {
    private _activeBullets: Map<string, IBullet> = new Map();
    private _eventManager: EventManager;
    private _totalCreated: number = 0;
    private _totalDestroyed: number = 0;
    private _isUpdating: boolean = false;
    private _pendingRemovals: string[] = [];

    constructor() {
        this._eventManager = EventManager.createLocal("BulletManager");
        this.setupEventListeners();
    }
    /** 获取所有活跃的子弹 */
    get activeBullets(): ReadonlyArray<IBullet> {
        return Array.from(this._activeBullets.values());
    }

    /** 创建子弹 */
    createBullet(config: IBulletConfig, caster: ICharacter, target?: ICharacter, targetPosition?: cc.Vec3): IBullet {
        const bulletId = `bullet_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        const bullet = new Bullet(bulletId, config, caster, firePosition, target, targetPosition);
        this.addBullet(bullet);
        return bullet;
    }

    /** 添加子弹到管理器 */
    addBullet(bullet: IBullet): void {
        if (this._activeBullets.has(bullet.id)) {
            console.warn(`Bullet ${bullet.id} already exists in manager`);
            return;
        }
        this._activeBullets.set(bullet.id, bullet);
        this._totalCreated++;
        console.log(`Bullet ${bullet.id} added to manager`);
        this._eventManager.emit(FightEvent.bulletFired, { bullet });
    }

    /** 移除子弹 */
    removeBullet(bulletId: string): void {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(bulletId);
            return;
        }
        this._internalRemoveBullet(bulletId, true);
    }

    /** 内部移除子弹方法 - 避免事件循环 */
    private _internalRemoveBullet(bulletId: string, shouldEmitEvent: boolean = false): void {
        const bullet = this._activeBullets.get(bulletId);
        if (bullet) {
            // 确保子弹被销毁，但不重复销毁
            if (!bullet.isDestroyed) {
                bullet.destroy();
            }
            this._activeBullets.delete(bulletId);
            this._totalDestroyed++;
            console.log(`Bullet ${bulletId} removed from manager`);

            // 只有在外部调用时才发射事件，避免循环
            if (shouldEmitEvent) {
                this._eventManager.emit(FightEvent.bulletDestroyed, { bullet });
            }
        }
    }

    /** 根据施法者移除子弹 */
    removeBulletsByCaster(caster: ICharacter): void {
        const bulletsToRemove: string[] = [];
        for (const [id, bullet] of this._activeBullets) {
            if (bullet.caster === caster) {
                bulletsToRemove.push(id);
            }
        }
        for (const id of bulletsToRemove) {
            this.removeBullet(id);
        }
        console.log(`Removed ${bulletsToRemove.length} bullets for caster ${caster.characterName}`);
    }

    /** 清除所有子弹 */
    clearAllBullets(): void {
        const bulletIds = Array.from(this._activeBullets.keys());
        for (const id of bulletIds) {
            this.removeBullet(id);
        }
        console.log(`Cleared all bullets (${bulletIds.length} bullets)`);
    }

    /** 更新所有子弹 */
    update(deltaTime: number): void {
        if (this._activeBullets.size === 0) {
            return;
        }
        this._isUpdating = true;
        const destroyedBullets: string[] = [];
        // 更新所有子弹
        for (const [id, bullet] of this._activeBullets) {
            const isDestroyed = bullet.update(deltaTime);
            if (isDestroyed) {
                destroyedBullets.push(id);
            }
        }
        this._isUpdating = false;
        // 移除已销毁的子弹 - 使用内部方法避免重复销毁
        for (const id of destroyedBullets) {
            this._internalRemoveBullet(id, false);
        }
        // 处理延迟移除的子弹
        if (this._pendingRemovals.length > 0) {
            for (const id of this._pendingRemovals) {
                this._internalRemoveBullet(id, true);
            }
            this._pendingRemovals.length = 0;
        }
    }

    /** 获取子弹统计信息 */
    getStats(): { activeCount: number; totalCreated: number; totalDestroyed: number } {
        return {
            activeCount: this._activeBullets.size,
            totalCreated: this._totalCreated,
            totalDestroyed: this._totalDestroyed
        };
    }

    /** 根据ID获取子弹 */
    getBullet(bulletId: string): IBullet | null {
        return this._activeBullets.get(bulletId) || null;
    }

    /** 根据施法者获取子弹列表 */
    getBulletsByCaster(caster: ICharacter): IBullet[] {
        const bullets: IBullet[] = [];
        for (const bullet of this._activeBullets.values()) {
            if (bullet.caster === caster) {
                bullets.push(bullet);
            }
        }
        return bullets;
    }

    /** 设置事件监听器 */
    private setupEventListeners(): void {
        // 可以在这里添加全局子弹事件监听
    }

    /** 获取调试信息 */
    getDebugInfo() {
        const bullets: any = {};
        for (const [id, bullet] of this._activeBullets) {
            bullets[id] = {
                caster: bullet.caster.characterName,
                target: bullet.target?.characterName || null,
                remainingHits: bullet.remainingHits,
                hasCollided: bullet.hasCollided,
                isDestroyed: bullet.isDestroyed
            };
        }
        return {
            stats: this.getStats(),
            bullets,
            pendingRemovals: this._pendingRemovals.length
        };
    }
    /** 打印调试信息 */
    printDebugInfo(): void {
        console.log("BulletManager Debug Info:", this.getDebugInfo());
    }
    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
    get eventManager(): EventManager {
        return this._eventManager;
    }

    /** 清理管理器 */
    cleanup(): void {
        this.clearAllBullets();
        this._eventManager.cleanup();
    }
}
