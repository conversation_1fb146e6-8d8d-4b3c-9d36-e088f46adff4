{"version": 3, "sources": ["assets\\fight\\buff\\BuffModelBeHurtFight.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,0DAAiE;AACjE,kDAA6C;AAC7C,wCAAsE;AAEtE,sEAA0F;AAC1F,4CAA2D;AAC3D,qEAAoE;AACpE,sCAAgD;AAGhD,iBAAiB;AACjB;IAuBI,8BAAY,MAAkB,EAAE,MAAkB;QAtB1C,QAAG,GAAW,oBAAoB,CAAC;QACnC,UAAK,GAAW,MAAM,CAAC;QACvB,iBAAY,GAAW,eAAe,CAAC;QACvC,UAAK,GAAa,gBAAQ,CAAC,IAAI,CAAC;QACxC,eAAe;QACP,cAAS,GAAW,IAAI,CAAC;QAEzB,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC;QAGtB,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAyB,EAAE,CAAC;QAE/C,cAAS,GAAW,8BAA8B,CAAC;QACnD,sBAAiB,GAAW,mCAAmC,CAAC;QAExE,4BAA4B;QACpB,yBAAoB,GAAW,GAAG,CAAC,CAAC,EAAE;QAC9C,mBAAmB;QACX,mCAA8B,GAAW,GAAG,CAAC;QAGjD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,2BAAY,EAAE,CAAC;QACxC,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAGD,sBAAI,oCAAE;QADN,YAAY;aACZ,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,sCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,6CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,sCAAI;aAAR,cAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,0CAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,0CAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,wCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,wCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,2CAAS;aAAb,cAA2B,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChF,sBAAI,oDAAkB;aAAtB,cAA8D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;;;OAAA;IAChG,sBAAI,gDAAc;aAAlB,cAAwD,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;OAAA;IACtF,sBAAI,0CAAQ;aAAZ,cAAqC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAC7D,sBAAI,kDAAgB;aAApB,cAA6C,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;OAAA;IAC7E,sBAAI,+CAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAE3D,sBAAI,4CAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACrD,UAAe,KAAa,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;;OAD7C;IAGrD,eAAe;IACL,2DAA4B,GAAtC;QACI,iBAAiB;QACjB,IAAM,cAAc,GAAG,IAAI,qCAAiB,CACrC,IAAI,CAAC,GAAG,qBAAkB,EAC7B,SAAS,EACT,QAAQ,EACR,4CAAqB,CAAC,UAAU,EAChC,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,QAAQ;QAChC,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,gBAAgB;QAChB,IAAM,oBAAoB,GAAG,IAAI,qCAAiB,CAC3C,IAAI,CAAC,GAAG,4BAAyB,EACpC,SAAS,EACT,uCAAsB,CAAC,YAAY,EACnC,4CAAqB,CAAC,GAAG,EACzB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO;QAChC,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;IACtE,CAAC;IAED,iBAAiB;IACjB,sCAAO,GAAP;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,IAAI,CAAC,OAAO,CAAC,aAAa,iBAAY,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;;YACnG,aAAa;YACb,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACjD;;;;;;;;;QACD,OAAO;QACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,kBAAkB;IAClB,qCAAM,GAAN,UAAO,SAAiB;;;YACpB,UAAU;YACV,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC9B;;;;;;;;;QACD,kBAAkB;QAClB,aAAa;IACjB,CAAC;IAED,mBAAmB;IACnB,uCAAQ,GAAR;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;;YACxE,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvD;;;;;;;;;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,sBAAsB;IACtB,2CAAY,GAAZ,UAAa,UAAuB,EAAE,QAAoB;QACtD,WAAW;QACX,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;YAChD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;SACvC;QACD,kBAAkB;QAClB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,iBAAiB;IACjB,qCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvB,SAAS;QACT,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,mBAAmB;IACnB,sCAAO,GAAP;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACxE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED,eAAe;IACf,uCAAQ,GAAR,UAAS,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;YAC/B,oBAAoB;YACpB,IAAI,CAAC,gCAAgC,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,UAAA,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SACpI;IACL,CAAC;IACD,eAAe;IACf,0CAAW,GAAX,UAAY,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;YAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aAC1B;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,CAAC,gCAAgC,EAAE,CAAC;aAC3C;YACD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,UAAA,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SACpI;IACL,CAAC;IACD,mBAAmB;IACnB,6CAAc,GAAd,UAAe,UAAkB;QAC7B,QAAQ,UAAU,EAAE;YAChB,KAAK,sBAAe,CAAC,mBAAmB,CAAC,CAAC,OAAO,IAAI,CAAC,oBAAoB,CAAC;YAC3E,KAAK,sBAAe,CAAC,6BAA6B,CAAC,CAAC,OAAO,IAAI,CAAC,8BAA8B,CAAC;YAC/F,KAAK,sBAAe,CAAC,WAAW,CAAC,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;YAChE,KAAK,sBAAe,CAAC,iBAAiB,CAAC,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;YACvE,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;SACrB;IACL,CAAC;IACD,yBAAyB;IACzB,4CAAa,GAAb,UAAc,SAAgB;QAC1B,eAAe;QACf,OAAO,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAChF,CAAC;IAED,iBAAiB;IACT,kDAAmB,GAA3B,UAA4B,UAAuB,EAAE,QAAoB;QACrE,YAAY;QACZ,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,EAAE;YAC3D,OAAO,KAAK,CAAC;SAChB;QACD,mBAAmB;QACnB,IAAI,UAAU,CAAC,UAAU,KAAK,oBAAU,CAAC,IAAI,EAAE;YAC3C,OAAO,KAAK,CAAC,CAAC,YAAY;SAC7B;QACD,OAAO;QACP,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW;QACxE,OAAO,MAAM,GAAG,MAAM,CAAC;IAC3B,CAAC;IAED,aAAa;IACL,mDAAoB,GAA5B,UAA6B,QAAoB;QAC7C,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,yBAAoB,QAAQ,CAAC,aAAa,MAAG,CAAC,CAAC;QACxF,SAAS;QACT,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QAClD,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACnF,SAAS;QACT,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;IACvI,CAAC;IAED,uBAAuB;IACf,+DAAgC,GAAxC;;;YACI,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvD;;;;;;;;;QACD,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,4BAA4B,EAAE,CAAC;;YACpC,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACjD;;;;;;;;;IACL,CAAC;IAED,eAAe;IACL,8CAAe,GAAzB;QACI,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,8BAA4B,IAAI,CAAC,KAAO,CAAC,CAAC;IAC1D,CAAC;IACD,aAAa;IACH,yCAAU,GAApB;QACI,WAAW;QACX,OAAO,CAAC,GAAG,CAAC,yBAAuB,IAAI,CAAC,KAAO,CAAC,CAAC;IACrD,CAAC;IACD,eAAe;IACL,sDAAuB,GAAjC;QACI,aAAa;QACb,OAAO,CAAC,GAAG,CAAC,uCAAqC,IAAI,CAAC,KAAO,CAAC,CAAC;IACnE,CAAC;IACD,eAAe;IACf,sCAAO,GAAP;QACI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC;IACL,2BAAC;AAAD,CApPA,AAoPC,IAAA;AApPY,oDAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport { CharacterAttributeName } from \"../types/CharacterTypes\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff, BuffType, IBuffPeriodicEffect } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { IAttributeModifier, AttributeModifierType } from \"../types/ICharacterAttributes\";\nimport { IDamageInfo, DamageType } from \"../types/IDamage\";\nimport { AttributeModifier } from \"../characters/AttributeModifier\";\nimport { EBuffEffectType } from \"../types/Buff\";\n\n\n/*** 受伤反击Buff实现*/\nexport class BuffModelBeHurtFight implements IBuff {\n    private _id: string = \"buff_be_hurt_fight\";\n    private _name: string = \"受伤反击\";\n    private _description: string = \"受到伤害时有概率反击攻击者\";\n    private _type: BuffType = BuffType.BUFF;\n    /**持续时间，以s计算 */\n    private _duration: number = 30.0;\n    private _remainingTime: number;\n    private _stackCount: number = 1;\n    private _maxStack: number = 3;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _isExpired: boolean = false;\n    private _attributeModifiers: IAttributeModifier[] = [];\n    private _periodicEffect?: IBuffPeriodicEffect;\n    private _iconPath: string = \"textures/buffs/be_hurt_fight\";\n    private _effectPrefabPath: string = \"prefabs/effects/CounterAttackAura\";\n    private _eventManager: EventManager;\n    /**Buff特有属性  - 默认 30%反击概率 */\n    private _counterAttackChance: number = 0.3; //\n    /**反击伤害倍率， 默认 1.5*/\n    private _counterAttackDamageMultiplier: number = 1.5;\n\n    constructor(caster: ICharacter, target: ICharacter) {\n        this._caster = caster;\n        this._target = target;\n        this._remainingTime = this._duration;\n        this._eventManager = new EventManager();\n        this.initializeAttributeModifiers();\n    }\n\n    // 实现IBuff接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get type(): BuffType { return this._type; }\n    get duration(): number { return this._duration; }\n    get maxStack(): number { return this._maxStack; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter { return this._target; }\n    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }\n    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }\n    get periodicEffect(): IBuffPeriodicEffect | undefined { return this._periodicEffect; }\n    get iconPath(): string | undefined { return this._iconPath; }\n    get effectPrefabPath(): string | undefined { return this._effectPrefabPath; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }\n    get stackCount(): number { return this._stackCount; }\n    set stackCount(value: number) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); }\n\n    /*** 初始化属性修改器*/\n    protected initializeAttributeModifiers(): void {\n        // 增加攻击力（每层增加10%）\n        const attackModifier = new AttributeModifier(\n            `${this._id}_attack_modifier`,\n            \"反击攻击力加成\",\n            \"attack\",\n            AttributeModifierType.PERCENTAGE,\n            0.1 * this._stackCount, // 每层10%\n            this._duration\n        );\n        // 增加暴击率（每层增加5%）\n        const criticalRateModifier = new AttributeModifier(\n            `${this._id}_critical_rate_modifier`,\n            \"反击暴击率加成\",\n            CharacterAttributeName.criticalRate,\n            AttributeModifierType.ADD,\n            0.05 * this._stackCount, // 每层5%\n            this._duration\n        );\n        this._attributeModifiers = [attackModifier, criticalRateModifier];\n    }\n\n    /*** Buff被添加时触发*/\n    onApply(): void {\n        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);\n        // 应用属性修改器到目标\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.addModifier(modifier);\n        }\n        // 播放特效\n        if (this._effectPrefabPath) {\n            this.playApplyEffect();\n        }\n        // 触发事件\n        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });\n    }\n\n    /*** Buff每帧更新时触发*/\n    onTick(deltaTime: number): void {\n        // 更新属性修改器\n        for (const modifier of this._attributeModifiers) {\n            modifier.update(deltaTime);\n        }\n        // 这里可以添加其他每帧更新的逻辑\n        // 比如粒子特效的更新等\n    }\n\n    /** * Buff被移除时触发 */\n    onRemove(): void {\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.removeModifier(modifier.id);\n        }\n        this.stopEffect();\n        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });\n    }\n\n    /** * 受到伤害时触发（核心功能） */\n    onTakeDamage(damageInfo: IDamageInfo, attacker: ICharacter): IDamageInfo {\n        // 检查是否触发反击\n        if (this.shouldCounterAttack(damageInfo, attacker)) {\n            this.performCounterAttack(attacker);\n        }\n        // 返回原始伤害信息（不修改伤害）\n        return damageInfo;\n    }\n\n    /** * 更新Buff状态 */\n    update(deltaTime: number): boolean {\n        if (this._isExpired) {\n            return true;\n        }\n        this._remainingTime -= deltaTime;\n        this.onTick(deltaTime);\n        // 检查是否过期\n        if (this._remainingTime <= 0) {\n            this._isExpired = true;\n            return true;\n        }\n        return false;\n    }\n\n    /** * 刷新Buff持续时间 */\n    refresh(): void {\n        this._remainingTime = this._duration;\n        this._isExpired = false;\n        console.log(`${this._name} refreshed on ${this._target.characterName}`);\n        this._eventManager.emit(FightEvent.buffRefreshed, { buff: this, target: this._target });\n    }\n\n    /** * 增加叠加层数 */\n    addStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.min(this._maxStack, this._stackCount + count);\n        if (this._stackCount !== oldStack) {\n            // 重新初始化属性修改器以反映新的层数\n            this.updateAttributeModifiersForStack();\n            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target, oldStack, newStack: this._stackCount });\n        }\n    }\n    /** * 减少叠加层数 */\n    removeStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(0, this._stackCount - count);\n        if (this._stackCount !== oldStack) {\n            if (this._stackCount === 0) {\n                this._isExpired = true;\n            } else {\n                // 重新初始化属性修改器以反映新的层数\n                this.updateAttributeModifiersForStack();\n            }\n            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target, oldStack, newStack: this._stackCount });\n        }\n    }\n    /*** 获取Buff的当前效果值*/\n    getEffectValue(effectType: string): number {\n        switch (effectType) {\n            case EBuffEffectType.counterAttackChance: return this._counterAttackChance;\n            case EBuffEffectType.counterAttackDamageMultiplier: return this._counterAttackDamageMultiplier;\n            case EBuffEffectType.attackBonus: return 0.1 * this._stackCount;\n            case EBuffEffectType.criticalRateBonus: return 0.05 * this._stackCount;\n            default: return 0;\n        }\n    }\n    /*** 检查Buff是否与另一个Buff冲突*/\n    conflictsWith(otherBuff: IBuff): boolean {\n        // 同类型的反击Buff冲突\n        return otherBuff.id === this._id || otherBuff.id.includes(\"counter_attack\");\n    }\n\n    /** * 检查是否应该反击 */\n    private shouldCounterAttack(damageInfo: IDamageInfo, attacker: ICharacter): boolean {\n        // 检查攻击者是否有效\n        if (!attacker || attacker.isDead || attacker === this._target) {\n            return false;\n        }\n        // 检查伤害类型（只对直接伤害反击）\n        if (damageInfo.damageType === DamageType.TRUE) {\n            return false; // 真实伤害不触发反击\n        }\n        // 概率检查\n        const random = Math.random();\n        const chance = this._counterAttackChance * this._stackCount; // 每层增加反击概率\n        return random < chance;\n    }\n\n    /** * 执行反击 */\n    private performCounterAttack(attacker: ICharacter): void {\n        console.log(`${this._target.characterName} counter attacks ${attacker.characterName}!`);\n        // 计算反击伤害\n        const baseDamage = this._target.attributes.attack;\n        const counterDamage = Math.floor(baseDamage * this._counterAttackDamageMultiplier);\n        // 造成反击伤害\n        attacker.takeDamage(counterDamage, this._target);\n        this.playCounterAttackEffect();\n        this._eventManager.emit(FightEvent.counterAttack, { buff: this, attacker: this._target, victim: attacker, damage: counterDamage });\n    }\n\n    /** * 更新属性修改器以反映新的层数 */\n    private updateAttributeModifiersForStack(): void {\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.removeModifier(modifier.id);\n        }\n        // 重新初始化修改器\n        this._attributeModifiers.length = 0;\n        this.initializeAttributeModifiers();\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.addModifier(modifier);\n        }\n    }\n\n    /** * 播放应用特效 */\n    protected playApplyEffect(): void {\n        // 这里应该调用特效管理器\n        console.log(`Playing apply effect for ${this._name}`);\n    }\n    /** * 停止特效 */\n    protected stopEffect(): void {\n        // 这里应该停止特效\n        console.log(`Stopping effect for ${this._name}`);\n    }\n    /** * 播放反击特效 */\n    protected playCounterAttackEffect(): void {\n        // 这里应该播放反击特效\n        console.log(`Playing counter attack effect for ${this._name}`);\n    }\n    /**  * 清理资源  */\n    cleanup(): void {\n        this._eventManager.cleanup();\n        this._attributeModifiers.length = 0;\n    }\n}\n"]}