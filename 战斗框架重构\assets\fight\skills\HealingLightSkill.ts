import { TimelineManager } from "../systems/TimelineManager";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from "../timeline/TimelineEvents";
import { IBuff } from "../types/IBuff";
import { <PERSON><PERSON>haracter } from "../types/ICharacter";
import { ISkill, SkillType, SkillTargetType } from "../types/ISkill";
import { ITimeline } from "../types/ITimeline";
import SkillName from "../types/SkillName";

/**
 * 治疗之光技能
 * 单体治疗技能，恢复目标生命值并提供短暂的生命恢复buff
 */
export class HealingLightSkill implements ISkill {
    private _id: string = SkillName.healing_light;
    private _name: string = "治疗之光";
    private _description: string = "释放治疗之光，恢复友军生命值并提供持续治疗效果";
    private _cooldown: number = 3.0;
    private _remainingCooldown: number = 0;
    private _mpCost: number = 30;
    private _staminaCost: number = 0;
    private _level: number = 1;
    private _type: SkillType = SkillType.ACTIVE;
    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ALLY;
    private _range: number = 200;
    private _timeline: ITimeline | null = null;
    private _passiveBuffs: IBuff[] = [];

    /** 技能配置 */
    private _config = {
        animationName: "skill_heal",
        soundId: "heal_cast",
        effectPath: "prefabs/effects/HealingLight",
        healAmount: 0, // 0表示使用施法者的魔法攻击力
        healMultiplier: 1.5, // 治疗倍率
        buffDuration: 5.0, // 持续治疗buff持续时间
        buffHealPerSecond: 10 // 每秒治疗量
    };

    // 实现ISkill接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get cooldown(): number { return this._cooldown; }
    get remainingCooldown(): number { return this._remainingCooldown; }
    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }
    get mpCost(): number { return this._mpCost; }
    get staminaCost(): number { return this._staminaCost; }
    get level(): number { return this._level; }
    get type(): SkillType { return this._type; }
    get targetType(): SkillTargetType { return this._targetType; }
    get range(): number { return this._range; }
    get timeline(): ITimeline { return this._timeline!; }
    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }
    get canUse(): boolean {
        return this._remainingCooldown <= 0;
    }

    /** 检查是否可以对目标使用技能 */
    canCastOn(caster: ICharacter, target?: ICharacter): boolean {
        if (!target) return false;
        if (target.isDead) return false;
        if (target.role === caster.role) return true; // 同阵营
        return false;
    }

    /** 检查资源消耗 */
    checkResourceCost(caster: ICharacter): boolean {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    }

    /** 消耗资源 */
    consumeResources(caster: ICharacter): void {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    }

    /** 释放技能 */
    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }

        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(`${caster.characterName} casts ${this._name} on ${target?.characterName}`);
        return true;
    }

    /** 创建技能Timeline */
    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {
        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const timeline = new Timeline(timelineId, this._name, 2.0, caster);

        // 0.0s: 播放施法动画和音效
        const castNode = new TimelineNode(
            `${timelineId}_cast`,
            0.0,
            new PlayAnimationTimelineEvent("cast_animation", this._config.animationName),
            false
        );
        timeline.addNode(castNode);

        const soundNode = new TimelineNode(
            `${timelineId}_sound`,
            0.1,
            new PlaySoundTimelineEvent("cast_sound", this._config.soundId),
            false
        );
        timeline.addNode(soundNode);

        // 0.8s: 执行治疗效果
        const healNode = new TimelineNode(
            `${timelineId}_heal`,
            0.8,
            new HealTimelineEvent("heal_effect", target!, caster, this.calculateHealAmount(caster)),
            false
        );
        timeline.addNode(healNode);

        // 1.0s: 播放治疗特效
        const effectNode = new TimelineNode(
            `${timelineId}_effect`,
            1.0,
            new PlayEffectTimelineEvent("heal_effect", this._config.effectPath, true, target?.node.position),
            false
        );
        timeline.addNode(effectNode);

        return timeline;
    }

    /** 计算治疗量 */
    private calculateHealAmount(caster: ICharacter): number {
        if (this._config.healAmount > 0) {
            return this._config.healAmount;
        }
        const baseMagicAttack = caster.attributes.magicAttack;
        return Math.floor(baseMagicAttack * this._config.healMultiplier);
    }

    /** 更新技能冷却 */
    update(deltaTime: number): void {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    }

    /** 重置冷却时间 */
    resetCooldown(): void {
        this._remainingCooldown = 0;
    }

    /** 升级技能 */
    levelUp(): void {
        this._level++;
        this._mpCost = Math.max(15, this._mpCost - 2);
        this._cooldown = Math.max(1.5, this._cooldown - 0.2);
        this._range += 15;
        this._config.healMultiplier += 0.1;
        console.log(`${this._name} leveled up to ${this._level}`);
    }

    /** 获取技能信息 */
    getSkillInfo() {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            healMultiplier: this._config.healMultiplier
        };
    }
}

/**
 * 治疗Timeline事件
 */
class HealTimelineEvent implements ITimelineEvent {
    private _id: string;
    private _type: TimelineEventType = TimelineEventType.HEAL;
    private _target: ICharacter;
    private _caster: ICharacter;
    private _healAmount: number;

    constructor(id: string, target: ICharacter, caster: ICharacter, healAmount: number) {
        this._id = id;
        this._target = target;
        this._caster = caster;
        this._healAmount = healAmount;
    }

    get id(): string { return this._id; }
    get type(): TimelineEventType { return this._type; }

    execute(timeline: ITimeline, nodeIndex: number, context?: any): void {
        if (this._target && !this._target.isDead) {
            // 执行治疗
            this._target.heal(this._healAmount);
            console.log(`${this._caster.characterName} heals ${this._target.characterName} for ${this._healAmount} HP`);

            // 添加持续治疗buff（这里需要实现HealOverTimeBuff）
            // const healBuff = new HealOverTimeBuff(this._caster, this._target, 5.0, 10);
            // this._target.buffManager.addBuff(healBuff);
        }
    }
}

// 需要导入TimelineEventType
import { ITimelineEvent, TimelineEventType } from "../types/ITimeline"; import { BattleManager } from "../systems/BattleManager";

