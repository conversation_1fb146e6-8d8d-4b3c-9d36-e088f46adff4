/**
 * 事件管理器
 * 提供事件的注册、触发和管理功能
 */

/*** 事件监听器接口*/
interface IEventListener {
    callback: Function;
    context?: any;
    once?: boolean;
}

/**
 * 事件管理器类
 */
export class EventManager {
    private static _globalInstance: EventManager;
    private _listeners: Map<string, IEventListener[]> = new Map();
    private _isDestroyed: boolean = false;
    private _instanceId: string;

    constructor(instanceId?: string) {
        this._instanceId = instanceId || `eventManager_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    /** 获取全局单例实例 */
    static getGlobal(): EventManager {
        if (!EventManager._globalInstance) {
            EventManager._globalInstance = new EventManager("global");
        }
        return EventManager._globalInstance;
    }

    /** 创建新的局部实例 */
    static createLocal(instanceId?: string): EventManager {
        return new EventManager(instanceId);
    }

    /** 全局事件快捷方法 */
    static on(event: string, callback: Function, context?: any): void {
        EventManager.getGlobal().on(event, callback, context);
    }

    static once(event: string, callback: Function, context?: any): void {
        EventManager.getGlobal().once(event, callback, context);
    }

    static off(event: string, callback?: Function, context?: any): void {
        EventManager.getGlobal().off(event, callback, context);
    }

    static emit(event: string, data?: any): void {
        EventManager.getGlobal().emit(event, data);
    }

    static hasListeners(event: string): boolean {
        return EventManager.getGlobal().hasListeners(event);
    }

    static getListenerCount(event: string): number {
        return EventManager.getGlobal().getListenerCount(event);
    }

    static getEventNames(): string[] {
        return EventManager.getGlobal().getEventNames();
    }

    static cleanup(): void {
        if (EventManager._globalInstance) {
            EventManager._globalInstance.cleanup();
            EventManager._globalInstance = null;
        }
    }

    static getDebugInfo(): any {
        return EventManager.getGlobal().getDebugInfo();
    }

    /** 获取实例ID */
    get instanceId(): string {     return this._instanceId; }

    /**
     * 注册事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    on(event: string, callback: Function, context?: any): void {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        const listeners = this._listeners.get(event)!;
        listeners.push({     callback,     context,     once: false });
    }

    /**
     * 注册一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    once(event: string, callback: Function, context?: any): void {
        if (this._isDestroyed) {
            console.warn("EventManager has been destroyed, cannot add listener");
            return;
        }

        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }

        const listeners = this._listeners.get(event)!;
        listeners.push({
            callback,
            context,
            once: true
        });
    }

    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    off(event: string, callback?: Function, context?: any): void {
        if (this._isDestroyed) {
            return;
        }
        const listeners = this._listeners.get(event);
        if (!listeners) {
            return;
        }
        if (!callback) {
            // 如果没有指定回调函数，移除所有监听器
            this._listeners.delete(event);
            return;
        }
        // 移除匹配的监听器
        const filteredListeners = listeners.filter(listener => {
            if (listener.callback !== callback) {
                return true;
            }
            if (context !== undefined && listener.context !== context) {
                return true;
            }
            return false;
        });
        if (filteredListeners.length === 0) {
            this._listeners.delete(event);
        } else {
            this._listeners.set(event, filteredListeners);
        }
    }

    /**
     * 触发事件
     * @param event 事件名称
     * @param data 事件数据
     */
    emit(event: string, data?: any): void {
        if (this._isDestroyed) {
            return;
        }
        const listeners = this._listeners.get(event);
        if (!listeners || listeners.length === 0) {
            return;
        }
        // 创建监听器副本，避免在回调中修改监听器列表时出现问题
        const listenersCopy = [...listeners];
        const onceListeners: IEventListener[] = [];
        for (const listener of listenersCopy) {
            try {
                if (listener.context) {
                    listener.callback.call(listener.context, data);
                } else {
                    listener.callback(data);
                }
                // 收集一次性监听器
                if (listener.once) {
                    onceListeners.push(listener);
                }
            } catch (error) {
                console.error(`Error in event listener for event "${event}":`, error);
            }
        }
        // 移除一次性监听器
        if (onceListeners.length > 0) {
            const remainingListeners = listeners.filter(listener => !onceListeners.includes(listener));
            if (remainingListeners.length === 0) {
                this._listeners.delete(event);
            } else {
                this._listeners.set(event, remainingListeners);
            }
        }
    }

    /**
     * 检查是否有指定事件的监听器
     * @param event 事件名称
     * @returns 是否有监听器
     */
    hasListeners(event: string): boolean {
        const listeners = this._listeners.get(event);
        return listeners !== undefined && listeners.length > 0;
    }

    /**
     * 获取指定事件的监听器数量
     * @param event 事件名称
     * @returns 监听器数量
     */
    getListenerCount(event: string): number {
        const listeners = this._listeners.get(event);
        return listeners ? listeners.length : 0;
    }

    /**
     * 获取所有事件名称
     * @returns 事件名称数组
     */
    getEventNames(): string[] {
        return Array.from(this._listeners.keys());
    }

    /**
     * 移除所有监听器
     */
    removeAllListeners(): void {
        this._listeners.clear();
    }

    /**
     * 移除指定事件的所有监听器
     * @param event 事件名称
     */
    removeAllListenersForEvent(event: string): void {
        this._listeners.delete(event);
    }

    /**
     * 清理事件管理器
     */
    cleanup(): void {
        this.removeAllListeners();
        this._isDestroyed = true;
    }

    /**
     * 获取调试信息
     */
    getDebugInfo(): any {
        const info: any = {};
        for (const [event, listeners] of this._listeners) {
            info[event] = {
                count: listeners.length,
                listeners: listeners.map(listener => ({
                    hasContext: !!listener.context,
                    isOnce: !!listener.once,
                    functionName: listener.callback.name || 'anonymous'
                }))
            };
        }
        return info;
    }

    /**
     * 打印调试信息
     */
    printDebugInfo(): void {
        console.log("EventManager Debug Info:", this.getDebugInfo());
    }
}
