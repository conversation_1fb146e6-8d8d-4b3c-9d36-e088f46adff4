import { Timeline } from "../timeline/Timeline";
import FightEvent from "../types/FightEvent";
import { ICharacter } from "../types/ICharacter";
import { ITimelineManager, ITimeline, ITimelineConfig } from "../types/ITimeline";
import { EventManager } from "./EventManager";

/*** Timeline管理器实现*/
export class TimelineManager implements ITimelineManager {
    private _activeTimelines: Map<string, ITimeline> = new Map();
    private _pausedTimelines: Set<string> = new Set();
    private _eventManager: EventManager;
    private _totalExecutedEvents: number = 0;
    private _isUpdating: boolean = false;
    private _pendingRemovals: string[] = [];
    constructor() {
        this._eventManager = EventManager.createLocal("TimelineManager");
    }
    /** * 获取所有活跃的Timeline */
    get activeTimelines(): ReadonlyArray<ITimeline> {
        return Array.from(this._activeTimelines.values());
    }
    /** * 添加Timeline */
    addTimeline(timeline: ITimeline): void {
        if (this._activeTimelines.has(timeline.id)) {
            console.warn(`Timeline ${timeline.id} already exists, replacing...`);
        }
        this._activeTimelines.set(timeline.id, timeline);
        timeline.eventManager.on(FightEvent.completedT, this.onTimelineCompleted.bind(this));
        timeline.eventManager.on(FightEvent.stoppedT, this.onTimelineStopped.bind(this));
        console.log(`Timeline ${timeline.id} added to manager`);
        this._eventManager.emit(FightEvent.timelineAdded, { timeline });
    }
    /*** 根据配置创建并添加Timeline*/
    createTimeline(config: ITimelineConfig, caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {
        const timeline = new Timeline(config.id, config.name, config.duration, caster, target, targets, position);
        for (const nodeConfig of config.nodes) {
            // 这里应该根据nodeConfig创建具体的TimelineNode， 现在先简化处理
            console.log(`Creating timeline node: ${nodeConfig.id}`);
        }
        this.addTimeline(timeline);
        return timeline;
    }
    /** * 移除Timeline */
    removeTimeline(timelineId: string): void {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(timelineId);
            return;
        }
        const timeline = this._activeTimelines.get(timelineId);
        if (timeline) {
            timeline.stop();
            timeline.eventManager.off(FightEvent.completedT, this.onTimelineCompleted.bind(this));
            timeline.eventManager.off(FightEvent.stoppedT, this.onTimelineStopped.bind(this));
            // 从映射中移除
            this._activeTimelines.delete(timelineId);
            this._pausedTimelines.delete(timelineId);
            console.log(`Timeline ${timelineId} removed from manager`);
            this._eventManager.emit(FightEvent.timelineRemoved, { timeline });
        }
    }
    /** * 根据施法者移除Timeline */
    removeTimelinesByCaster(caster: ICharacter): void {
        const timelinesToRemove: string[] = [];
        for (const [id, timeline] of this._activeTimelines) {
            if (timeline.caster === caster) {
                timelinesToRemove.push(id);
            }
        }
        for (const id of timelinesToRemove) {
            this.removeTimeline(id);
        }
        console.log(`Removed ${timelinesToRemove.length} timelines for caster ${caster.characterName}`);
    }
    /** * 暂停所有Timeline */
    pauseAll(): void {
        for (const timeline of this._activeTimelines.values()) {
            if (!timeline.isPaused) {
                timeline.pause();
                this._pausedTimelines.add(timeline.id);
            }
        }
        console.log(`Paused ${this._pausedTimelines.size} timelines`);
        this._eventManager.emit(FightEvent.allTimelinesPaused);
    }
    /** * 恢复所有Timeline */
    resumeAll(): void {
        for (const timeline of this._activeTimelines.values()) {
            if (timeline.isPaused) {
                timeline.resume();
                this._pausedTimelines.delete(timeline.id);
            }
        }
        console.log(`Resumed all timelines`);
        this._eventManager.emit(FightEvent.allTimelinesResumed);
    }
    /*** 清除所有Timeline*/
    clearAll(): void {
        const timelineIds = Array.from(this._activeTimelines.keys());
        for (const id of timelineIds) {
            this.removeTimeline(id);
        }
        this._pausedTimelines.clear();
        this._totalExecutedEvents = 0;
        console.log(`Cleared all timelines`);
        this._eventManager.emit(FightEvent.allTimelinesCleared);
    }
    /** * 更新所有Timeline */
    update(deltaTime: number): void {
        if (this._activeTimelines.size === 0) {
            return;
        }
        this._isUpdating = true;
        const completedTimelines: string[] = [];
        // 更新所有Timeline
        for (const [id, timeline] of this._activeTimelines) {
            if (!timeline.isPaused) {
                const isCompleted = timeline.update(deltaTime);
                if (isCompleted) {
                    completedTimelines.push(id);
                }
            }
        }
        this._isUpdating = false;
        // 移除已完成的Timeline
        for (const id of completedTimelines) {
            this.removeTimeline(id);
        }
        // 处理延迟移除的Timeline
        if (this._pendingRemovals.length > 0) {
            for (const id of this._pendingRemovals) {
                this.removeTimeline(id);
            }
            this._pendingRemovals.length = 0;
        }
    }
    /** * 获取Timeline统计信息 */
    getStats(): { activeCount: number; pausedCount: number; totalExecutedEvents: number } {
        return {
            activeCount: this._activeTimelines.size,
            pausedCount: this._pausedTimelines.size,
            totalExecutedEvents: this._totalExecutedEvents
        };
    }
    /** * 根据ID获取Timeline */
    getTimeline(timelineId: string): ITimeline | null {
        return this._activeTimelines.get(timelineId) || null;
    }
    /*** 根据施法者获取Timeline列表*/
    getTimelinesByCaster(caster: ICharacter): ITimeline[] {
        const timelines: ITimeline[] = [];
        for (const timeline of this._activeTimelines.values()) {
            if (timeline.caster === caster) {
                timelines.push(timeline);
            }
        }
        return timelines;
    }
    /** * 暂停指定Timeline */
    pauseTimeline(timelineId: string): void {
        const timeline = this._activeTimelines.get(timelineId);
        if (timeline && !timeline.isPaused) {
            timeline.pause();
            this._pausedTimelines.add(timelineId);
            console.log(`Timeline ${timelineId} paused`);
            this._eventManager.emit(FightEvent.timelinePaused, { timeline });
        }
    }
    /**  * 恢复指定Timeline  */
    resumeTimeline(timelineId: string): void {
        const timeline = this._activeTimelines.get(timelineId);
        if (timeline && timeline.isPaused) {
            timeline.resume();
            this._pausedTimelines.delete(timelineId);
            console.log(`Timeline ${timelineId} resumed`);
            this._eventManager.emit(FightEvent.timelineResumed, { timeline });
        }
    }
    /*** Timeline完成时的回调*/
    private onTimelineCompleted(event: any): void {
        const timeline = event.timeline as ITimeline;
        console.log(`Timeline ${timeline.id} completed`);
        this._totalExecutedEvents++;
        this._eventManager.emit(FightEvent.timelineCompleted, { timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    }
    /*** Timeline停止时的回调*/
    private onTimelineStopped(event: any): void {
        const timeline = event.timeline as ITimeline;
        console.log(`Timeline ${timeline.id} stopped`);
        this._eventManager.emit(FightEvent.timelineStopped, { timeline });
        // 延迟移除，避免在更新过程中修改集合
        this.scheduleRemoval(timeline.id);
    }
    /*** 安排移除Timeline*/
    private scheduleRemoval(timelineId: string): void {
        if (this._isUpdating) {
            this._pendingRemovals.push(timelineId);
        } else {
            this.removeTimeline(timelineId);
        }
    }
    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
    get eventManager(): EventManager {
        return this._eventManager;
    }
    /*** 获取调试信息*/
    getDebugInfo() {
        const timelines = {} as ITimeline
        for (const [id, timeline] of this._activeTimelines) {
            timelines[id] = {
                name: timeline.name,
                duration: timeline.duration,
                timeElapsed: timeline.timeElapsed,
                isCompleted: timeline.isCompleted,
                isPaused: timeline.isPaused,
                nodeCount: timeline.nodes.length,
                caster: timeline.caster.characterName,
                target: timeline.target?.characterName || null
            };
        }
        return {
            stats: this.getStats(),
            timelines,
            pendingRemovals: this._pendingRemovals.length
        };
    }
    /*** 打印调试信息*/
    printDebugInfo(): void {
        console.log("TimelineManager Debug Info:", this.getDebugInfo());
    }
    /*** 清理管理器*/
    cleanup(): void {
        this.clearAll();
        this._eventManager.cleanup();
    }
}
