
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/base/RoleBase.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ab292g0D99OMpf3Zo/sgxjh', 'RoleBase');
// card/cardgame/src/base/RoleBase.ts

// import BuffManager from "../BuffManager";
// import { ECamp } from "../CampManager";
// import CardManager from "../CardManager";
// import EquipmentManager from "../EquipmentManager";
// import { BRO } from "../EventManager";
// import FightManager from "../FightManager";
// import SkillManager from "../SkillManager";
// import Base from "./Base";
// const { ccclass, property } = cc._decorator;
// export enum ERoleStatus {
//     /**角色状态-正常 */
//     Normal = `Normal`,
//     /**角色状态-死亡 */
//     Death = `Death`,
//     /**角色状态-冻结 */
//     Frozen = `Frozen`,
// }
// declare global {
//     type IRoleBaseName = 'RoleBase'
//     export interface IRoleBaseData extends IBaseDataType {
//         hp: number;
//         hpUp: number;
//         roleName: string;
//         /**攻击力 */
//         attack: number;
//         /**防御力 */
//         defence: number;
//         level: number;
//         camp: ECamp;
//         status?: ERoleStatus;
//         exp?: number;
//     }
// }
// /**
//  * @features : 角色控制基类
//  * @description: 游戏中多个角色的控制基类
//  * @Date : 2020-08-12 23:28:52
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:57:04
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class RoleBase extends Base implements IRoleBaseData {
//     data: IRoleBaseData;
//     /**角色卡片管理 */
//     cardMgr = new CardManager
//     /**角色技能管理 */
//     skillMgr = new SkillManager
//     /**对角色使用buff管理 */
//     buffMgr = new BuffManager
//     /**角色装备 */
//     equipMgr = new EquipmentManager
//     /**角色id */
//     @Base.ViewLinked
//     id: string;
//     /**角色血量 */
//     @Base.ViewLinked
//     hp: number;
//     @Base.ViewLinked
//     hpUp: number;
//     /**角色名字 */
//     @Base.ViewLinked
//     roleName: string;
//     /**角色状态 */
//     @Base.ViewLinked
//     status: ERoleStatus;
//     /**角色的攻击力 */
//     @Base.ViewLinked
//     attack: number;
//     /**角色的防御力 */
//     @Base.ViewLinked
//     defence: number;
//     /**角色的等级 */
//     @Base.ViewLinked
//     level: number;
//     /**角色的经验值 */
//     @Base.ViewLinked
//     exp: number;
//     /**卡牌的阵营 */
//     @Base.ViewLinked
//     camp: ECamp;
//     get isDeath() { return this.status == ERoleStatus.Death; }
//     initRole(initData: IRoleBaseData) {
//         let { hp, hpUp, roleName, attack, defence, level, camp } = initData
//         this.data = { ...initData }
//         this.camp = camp
//         this.hpUp = hpUp
//         this.hp = hp
//         this.roleName = roleName
//         this.attack = attack
//         this.defence = defence
//         this.level = level
//         this.exp = 0
//         this.status = ERoleStatus.Normal
//     }
//     expChangeView(newValue: number) {
//         if (newValue >= 100) {
//             this.exp = 0;
//             this.level++;
//         }
//     }
//     hpChangeView(newValue: number) {
//         if (newValue <= 0) {
//             this.status = ERoleStatus.Death;
//             return 0
//         } else if (newValue > this.hpUp) {
//             return this.hpUp
//         } else {
//             return newValue
//         }
//     }
//     statusAfterChangeView(value: ERoleStatus) {
//         if (value == ERoleStatus.Death) {
//             BRO.broadcast(BRO.keys.RefreshUI, { [`${this.camp}${this.className}hp`]: 0 }, this as any)
//             FightManager.campManager.getCampByName(this.camp).roleMgr.deleateRole(this);
//         }
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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