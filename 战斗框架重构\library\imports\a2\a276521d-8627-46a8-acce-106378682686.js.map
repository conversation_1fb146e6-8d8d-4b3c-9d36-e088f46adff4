{"version": 3, "sources": ["assets\\fight\\buff\\AttackBoostBuff.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,wDAAuD;AACvD,kDAA6C;AAC7C,wCAAiD;AAEjD,sEAA0F;AAE1F,qEAAoE;AACpE,sCAAgD;AAEhD;;;GAGG;AACH;IAuBI,yBAAY,MAAkB,EAAE,MAAkB,EAAE,QAAgB,EAAE,gBAAwB,EAAE,qBAA8B;QArBtH,UAAK,GAAW,MAAM,CAAC;QACvB,iBAAY,GAAW,aAAa,CAAC;QACrC,UAAK,GAAa,gBAAQ,CAAC,IAAI,CAAC;QAGhC,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC;QAGtB,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAwB,EAAE,CAAC;QAOtD,OAAO;QACC,cAAS,GAAW,0BAA0B,CAAC;QAC/C,sBAAiB,GAAW,4BAA4B,CAAC;QAG7D,IAAI,CAAC,GAAG,GAAG,kBAAgB,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QACvF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,IAAI,gBAAgB,CAAC;QACxE,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,UAAQ,IAAI,CAAC,GAAK,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,GAAG,mCAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,2BAAO,QAAQ,WAAG,CAAC;QACvF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAGD,sBAAI,+BAAE;QADN,YAAY;aACZ,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,iCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,wCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,iCAAI;aAAR,cAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,qCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,qCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,mCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,mCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,sCAAS;aAAb,cAA2B,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChF,sBAAI,+CAAkB;aAAtB,cAA8D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;;;OAAA;IAChG,sBAAI,qCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,6CAAgB;aAApB,cAAiC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,0CAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAE3D,sBAAI,uCAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACrD,UAAe,KAAa;YACxB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAChE,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,EAAE;gBAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;QACL,CAAC;;;OAPoD;IASrD,cAAc;IACN,kDAAwB,GAAhC;QACI,SAAS;QACT,IAAM,cAAc,GAAG,IAAI,qCAAiB,CACrC,IAAI,CAAC,GAAG,YAAS,EACpB,OAAO,EACP,QAAQ,EACR,4CAAqB,CAAC,UAAU,EAChC,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAC/C,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,WAAW;QACX,IAAM,mBAAmB,GAAG,IAAI,qCAAiB,CAC1C,IAAI,CAAC,GAAG,kBAAe,EAC1B,SAAS,EACT,aAAa,EACb,4CAAqB,CAAC,UAAU,EAChC,CAAC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EACpD,IAAI,CAAC,SAAS,CACjB,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED,cAAc;IACN,kDAAwB,GAAhC;;;YACI,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAM,iBAAiB,GAAG,QAAQ,CAAA;gBAClC,IAAI,iBAAiB,CAAC,aAAa,KAAK,QAAQ,EAAE;oBAC9C,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC/E;qBAAM,IAAI,iBAAiB,CAAC,aAAa,KAAK,aAAa,EAAE;oBAC1D,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;iBACpF;aACJ;;;;;;;;;IACL,CAAC;IAED,iBAAiB;IACjB,iCAAO,GAAP;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,IAAI,CAAC,OAAO,CAAC,aAAa,iBAAY,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;;YACnG,aAAa;YACb,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aACjD;;;;;;;;;QACD,OAAO;QACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,kBAAkB;IAClB,gCAAM,GAAN,UAAO,SAAiB;;;YACpB,UAAU;YACV,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aAC9B;;;;;;;;;IACL,CAAC;IAED,iBAAiB;IACjB,kCAAQ,GAAR;;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;;YACxE,UAAU;YACV,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvD;;;;;;;;;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,cAAc;IACd,sCAAY,GAAZ,UAAc,UAAuB,EAAE,OAAmB;QACtD,0BAA0B;QAC1B,IAAM,qBAAqB,GAAG,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY;QAClE,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,GAAG,qBAAqB,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,cAAS,WAAW,uBAAkB,qBAAqB,GAAG,GAAG,iCAA4B,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;QAC3I,mCAAmC;QACnC,2BAA2B;QAC3B,UAAU,CAAC,mBAAmB,IAAI,WAAW,CAAC;QAC9C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,eAAe;IACf,gCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,iBAAiB;IACjB,iCAAO,GAAP;;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;;YACrC,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,QAAQ,WAAA;gBACf,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;aAC3C;;;;;;;;;QACD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC5E,CAAC;IAED,aAAa;IACb,kCAAQ,GAAR,UAAS,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,aAAa;IACb,qCAAW,GAAX,UAAY,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,mBAAmB;IACnB,wCAAc,GAAd,UAAe,UAAkB;QAC7B,QAAQ,UAAU,EAAE;YAChB,KAAK,sBAAe,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;YACrD,KAAK,sBAAe,CAAC,uBAAuB;gBACxC,OAAO,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC1D,KAAK,sBAAe,CAAC,uBAAuB;gBACxC,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;YACjE;gBACI,OAAO,CAAC,CAAC;SAChB;IACL,CAAC;IAED,yBAAyB;IACzB,uCAAa,GAAb,UAAc,UAAiB;QAC3B,wBAAwB;QACxB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACL,yCAAe,GAAvB;QACI,OAAO,CAAC,GAAG,CAAC,8BAA4B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACvF,kBAAkB;IACtB,CAAC;IAED,WAAW;IACH,oCAAU,GAAlB;QACI,OAAO,CAAC,GAAG,CAAC,yBAAuB,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QAClF,kBAAkB;IACtB,CAAC;IAED,aAAa;IACb,sCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;YAClD,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAe,CAAC,uBAAuB,CAAC;YAChF,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,SAAS,EAAE,IAAI,CAAC,UAAU;SAC7B,CAAC;IACN,CAAC;IACL,sBAAC;AAAD,CA1OA,AA0OC,IAAA;AA1OY,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff, BuffType } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { IAttributeModifier, AttributeModifierType } from \"../types/ICharacterAttributes\";\nimport { IDamageInfo } from \"../types/IDamage\";\nimport { AttributeModifier } from \"../characters/AttributeModifier\";\nimport { EBuffEffectType } from \"../types/Buff\";\n\n/**\n * 攻击力提升Buff\n * 提升角色的攻击力和魔法攻击力\n */\nexport class AttackBoostBuff implements IBuff {\n    private _id: string;\n    private _name: string = \"攻击强化\";\n    private _description: string = \"提升攻击力和魔法攻击力\";\n    private _type: BuffType = BuffType.BUFF;\n    private _duration: number;\n    private _remainingTime: number;\n    private _stackCount: number = 1;\n    private _maxStack: number = 5;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _isExpired: boolean = false;\n    private _attributeModifiers: AttributeModifier[] = [];\n    private _eventManager: EventManager;\n\n    // 攻击力提升配置\n    private _attackMultiplier: number;\n    private _magicAttackMultiplier: number;\n\n    // 视觉效果\n    private _iconPath: string = \"icons/buffs/attack_boost\";\n    private _effectPrefabPath: string = \"prefabs/effects/AttackAura\";\n\n    constructor(caster: ICharacter, target: ICharacter, duration: number, attackMultiplier: number, magicAttackMultiplier?: number) {\n        this._id = `attack_boost_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        this._caster = caster;\n        this._target = target;\n        this._duration = duration;\n        this._remainingTime = duration;\n        this._attackMultiplier = attackMultiplier;\n        this._magicAttackMultiplier = magicAttackMultiplier || attackMultiplier;\n        this._eventManager = EventManager.createLocal(`buff_${this._id}`);\n        this._description = `攻击力提升${Math.round((attackMultiplier - 1) * 100)}%，持续${duration}秒`;\n        this.createAttributeModifiers();\n    }\n\n    // 实现IBuff接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get type(): BuffType { return this._type; }\n    get duration(): number { return this._duration; }\n    get maxStack(): number { return this._maxStack; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter { return this._target; }\n    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }\n    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }\n    get iconPath(): string { return this._iconPath; }\n    get effectPrefabPath(): string { return this._effectPrefabPath; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }\n    get stackCount(): number { return this._stackCount; }\n    set stackCount(value: number) {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(0, Math.min(value, this._maxStack));\n        if (oldStack !== this._stackCount) {\n            this.updateAttributeModifiers();\n        }\n    }\n\n    /** 创建属性修改器 */\n    private createAttributeModifiers(): void {\n        // 攻击力修改器\n        const attackModifier = new AttributeModifier(\n            `${this._id}_attack`,\n            \"攻击力提升\",\n            \"attack\",\n            AttributeModifierType.PERCENTAGE,\n            (this._attackMultiplier - 1) * this._stackCount,\n            this._duration\n        );\n        // 魔法攻击力修改器\n        const magicAttackModifier = new AttributeModifier(\n            `${this._id}_magic_attack`,\n            \"魔法攻击力提升\",\n            \"magicAttack\",\n            AttributeModifierType.PERCENTAGE,\n            (this._magicAttackMultiplier - 1) * this._stackCount,\n            this._duration\n        );\n        this._attributeModifiers = [attackModifier, magicAttackModifier];\n    }\n\n    /** 更新属性修改器 */\n    private updateAttributeModifiers(): void {\n        for (const modifier of this._attributeModifiers) {\n            const attributeModifier = modifier\n            if (attributeModifier.attributeName === \"attack\") {\n                attributeModifier.setValue((this._attackMultiplier - 1) * this._stackCount);\n            } else if (attributeModifier.attributeName === \"magicAttack\") {\n                attributeModifier.setValue((this._magicAttackMultiplier - 1) * this._stackCount);\n            }\n        }\n    }\n\n    /** Buff被添加时触发 */\n    onApply(): void {\n        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);\n        // 应用属性修改器到目标\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.addModifier(modifier);\n        }\n        // 播放特效\n        if (this._effectPrefabPath) {\n            this.playApplyEffect();\n        }\n        // 触发事件\n        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });\n    }\n\n    /** Buff每帧更新时触发 */\n    onTick(deltaTime: number): void {\n        // 更新属性修改器\n        for (const modifier of this._attributeModifiers) {\n            modifier.update(deltaTime);\n        }\n    }\n\n    /** Buff被移除时触发 */\n    onRemove(): void {\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n        // 移除属性修改器\n        for (const modifier of this._attributeModifiers) {\n            this._target.attributes.removeModifier(modifier.id);\n        }\n        this.stopEffect();\n        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });\n    }\n\n    /** 造成伤害时触发 */\n    onDealDamage?(damageInfo: IDamageInfo, _target: ICharacter): IDamageInfo {\n        // 攻击力提升buff可以在这里添加额外的伤害加成\n        const bonusDamagePercentage = 0.1 * this._stackCount; // 每层额外10%伤害\n        const bonusDamage = Math.floor(damageInfo.baseDamage * bonusDamagePercentage);\n        console.log(`${this._name} adds ${bonusDamage} bonus damage (${bonusDamagePercentage * 100}% of base damage, Stack: ${this._stackCount})`);\n        // 正确的方式：通过damageAmplification来增加伤害\n        // 这样伤害计算系统会重新计算finalDamage\n        damageInfo.damageAmplification += bonusDamage;\n        return damageInfo;\n    }\n\n    /** 更新Buff状态 */\n    update(deltaTime: number): boolean {\n        if (this._isExpired) {\n            return true;\n        }\n        this._remainingTime -= deltaTime;\n        this.onTick(deltaTime);\n        if (this._remainingTime <= 0) {\n            this._isExpired = true;\n            return true;\n        }\n        return false;\n    }\n\n    /** 刷新Buff持续时间 */\n    refresh(): void {\n        this._remainingTime = this._duration;\n        for (const modifier of this._attributeModifiers) {\n            modifier.remainingTime = this._duration;\n        }\n        console.log(`${this._name} refreshed on ${this._target.characterName}`);\n    }\n\n    /** 增加叠加层数 */\n    addStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.min(this._stackCount + count, this._maxStack);\n        if (this._stackCount > oldStack) {\n            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 减少叠加层数 */\n    removeStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(this._stackCount - count, 0);\n        if (this._stackCount < oldStack) {\n            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 获取Buff的当前效果值 */\n    getEffectValue(effectType: string): number {\n        switch (effectType) {\n            case EBuffEffectType.attack_multiplier:\n                return this._attackMultiplier * this._stackCount;\n            case EBuffEffectType.magic_attack_multiplier:\n                return this._magicAttackMultiplier * this._stackCount;\n            case EBuffEffectType.attack_bonus_percentage:\n                return (this._attackMultiplier - 1) * this._stackCount * 100;\n            default:\n                return 0;\n        }\n    }\n\n    /** 检查Buff是否与另一个Buff冲突 */\n    conflictsWith(_otherBuff: IBuff): boolean {\n        // 同类型的攻击力提升buff不冲突，可以叠加\n        return false;\n    }\n\n    /** 播放应用特效 */\n    private playApplyEffect(): void {\n        console.log(`Playing apply effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效播放逻辑\n    }\n\n    /** 停止特效 */\n    private stopEffect(): void {\n        console.log(`Stopping effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效停止逻辑\n    }\n\n    /** 获取调试信息 */\n    getDebugInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            type: this._type,\n            duration: this._duration,\n            remainingTime: this._remainingTime,\n            stackCount: this._stackCount,\n            maxStack: this._maxStack,\n            attackMultiplier: this._attackMultiplier,\n            magicAttackMultiplier: this._magicAttackMultiplier,\n            currentAttackBonus: this.getEffectValue(EBuffEffectType.attack_bonus_percentage),\n            caster: this._caster.characterName,\n            target: this._target.characterName,\n            isExpired: this._isExpired\n        };\n    }\n}\n"]}