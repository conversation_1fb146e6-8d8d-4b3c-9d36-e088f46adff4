
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/CharacterTypes.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7fa9514lSZAtbXNKrWpSJvw', 'CharacterTypes');
// fight/types/CharacterTypes.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttackState = exports.CharacterAttributeName = exports.CharacterSelectTag = exports.CharacterState = exports.CharacterRole = void 0;
/*** 角色阵营枚举*/
var CharacterRole;
(function (CharacterRole) {
    /** 无效/未定义 */
    CharacterRole[CharacterRole["INVALID"] = -1] = "INVALID";
    /** 英雄/玩家 */
    CharacterRole[CharacterRole["HERO"] = 0] = "HERO";
    /** 敌人 */
    CharacterRole[CharacterRole["ENEMY"] = 1] = "ENEMY";
    /** 子弹/投射物 */
    CharacterRole[CharacterRole["BULLET"] = 2] = "BULLET";
    /** 伙伴/宠物 */
    CharacterRole[CharacterRole["PARTNER"] = 3] = "PARTNER";
    /** 中立 */
    CharacterRole[CharacterRole["NEUTRAL"] = 4] = "NEUTRAL";
})(CharacterRole = exports.CharacterRole || (exports.CharacterRole = {}));
/*** 角色状态枚举*/
var CharacterState;
(function (CharacterState) {
    /** 空闲 */
    CharacterState["IDLE"] = "idle";
    /** 移动 */
    CharacterState["MOVING"] = "moving";
    /** 攻击 */
    CharacterState["ATTACKING"] = "attacking";
    /** 释放技能 */
    CharacterState["CASTING"] = "casting";
    /** 受伤 */
    CharacterState["HURT"] = "hurt";
    /** 死亡 */
    CharacterState["DEAD"] = "dead";
    /** 眩晕 */
    CharacterState["STUNNED"] = "stunned";
    /** 沉默 */
    CharacterState["SILENCED"] = "silenced";
    /** 无敌 */
    CharacterState["INVINCIBLE"] = "invincible";
})(CharacterState = exports.CharacterState || (exports.CharacterState = {}));
/*** 角色选择标签枚举*/
var CharacterSelectTag;
(function (CharacterSelectTag) {
    /** 未选中 */
    CharacterSelectTag[CharacterSelectTag["NONE"] = 0] = "NONE";
    /** 玩家选中 */
    CharacterSelectTag[CharacterSelectTag["PLAYER"] = 1] = "PLAYER";
    /** 伙伴选中 */
    CharacterSelectTag[CharacterSelectTag["PARTNER"] = 2] = "PARTNER";
})(CharacterSelectTag = exports.CharacterSelectTag || (exports.CharacterSelectTag = {}));
var CharacterAttributeName = /** @class */ (function () {
    function CharacterAttributeName() {
    }
    /** 生命值 */
    CharacterAttributeName.hp = 'hp';
    /** 最大生命值 */
    CharacterAttributeName.maxHp = 'maxHp';
    /** 魔法值 */
    CharacterAttributeName.mp = 'mp';
    /** 最大魔法值 */
    CharacterAttributeName.maxMp = 'maxMp';
    /** 最大耐力 */
    CharacterAttributeName.maxStamina = 'maxStamina';
    /** 攻击力 */
    CharacterAttributeName.attack = 'attack';
    /** 防御力 */
    CharacterAttributeName.defense = 'defense';
    /** 攻击速度 */
    CharacterAttributeName.attackSpeed = 'attackSpeed';
    /** 移动速度 */
    CharacterAttributeName.moveSpeed = 'moveSpeed';
    /** 攻击范围 */
    CharacterAttributeName.attackRange = 'attackRange';
    /** 暴击率 */
    CharacterAttributeName.criticalRate = 'criticalRate';
    /** 暴击伤害 */
    CharacterAttributeName.criticalDamage = 'criticalDamage';
    /** 命中率 */
    CharacterAttributeName.hitRate = 'hitRate';
    /** 闪避率 */
    CharacterAttributeName.dodgeRate = 'dodgeRate';
    /** 等级 */
    CharacterAttributeName.level = 'level';
    /** 经验值 */
    CharacterAttributeName.experience = 'experience';
    /**当前hp */
    CharacterAttributeName.currentHp = 'currentHp';
    /**当前Mp */
    CharacterAttributeName.currentMp = 'currentMp';
    /**当前耐力*/
    CharacterAttributeName.currentStamina = 'currentStamina';
    return CharacterAttributeName;
}());
exports.CharacterAttributeName = CharacterAttributeName;
/** * 攻击状态枚举 */
var AttackState;
(function (AttackState) {
    /** 空闲状态 */
    AttackState["IDLE"] = "idle";
    /** 前摇阶段 */
    AttackState["WINDUP"] = "windup";
    /** 伤害阶段 */
    AttackState["DAMAGE"] = "damage";
    /** 后摇阶段 */
    AttackState["RECOVERY"] = "recovery";
})(AttackState = exports.AttackState || (exports.AttackState = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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