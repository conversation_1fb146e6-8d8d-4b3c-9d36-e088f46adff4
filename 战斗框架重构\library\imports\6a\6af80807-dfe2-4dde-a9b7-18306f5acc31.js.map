{"version": 3, "sources": ["assets\\fight\\examples\\SkillAndBuffExample.ts"], "names": [], "mappings": ";;;;;;;AAAA,0DAAyD;AACzD,qDAAoD;AACpD,0DAAwD;AAExD,OAAO;AACP,+DAA8D;AAC9D,iEAAgE;AAChE,iEAAgE;AAChE,iEAAgE;AAEhE,SAAS;AACT,2DAA0D;AAC1D,6DAA4D;AAC5D,6CAA4C;AAC5C,iDAAgD;AAEhD;;;GAGG;AACH;IAMI;QACI,IAAI,CAAC,aAAa,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,YAAY;IACJ,kDAAoB,GAA5B;QACI,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACzB,SAAS,EAAE,gBAAgB;YAC3B,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,8BAAa,CAAC,IAAI;YACxB,iBAAiB,EAAE;gBACf,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,UAAU,EAAE,GAAG;gBACf,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,GAAG;gBACd,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,KAAK,GAAG,IAAI,qBAAS,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;YACxB,SAAS,EAAE,WAAW;YACtB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,8BAAa,CAAC,KAAK;YACzB,iBAAiB,EAAE;gBACf,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,UAAU,EAAE,GAAG;gBACf,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,GAAG;gBACjB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACzB,SAAS,EAAE,eAAe;YAC1B,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,8BAAa,CAAC,IAAI;YACxB,iBAAiB,EAAE;gBACf,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,EAAE,EAAE,GAAG;gBACP,KAAK,EAAE,GAAG;gBACV,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE;gBACV,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;aAChB;SACJ,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,aAAW,IAAI,CAAC,MAAM,CAAC,aAAa,eAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QAC9H,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,KAAK,CAAC,aAAa,eAAU,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QAC1H,OAAO,CAAC,GAAG,CAAC,aAAW,IAAI,CAAC,MAAM,CAAC,aAAa,eAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;IAClI,CAAC;IAED,WAAW;IACH,yCAAW,GAAnB;QACI,UAAU;QACV,IAAM,aAAa,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC7C,IAAM,WAAW,GAAG,IAAI,qCAAiB,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAE/C,WAAW;QACX,IAAM,SAAS,GAAG,IAAI,qCAAiB,EAAE,CAAC;QAC1C,IAAM,YAAY,GAAG,IAAI,qCAAiB,EAAE,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAEhD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;IAED,WAAW;IACH,wCAAU,GAAlB;QACI,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAE5F,iBAAiB;QACjB,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,aAAa;QACb,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,gBAAgB;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,qBAAqB;QACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,gBAAgB;QAChB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,cAAc;QACd,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,eAAe;QACf,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,SAAS;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC7C,CAAC;IAED,kBAAkB;IACV,wDAA0B,GAAlC;QACI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,yCAAW,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAQ,CAAC,CAAC;QAE3E,IAAM,UAAU,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE5C,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,iFAAqB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAQ,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,mBAAS,EAAE,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,cAAc;IACN,uDAAyB,GAAjC;QACI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAC9E,IAAI,aAAa,EAAE;YACf,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,sBAAO,aAAa,CAAC,IAAM,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,iCAAW,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;YAEzF,IAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,8BAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,iBAAiB;IACT,qDAAuB,GAA/B;QACI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,yBAAU,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QAE1G,IAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,8BAAO,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,2BAAO,EAAE,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,aAAa;IACL,qDAAuB,GAA/B;QACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAEhC,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,+BAAW,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QAE9G,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACrE,IAAI,SAAS,EAAE;YACX,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAM,IAAI,CAAC,MAAM,CAAC,IAAI,sBAAO,SAAS,CAAC,IAAM,CAAC,CAAC;YAC9E,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,0CAAS,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;SACjD;QAED,aAAa;QACb,IAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,sDAAW,CAAC,CAAC;IAChD,CAAC;IAED,iBAAiB;IACT,mDAAqB,GAA7B;QACI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,+CAAY,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAW,CAAC,CAAC;QAE7E,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC5D,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,oCAAQ,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,qDAAa,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAW,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,2BAAO,EAAE,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,eAAe;IACP,qDAAuB,GAA/B;QACI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACvE,IAAI,WAAW,EAAE;YACb,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,sBAAO,WAAW,CAAC,IAAM,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,iCAAW,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;YAEzF,IAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,OAAO,CAAC,GAAG,CAAC,0CAAS,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;SACjD;IACL,CAAC;IAED,gBAAgB;IACR,qDAAuB,GAA/B;QACI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxE,IAAI,YAAY,EAAE;YACd,IAAM,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,6BAAS,cAAc,CAAC,CAAC,UAAK,cAAc,CAAC,CAAC,uBAAQ,YAAY,CAAC,IAAM,CAAC,CAAC;YAE1G,IAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,oCAAQ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC;SAChD;IACL,CAAC;IAED,aAAa;IACL,mDAAqB,GAA7B;QAAA,iBAoCC;QAnCG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAErC,IAAM,cAAc,GAAG,GAAG,CAAC,CAAC,YAAY;QACxC,IAAM,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO;QAC9B,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAM,WAAW,GAAG,WAAW,CAAC;YAC5B,WAAW,IAAI,cAAc,CAAC;YAE9B,cAAc;YACd,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/C,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC9C,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAE/C,SAAS;YACT,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAChD,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/C,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAEhD,UAAU;YACV,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAE1C,WAAW;YACX,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,cAAc,CAAC,EAAE;gBACtE,OAAO,CAAC,GAAG,CAAC,qBAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAG,CAAC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAI,KAAI,CAAC,MAAM,CAAC,IAAI,aAAQ,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,iBAAY,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;gBACnK,OAAO,CAAC,GAAG,CAAI,KAAI,CAAC,KAAK,CAAC,IAAI,aAAQ,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,iBAAY,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;gBAC/J,OAAO,CAAC,GAAG,CAAI,KAAI,CAAC,MAAM,CAAC,IAAI,aAAQ,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,iBAAY,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;aACtK;YAED,IAAI,WAAW,IAAI,SAAS,EAAE;gBAC1B,aAAa,CAAC,WAAW,CAAC,CAAC;gBAC3B,KAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3B;QACL,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,aAAa;IACL,8CAAgB,GAAxB;QACI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,MAAG,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,2BAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAQ,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,0BAAc,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;QAE1E,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,CAAC,IAAI,MAAG,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,iCAAW,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAW,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,0BAAc,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;QAEzE,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,MAAM,CAAC,IAAI,MAAG,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,WAAS,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,SAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAO,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,0BAAc,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,MAAQ,CAAC,CAAC;QAE1E,SAAS;QACT,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;IAChE,CAAC;IACL,0BAAC;AAAD,CAnTA,AAmTC,IAAA;AAnTY,kDAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BattleManager } from \"../systems/BattleManager\";\nimport { Character } from \"../characters/Character\";\nimport { CharacterRole } from \"../types/CharacterTypes\";\n\n// 导入技能\nimport { PlayerSkillFire1 } from \"../skills/PlayerSkillFire1\";\nimport { HealingLightSkill } from \"../skills/HealingLightSkill\";\nimport { ThunderStormSkill } from \"../skills/ThunderStormSkill\";\nimport { ChargeAttackSkill } from \"../skills/ChargeAttackSkill\";\n\n// 导入buff\nimport { AttackBoostBuff } from \"../buff/AttackBoostBuff\";\nimport { HealOverTimeBuff } from \"../buff/HealOverTimeBuff\";\nimport { StunBuff } from \"../buff/StunBuff\";\nimport { PoisonBuff } from \"../buff/PoisonBuff\";\n\n/**\n * 技能和Buff使用示例\n * 展示如何使用新的技能系统和buff系统\n */\nexport class SkillAndBuffExample {\n    private battleManager: BattleManager;\n    private player: Character;\n    private enemy: Character;\n    private healer: Character;\n\n    constructor() {\n        this.battleManager = BattleManager.getInstance();\n        this.initializeCharacters();\n        this.setupSkills();\n        this.runExample();\n    }\n\n    /** 初始化角色 */\n    private initializeCharacters(): void {\n        // 创建玩家角色（战士）\n        this.player = new Character();\n        this.player.setCharacterData({\n            prefabKey: \"player_warrior\",\n            name: \"勇敢的战士\",\n            role: CharacterRole.HERO,\n            initialAttributes: {\n                hp: 1000,\n                maxHp: 1000,\n                mp: 200,\n                maxMp: 200,\n                maxStamina: 150,\n                attack: 120,\n                defense: 80,\n                attackSpeed: 1.0,\n                moveSpeed: 200,\n                attackRange: 150,\n                criticalRate: 0.15,\n                criticalDamage: 1.5,\n                hitRate: 0.95,\n                dodgeRate: 0.1,\n                level: 1,\n                experience: 0\n            }\n        });\n\n        // 创建敌人角色\n        this.enemy = new Character();\n        this.enemy.setCharacterData({\n            prefabKey: \"enemy_orc\",\n            name: \"兽人战士\",\n            role: CharacterRole.ENEMY,\n            initialAttributes: {\n                hp: 800,\n                maxHp: 800,\n                mp: 100,\n                maxMp: 100,\n                maxStamina: 120,\n                attack: 100,\n                defense: 60,\n                attackSpeed: 1.0,\n                moveSpeed: 180,\n                attackRange: 120,\n                criticalRate: 0.1,\n                criticalDamage: 1.3,\n                hitRate: 0.9,\n                dodgeRate: 0.05,\n                level: 1,\n                experience: 0\n            }\n        });\n\n        // 创建治疗师角色\n        this.healer = new Character();\n        this.healer.setCharacterData({\n            prefabKey: \"healer_priest\",\n            name: \"神圣牧师\",\n            role: CharacterRole.HERO,\n            initialAttributes: {\n                hp: 600,\n                maxHp: 600,\n                mp: 300,\n                maxMp: 300,\n                maxStamina: 80,\n                attack: 50,\n                defense: 40,\n                attackSpeed: 1.0,\n                moveSpeed: 160,\n                attackRange: 200,\n                criticalRate: 0.05,\n                criticalDamage: 1.2,\n                hitRate: 0.98,\n                dodgeRate: 0.15,\n                level: 1,\n                experience: 0\n            }\n        });\n\n        console.log(\"Characters initialized:\");\n        console.log(`Player: ${this.player.characterName} - HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);\n        console.log(`Enemy: ${this.enemy.characterName} - HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);\n        console.log(`Healer: ${this.healer.characterName} - HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}`);\n    }\n\n    /** 设置技能 */\n    private setupSkills(): void {\n        // 为玩家添加技能\n        const fireballSkill = new PlayerSkillFire1();\n        const chargeSkill = new ChargeAttackSkill();\n        this.player.skillManager.addSkill(fireballSkill);\n        this.player.skillManager.addSkill(chargeSkill);\n\n        // 为治疗师添加技能\n        const healSkill = new HealingLightSkill();\n        const thunderSkill = new ThunderStormSkill();\n        this.healer.skillManager.addSkill(healSkill);\n        this.healer.skillManager.addSkill(thunderSkill);\n\n        console.log(\"Skills added to characters\");\n    }\n\n    /** 运行示例 */\n    private runExample(): void {\n        console.log(\"\\n=== 技能和Buff系统演示开始 ===\\n\");\n\n        // 开始战斗\n        this.battleManager.startBattle(\"skill_demo_battle\", [this.player, this.enemy, this.healer]);\n\n        // 演示1: 攻击力提升buff\n        this.demonstrateAttackBoostBuff();\n\n        // 演示2: 火球术攻击\n        this.demonstrateFireballAttack();\n\n        // 演示3: 毒素debuff\n        this.demonstratePoisonDebuff();\n\n        // 演示4: 治疗技能和持续治疗buff\n        this.demonstrateHealingSkill();\n\n        // 演示5: 眩晕debuff\n        this.demonstrateStunDebuff();\n\n        // 演示6: 冲锋攻击技能\n        this.demonstrateChargeAttack();\n\n        // 演示7: 雷暴术范围攻击\n        this.demonstrateThunderStorm();\n\n        // 模拟战斗更新\n        this.simulateBattleUpdates();\n\n        console.log(\"\\n=== 技能和Buff系统演示结束 ===\\n\");\n    }\n\n    /** 演示攻击力提升buff */\n    private demonstrateAttackBoostBuff(): void {\n        console.log(\"\\n--- 演示攻击力提升Buff ---\");\n        console.log(`${this.player.name} 当前攻击力: ${this.player.attributes.attack}`);\n\n        const attackBuff = new AttackBoostBuff(this.player, this.player, 10.0, 1.5);\n        this.player.buffManager.addBuff(attackBuff);\n\n        console.log(`${this.player.name} 获得攻击力提升buff后攻击力: ${this.player.attributes.attack}`);\n        console.log(`Buff信息:`, attackBuff.getDebugInfo());\n    }\n\n    /** 演示火球术攻击 */\n    private demonstrateFireballAttack(): void {\n        console.log(\"\\n--- 演示火球术攻击 ---\");\n        const fireballSkill = this.player.skillManager.getSkill(\"player_skill_fire1\");\n        if (fireballSkill) {\n            console.log(`${this.player.name} 释放 ${fireballSkill.name}`);\n            console.log(`敌人当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);\n\n            const success = fireballSkill.cast(this.player, this.enemy);\n            console.log(`技能释放${success ? '成功' : '失败'}`);\n        }\n    }\n\n    /** 演示毒素debuff */\n    private demonstratePoisonDebuff(): void {\n        console.log(\"\\n--- 演示毒素Debuff ---\");\n        console.log(`${this.enemy.name} 当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);\n\n        const poisonBuff = new PoisonBuff(this.player, this.enemy, 8.0, 25);\n        this.enemy.buffManager.addBuff(poisonBuff);\n\n        console.log(`${this.enemy.name} 中毒了！`);\n        console.log(`毒素信息:`, poisonBuff.getDebugInfo());\n    }\n\n    /** 演示治疗技能 */\n    private demonstrateHealingSkill(): void {\n        console.log(\"\\n--- 演示治疗技能 ---\");\n\n        // 先让玩家受伤\n        this.player.attributes.takeDamage(300);\n        console.log(`${this.player.name} 受伤后HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);\n\n        const healSkill = this.healer.skillManager.getSkill(\"healing_light\");\n        if (healSkill) {\n            console.log(`${this.healer.name} 对 ${this.player.name} 释放 ${healSkill.name}`);\n            const success = healSkill.cast(this.healer, this.player);\n            console.log(`治疗技能释放${success ? '成功' : '失败'}`);\n        }\n\n        // 添加持续治疗buff\n        const healOverTimeBuff = new HealOverTimeBuff(this.healer, this.player, 6.0, 20);\n        this.player.buffManager.addBuff(healOverTimeBuff);\n        console.log(`${this.player.name} 获得持续治疗效果`);\n    }\n\n    /** 演示眩晕debuff */\n    private demonstrateStunDebuff(): void {\n        console.log(\"\\n--- 演示眩晕Debuff ---\");\n        console.log(`${this.enemy.name} 当前移动速度: ${this.enemy.attributes.moveSpeed}`);\n\n        const stunBuff = new StunBuff(this.player, this.enemy, 3.0);\n        this.enemy.buffManager.addBuff(stunBuff);\n\n        console.log(`${this.enemy.name} 被眩晕了！`);\n        console.log(`${this.enemy.name} 眩晕后移动速度: ${this.enemy.attributes.moveSpeed}`);\n        console.log(`眩晕信息:`, stunBuff.getDebugInfo());\n    }\n\n    /** 演示冲锋攻击技能 */\n    private demonstrateChargeAttack(): void {\n        console.log(\"\\n--- 演示冲锋攻击技能 ---\");\n        const chargeSkill = this.player.skillManager.getSkill(\"charge_attack\");\n        if (chargeSkill) {\n            console.log(`${this.player.name} 对 ${this.enemy.name} 释放 ${chargeSkill.name}`);\n            console.log(`敌人当前HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);\n\n            const success = chargeSkill.cast(this.player, this.enemy);\n            console.log(`冲锋攻击释放${success ? '成功' : '失败'}`);\n        }\n    }\n\n    /** 演示雷暴术范围攻击 */\n    private demonstrateThunderStorm(): void {\n        console.log(\"\\n--- 演示雷暴术范围攻击 ---\");\n        const thunderSkill = this.healer.skillManager.getSkill(\"thunder_storm\");\n        if (thunderSkill) {\n            const targetPosition = cc.v3(100, 100, 0);\n            console.log(`${this.healer.name} 在位置 (${targetPosition.x}, ${targetPosition.y}) 释放 ${thunderSkill.name}`);\n\n            const success = thunderSkill.cast(this.healer, undefined, undefined, targetPosition);\n            console.log(`雷暴术释放${success ? '成功' : '失败'}`);\n        }\n    }\n\n    /** 模拟战斗更新 */\n    private simulateBattleUpdates(): void {\n        console.log(\"\\n--- 模拟战斗更新 (5秒) ---\");\n\n        const updateInterval = 0.1; // 100ms更新间隔\n        const totalTime = 5.0; // 总共5秒\n        let currentTime = 0;\n\n        const updateTimer = setInterval(() => {\n            currentTime += updateInterval;\n\n            // 更新所有角色的buff\n            this.player.buffManager.update(updateInterval);\n            this.enemy.buffManager.update(updateInterval);\n            this.healer.buffManager.update(updateInterval);\n\n            // 更新技能冷却\n            this.player.skillManager.update(updateInterval);\n            this.enemy.skillManager.update(updateInterval);\n            this.healer.skillManager.update(updateInterval);\n\n            // 更新战斗管理器\n            this.battleManager.update(updateInterval);\n\n            // 每秒输出一次状态\n            if (Math.floor(currentTime) !== Math.floor(currentTime - updateInterval)) {\n                console.log(`\\n时间: ${Math.floor(currentTime)}s`);\n                console.log(`${this.player.name} HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}, Buffs: ${this.player.buffManager.getAllBuffs().length}`);\n                console.log(`${this.enemy.name} HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}, Buffs: ${this.enemy.buffManager.getAllBuffs().length}`);\n                console.log(`${this.healer.name} HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}, Buffs: ${this.healer.buffManager.getAllBuffs().length}`);\n            }\n\n            if (currentTime >= totalTime) {\n                clearInterval(updateTimer);\n                this.printFinalStatus();\n            }\n        }, updateInterval * 1000);\n    }\n\n    /** 打印最终状态 */\n    private printFinalStatus(): void {\n        console.log(\"\\n--- 最终状态 ---\");\n        console.log(`${this.player.name}:`);\n        console.log(`  HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}`);\n        console.log(`  攻击力: ${this.player.attributes.attack}`);\n        console.log(`  活跃Buffs: ${this.player.buffManager.getAllBuffs().length}`);\n\n        console.log(`${this.enemy.name}:`);\n        console.log(`  HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}`);\n        console.log(`  移动速度: ${this.enemy.attributes.moveSpeed}`);\n        console.log(`  活跃Buffs: ${this.enemy.buffManager.getAllBuffs().length}`);\n\n        console.log(`${this.healer.name}:`);\n        console.log(`  HP: ${this.healer.attributes.currentHp}/${this.healer.attributes.maxHp}`);\n        console.log(`  MP: ${this.healer.attributes.currentMp}/${this.healer.attributes.maxMp}`);\n        console.log(`  活跃Buffs: ${this.healer.buffManager.getAllBuffs().length}`);\n\n        // 打印战斗统计\n        console.log(\"\\n战斗统计:\", this.battleManager.getBattleStats());\n    }\n}\n"]}