"use strict";
cc._RF.push(module, '087e2LAsmRCjobhhJ95y2R5', 'SkillManager');
// card/cardgame/src/SkillManager.ts

// import Base from "./Base/Base";
// import SkillBase from "./Base/SkillBase";
// const { ccclass, property } = cc._decorator;
// declare global {
//     export interface ISkillManager {
//     }
// }
// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// @ccclass
// export default class SkillManager extends Base {
//     /**所有已加载的技能列表 */
//     skillMap: Map<string, ISkillDataType> = new Map();
//     /**加载技能数据表 */
//     loadSkillTableData() {
//     }
//     /**根据id获取技能表数据 */
//     getSkillDataById(id: string) {
//         let skill: SkillBase;
//         try {
//             let skillClassName: { new(): SkillBase; } = eval(id);
//             skill = new skillClassName();
//             //设置数据
//             skill.data = this.skillMap.get(id);
//             skill.initSkillData();
//         } catch (error) {
//             cc.error(`创建技能失败，id：${id},err: ${error}`);
//         }
//         return skill;
//     }
//     /**使用技能 */
//     useSkill(skill: SkillBase) { }
//     /**升级技能 */
//     levelUpSkill(skill: SkillBase) { }
//     /**降级技能 */
//     downgradeSkill(skill: SkillBase) { }
//     /**冻结技能 */
//     frozenSkill(skill: SkillBase) { }
// }

cc._RF.pop();