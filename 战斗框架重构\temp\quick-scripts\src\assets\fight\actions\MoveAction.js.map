{"version": 3, "sources": ["assets\\fight\\actions\\MoveAction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA6C;AAEvC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,aAAa;AACb,IAAY,QAOX;AAPD,WAAY,QAAQ;IAChB,WAAW;IACX,+BAAmB,CAAA;IACnB,WAAW;IACX,6BAAiB,CAAA;IACjB,WAAW;IACX,6BAAiB,CAAA;AACrB,CAAC,EAPW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAOnB;AACD,aAAa;AACb,IAAY,SAOX;AAPD,WAAY,SAAS;IACjB,SAAS;IACT,0BAAa,CAAA;IACb,UAAU;IACV,8BAAiB,CAAA;IACjB,UAAU;IACV,oCAAuB,CAAA;AAC3B,CAAC,EAPW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAOpB;AA+BD,cAAc;AAEd;IAAgC,8BAAY;IAA5C;QAAA,qEA8RC;QA7RG,YAAY;QACJ,mBAAa,GAAc,SAAS,CAAC,IAAI,CAAC;QAClD,UAAU;QACF,cAAQ,GAAY,IAAI,CAAC;QACjC,UAAU;QACF,eAAS,GAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,UAAU;QACF,mBAAa,GAAkB,IAAI,CAAC;QAC5C,UAAU;QACF,gBAAU,GAAoD,EAAE,CAAC;QACzE,eAAe;QACP,oBAAc,GAAY,IAAI,CAAC;QAC/B,oBAAc,GAAgB,IAAI,CAAC;QACnC,wBAAkB,GAAY,IAAI,CAAC;QACnC,mBAAa,GAAW,CAAC,CAAC;QAElC,UAAU;QAEV,kBAAY,GAAW,GAAG,CAAC;QAE3B,gBAAU,GAAY,IAAI,CAAC;QAE3B,oBAAc,GAAW,CAAC,CAAC;;IAuQ/B,CAAC;IArQG,sBAAI,oCAAY;QADhB,eAAe;aACf,cAAgC,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAE5D,sBAAI,gCAAQ;QADZ,iBAAiB;aACjB,cAA0B,OAAO,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAEzE,sBAAI,+BAAO;QADX,eAAe;aACf,cAAyB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhD,eAAe;aACf,UAAY,KAAc;YACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,KAAK,EAAE;gBACR,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;QACL,CAAC;;;OAR+C;IAWhD,sBAAI,gCAAQ;QADZ,eAAe;aACf,cAA0B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;;OAAA;IAC1D;;;OAGG;IACH,2BAAM,GAAN,UAAO,MAAe;QAClB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,2BAAM,GAAN,UAAO,MAAe,EAAE,MAAoB;QACxC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,IAAM,UAAU,cACZ,IAAI,EAAE,QAAQ,CAAC,MAAM,EACrB,KAAK,EAAE,IAAI,CAAC,YAAY,EACxB,SAAS,EAAE,KAAK,IACb,MAAM,CACZ,CAAC;QACF,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,EAAE;YACvC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;YACxE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;SACxE;IACL,CAAC;IAED;;;OAGG;IACH,gCAAW,GAAX,UAAY,MAAqB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,aAAa,cACd,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EACpB,KAAK,EAAE,IAAI,CAAC,YAAY,EACxB,WAAW,EAAE,EAAE,EACf,WAAW,EAAE,IAAI,IACd,MAAM,CACZ,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,aAAa;IACb,+BAAU,GAAV;QACI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,EAAE;YAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACjC;IACL,CAAC;IACD,aAAa;IACb,6BAAQ,GAAR;QACI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,EAAE;YACvC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACjC;IACL,CAAC;IACD;;;OAGG;IACH,6BAAQ,GAAR,UAAS,KAAa;QAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IACD;;;;OAIG;IACH,wCAAmB,GAAnB,UAAoB,MAAe;QAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IACD,aAAa;IACH,2BAAM,GAAhB,UAAiB,SAAiB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxB,KAAK,SAAS,CAAC,MAAM;gBACjB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC7B,MAAM;YACV,KAAK,SAAS,CAAC,SAAS;gBACpB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAChC,MAAM;YACV,KAAK,SAAS,CAAC,IAAI;gBACf,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC3B,MAAM;SACb;IACL,CAAC;IACD,kBAAkB;IACR,8BAAS,GAAnB;QACI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IACD,kBAAkB;IACV,gCAAW,GAAnB,UAAoB,MAAe,EAAE,MAAmB;;QACpD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAChC,SAAS;QACT,MAAA,MAAM,CAAC,OAAO,+CAAd,MAAM,EAAa;IACvB,CAAC;IACD,iBAAiB;IACT,iCAAY,GAApB,UAAqB,SAAiB;;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1E,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO;SACV;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;QACtC,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACzD,WAAW;QACX,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,SAAS;YAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO;SACV;QACD,OAAO;QACP,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QAC7D,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAM,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC;QACvC,OAAO;QACP,IAAM,UAAU,GAAG,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC/B,OAAO;QACP,IAAM,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QAC3E,IAAM,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF,SAAS;QACT,MAAA,MAAA,IAAI,CAAC,cAAc,EAAC,UAAU,mDAAG,IAAI,CAAC,aAAa,EAAE;IACzD,CAAC;IACD,eAAe;IACP,oCAAe,GAAvB,UAAwB,SAAiB;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE;YACzF,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;SACV;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;QAClC,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvF,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACzD,WAAW;QACX,IAAI,QAAQ,IAAI,MAAM,CAAC,WAAY,EAAE;YACjC,OAAO,CAAC,YAAY;SACvB;QACD,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAY,EAAE;YAChC,WAAW;YACX,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAO,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACvE,OAAO;SACV;QACD,SAAS;QACT,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QAChD,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAM,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC;QACvC,OAAO;QACP,IAAM,UAAU,GAAG,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,eAAe;IACP,+BAAU,GAAlB,UAAmB,SAAiB;QAChC,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;SACjC;QACD,WAAW;QACX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAG,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SACtD;IACL,CAAC;IACD,eAAe;IACP,kCAAa,GAArB,UAAsB,UAAmB;QACrC,IAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtE,IAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAClB,IAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAC7E,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;SACvC;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;SACvC;IACL,CAAC;IACD,iBAAiB;IACT,wCAAmB,GAA3B;;QACI,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAA,MAAA,IAAI,CAAC,cAAc,EAAC,UAAU,mDAAK;SACtC;QACD,YAAY;QACZ,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACzC,IAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClF,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;SACvC;QACD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IACD,eAAe;IACP,6BAAQ,GAAhB,UAAiB,QAAmB;QAChC,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;YACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;YAC9B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC3C;IACL,CAAC;IACD,iBAAiB;IACT,mCAAc,GAAtB,UAAuB,QAAmB,EAAE,QAAmB;QAC3D,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrF,YAAY;QACZ,IAAI,QAAQ,KAAK,SAAS,CAAC,IAAI,EAAE;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;SAC1B;IACL,CAAC;IACD,eAAe;IACf,gCAAW,GAAX;;QACI,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACnC,QAAQ,EAAE,IAAI,CAAC,aAAa;YAC5B,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc;YAChC,WAAW,EAAE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS;YACvD,YAAY,EAAE,aAAA,IAAI,CAAC,aAAa,0CAAE,MAAM,0CAAE,IAAI,KAAI,IAAI;SACzD,CAAC;IACN,CAAC;IA1QD;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;oDACQ;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;kDACM;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;sDACM;IAvBlB,UAAU;QADtB,OAAO;OACK,UAAU,CA8RtB;IAAD,iBAAC;CA9RD,AA8RC,CA9R+B,EAAE,CAAC,SAAS,GA8R3C;AA9RY,gCAAU", "file": "", "sourceRoot": "/", "sourcesContent": ["import FightEvent from \"../types/FightEvent\";\n\nconst { ccclass, property } = cc._decorator;\n\n/*** 移动类型枚举*/\nexport enum MoveType {\n    /** 瞬间移动 */\n    INSTANT = \"instant\",\n    /** 平滑移动 */\n    SMOOTH = \"smooth\",\n    /** 跟随移动 */\n    FOLLOW = \"follow\"\n}\n/*** 移动状态枚举*/\nexport enum MoveState {\n    /** 静止 */\n    IDLE = \"idle\",\n    /** 移动中 */\n    MOVING = \"moving\",\n    /** 跟随中 */\n    FOLLOWING = \"following\"\n}\n/*** 移动配置接口*/\nexport interface IMoveConfig {\n    /** 移动类型 */\n    type: MoveType;\n    /** 移动速度（像素/秒） */\n    speed?: number;\n    /** 是否使用缓动 */\n    useEasing?: boolean;\n    /** 缓动类型 */\n    easingType?: string;\n    /** 移动完成回调 */\n    onComplete?: () => void;\n    /** 移动开始回调 */\n    onStart?: () => void;\n    /** 移动进度回调 */\n    onProgress?: (progress: number) => void;\n}\n/** * 跟随配置接口 */\nexport interface IFollowConfig {\n    /** 跟随目标 */\n    target: cc.Node;\n    /** 跟随偏移 */\n    offset: cc.Vec3;\n    /** 跟随速度 */\n    speed?: number;\n    /** 最小跟随距离 */\n    minDistance?: number;\n    /** 最大跟随距离 */\n    maxDistance?: number;\n}\n/** * 移动动作类 */\n@ccclass\nexport class MoveAction extends cc.Component {\n    /**当前移动状态 */\n    private _currentState: MoveState = MoveState.IDLE;\n    /**移动权限 */\n    private _canMove: boolean = true;\n    /**当前速度 */\n    private _velocity: cc.Vec3 = cc.Vec3.ZERO;\n    /**跟随配置 */\n    private _followConfig: IFollowConfig = null;\n    /**移动队列 */\n    private _moveQueue: Array<{ target: cc.Vec3; config: IMoveConfig }> = [];\n    /**当前移动目标和配置 */\n    private _currentTarget: cc.Vec3 = null;\n    private _currentConfig: IMoveConfig = null;\n    private _moveStartPosition: cc.Vec3 = null;\n    private _moveProgress: number = 0;\n\n    /**配置属性 */\n    @property(cc.Float)\n    defaultSpeed: number = 100;\n    @property(cc.Boolean)\n    allowQueue: boolean = true;\n    @property(cc.Integer)\n    maxQueueLength: number = 5;\n    /*** 获取当前移动状态*/\n    get currentState(): MoveState { return this._currentState; }\n    /**  * 是否正在移动  */\n    get isMoving(): boolean { return this._currentState !== MoveState.IDLE; }\n    /** * 是否可以移动 */\n    get canMove(): boolean { return this._canMove; }\n\n    /** * 设置移动权限 */\n    set canMove(value: boolean) {\n        this._canMove = value;\n        if (!value) {\n            this.stopMove();\n        }\n    }\n\n    /** * 获取当前速度 */\n    get velocity(): cc.Vec3 { return this._velocity.clone(); }\n    /**\n     * 瞬间移动一个偏移量\n     * @param offset 移动偏移量\n     */\n    moveBy(offset: cc.Vec3): void {\n        if (!this._canMove) {\n            return;\n        }\n        this._velocity = offset.clone();\n    }\n\n    /**\n     * 移动到指定位置\n     * @param target 目标位置（世界坐标）\n     * @param config 移动配置\n     */\n    moveTo(target: cc.Vec3, config?: IMoveConfig): void {\n        if (!this._canMove) {\n            return;\n        }\n        const moveConfig: IMoveConfig = {\n            type: MoveType.SMOOTH,\n            speed: this.defaultSpeed,\n            useEasing: false,\n            ...config\n        };\n        if (this._currentState === MoveState.IDLE) {\n            this.startMoveTo(target, moveConfig);\n        } else if (this.allowQueue && this._moveQueue.length < this.maxQueueLength) {\n            this._moveQueue.push({ target: target.clone(), config: moveConfig });\n        }\n    }\n\n    /**\n     * 开始跟随目标\n     * @param config 跟随配置\n     */\n    startFollow(config: IFollowConfig): void {\n        if (!this._canMove) {\n            return;\n        }\n        this.stopMove();\n        this._followConfig = {\n            offset: cc.Vec3.ZERO,\n            speed: this.defaultSpeed,\n            minDistance: 10,\n            maxDistance: 1000,\n            ...config\n        };\n        this.setState(MoveState.FOLLOWING);\n    }\n\n    /** * 停止跟随 */\n    stopFollow(): void {\n        if (this._currentState === MoveState.FOLLOWING) {\n            this._followConfig = null;\n            this.setState(MoveState.IDLE);\n        }\n    }\n    /** * 停止移动 */\n    stopMove(): void {\n        this._velocity = cc.Vec3.ZERO;\n        this._currentTarget = null;\n        this._currentConfig = null;\n        this._moveStartPosition = null;\n        this._moveProgress = 0;\n        this._moveQueue.length = 0;\n        if (this._currentState !== MoveState.IDLE) {\n            this.setState(MoveState.IDLE);\n        }\n    }\n    /**\n     * 设置移动速度\n     * @param speed 速度值\n     */\n    setSpeed(speed: number): void {\n        this.defaultSpeed = Math.max(0, speed);\n    }\n    /**\n     * 获取到目标的距离\n     * @param target 目标位置\n     * @returns 距离\n     */\n    getDistanceToTarget(target: cc.Vec3): number {\n        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        return cc.Vec3.distance(currentPos, target);\n    }\n    /*** 更新移动逻辑*/\n    protected update(deltaTime: number): void {\n        if (!this._canMove) {\n            return;\n        }\n        switch (this._currentState) {\n            case MoveState.MOVING:\n                this.updateMoving(deltaTime);\n                break;\n            case MoveState.FOLLOWING:\n                this.updateFollowing(deltaTime);\n                break;\n            case MoveState.IDLE:\n                this.updateIdle(deltaTime);\n                break;\n        }\n    }\n    /** * 组件禁用时停止移动 */\n    protected onDisable(): void {\n        this.stopMove();\n        this.stopFollow();\n    }\n    /** * 开始移动到目标位置 */\n    private startMoveTo(target: cc.Vec3, config: IMoveConfig): void {\n        this._currentTarget = target.clone();\n        this._currentConfig = config;\n        this._moveStartPosition = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        this._moveProgress = 0;\n        this.setState(MoveState.MOVING);\n        // 触发开始回调\n        config.onStart?.();\n    }\n    /**  * 更新移动状态  */\n    private updateMoving(deltaTime: number): void {\n        if (!this._currentTarget || !this._currentConfig || !this._moveStartPosition) {\n            this.setState(MoveState.IDLE);\n            return;\n        }\n        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const targetPos = this._currentTarget;\n        const distance = cc.Vec3.distance(currentPos, targetPos);\n        // 检查是否到达目标\n        if (distance <= 5) { // 5像素的容差\n            this.completeCurrentMove();\n            return;\n        }\n        // 计算移动\n        const speed = this._currentConfig.speed || this.defaultSpeed;\n        const direction = targetPos.subtract(currentPos).normalize();\n        const moveDistance = speed * deltaTime;\n        // 应用移动\n        const moveVector = direction.multiplyScalar(moveDistance);\n        this.applyMovement(moveVector);\n        // 更新进度\n        const totalDistance = cc.Vec3.distance(this._moveStartPosition, targetPos);\n        const movedDistance = cc.Vec3.distance(this._moveStartPosition, currentPos);\n        this._moveProgress = totalDistance > 0 ? Math.min(1, movedDistance / totalDistance) : 1;\n        // 触发进度回调\n        this._currentConfig.onProgress?.(this._moveProgress);\n    }\n    /** * 更新跟随状态 */\n    private updateFollowing(deltaTime: number): void {\n        if (!this._followConfig || !this._followConfig.target || !this._followConfig.target.isValid) {\n            this.stopFollow();\n            return;\n        }\n        const config = this._followConfig;\n        const targetPos = config.target.convertToWorldSpaceAR(cc.Vec3.ZERO).add(config.offset);\n        const currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const distance = cc.Vec3.distance(currentPos, targetPos);\n        // 检查是否需要移动\n        if (distance <= config.minDistance!) {\n            return; // 太近了，不需要移动\n        }\n        if (distance > config.maxDistance!) {\n            // 太远了，直接传送\n            this.node.position = this.node.parent!.convertToNodeSpaceAR(targetPos);\n            return;\n        }\n        // 计算跟随移动\n        const speed = config.speed || this.defaultSpeed;\n        const direction = targetPos.subtract(currentPos).normalize();\n        const moveDistance = speed * deltaTime;\n        // 应用移动\n        const moveVector = direction.multiplyScalar(moveDistance);\n        this.applyMovement(moveVector);\n    }\n\n    /** * 更新空闲状态 */\n    private updateIdle(deltaTime: number): void {\n        // 处理瞬间移动\n        if (!this._velocity.equals(cc.Vec3.ZERO)) {\n            this.applyMovement(this._velocity);\n            this._velocity = cc.Vec3.ZERO;\n        }\n        // 处理队列中的移动\n        if (this._moveQueue.length > 0) {\n            const nextMove = this._moveQueue.shift()!;\n            this.startMoveTo(nextMove.target, nextMove.config);\n        }\n    }\n    /**  * 应用移动  */\n    private applyMovement(moveVector: cc.Vec3): void {\n        const currentWorldPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        const targetWorldPos = currentWorldPos.add(moveVector);\n        if (this.node.parent) {\n            const targetLocalPos = this.node.parent.convertToNodeSpaceAR(targetWorldPos);\n            this.node.position = targetLocalPos;\n        } else {\n            this.node.position = targetWorldPos;\n        }\n    }\n    /**  * 完成当前移动  */\n    private completeCurrentMove(): void {\n        if (this._currentConfig) {\n            this._currentConfig.onComplete?.();\n        }\n        // 精确设置到目标位置\n        if (this._currentTarget && this.node.parent) {\n            const targetLocalPos = this.node.parent.convertToNodeSpaceAR(this._currentTarget);\n            this.node.position = targetLocalPos;\n        }\n        this.setState(MoveState.IDLE);\n    }\n    /** * 设置移动状态 */\n    private setState(newState: MoveState): void {\n        if (this._currentState !== newState) {\n            const oldState = this._currentState;\n            this._currentState = newState;\n            this.onStateChanged(oldState, newState);\n        }\n    }\n    /**  * 状态改变处理  */\n    private onStateChanged(oldState: MoveState, newState: MoveState): void {\n        // 发送状态改变事件\n        this.node.emit(FightEvent.moveStateChanged, { oldState, newState, component: this });\n        // 清理状态特定的数据\n        if (newState === MoveState.IDLE) {\n            this._currentTarget = null;\n            this._currentConfig = null;\n            this._moveStartPosition = null;\n            this._moveProgress = 0;\n        }\n    }\n    /** * 获取移动信息 */\n    getMoveInfo() {\n        return {\n            currentState: this._currentState,\n            isMoving: this.isMoving,\n            canMove: this._canMove,\n            velocity: this._velocity,\n            queueLength: this._moveQueue.length,\n            progress: this._moveProgress,\n            hasTarget: !!this._currentTarget,\n            isFollowing: this._currentState === MoveState.FOLLOWING,\n            followTarget: this._followConfig?.target?.name || null\n        };\n    }\n}\n"]}