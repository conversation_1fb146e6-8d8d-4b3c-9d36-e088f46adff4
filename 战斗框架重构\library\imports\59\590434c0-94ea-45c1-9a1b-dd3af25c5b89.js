"use strict";
cc._RF.push(module, '59043TAlOpFwZob3TryXFuJ', 'BulletSystem');
// fight/systems/BulletSystem.ts

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulletLauncher = exports.Bullet = void 0;
var FightEvent_1 = require("../types/FightEvent");
var IBullet_1 = require("../types/IBullet");
var EventManager_1 = require("./EventManager");
/**
 * 子弹系统实现
 * 提供基础的子弹发射和管理功能
 */
var Bullet = /** @class */ (function () {
    function Bullet(id, config, caster, firePosition, target, targetPosition) {
        this._timeElapsed = 0;
        this._hasCollided = false;
        this._isDestroyed = false;
        this._id = id;
        this._config = config;
        this._caster = caster;
        this._target = target;
        this._targetPosition = targetPosition;
        this._firePosition = firePosition;
        this._remainingHits = config.maxHits;
        this._eventManager = EventManager_1.EventManager.createLocal("Bullet_" + id);
        // 创建轨迹实例
        this._trajectory = this.createTrajectory();
        this.createBulletNode();
    }
    Object.defineProperty(Bullet.prototype, "id", {
        // 实现IBullet接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "type", {
        get: function () { return this._config.type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "node", {
        get: function () { return this._node; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "targetPosition", {
        get: function () { return this._targetPosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "firePosition", {
        get: function () { return this._firePosition; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "currentPosition", {
        get: function () { return this._node ? this._node.position : cc.Vec3.ZERO; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "speed", {
        get: function () { return this._config.speed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "lifeTime", {
        get: function () { return this._config.lifeTime; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "timeElapsed", {
        get: function () { return this._timeElapsed; },
        set: function (value) { this._timeElapsed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "remainingHits", {
        get: function () { return this._remainingHits; },
        set: function (value) { this._remainingHits = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "maxHits", {
        get: function () { return this._config.maxHits; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "hasCollided", {
        get: function () { return this._hasCollided; },
        set: function (value) { this._hasCollided = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "isDestroyed", {
        get: function () { return this._isDestroyed; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Bullet.prototype, "trajectory", {
        get: function () { return this._trajectory; },
        enumerable: false,
        configurable: true
    });
    /*** 更新子弹*/
    Bullet.prototype.update = function (deltaTime) {
        if (this._isDestroyed) {
            return true; // 已销毁
        }
        this._timeElapsed += deltaTime;
        // 检查生命周期
        if (this._timeElapsed >= this._config.lifeTime) {
            this.destroy();
            return true;
        }
        // 更新位置
        this.updateMovement(deltaTime);
        // 检查碰撞
        this.checkCollision();
        return false;
    };
    /** * 命中目标 */
    Bullet.prototype.hit = function (target) {
        var _a, _b;
        if (this._isDestroyed || this._remainingHits <= 0) {
            return false;
        }
        // 造成伤害
        var damage = this.calculateDamage();
        target.takeDamageSimple(damage, this._caster);
        this._remainingHits--;
        this._hasCollided = true;
        console.log("Bullet " + this._id + " hit " + target.characterName + " for " + damage + " damage");
        // 播放命中音效
        if ((_a = this._config.audio) === null || _a === void 0 ? void 0 : _a.hitSound) {
            console.log("Playing hit sound: " + this._config.audio.hitSound);
        }
        // 触发命中事件
        this._eventManager.emit(FightEvent_1.default.bulletHit, { bullet: this, target: target, damage: damage });
        // 检查是否需要销毁
        if (!((_b = this._config.collision) === null || _b === void 0 ? void 0 : _b.piercing) || this._remainingHits <= 0) {
            this.destroy();
        }
        return true;
    };
    /** * 设置目标 */
    Bullet.prototype.setTarget = function (target) {
        this._target = target;
    };
    /** * 设置目标位置 */
    Bullet.prototype.setTargetPosition = function (position) {
        this._targetPosition = position;
    };
    /**  * 销毁子弹  */
    Bullet.prototype.destroy = function () {
        if (this._isDestroyed)
            return;
        this._isDestroyed = true;
        if (this._node && this._node.isValid) {
            this._node.destroy();
        }
        this._eventManager.emit(FightEvent_1.default.bulletDestroyed, { bullet: this });
        this._eventManager.cleanup();
    };
    /** * 创建轨迹实例 */
    Bullet.prototype.createTrajectory = function () {
        var _this = this;
        // 简化实现，返回一个基础轨迹 
        return {
            type: this._config.trajectory.type,
            calculateNextPosition: function (_bullet, deltaTime) {
                return _this.calculateNextPosition(deltaTime);
            },
            calculateRotation: function (_bullet) {
                return 0; // 简化实现
            },
            hasReachedTarget: function (bullet, threshold) {
                if (!_this._target)
                    return false;
                var distance = cc.Vec3.distance(bullet.currentPosition, _this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
                return distance <= threshold;
            }
        };
    };
    /** * 计算下一帧位置 */
    Bullet.prototype.calculateNextPosition = function (deltaTime) {
        if (!this._node)
            return cc.Vec3.ZERO;
        var currentPos = this._node.position;
        var targetPos;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        else if (this._targetPosition) {
            targetPos = this._targetPosition;
        }
        else {
            return currentPos; // 没有目标，保持当前位置
        }
        // 计算移动方向和距离
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = this._config.speed * deltaTime;
        return currentPos.add(direction.multiplyScalar(moveDistance));
    };
    /** * 创建子弹节点 */
    Bullet.prototype.createBulletNode = function () {
        this._node = new cc.Node("Bullet_" + this._id);
        // 添加精灵组件
        this._node.addComponent(cc.Sprite);
        // 这里应该加载子弹贴图
        // cc.resources.load(this._config.prefabPath, cc.SpriteFrame, (err, spriteFrame) => {
        //     if (!err && sprite.isValid) {
        //         sprite.spriteFrame = spriteFrame;
        //     }
        // });
        // 设置初始位置
        this._node.position = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 添加到场景
        cc.director.getScene().addChild(this._node);
    };
    /** * 更新移动 */
    Bullet.prototype.updateMovement = function (deltaTime) {
        if (!this._node || !this._node.isValid)
            return;
        var currentPos = this._node.position;
        var targetPos;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        else if (this._targetPosition) {
            targetPos = this._targetPosition;
        }
        else {
            // 没有目标，直接销毁
            this.destroy();
            return;
        }
        // 计算移动方向
        var direction = targetPos.subtract(currentPos).normalize();
        var moveDistance = this._config.speed * deltaTime;
        // 根据轨迹类型移动
        switch (this._config.trajectory.type) {
            case IBullet_1.TrajectoryType.LINEAR:
                this.moveLinear(direction, moveDistance);
                break;
            case IBullet_1.TrajectoryType.PARABOLIC:
                this.moveParabolic(targetPos, deltaTime);
                break;
            case IBullet_1.TrajectoryType.HOMING:
                this.moveHoming(targetPos, deltaTime);
                break;
            default:
                this.moveLinear(direction, moveDistance);
                break;
        }
        // 检查是否到达目标
        var distanceToTarget = cc.Vec3.distance(this._node.position, targetPos);
        if (distanceToTarget <= 10) { // 10像素的容错范围
            this.onHitTarget();
        }
    };
    /*** 线性移动*/
    Bullet.prototype.moveLinear = function (direction, distance) {
        var newPos = this._node.position.add(direction.multiplyScalar(distance));
        this._node.position = newPos;
    };
    /*** 抛物线移动(简化的抛物线实现)*/
    Bullet.prototype.moveParabolic = function (targetPos, _deltaTime) {
        var progress = this._timeElapsed / this._config.lifeTime;
        var startPos = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 计算抛物线轨迹
        var midPoint = startPos.add(targetPos).multiplyScalar(0.5);
        midPoint.y += 100; // 抛物线高度
        var x = cc.misc.lerp(startPos.x, targetPos.x, progress);
        var y = this.calculateParabolicY(startPos, midPoint, targetPos, progress);
        this._node.position = cc.v3(x, y, 0);
    };
    /** * 追踪移动 */
    Bullet.prototype.moveHoming = function (targetPos, deltaTime) {
        var currentPos = this._node.position;
        var direction = targetPos.subtract(currentPos).normalize();
        // 追踪子弹有更强的转向能力
        var moveDistance = this._config.speed * deltaTime;
        var newPos = currentPos.add(direction.multiplyScalar(moveDistance));
        this._node.position = newPos;
    };
    /** * 计算抛物线Y坐标 */
    Bullet.prototype.calculateParabolicY = function (start, mid, end, t) {
        // 二次贝塞尔曲线
        var oneMinusT = 1 - t;
        return oneMinusT * oneMinusT * start.y + 2 * oneMinusT * t * mid.y + t * t * end.y;
    };
    /** * 检查碰撞 */
    Bullet.prototype.checkCollision = function () {
        if (!this._config.collision)
            return;
        // 简化的碰撞检测
        var bulletPos = this._node.position;
        var collisionRadius = this._config.collision.radius;
        // 这里应该使用物理系统或碰撞检测系统
        // 现在简化为距离检测
        if (this._target && !this._target.isDead) {
            var targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
            var distance = cc.Vec3.distance(bulletPos, targetPos);
            if (distance <= collisionRadius) {
                this.onHitTarget();
            }
        }
    };
    /*** 命中目标*/
    Bullet.prototype.onHitTarget = function () {
        if (!this._target) {
            return;
        }
        // 使用hit方法处理命中逻辑
        this.hit(this._target);
    };
    /** * 计算伤害 */
    Bullet.prototype.calculateDamage = function () {
        // 简化的伤害计算，使用施法者的攻击力
        var baseDamage = this._caster.attributes.attack;
        // 这里可以添加更复杂的伤害计算逻辑
        return Math.floor(baseDamage);
    };
    Object.defineProperty(Bullet.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    return Bullet;
}());
exports.Bullet = Bullet;
/*** 子弹发射器实现类*/
var BulletLauncher = /** @class */ (function () {
    function BulletLauncher(id, caster, bulletConfig) {
        this._firePosition = cc.Vec3.ZERO;
        this._fireDirection = cc.Vec3.ZERO;
        this._fireAngle = 0;
        this._fireSpeed = 400;
        this._id = id;
        this._caster = caster;
        this._bulletConfig = bulletConfig;
        this._eventManager = EventManager_1.EventManager.createLocal("BulletLauncher_" + id);
    }
    Object.defineProperty(BulletLauncher.prototype, "id", {
        // 实现IBulletLauncher接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "bulletConfig", {
        get: function () { return this._bulletConfig; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "firePosition", {
        get: function () { return this._firePosition; },
        set: function (value) { this._firePosition = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireDirection", {
        get: function () { return this._fireDirection; },
        set: function (value) { this._fireDirection = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireAngle", {
        get: function () { return this._fireAngle; },
        set: function (value) { this._fireAngle = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "fireSpeed", {
        get: function () { return this._fireSpeed; },
        set: function (value) { this._fireSpeed = value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BulletLauncher.prototype, "bulletTimesp", {
        get: function () { return Date.now() + "_" + Math.random().toString(36).substring(2, 11); },
        enumerable: false,
        configurable: true
    });
    /*** 发射单个子弹*/
    BulletLauncher.prototype.fire = function (target, targetPosition) {
        if (!target && !targetPosition) {
            console.warn("BulletLauncher: No target or target position provided");
            return null;
        }
        var bulletId = this._id + "_bullet_" + this.bulletTimesp;
        var bullet = new Bullet(bulletId, this._bulletConfig, this._caster, this._firePosition, target, targetPosition);
        console.log("BulletLauncher " + this._id + " fired bullet " + bulletId);
        this._eventManager.emit(FightEvent_1.default.bulletFired, { launcher: this, bullet: bullet, target: target, targetPosition: targetPosition });
        return bullet;
    };
    /*** 发射多个子弹（散射）*/
    BulletLauncher.prototype.fireBurst = function (count, spread, target, targetPosition) {
        var bullets = [];
        if (count <= 0)
            return bullets;
        // 计算散射角度
        var startAngle = -spread / 2;
        var angleStep = count > 1 ? spread / (count - 1) : 0;
        for (var i = 0; i < count; i++) {
            var angle = startAngle + angleStep * i;
            // 计算散射后的目标位置
            var burstTargetPos = void 0;
            if (targetPosition) {
                burstTargetPos = this.calculateBurstPosition(targetPosition, angle);
            }
            else if (target) {
                var originalPos = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
                burstTargetPos = this.calculateBurstPosition(originalPos, angle);
            }
            else {
                continue;
            }
            var bullet = this.fire(undefined, burstTargetPos);
            if (bullet) {
                bullets.push(bullet);
            }
        }
        console.log("BulletLauncher " + this._id + " fired burst of " + bullets.length + " bullets");
        return bullets;
    };
    /** * 设置发射参数 */
    BulletLauncher.prototype.setFireParams = function (position, direction, speed) {
        this._firePosition = position;
        this._fireDirection = direction.normalize();
        if (speed !== undefined) {
            this._fireSpeed = speed;
        }
        // 计算角度
        this._fireAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
    };
    /** * 计算散射位置 */
    BulletLauncher.prototype.calculateBurstPosition = function (originalPos, angleOffset) {
        var distance = cc.Vec3.distance(this._firePosition, originalPos);
        var originalAngle = Math.atan2(originalPos.y - this._firePosition.y, originalPos.x - this._firePosition.x);
        var newAngle = originalAngle + angleOffset * Math.PI / 180;
        return cc.v3(this._firePosition.x + Math.cos(newAngle) * distance, this._firePosition.y + Math.sin(newAngle) * distance, originalPos.z);
    };
    /** * 更新子弹配置 */
    BulletLauncher.prototype.updateBulletConfig = function (config) {
        this._bulletConfig = __assign(__assign({}, this._bulletConfig), config);
    };
    Object.defineProperty(BulletLauncher.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /**  * 清理资源  */
    BulletLauncher.prototype.cleanup = function () {
        this._eventManager.cleanup();
    };
    return BulletLauncher;
}());
exports.BulletLauncher = BulletLauncher;

cc._RF.pop();