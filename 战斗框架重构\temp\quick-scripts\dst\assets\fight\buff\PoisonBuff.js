
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/buff/PoisonBuff.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'abe2awry7lKwa5ERVnLiXT+', 'PoisonBuff');
// fight/buff/PoisonBuff.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoisonBuff = void 0;
var EventManager_1 = require("../systems/EventManager");
var FightEvent_1 = require("../types/FightEvent");
var IBuff_1 = require("../types/IBuff");
var ICharacterAttributes_1 = require("../types/ICharacterAttributes");
var AttributeModifier_1 = require("../characters/AttributeModifier");
var Buff_1 = require("../types/Buff");
/**
 * 毒素Debuff
 * 持续造成毒素伤害并降低目标的生命恢复效果
 */
var PoisonBuff = /** @class */ (function () {
    function PoisonBuff(caster, target, duration, damagePerSecond) {
        this._name = "中毒";
        this._description = "持续受到毒素伤害，生命恢复效果降低";
        this._type = IBuff_1.BuffType.DEBUFF;
        this._stackCount = 1;
        this._maxStack = 10;
        this._isExpired = false;
        this._attributeModifiers = [];
        this._damageInterval = 1.0; // 每秒触发一次
        this._lastDamageTime = 0;
        this._healingReduction = 0.5; // 治疗效果减少50%
        // 视觉效果
        this._iconPath = "icons/buffs/poison";
        this._effectPrefabPath = "prefabs/effects/PoisonAura";
        this._id = "poison_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._damagePerSecond = damagePerSecond;
        this._eventManager = EventManager_1.EventManager.createLocal("buff_" + this._id);
        this._description = "\u6BCF\u79D2\u53D7\u5230" + damagePerSecond + "\u70B9\u6BD2\u7D20\u4F24\u5BB3\uFF0C\u6CBB\u7597\u6548\u679C\u964D\u4F4E" + Math.round(this._healingReduction * 100) + "%\uFF0C\u6301\u7EED" + duration + "\u79D2";
        // 创建属性修改器
        this.createAttributeModifiers();
    }
    Object.defineProperty(PoisonBuff.prototype, "id", {
        // 实现IBuff接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "duration", {
        get: function () { return this._duration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "maxStack", {
        get: function () { return this._maxStack; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "caster", {
        get: function () { return this._caster; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "target", {
        get: function () { return this._target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "isExpired", {
        get: function () { return this._isExpired || this._remainingTime <= 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "attributeModifiers", {
        get: function () { return this._attributeModifiers; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "iconPath", {
        get: function () { return this._iconPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "effectPrefabPath", {
        get: function () { return this._effectPrefabPath; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "remainingTime", {
        get: function () { return this._remainingTime; },
        set: function (value) { this._remainingTime = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "stackCount", {
        get: function () { return this._stackCount; },
        set: function (value) {
            var oldStack = this._stackCount;
            this._stackCount = Math.max(0, Math.min(value, this._maxStack));
            if (oldStack !== this._stackCount) {
                this.updateAttributeModifiers();
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PoisonBuff.prototype, "periodicEffect", {
        get: function () {
            return {
                interval: this._damageInterval,
                effectType: IBuff_1.BuffPeriodicEffectType.DAMAGE_OVER_TIME,
                value: this._damagePerSecond,
                stackable: true
            };
        },
        enumerable: false,
        configurable: true
    });
    /** 创建属性修改器 */
    PoisonBuff.prototype.createAttributeModifiers = function () {
        // 生命恢复效果减少修改器
        var healingReductionModifier = new AttributeModifier_1.AttributeModifier(this._id + "_healing_reduction", "毒素治疗减少", "healingReceived", ICharacterAttributes_1.AttributeModifierType.PERCENTAGE, -this._healingReduction * this._stackCount, this._duration);
        this._attributeModifiers = [healingReductionModifier];
    };
    /** 更新属性修改器 */
    PoisonBuff.prototype.updateAttributeModifiers = function () {
        var e_1, _a;
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                var attributeModifier = modifier;
                if (attributeModifier.attributeName === "healingReceived") {
                    attributeModifier.setValue(-this._healingReduction * this._stackCount);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    /** Buff被添加时触发 */
    PoisonBuff.prototype.onApply = function () {
        var e_2, _a;
        console.log(this._name + " applied to " + this._target.characterName + " (Stack: " + this._stackCount + ")");
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.addModifier(modifier);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        this._eventManager.emit(FightEvent_1.default.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent_1.default.characterPoisoned, { target: this._target, caster: this._caster });
    };
    /** Buff每帧更新时触发 */
    PoisonBuff.prototype.onTick = function (deltaTime) {
        var e_3, _a;
        this._lastDamageTime += deltaTime;
        // 检查是否到了造成伤害的时间
        if (this._lastDamageTime >= this._damageInterval) {
            this.dealPoisonDamage();
            this._lastDamageTime = 0;
        }
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.update(deltaTime);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    /** Buff被移除时触发 */
    PoisonBuff.prototype.onRemove = function () {
        var e_4, _a;
        console.log(this._name + " removed from " + this._target.characterName);
        try {
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                this._target.attributes.removeModifier(modifier.id);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent_1.default.buffRemoved, { buff: this, target: this._target });
    };
    /** 造成毒素伤害 */
    PoisonBuff.prototype.dealPoisonDamage = function () {
        if (this._target && !this._target.isDead) {
            var totalDamage = this._damagePerSecond * this._stackCount;
            // 直接造成毒素伤害
            this._target.takeDamage(totalDamage, this._caster);
            console.log(this._target.characterName + " takes " + totalDamage + " poison damage from " + this._name + " (Stack: " + this._stackCount + ")");
            this.playDamageEffect();
            // 触发毒素伤害事件
            this._eventManager.emit(FightEvent_1.default.poisonDamageDealt, {
                target: this._target,
                caster: this._caster,
                damage: totalDamage,
                source: this
            });
        }
    };
    /** 更新Buff状态 */
    PoisonBuff.prototype.update = function (deltaTime) {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    };
    /** 刷新Buff持续时间 */
    PoisonBuff.prototype.refresh = function () {
        var e_5, _a;
        this._remainingTime = this._duration;
        this._lastDamageTime = 0;
        try {
            // 刷新属性修改器时间
            for (var _b = __values(this._attributeModifiers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var modifier = _c.value;
                modifier.remainingTime = this._duration;
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        console.log(this._name + " refreshed on " + this._target.characterName);
    };
    /** 增加叠加层数 */
    PoisonBuff.prototype.addStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.min(this._stackCount + count, this._maxStack);
        if (this._stackCount > oldStack) {
            console.log(this._name + " stack increased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 减少叠加层数 */
    PoisonBuff.prototype.removeStack = function (count) {
        if (count === void 0) { count = 1; }
        var oldStack = this._stackCount;
        this._stackCount = Math.max(this._stackCount - count, 0);
        if (this._stackCount < oldStack) {
            console.log(this._name + " stack decreased to " + this._stackCount + " on " + this._target.characterName);
            this._eventManager.emit(FightEvent_1.default.buffStackChanged, { buff: this, target: this._target });
        }
    };
    /** 获取Buff的当前效果值 */
    PoisonBuff.prototype.getEffectValue = function (effectType) {
        switch (effectType) {
            case Buff_1.EBuffEffectType.damage_per_second:
                return this._damagePerSecond * this._stackCount;
            case Buff_1.EBuffEffectType.total_damage_remaining:
                return this._damagePerSecond * this._stackCount * this._remainingTime;
            case Buff_1.EBuffEffectType.healing_reduction:
                return this._healingReduction * this._stackCount;
            default:
                return 0;
        }
    };
    /** 检查Buff是否与另一个Buff冲突 */
    PoisonBuff.prototype.conflictsWith = function (_otherBuff) {
        // 毒素buff可以与其他毒素叠加，不冲突
        return false;
    };
    /** 检查是否可以被净化 */
    PoisonBuff.prototype.canBeDispelled = function () {
        return true; // 毒素可以被净化技能移除
    };
    /** 播放应用特效 */
    PoisonBuff.prototype.playApplyEffect = function () {
        console.log("Playing poison effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的中毒特效播放逻辑
    };
    /** 播放伤害特效 */
    PoisonBuff.prototype.playDamageEffect = function () {
        console.log("Playing poison damage effect on " + this._target.characterName);
        // 这里应该实现毒素伤害的视觉特效
    };
    /** 停止特效 */
    PoisonBuff.prototype.stopEffect = function () {
        console.log("Stopping poison effect for " + this._name + " on " + this._target.characterName);
        // 这里应该实现实际的特效停止逻辑
    };
    /** 获取调试信息 */
    PoisonBuff.prototype.getDebugInfo = function () {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            damagePerSecond: this._damagePerSecond,
            totalDamageRemaining: this.getEffectValue(Buff_1.EBuffEffectType.total_damage_remaining),
            healingReduction: this.getEffectValue(Buff_1.EBuffEffectType.healing_reduction),
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    };
    return PoisonBuff;
}());
exports.PoisonBuff = PoisonBuff;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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