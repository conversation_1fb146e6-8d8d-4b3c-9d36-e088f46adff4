"use strict";
cc._RF.push(module, '12f73yZztdEmapoujvGAGoA', 'BattleManager');
// fight/systems/BattleManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattleManager = void 0;
var TimelineManager_1 = require("./TimelineManager");
var BulletManager_1 = require("./BulletManager");
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
var CharacterTypes_1 = require("../types/CharacterTypes");
var DamageManager_1 = require("./DamageManager");
/**
 * 战斗管理器 - 统一管理战斗流程
 * 负责协调各个系统的工作
 */
var BattleManager = /** @class */ (function () {
    function BattleManager() {
        this._isInBattle = false;
        this._battleDuration = 0;
        this._participants = new Set();
        this._battleId = "";
        this._timelineManager = new TimelineManager_1.TimelineManager;
        this._bulletManager = new BulletManager_1.BulletManager;
        this._damgeManager = new DamageManager_1.DamageManager;
        this._eventManager = EventManager_1.EventManager.getGlobal();
        this.setupEventListeners();
        console.log("BattleManager using global EventManager:", this._eventManager.instanceId);
    }
    Object.defineProperty(BattleManager, "instance", {
        get: function () {
            if (this._instance == null) {
                this._instance = new BattleManager;
            }
            return this._instance;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "isInBattle", {
        /** 获取战斗状态 */
        get: function () { return this._isInBattle; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "battleDuration", {
        /** 获取战斗时长 */
        get: function () { return this._battleDuration; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "participants", {
        /** 获取参战者列表 */
        get: function () { return Array.from(this._participants); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "timelineManager", {
        get: function () { return this._timelineManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "bulletManager", {
        get: function () { return this._bulletManager; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BattleManager.prototype, "damageManager", {
        get: function () { return this._damgeManager; },
        enumerable: false,
        configurable: true
    });
    /** 开始战斗 */
    BattleManager.prototype.startBattle = function (battleId, participants) {
        var e_1, _a;
        if (this._isInBattle) {
            console.warn("Battle is already in progress");
            return;
        }
        this._battleId = battleId;
        this._isInBattle = true;
        this._battleDuration = 0;
        this._participants.clear();
        try {
            // 添加参战者
            for (var participants_1 = __values(participants), participants_1_1 = participants_1.next(); !participants_1_1.done; participants_1_1 = participants_1.next()) {
                var participant = participants_1_1.value;
                this._participants.add(participant);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (participants_1_1 && !participants_1_1.done && (_a = participants_1.return)) _a.call(participants_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // 清理之前的Timeline
        this._timelineManager.clearAll();
        console.log("Battle " + battleId + " started with " + participants.length + " participants");
        // 发送全局战斗开始事件
        EventManager_1.EventManager.emit(FightEvent_1.default.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
        // 发送局部事件（用于内部管理）
        this._eventManager.emit(FightEvent_1.default.battleStarted, {
            battleId: this._battleId,
            participants: Array.from(this._participants)
        });
    };
    /** 结束战斗 */
    BattleManager.prototype.endBattle = function (reason) {
        if (reason === void 0) { reason = "normal"; }
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        // 暂停所有Timeline
        this._timelineManager.pauseAll();
        console.log("Battle " + this._battleId + " ended. Reason: " + reason + ", Duration: " + this._battleDuration + "s");
        // 发送全局战斗结束事件
        EventManager_1.EventManager.emit(FightEvent_1.default.battleEnded, {
            battleId: this._battleId,
            reason: reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 发送局部事件
        this._eventManager.emit(FightEvent_1.default.battleEnded, {
            battleId: this._battleId,
            reason: reason,
            duration: this._battleDuration,
            participants: Array.from(this._participants)
        });
        // 重置状态
        this._isInBattle = false;
        this._battleId = "";
        this._participants.clear();
        // 清理所有Timeline和子弹
        this._timelineManager.clearAll();
        this._bulletManager.clearAllBullets();
    };
    /** 暂停战斗 */
    BattleManager.prototype.pauseBattle = function () {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.pauseAll();
        console.log("Battle " + this._battleId + " paused");
        this._eventManager.emit(FightEvent_1.default.battlePaused, { battleId: this._battleId });
    };
    /** 恢复战斗 */
    BattleManager.prototype.resumeBattle = function () {
        if (!this._isInBattle) {
            console.warn("No battle in progress");
            return;
        }
        this._timelineManager.resumeAll();
        console.log("Battle " + this._battleId + " resumed");
        this._eventManager.emit(FightEvent_1.default.battleResumed, { battleId: this._battleId });
    };
    /** 添加参战者 */
    BattleManager.prototype.addParticipant = function (character) {
        this._participants.add(character);
        console.log(character.characterName + " joined the battle");
        this._eventManager.emit(FightEvent_1.default.participantAdded, { battleId: this._battleId, character: character });
    };
    /** 移除参战者 */
    BattleManager.prototype.removeParticipant = function (character) {
        if (this._participants.delete(character)) {
            // 移除该角色相关的所有Timeline
            this._timelineManager.removeTimelinesByCaster(character);
            console.log(character.characterName + " left the battle");
            this._eventManager.emit(FightEvent_1.default.participantRemoved, { battleId: this._battleId, character: character });
            // 检查战斗是否应该结束
            this.checkBattleEnd();
        }
    };
    /** 更新战斗状态 */
    BattleManager.prototype.update = function (deltaTime) {
        var e_2, _a;
        if (!this._isInBattle) {
            return;
        }
        this._battleDuration += deltaTime;
        this._timelineManager.update(deltaTime);
        this._bulletManager.update(deltaTime);
        try {
            // 更新参战者状态
            for (var _b = __values(this._participants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var participant = _c.value;
                if (participant.update) {
                    participant.update(deltaTime);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        this.checkBattleEnd();
    };
    /** 检查战斗是否应该结束 */
    BattleManager.prototype.checkBattleEnd = function () {
        if (!this._isInBattle) {
            return;
        }
        // 检查是否有存活的敌对双方
        var alivePlayers = Array.from(this._participants).filter(function (p) { return !p.isDead && p.role === CharacterTypes_1.CharacterRole.HERO; });
        var aliveEnemies = Array.from(this._participants).filter(function (p) { return !p.isDead && p.role === CharacterTypes_1.CharacterRole.ENEMY; });
        if (alivePlayers.length === 0) {
            this.endBattle("players_defeated");
        }
        else if (aliveEnemies.length === 0) {
            this.endBattle("enemies_defeated");
        }
    };
    /** 设置事件监听器 */
    BattleManager.prototype.setupEventListeners = function () {
        var _this = this;
        // 监听角色死亡事件
        this._eventManager.on(FightEvent_1.default.characterDied, function (event) {
            var character = event.character;
            console.log(character.characterName + " died in battle");
            // 移除死亡角色的Timeline
            _this._timelineManager.removeTimelinesByCaster(character);
            // 检查战斗是否结束
            _this.checkBattleEnd();
        });
        // 监听Timeline事件
        this._eventManager.on(FightEvent_1.default.timelineCompleted, function () {
            console.log("Timeline completed in battle " + _this._battleId);
        });
    };
    /** 获取战斗统计信息 */
    BattleManager.prototype.getBattleStats = function () {
        return {
            battleId: this._battleId,
            isInBattle: this._isInBattle,
            duration: this._battleDuration,
            participantCount: this._participants.size,
            timelineStats: this._timelineManager.getStats(),
            bulletStats: this._bulletManager.getStats(),
            participants: Array.from(this._participants).map(function (p) { return ({
                characterName: p.characterName,
                role: p.role,
                isDead: p.isDead,
                hp: p.attributes.currentHp || 0,
                maxHp: p.attributes.maxHp || 0
            }); })
        };
    };
    /** 打印调试信息 */
    BattleManager.prototype.printDebugInfo = function () {
        console.log("BattleManager Debug Info:", this.getBattleStats());
        this._timelineManager.printDebugInfo();
    };
    Object.defineProperty(BattleManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    BattleManager.prototype.cleanup = function () {
        this.endBattle("cleanup");
        this._eventManager.cleanup();
        this._timelineManager.cleanup();
        this._bulletManager.cleanup();
    };
    return BattleManager;
}());
exports.BattleManager = BattleManager;

cc._RF.pop();