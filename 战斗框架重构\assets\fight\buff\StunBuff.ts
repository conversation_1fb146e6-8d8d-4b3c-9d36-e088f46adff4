import { EventManager } from "../systems/EventManager";
import FightEvent from "../types/FightEvent";
import { IBuff, BuffType } from "../types/IBuff";
import { ICharacter } from "../types/ICharacter";
import { IAttributeModifier, AttributeModifierType } from "../types/ICharacterAttributes";
import { ISkill } from "../types/ISkill";
import { ITimeline } from "../types/ITimeline";
import { AttributeModifier } from "../characters/AttributeModifier";
import { EBuffEffectType } from "../types/Buff";

/**
 * 眩晕Debuff
 * 使目标无法行动，无法释放技能和移动
 */
export class StunBuff implements IBuff {
    private _id: string;
    private _name: string = "眩晕";
    private _description: string = "无法行动、释放技能或移动";
    private _type: BuffType = BuffType.DEBUFF;
    private _duration: number;
    private _remainingTime: number;
    private _stackCount: number = 1;
    private _maxStack: number = 1; // 眩晕不可叠加
    private _caster: ICharacter;
    private _target: ICharacter;
    private _isExpired: boolean = false;
    private _attributeModifiers: IAttributeModifier[] = [];
    private _eventManager: EventManager;
    // 眩晕配置
    private _preventMovement: boolean = true;
    private _preventSkills: boolean = true;
    private _preventBasicAttack: boolean = true;
    // 视觉效果
    private _iconPath: string = "icons/buffs/stun";
    private _effectPrefabPath: string = "prefabs/effects/StunEffect";

    constructor(caster: ICharacter, target: ICharacter, duration: number) {
        this._id = `stun_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        this._caster = caster;
        this._target = target;
        this._duration = duration;
        this._remainingTime = duration;
        this._eventManager = EventManager.createLocal(`buff_${this._id}`);
        this._description = `无法行动，持续${duration}秒`;
        // 创建属性修改器（降低移动速度到0）
        this.createAttributeModifiers();
    }

    // 实现IBuff接口
    get id(): string { return this._id; }
    get name(): string { return this._name; }
    get description(): string { return this._description; }
    get type(): BuffType { return this._type; }
    get duration(): number { return this._duration; }
    get remainingTime(): number { return this._remainingTime; }
    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }
    get stackCount(): number { return this._stackCount; }
    set stackCount(_value: number) { this._stackCount = 1; } // 眩晕不可叠加
    get maxStack(): number { return this._maxStack; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter { return this._target; }
    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }
    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }
    get iconPath(): string { return this._iconPath; }
    get effectPrefabPath(): string { return this._effectPrefabPath; }

    /** 创建属性修改器 */
    private createAttributeModifiers(): void {
        // 移动速度修改器（设为0）
        const moveSpeedModifier = new AttributeModifier(
            `${this._id}_move_speed`,
            "眩晕移动限制",
            "moveSpeed",
            AttributeModifierType.OVERRIDE,
            0,
            this._duration
        );
        this._attributeModifiers = [moveSpeedModifier];
    }

    /** Buff被添加时触发 */
    onApply(): void {
        console.log(`${this._name} applied to ${this._target.characterName} for ${this._duration} seconds`);
        // 应用属性修改器到目标
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.addModifier(modifier);
        }
        // 播放眩晕特效
        if (this._effectPrefabPath) {
            this.playApplyEffect();
        }
        // 中断目标当前的行动
        this.interruptCurrentActions();
        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent.characterStunned, { target: this._target, caster: this._caster, duration: this._duration });
    }

    /** Buff每帧更新时触发 */
    onTick(deltaTime: number): void {
        for (const modifier of this._attributeModifiers) {
            modifier.update(deltaTime);
        }
        // 持续检查并阻止行动
        this.preventActions();
    }

    /** Buff被移除时触发 */
    onRemove(): void {
        console.log(`${this._name} removed from ${this._target.characterName}`);
        // 移除属性修改器
        for (const modifier of this._attributeModifiers) {
            this._target.attributes.removeModifier(modifier.id);
        }
        this.stopEffect();
        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });
        this._eventManager.emit(FightEvent.characterStunEnded, { target: this._target });
    }

    /** 释放技能时触发 - 阻止技能释放 */
    onSkillCast?(skill: ISkill, timeline: ITimeline): ITimeline {
        if (this._preventSkills) {
            console.log(`${this._target.characterName} cannot cast ${skill.name} while stunned`);
            // 返回null或空timeline来阻止技能释放
            return null
        }
        return timeline;
    }

    /** 中断当前行动 */
    private interruptCurrentActions(): void {
        // 中断当前的移动
        if (this._target.node) {
            this._target.node.stopAllActions();
        }
        // 中断当前的技能释放（如果有的话）
        // 这里需要与SkillManager配合
        console.log(`Interrupting all actions for ${this._target.characterName}`);
    }

    /** 阻止行动 */
    private preventActions(): void {
        // 这里可以添加持续的行动阻止逻辑
        // 比如重置移动输入、阻止技能队列等
    }

    /** 更新Buff状态 */
    update(deltaTime: number): boolean {
        if (this._isExpired) {
            return true;
        }
        this._remainingTime -= deltaTime;
        this.onTick(deltaTime);
        if (this._remainingTime <= 0) {
            this._isExpired = true;
            return true;
        }
        return false;
    }

    /** 刷新Buff持续时间 */
    refresh(): void {
        this._remainingTime = this._duration;
        for (const modifier of this._attributeModifiers) {
            modifier.remainingTime = this._duration;
        }
        console.log(`${this._name} refreshed on ${this._target.characterName}`);
    }

    /** 增加叠加层数 - 眩晕不可叠加，但可以刷新时间 */
    addStack(_count: number = 1): void {
        // 眩晕不叠加层数，但刷新持续时间
        this.refresh();
        console.log(`${this._name} duration refreshed on ${this._target.characterName}`);
    }

    /** 减少叠加层数 - 眩晕直接移除 */
    removeStack(_count: number = 1): void {
        this._stackCount = 0;
        this._isExpired = true;
        console.log(`${this._name} removed from ${this._target.characterName}`);
    }

    /** 获取Buff的当前效果值 */
    getEffectValue(effectType: string): number {
        switch (effectType) {
            case EBuffEffectType.movement_blocked:
                return this._preventMovement ? 1 : 0;
            case EBuffEffectType.skills_blocked:
                return this._preventSkills ? 1 : 0;
            case EBuffEffectType.basic_attack_blocked:
                return this._preventBasicAttack ? 1 : 0;
            default:
                return 0;
        }
    }

    /** 检查Buff是否与另一个Buff冲突 */
    conflictsWith(otherBuff: IBuff): boolean {
        // 眩晕与其他控制类debuff可能冲突
        if (otherBuff.type === BuffType.DEBUFF && otherBuff.name.includes("眩晕")) {
            return true;
        }
        return false;
    }

    /** 检查是否可以被净化 */
    canBeDispelled(): boolean {
        return true; // 眩晕可以被净化技能移除
    }

    /** 播放应用特效 */
    private playApplyEffect(): void {
        console.log(`Playing stun effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的眩晕特效播放逻辑
        // 比如在目标头顶显示眩晕星星特效
    }

    /** 停止特效 */
    private stopEffect(): void {
        console.log(`Stopping stun effect for ${this._name} on ${this._target.characterName}`);
        // 这里应该实现实际的特效停止逻辑
    }

    /** 获取调试信息 */
    getDebugInfo() {
        return {
            id: this._id,
            name: this._name,
            type: this._type,
            duration: this._duration,
            remainingTime: this._remainingTime,
            stackCount: this._stackCount,
            maxStack: this._maxStack,
            preventMovement: this._preventMovement,
            preventSkills: this._preventSkills,
            preventBasicAttack: this._preventBasicAttack,
            caster: this._caster.characterName,
            target: this._target.characterName,
            isExpired: this._isExpired
        };
    }
}
