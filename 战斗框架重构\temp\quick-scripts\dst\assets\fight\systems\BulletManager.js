
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/systems/BulletManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6fca8qVBjpDqqQOlXECl0dj', 'BulletManager');
// fight/systems/BulletManager.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulletManager = void 0;
var EventManager_1 = require("./EventManager");
var FightEvent_1 = require("../types/FightEvent");
var BulletSystem_1 = require("./BulletSystem");
/**
 * 子弹管理器实现
 * 负责管理所有活跃的子弹
 */
var BulletManager = /** @class */ (function () {
    function BulletManager() {
        this._activeBullets = new Map();
        this._totalCreated = 0;
        this._totalDestroyed = 0;
        this._isUpdating = false;
        this._pendingRemovals = [];
        this._eventManager = EventManager_1.EventManager.createLocal("BulletManager");
        this.setupEventListeners();
    }
    Object.defineProperty(BulletManager.prototype, "activeBullets", {
        /** 获取所有活跃的子弹 */
        get: function () {
            return Array.from(this._activeBullets.values());
        },
        enumerable: false,
        configurable: true
    });
    /** 创建子弹 */
    BulletManager.prototype.createBullet = function (config, caster, target, targetPosition) {
        var bulletId = "bullet_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var firePosition = caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var bullet = new BulletSystem_1.Bullet(bulletId, config, caster, firePosition, target, targetPosition);
        this.addBullet(bullet);
        return bullet;
    };
    /** 添加子弹到管理器 */
    BulletManager.prototype.addBullet = function (bullet) {
        if (this._activeBullets.has(bullet.id)) {
            console.warn("Bullet " + bullet.id + " already exists in manager");
            return;
        }
        this._activeBullets.set(bullet.id, bullet);
        this._totalCreated++;
        console.log("Bullet " + bullet.id + " added to manager");
        this._eventManager.emit(FightEvent_1.default.bulletFired, { bullet: bullet });
    };
    /** 移除子弹 */
    BulletManager.prototype.removeBullet = function (bulletId) {
        if (this._isUpdating) {
            // 如果正在更新中，延迟移除
            this._pendingRemovals.push(bulletId);
            return;
        }
        this._internalRemoveBullet(bulletId, true);
    };
    /** 内部移除子弹方法 - 避免事件循环 */
    BulletManager.prototype._internalRemoveBullet = function (bulletId, shouldEmitEvent) {
        if (shouldEmitEvent === void 0) { shouldEmitEvent = false; }
        var bullet = this._activeBullets.get(bulletId);
        if (bullet) {
            // 确保子弹被销毁，但不重复销毁
            if (!bullet.isDestroyed) {
                bullet.destroy();
            }
            this._activeBullets.delete(bulletId);
            this._totalDestroyed++;
            console.log("Bullet " + bulletId + " removed from manager");
            // 只有在外部调用时才发射事件，避免循环
            if (shouldEmitEvent) {
                this._eventManager.emit(FightEvent_1.default.bulletDestroyed, { bullet: bullet });
            }
        }
    };
    /** 根据施法者移除子弹 */
    BulletManager.prototype.removeBulletsByCaster = function (caster) {
        var e_1, _a, e_2, _b;
        var bulletsToRemove = [];
        try {
            for (var _c = __values(this._activeBullets), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], bullet = _e[1];
                if (bullet.caster === caster) {
                    bulletsToRemove.push(id);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var bulletsToRemove_1 = __values(bulletsToRemove), bulletsToRemove_1_1 = bulletsToRemove_1.next(); !bulletsToRemove_1_1.done; bulletsToRemove_1_1 = bulletsToRemove_1.next()) {
                var id = bulletsToRemove_1_1.value;
                this.removeBullet(id);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (bulletsToRemove_1_1 && !bulletsToRemove_1_1.done && (_b = bulletsToRemove_1.return)) _b.call(bulletsToRemove_1);
            }
            finally { if (e_2) throw e_2.error; }
        }
        console.log("Removed " + bulletsToRemove.length + " bullets for caster " + caster.characterName);
    };
    /** 清除所有子弹 */
    BulletManager.prototype.clearAllBullets = function () {
        var e_3, _a;
        var bulletIds = Array.from(this._activeBullets.keys());
        try {
            for (var bulletIds_1 = __values(bulletIds), bulletIds_1_1 = bulletIds_1.next(); !bulletIds_1_1.done; bulletIds_1_1 = bulletIds_1.next()) {
                var id = bulletIds_1_1.value;
                this.removeBullet(id);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (bulletIds_1_1 && !bulletIds_1_1.done && (_a = bulletIds_1.return)) _a.call(bulletIds_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        console.log("Cleared all bullets (" + bulletIds.length + " bullets)");
    };
    /** 更新所有子弹 */
    BulletManager.prototype.update = function (deltaTime) {
        var e_4, _a, e_5, _b, e_6, _c;
        if (this._activeBullets.size === 0) {
            return;
        }
        this._isUpdating = true;
        var destroyedBullets = [];
        try {
            // 更新所有子弹
            for (var _d = __values(this._activeBullets), _e = _d.next(); !_e.done; _e = _d.next()) {
                var _f = __read(_e.value, 2), id = _f[0], bullet = _f[1];
                var isDestroyed = bullet.update(deltaTime);
                if (isDestroyed) {
                    destroyedBullets.push(id);
                }
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_4) throw e_4.error; }
        }
        this._isUpdating = false;
        try {
            // 移除已销毁的子弹 - 使用内部方法避免重复销毁
            for (var destroyedBullets_1 = __values(destroyedBullets), destroyedBullets_1_1 = destroyedBullets_1.next(); !destroyedBullets_1_1.done; destroyedBullets_1_1 = destroyedBullets_1.next()) {
                var id = destroyedBullets_1_1.value;
                this._internalRemoveBullet(id, false);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (destroyedBullets_1_1 && !destroyedBullets_1_1.done && (_b = destroyedBullets_1.return)) _b.call(destroyedBullets_1);
            }
            finally { if (e_5) throw e_5.error; }
        }
        // 处理延迟移除的子弹
        if (this._pendingRemovals.length > 0) {
            try {
                for (var _g = __values(this._pendingRemovals), _h = _g.next(); !_h.done; _h = _g.next()) {
                    var id = _h.value;
                    this._internalRemoveBullet(id, true);
                }
            }
            catch (e_6_1) { e_6 = { error: e_6_1 }; }
            finally {
                try {
                    if (_h && !_h.done && (_c = _g.return)) _c.call(_g);
                }
                finally { if (e_6) throw e_6.error; }
            }
            this._pendingRemovals.length = 0;
        }
    };
    /** 获取子弹统计信息 */
    BulletManager.prototype.getStats = function () {
        return {
            activeCount: this._activeBullets.size,
            totalCreated: this._totalCreated,
            totalDestroyed: this._totalDestroyed
        };
    };
    /** 根据ID获取子弹 */
    BulletManager.prototype.getBullet = function (bulletId) {
        return this._activeBullets.get(bulletId) || null;
    };
    /** 根据施法者获取子弹列表 */
    BulletManager.prototype.getBulletsByCaster = function (caster) {
        var e_7, _a;
        var bullets = [];
        try {
            for (var _b = __values(this._activeBullets.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                var bullet = _c.value;
                if (bullet.caster === caster) {
                    bullets.push(bullet);
                }
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return bullets;
    };
    /** 设置事件监听器 */
    BulletManager.prototype.setupEventListeners = function () {
        // 可以在这里添加全局子弹事件监听
    };
    /** 获取调试信息 */
    BulletManager.prototype.getDebugInfo = function () {
        var e_8, _a;
        var _b;
        var bullets = {};
        try {
            for (var _c = __values(this._activeBullets), _d = _c.next(); !_d.done; _d = _c.next()) {
                var _e = __read(_d.value, 2), id = _e[0], bullet = _e[1];
                bullets[id] = {
                    caster: bullet.caster.characterName,
                    target: ((_b = bullet.target) === null || _b === void 0 ? void 0 : _b.characterName) || null,
                    remainingHits: bullet.remainingHits,
                    hasCollided: bullet.hasCollided,
                    isDestroyed: bullet.isDestroyed
                };
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return {
            stats: this.getStats(),
            bullets: bullets,
            pendingRemovals: this._pendingRemovals.length
        };
    };
    /** 打印调试信息 */
    BulletManager.prototype.printDebugInfo = function () {
        console.log("BulletManager Debug Info:", this.getDebugInfo());
    };
    Object.defineProperty(BulletManager.prototype, "eventManager", {
        /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */
        get: function () {
            return this._eventManager;
        },
        enumerable: false,
        configurable: true
    });
    /** 清理管理器 */
    BulletManager.prototype.cleanup = function () {
        this.clearAllBullets();
        this._eventManager.cleanup();
    };
    return BulletManager;
}());
exports.BulletManager = BulletManager;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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