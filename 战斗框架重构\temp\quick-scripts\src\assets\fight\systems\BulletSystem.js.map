{"version": 3, "sources": ["assets\\fight\\systems\\BulletSystem.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,kDAA6C;AAC7C,4CAA0H;AAE1H,+CAA8C;AAG9C;;;GAGG;AACH;IAeI,gBAAY,EAAU,EAAE,MAAqB,EAAE,MAAkB,EAAE,YAAqB,EAAE,MAAmB,EAAE,cAAwB;QAP/H,iBAAY,GAAW,CAAC,CAAC;QAEzB,iBAAY,GAAY,KAAK,CAAC;QAC9B,iBAAY,GAAY,KAAK,CAAC;QAKlC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,YAAU,EAAI,CAAC,CAAC;QAC9D,SAAS;QACT,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAGD,sBAAI,sBAAE;QADN,cAAc;aACd,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,wBAAI;aAAR,cAAyB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IACpD,sBAAI,wBAAI;aAAR,cAAsB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC1C,sBAAI,0BAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,0BAAM;aAAV,cAAuC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7D,sBAAI,kCAAc;aAAlB,cAA4C,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;OAAA;IAC1E,sBAAI,gCAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAI,mCAAe;aAAnB,cAAiC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAC1F,sBAAI,yBAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAClD,sBAAI,4BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAI,+BAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aACvD,UAAgB,KAAa,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAEvD,sBAAI,iCAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAE3D,sBAAI,2BAAO;aAAX,cAAwB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACtD,sBAAI,+BAAW;aAAf,cAA6B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aACxD,UAAgB,KAAc,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAExD,sBAAI,+BAAW;aAAf,cAA6B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAI,8BAAU;aAAd,cAAsC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAEhE,WAAW;IACX,uBAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,OAAO,IAAI,CAAC,CAAC,MAAM;SACtB;QACD,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC;QAC/B,SAAS;QACT,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;SACf;QACD,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC/B,OAAO;QACP,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACb,oBAAG,GAAH,UAAI,MAAkB;;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;QACD,OAAO;QACP,IAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,YAAU,IAAI,CAAC,GAAG,aAAQ,MAAM,CAAC,aAAa,aAAQ,MAAM,YAAS,CAAC,CAAC;QACnF,SAAS;QACT,UAAI,IAAI,CAAC,OAAO,CAAC,KAAK,0CAAE,QAAQ,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,wBAAsB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAU,CAAC,CAAC;SACpE;QACD,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAChG,WAAW;QACX,IAAI,QAAC,IAAI,CAAC,OAAO,CAAC,SAAS,0CAAE,QAAQ,CAAA,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,aAAa;IACb,0BAAS,GAAT,UAAU,MAAkB;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IACD,eAAe;IACf,kCAAiB,GAAjB,UAAkB,QAAiB;QAC/B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IACpC,CAAC;IACD,eAAe;IACf,wBAAO,GAAP;QACI,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IAED,eAAe;IACP,iCAAgB,GAAxB;QAAA,iBAmBC;QAlBG,iBAAiB;QACjB,OAAO;YACH,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;YAClC,qBAAqB,EAAE,UAAC,OAAgB,EAAE,SAAiB;gBACvD,OAAO,KAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;YACD,iBAAiB,EAAE,UAAC,OAAgB;gBAChC,OAAO,CAAC,CAAC,CAAC,OAAO;YACrB,CAAC;YACD,gBAAgB,EAAE,UAAC,MAAe,EAAE,SAAiB;gBACjD,IAAI,CAAC,KAAI,CAAC,OAAO;oBAAE,OAAO,KAAK,CAAC;gBAChC,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAC7B,MAAM,CAAC,eAAe,EACtB,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CACxD,CAAC;gBACF,OAAO,QAAQ,IAAI,SAAS,CAAC;YACjC,CAAC;SACJ,CAAC;IACN,CAAC;IACD,gBAAgB;IACR,sCAAqB,GAA7B,UAA8B,SAAiB;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QACrC,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACvC,IAAI,SAAkB,CAAC;QACvB,SAAS;QACT,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrE;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE;YAC7B,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;SACpC;aAAM;YACH,OAAO,UAAU,CAAC,CAAC,cAAc;SACpC;QACD,YAAY;QACZ,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;QACpD,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;IAClE,CAAC;IACD,eAAe;IACP,iCAAgB,GAAxB;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAU,IAAI,CAAC,GAAK,CAAC,CAAC;QAC/C,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACnC,aAAa;QACb,qFAAqF;QACrF,oCAAoC;QACpC,4CAA4C;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5E,QAAQ;QACR,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IACD,aAAa;IACL,+BAAc,GAAtB,UAAuB,SAAiB;QACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;YAAE,OAAO;QAC/C,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACvC,IAAI,SAAkB,CAAC;QACvB,SAAS;QACT,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrE;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE;YAC7B,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;SACpC;aAAM;YACH,YAAY;YACZ,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;SACV;QACD,SAAS;QACT,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;QACpD,WAAW;QACX,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;YAClC,KAAK,wBAAc,CAAC,MAAM;gBACtB,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,wBAAc,CAAC,SAAS;gBACzB,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,wBAAc,CAAC,MAAM;gBACtB,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACtC,MAAM;YACV;gBAAS,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBAAC,MAAM;SAC5D;QACD,WAAW;QACX,IAAM,gBAAgB,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC1E,IAAI,gBAAgB,IAAI,EAAE,EAAE,EAAE,YAAY;YACtC,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;IACL,CAAC;IACD,WAAW;IACH,2BAAU,GAAlB,UAAmB,SAAkB,EAAE,QAAgB;QACnD,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;IACjC,CAAC;IACD,sBAAsB;IACd,8BAAa,GAArB,UAAsB,SAAkB,EAAE,UAAkB;QACxD,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC3D,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE,UAAU;QACV,IAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7D,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ;QAC3B,IAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC1D,IAAM,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,aAAa;IACL,2BAAU,GAAlB,UAAmB,SAAkB,EAAE,SAAiB;QACpD,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACvC,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;QAC7D,eAAe;QACf,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC;QACpD,IAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;IACjC,CAAC;IACD,iBAAiB;IACT,oCAAmB,GAA3B,UAA4B,KAAc,EAAE,GAAY,EAAE,GAAY,EAAE,CAAS;QAC7E,UAAU;QACV,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,OAAO,SAAS,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACvF,CAAC;IACD,aAAa;IACL,+BAAc,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS;YAAE,OAAO;QACpC,UAAU;QACV,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QACtC,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;QACtD,oBAAoB;QACpB,YAAY;QACZ,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACxD,IAAI,QAAQ,IAAI,eAAe,EAAE;gBAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;aACtB;SACJ;IACL,CAAC;IACD,WAAW;IACH,4BAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QACD,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IACD,aAAa;IACL,gCAAe,GAAvB;QACI,oBAAoB;QACpB,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QAClD,mBAAmB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED,sBAAI,gCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACL,aAAC;AAAD,CA5QA,AA4QC,IAAA;AA5QY,wBAAM;AA8QnB,eAAe;AACf;IASI,wBAAY,EAAU,EAAE,MAAkB,EAAE,YAA2B;QAL/D,kBAAa,GAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QACtC,mBAAc,GAAY,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QACvC,eAAU,GAAW,CAAC,CAAC;QACvB,eAAU,GAAW,GAAG,CAAC;QAG7B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,oBAAkB,EAAI,CAAC,CAAC;IAC1E,CAAC;IAGD,sBAAI,8BAAE;QADN,sBAAsB;aACtB,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,kCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,wCAAY;aAAhB,cAAoC,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAChE,sBAAI,wCAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;aAC1D,UAAiB,KAAc,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAE1D,sBAAI,yCAAa;aAAjB,cAA+B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC5D,UAAkB,KAAc,IAAI,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAE5D,sBAAI,qCAAS;aAAb,cAA0B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aACnD,UAAc,KAAa,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAEnD,sBAAI,qCAAS;aAAb,cAA0B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aACnD,UAAc,KAAa,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;;;OADN;IAEnD,sBAAI,wCAAY;aAAhB,cAAqB,OAAU,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAA,CAAC,CAAC;;;OAAA;IAC5F,aAAa;IACb,6BAAI,GAAJ,UAAK,MAAmB,EAAE,cAAwB;QAC9C,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE;YAC5B,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;SACf;QACD,IAAM,QAAQ,GAAM,IAAI,CAAC,GAAG,gBAAW,IAAI,CAAC,YAAc,CAAC;QAC3D,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAClH,OAAO,CAAC,GAAG,CAAC,oBAAkB,IAAI,CAAC,GAAG,sBAAiB,QAAU,CAAC,CAAC;QACnE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,CAAC,CAAC;QACpI,OAAO,MAAM,CAAC;IAClB,CAAC;IACD,iBAAiB;IACjB,kCAAS,GAAT,UAAU,KAAa,EAAE,MAAc,EAAE,MAAmB,EAAE,cAAwB;QAClF,IAAM,OAAO,GAAc,EAAE,CAAC;QAC9B,IAAI,KAAK,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAC/B,SAAS;QACT,IAAM,UAAU,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAM,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAM,KAAK,GAAG,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC;YACzC,aAAa;YACb,IAAI,cAAc,SAAS,CAAC;YAC5B,IAAI,cAAc,EAAE;gBAChB,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;aACvE;iBAAM,IAAI,MAAM,EAAE;gBACf,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpE,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aACpE;iBAAM;gBACH,SAAS;aACZ;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACpD,IAAI,MAAM,EAAE;gBACR,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACxB;SACJ;QACD,OAAO,CAAC,GAAG,CAAC,oBAAkB,IAAI,CAAC,GAAG,wBAAmB,OAAO,CAAC,MAAM,aAAU,CAAC,CAAC;QACnF,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,eAAe;IACf,sCAAa,GAAb,UAAc,QAAiB,EAAE,SAAkB,EAAE,KAAc;QAC/D,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QAC5C,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SAC3B;QACD,OAAO;QACP,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;IAC3E,CAAC;IACD,eAAe;IACP,+CAAsB,GAA9B,UAA+B,WAAoB,EAAE,WAAmB;QACpE,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACnE,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC7G,IAAM,QAAQ,GAAG,aAAa,GAAG,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAC7D,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IAC5I,CAAC;IACD,eAAe;IACf,2CAAkB,GAAlB,UAAmB,MAA8B;QAC7C,IAAI,CAAC,aAAa,yBAAQ,IAAI,CAAC,aAAa,GAAK,MAAM,CAAE,CAAC;IAC9D,CAAC;IAED,sBAAI,wCAAY;QADhB,gCAAgC;aAChC;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,eAAe;IACf,gCAAO,GAAP;QACI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACL,qBAAC;AAAD,CAnGA,AAmGC,IAAA;AAnGY,wCAAc", "file": "", "sourceRoot": "/", "sourcesContent": ["import FightEvent from \"../types/FightEvent\";\nimport { IBullet, IBulletConfig, IBulletTrajectory, BulletType, TrajectoryType, IBulletLauncher } from \"../types/IBullet\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { EventManager } from \"./EventManager\";\n\n\n/**\n * 子弹系统实现\n * 提供基础的子弹发射和管理功能\n */\nexport class Bullet implements IBullet {\n    private _id: string;\n    private _config: IBulletConfig;\n    private _caster: ICharacter;\n    private _target?: ICharacter;\n    private _targetPosition?: cc.Vec3;\n    private _node: cc.Node;\n    private _firePosition: cc.Vec3;\n    private _timeElapsed: number = 0;\n    private _remainingHits: number;\n    private _hasCollided: boolean = false;\n    private _isDestroyed: boolean = false;\n    private _trajectory: IBulletTrajectory;\n    private _eventManager: EventManager;\n\n    constructor(id: string, config: IBulletConfig, caster: <PERSON>Character, firePosition: cc.Vec3, target?: ICharacter, targetPosition?: cc.Vec3) {\n        this._id = id;\n        this._config = config;\n        this._caster = caster;\n        this._target = target;\n        this._targetPosition = targetPosition;\n        this._firePosition = firePosition;\n        this._remainingHits = config.maxHits;\n        this._eventManager = EventManager.createLocal(`Bullet_${id}`);\n        // 创建轨迹实例\n        this._trajectory = this.createTrajectory();\n        this.createBulletNode();\n    }\n\n    // 实现IBullet接口\n    get id(): string { return this._id; }\n    get type(): BulletType { return this._config.type; }\n    get node(): cc.Node { return this._node; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter | undefined { return this._target; }\n    get targetPosition(): cc.Vec3 | undefined { return this._targetPosition; }\n    get firePosition(): cc.Vec3 { return this._firePosition; }\n    get currentPosition(): cc.Vec3 { return this._node ? this._node.position : cc.Vec3.ZERO; }\n    get speed(): number { return this._config.speed; }\n    get lifeTime(): number { return this._config.lifeTime; }\n    get timeElapsed(): number { return this._timeElapsed; }\n    set timeElapsed(value: number) { this._timeElapsed = value; }\n    get remainingHits(): number { return this._remainingHits; }\n    set remainingHits(value: number) { this._remainingHits = value; }\n    get maxHits(): number { return this._config.maxHits; }\n    get hasCollided(): boolean { return this._hasCollided; }\n    set hasCollided(value: boolean) { this._hasCollided = value; }\n    get isDestroyed(): boolean { return this._isDestroyed; }\n    get trajectory(): IBulletTrajectory { return this._trajectory; }\n\n    /*** 更新子弹*/\n    update(deltaTime: number): boolean {\n        if (this._isDestroyed) {\n            return true; // 已销毁\n        }\n        this._timeElapsed += deltaTime;\n        // 检查生命周期\n        if (this._timeElapsed >= this._config.lifeTime) {\n            this.destroy();\n            return true;\n        }\n        // 更新位置\n        this.updateMovement(deltaTime);\n        // 检查碰撞\n        this.checkCollision();\n        return false;\n    }\n\n    /** * 命中目标 */\n    hit(target: ICharacter): boolean {\n        if (this._isDestroyed || this._remainingHits <= 0) {\n            return false;\n        }\n        // 造成伤害\n        const damage = this.calculateDamage();\n        target.takeDamage(damage, this._caster);\n        this._remainingHits--;\n        this._hasCollided = true;\n        console.log(`Bullet ${this._id} hit ${target.characterName} for ${damage} damage`);\n        // 播放命中音效\n        if (this._config.audio?.hitSound) {\n            console.log(`Playing hit sound: ${this._config.audio.hitSound}`);\n        }\n        // 触发命中事件\n        this._eventManager.emit(FightEvent.bulletHit, { bullet: this, target: target, damage: damage });\n        // 检查是否需要销毁\n        if (!this._config.collision?.piercing || this._remainingHits <= 0) {\n            this.destroy();\n        }\n        return true;\n    }\n    /** * 设置目标 */\n    setTarget(target: ICharacter): void {\n        this._target = target;\n    }\n    /** * 设置目标位置 */\n    setTargetPosition(position: cc.Vec3): void {\n        this._targetPosition = position;\n    }\n    /**  * 销毁子弹  */\n    destroy(): void {\n        if (this._isDestroyed) return;\n        this._isDestroyed = true;\n        if (this._node && this._node.isValid) {\n            this._node.destroy();\n        }\n        this._eventManager.emit(FightEvent.bulletDestroyed, { bullet: this });\n        this._eventManager.cleanup();\n    }\n\n    /** * 创建轨迹实例 */\n    private createTrajectory(): IBulletTrajectory {\n        // 简化实现，返回一个基础轨迹 \n        return {\n            type: this._config.trajectory.type,\n            calculateNextPosition: (_bullet: IBullet, deltaTime: number) => {\n                return this.calculateNextPosition(deltaTime);\n            },\n            calculateRotation: (_bullet: IBullet) => {\n                return 0; // 简化实现\n            },\n            hasReachedTarget: (bullet: IBullet, threshold: number) => {\n                if (!this._target) return false;\n                const distance = cc.Vec3.distance(\n                    bullet.currentPosition,\n                    this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO)\n                );\n                return distance <= threshold;\n            }\n        };\n    }\n    /** * 计算下一帧位置 */\n    private calculateNextPosition(deltaTime: number): cc.Vec3 {\n        if (!this._node) return cc.Vec3.ZERO;\n        const currentPos = this._node.position;\n        let targetPos: cc.Vec3;\n        // 确定目标位置\n        if (this._target && !this._target.isDead) {\n            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        } else if (this._targetPosition) {\n            targetPos = this._targetPosition;\n        } else {\n            return currentPos; // 没有目标，保持当前位置\n        }\n        // 计算移动方向和距离\n        const direction = targetPos.subtract(currentPos).normalize();\n        const moveDistance = this._config.speed * deltaTime;\n        return currentPos.add(direction.multiplyScalar(moveDistance));\n    }\n    /** * 创建子弹节点 */\n    private createBulletNode(): void {\n        this._node = new cc.Node(`Bullet_${this._id}`);\n        // 添加精灵组件\n        this._node.addComponent(cc.Sprite);\n        // 这里应该加载子弹贴图\n        // cc.resources.load(this._config.prefabPath, cc.SpriteFrame, (err, spriteFrame) => {\n        //     if (!err && sprite.isValid) {\n        //         sprite.spriteFrame = spriteFrame;\n        //     }\n        // });\n        // 设置初始位置\n        this._node.position = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        // 添加到场景\n        cc.director.getScene().addChild(this._node);\n    }\n    /** * 更新移动 */\n    private updateMovement(deltaTime: number): void {\n        if (!this._node || !this._node.isValid) return;\n        const currentPos = this._node.position;\n        let targetPos: cc.Vec3;\n        // 确定目标位置\n        if (this._target && !this._target.isDead) {\n            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        } else if (this._targetPosition) {\n            targetPos = this._targetPosition;\n        } else {\n            // 没有目标，直接销毁\n            this.destroy();\n            return;\n        }\n        // 计算移动方向\n        const direction = targetPos.subtract(currentPos).normalize();\n        const moveDistance = this._config.speed * deltaTime;\n        // 根据轨迹类型移动\n        switch (this._config.trajectory.type) {\n            case TrajectoryType.LINEAR:\n                this.moveLinear(direction, moveDistance);\n                break;\n            case TrajectoryType.PARABOLIC:\n                this.moveParabolic(targetPos, deltaTime);\n                break;\n            case TrajectoryType.HOMING:\n                this.moveHoming(targetPos, deltaTime);\n                break;\n            default: this.moveLinear(direction, moveDistance); break;\n        }\n        // 检查是否到达目标\n        const distanceToTarget = cc.Vec3.distance(this._node.position, targetPos);\n        if (distanceToTarget <= 10) { // 10像素的容错范围\n            this.onHitTarget();\n        }\n    }\n    /*** 线性移动*/\n    private moveLinear(direction: cc.Vec3, distance: number): void {\n        const newPos = this._node.position.add(direction.multiplyScalar(distance));\n        this._node.position = newPos;\n    }\n    /*** 抛物线移动(简化的抛物线实现)*/\n    private moveParabolic(targetPos: cc.Vec3, _deltaTime: number): void {\n        const progress = this._timeElapsed / this._config.lifeTime;\n        const startPos = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n        // 计算抛物线轨迹\n        const midPoint = startPos.add(targetPos).multiplyScalar(0.5);\n        midPoint.y += 100; // 抛物线高度\n        const x = cc.misc.lerp(startPos.x, targetPos.x, progress);\n        const y = this.calculateParabolicY(startPos, midPoint, targetPos, progress);\n        this._node.position = cc.v3(x, y, 0);\n    }\n    /** * 追踪移动 */\n    private moveHoming(targetPos: cc.Vec3, deltaTime: number): void {\n        const currentPos = this._node.position;\n        const direction = targetPos.subtract(currentPos).normalize();\n        // 追踪子弹有更强的转向能力\n        const moveDistance = this._config.speed * deltaTime;\n        const newPos = currentPos.add(direction.multiplyScalar(moveDistance));\n        this._node.position = newPos;\n    }\n    /** * 计算抛物线Y坐标 */\n    private calculateParabolicY(start: cc.Vec3, mid: cc.Vec3, end: cc.Vec3, t: number): number {\n        // 二次贝塞尔曲线\n        const oneMinusT = 1 - t;\n        return oneMinusT * oneMinusT * start.y + 2 * oneMinusT * t * mid.y + t * t * end.y;\n    }\n    /** * 检查碰撞 */\n    private checkCollision(): void {\n        if (!this._config.collision) return;\n        // 简化的碰撞检测\n        const bulletPos = this._node.position;\n        const collisionRadius = this._config.collision.radius;\n        // 这里应该使用物理系统或碰撞检测系统\n        // 现在简化为距离检测\n        if (this._target && !this._target.isDead) {\n            const targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n            const distance = cc.Vec3.distance(bulletPos, targetPos);\n            if (distance <= collisionRadius) {\n                this.onHitTarget();\n            }\n        }\n    }\n    /*** 命中目标*/\n    private onHitTarget(): void {\n        if (!this._target) {\n            return;\n        }\n        // 使用hit方法处理命中逻辑\n        this.hit(this._target);\n    }\n    /** * 计算伤害 */\n    private calculateDamage(): number {\n        // 简化的伤害计算，使用施法者的攻击力\n        const baseDamage = this._caster.attributes.attack;\n        // 这里可以添加更复杂的伤害计算逻辑\n        return Math.floor(baseDamage);\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n}\n\n/*** 子弹发射器实现类*/\nexport class BulletLauncher implements IBulletLauncher {\n    private _id: string;\n    private _caster: ICharacter;\n    private _bulletConfig: IBulletConfig;\n    private _firePosition: cc.Vec3 = cc.Vec3.ZERO;\n    private _fireDirection: cc.Vec3 = cc.Vec3.ZERO;\n    private _fireAngle: number = 0;\n    private _fireSpeed: number = 400;\n    private _eventManager: EventManager;\n    constructor(id: string, caster: ICharacter, bulletConfig: IBulletConfig) {\n        this._id = id;\n        this._caster = caster;\n        this._bulletConfig = bulletConfig;\n        this._eventManager = EventManager.createLocal(`BulletLauncher_${id}`);\n    }\n\n    // 实现IBulletLauncher接口\n    get id(): string { return this._id; }\n    get caster(): ICharacter { return this._caster; }\n    get bulletConfig(): IBulletConfig { return this._bulletConfig; }\n    get firePosition(): cc.Vec3 { return this._firePosition; }\n    set firePosition(value: cc.Vec3) { this._firePosition = value; }\n    get fireDirection(): cc.Vec3 { return this._fireDirection; }\n    set fireDirection(value: cc.Vec3) { this._fireDirection = value; }\n    get fireAngle(): number { return this._fireAngle; }\n    set fireAngle(value: number) { this._fireAngle = value; }\n    get fireSpeed(): number { return this._fireSpeed; }\n    set fireSpeed(value: number) { this._fireSpeed = value; }\n    get bulletTimesp() { return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}` }\n    /*** 发射单个子弹*/\n    fire(target?: ICharacter, targetPosition?: cc.Vec3): IBullet | null {\n        if (!target && !targetPosition) {\n            console.warn(\"BulletLauncher: No target or target position provided\");\n            return null;\n        }\n        const bulletId = `${this._id}_bullet_${this.bulletTimesp}`;\n        const bullet = new Bullet(bulletId, this._bulletConfig, this._caster, this._firePosition, target, targetPosition);\n        console.log(`BulletLauncher ${this._id} fired bullet ${bulletId}`);\n        this._eventManager.emit(FightEvent.bulletFired, { launcher: this, bullet: bullet, target: target, targetPosition: targetPosition });\n        return bullet;\n    }\n    /*** 发射多个子弹（散射）*/\n    fireBurst(count: number, spread: number, target?: ICharacter, targetPosition?: cc.Vec3): IBullet[] {\n        const bullets: IBullet[] = [];\n        if (count <= 0) return bullets;\n        // 计算散射角度\n        const startAngle = -spread / 2;\n        const angleStep = count > 1 ? spread / (count - 1) : 0;\n        for (let i = 0; i < count; i++) {\n            const angle = startAngle + angleStep * i;\n            // 计算散射后的目标位置\n            let burstTargetPos: cc.Vec3;\n            if (targetPosition) {\n                burstTargetPos = this.calculateBurstPosition(targetPosition, angle);\n            } else if (target) {\n                const originalPos = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);\n                burstTargetPos = this.calculateBurstPosition(originalPos, angle);\n            } else {\n                continue;\n            }\n            const bullet = this.fire(undefined, burstTargetPos);\n            if (bullet) {\n                bullets.push(bullet);\n            }\n        }\n        console.log(`BulletLauncher ${this._id} fired burst of ${bullets.length} bullets`);\n        return bullets;\n    }\n\n    /** * 设置发射参数 */\n    setFireParams(position: cc.Vec3, direction: cc.Vec3, speed?: number): void {\n        this._firePosition = position;\n        this._fireDirection = direction.normalize();\n        if (speed !== undefined) {\n            this._fireSpeed = speed;\n        }\n        // 计算角度\n        this._fireAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n    }\n    /** * 计算散射位置 */\n    private calculateBurstPosition(originalPos: cc.Vec3, angleOffset: number): cc.Vec3 {\n        const distance = cc.Vec3.distance(this._firePosition, originalPos);\n        const originalAngle = Math.atan2(originalPos.y - this._firePosition.y, originalPos.x - this._firePosition.x);\n        const newAngle = originalAngle + angleOffset * Math.PI / 180;\n        return cc.v3(this._firePosition.x + Math.cos(newAngle) * distance, this._firePosition.y + Math.sin(newAngle) * distance, originalPos.z);\n    }\n    /** * 更新子弹配置 */\n    updateBulletConfig(config: Partial<IBulletConfig>): void {\n        this._bulletConfig = { ...this._bulletConfig, ...config };\n    }\n    /** 获取事件管理器（供外部直接使用，避免包装方法冗余） */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n\n    /**  * 清理资源  */\n    cleanup(): void {\n        this._eventManager.cleanup();\n    }\n}\n"]}