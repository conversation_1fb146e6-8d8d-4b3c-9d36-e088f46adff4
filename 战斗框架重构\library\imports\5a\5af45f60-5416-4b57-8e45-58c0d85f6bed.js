"use strict";
cc._RF.push(module, '5af459gVBZLV45FWMDYX2vt', 'Buff');
// fight/types/Buff.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EBuffEffectType = void 0;
/**
 * Buff效果类型枚举
 * 定义了游戏中所有可能的Buff效果类型常量
 */
var EBuffEffectType = /** @class */ (function () {
    function EBuffEffectType() {
    }
    /** 反击几率 - 受到攻击时触发反击的概率 (0-1之间的小数) */
    EBuffEffectType.counterAttackChance = 'counterAttackChance';
    /** 反击伤害倍数 - 反击时造成伤害的倍数 */
    EBuffEffectType.counterAttackDamageMultiplier = 'counterAttackDamageMultiplier';
    /** 攻击力加成 - 固定数值的攻击力增加 */
    EBuffEffectType.attackBonus = 'attackBonus';
    /** 暴击率加成 - 增加暴击几率的百分比 (0-1之间的小数) */
    EBuffEffectType.criticalRateBonus = 'criticalRateBonus';
    /** 物理攻击倍数 - 物理攻击伤害的乘数效果 */
    EBuffEffectType.attack_multiplier = 'attack_multiplier';
    /** 魔法攻击倍数 - 魔法攻击伤害的乘数效果 */
    EBuffEffectType.magic_attack_multiplier = 'magic_attack_multiplier';
    /** 攻击力百分比加成 - 基于基础攻击力的百分比增加 */
    EBuffEffectType.attack_bonus_percentage = 'attack_bonus_percentage';
    /** 每秒治疗量 - 持续治疗效果，每秒恢复的生命值 */
    EBuffEffectType.heal_per_second = 'heal_per_second';
    /** 总治疗量 - 一次性治疗效果的总量 */
    EBuffEffectType.total_heal = 'total_heal';
    /** 每秒伤害 - 持续伤害效果，每秒造成的伤害 (毒、燃烧等) */
    EBuffEffectType.damage_per_second = 'damage_per_second';
    /** 剩余总伤害 - 持续伤害效果的剩余总伤害量 */
    EBuffEffectType.total_damage_remaining = 'total_damage_remaining';
    /** 治疗减免 - 降低受到治疗效果的百分比 (0-1之间的小数) */
    EBuffEffectType.healing_reduction = 'healing_reduction';
    /** 移动封锁 - 阻止单位移动的控制效果 (布尔值) */
    EBuffEffectType.movement_blocked = 'movement_blocked';
    /** 技能封锁 - 阻止使用技能的控制效果 (布尔值) */
    EBuffEffectType.skills_blocked = 'skills_blocked';
    /** 普攻封锁 - 阻止使用普通攻击的控制效果 (布尔值) */
    EBuffEffectType.basic_attack_blocked = 'basic_attack_blocked';
    return EBuffEffectType;
}());
exports.EBuffEffectType = EBuffEffectType;

cc._RF.pop();