
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/types/ISkill.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7e5eaI0Wf5JeKspR68MspJA', 'ISkill');
// fight/types/ISkill.ts

"use strict";
/**
 * 技能系统接口定义
 * 基于Timeline的技能系统，保留原有的复杂效果支持
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillEffectType = exports.SkillTargetType = exports.SkillType = void 0;
/*** 技能类型枚举*/
var SkillType;
(function (SkillType) {
    /** 主动技能 */
    SkillType["ACTIVE"] = "active";
    /** 被动技能 */
    SkillType["PASSIVE"] = "passive";
    /** 普通攻击 */
    SkillType["BASIC_ATTACK"] = "basic_attack";
    /** 终极技能 */
    SkillType["ULTIMATE"] = "ultimate";
})(SkillType = exports.SkillType || (exports.SkillType = {}));
/*** 技能目标类型枚举*/
var SkillTargetType;
(function (SkillTargetType) {
    /** 无目标 */
    SkillTargetType["NONE"] = "none";
    /** 单个敌人 */
    SkillTargetType["SINGLE_ENEMY"] = "single_enemy";
    /** 多个敌人 */
    SkillTargetType["MULTIPLE_ENEMIES"] = "multiple_enemies";
    /** 单个友军 */
    SkillTargetType["SINGLE_ALLY"] = "single_ally";
    /** 多个友军 */
    SkillTargetType["MULTIPLE_ALLIES"] = "multiple_allies";
    /** 自己 */
    SkillTargetType["SELF"] = "self";
    /** 地面位置 */
    SkillTargetType["GROUND"] = "ground";
    /** 所有敌人 */
    SkillTargetType["ALL_ENEMIES"] = "all_enemies";
    /** 所有友军 */
    SkillTargetType["ALL_ALLIES"] = "all_allies";
})(SkillTargetType = exports.SkillTargetType || (exports.SkillTargetType = {}));
/*** 技能效果类型枚举*/
var SkillEffectType;
(function (SkillEffectType) {
    /** 伤害 */
    SkillEffectType["DAMAGE"] = "damage";
    /** 治疗 */
    SkillEffectType["HEAL"] = "heal";
    /** 添加Buff */
    SkillEffectType["ADD_BUFF"] = "add_buff";
    /** 移除Buff */
    SkillEffectType["REMOVE_BUFF"] = "remove_buff";
    /** 召唤 */
    SkillEffectType["SUMMON"] = "summon";
    /** 传送 */
    SkillEffectType["TELEPORT"] = "teleport";
    /** 击退 */
    SkillEffectType["KNOCKBACK"] = "knockback";
    /** 眩晕 */
    SkillEffectType["STUN"] = "stun";
})(SkillEffectType = exports.SkillEffectType || (exports.SkillEffectType = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcZmlnaHRcXHR5cGVzXFxJU2tpbGwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7R0FHRzs7O0FBa0hILGFBQWE7QUFDYixJQUFZLFNBU1g7QUFURCxXQUFZLFNBQVM7SUFDakIsV0FBVztJQUNYLDhCQUFpQixDQUFBO0lBQ2pCLFdBQVc7SUFDWCxnQ0FBbUIsQ0FBQTtJQUNuQixXQUFXO0lBQ1gsMENBQTZCLENBQUE7SUFDN0IsV0FBVztJQUNYLGtDQUFxQixDQUFBO0FBQ3pCLENBQUMsRUFUVyxTQUFTLEdBQVQsaUJBQVMsS0FBVCxpQkFBUyxRQVNwQjtBQUVELGVBQWU7QUFDZixJQUFZLGVBbUJYO0FBbkJELFdBQVksZUFBZTtJQUN2QixVQUFVO0lBQ1YsZ0NBQWEsQ0FBQTtJQUNiLFdBQVc7SUFDWCxnREFBNkIsQ0FBQTtJQUM3QixXQUFXO0lBQ1gsd0RBQXFDLENBQUE7SUFDckMsV0FBVztJQUNYLDhDQUEyQixDQUFBO0lBQzNCLFdBQVc7SUFDWCxzREFBbUMsQ0FBQTtJQUNuQyxTQUFTO0lBQ1QsZ0NBQWEsQ0FBQTtJQUNiLFdBQVc7SUFDWCxvQ0FBaUIsQ0FBQTtJQUNqQixXQUFXO0lBQ1gsOENBQTJCLENBQUE7SUFDM0IsV0FBVztJQUNYLDRDQUF5QixDQUFBO0FBQzdCLENBQUMsRUFuQlcsZUFBZSxHQUFmLHVCQUFlLEtBQWYsdUJBQWUsUUFtQjFCO0FBRUQsZUFBZTtBQUNmLElBQVksZUFpQlg7QUFqQkQsV0FBWSxlQUFlO0lBQ3ZCLFNBQVM7SUFDVCxvQ0FBaUIsQ0FBQTtJQUNqQixTQUFTO0lBQ1QsZ0NBQWEsQ0FBQTtJQUNiLGFBQWE7SUFDYix3Q0FBcUIsQ0FBQTtJQUNyQixhQUFhO0lBQ2IsOENBQTJCLENBQUE7SUFDM0IsU0FBUztJQUNULG9DQUFpQixDQUFBO0lBQ2pCLFNBQVM7SUFDVCx3Q0FBcUIsQ0FBQTtJQUNyQixTQUFTO0lBQ1QsMENBQXVCLENBQUE7SUFDdkIsU0FBUztJQUNULGdDQUFhLENBQUE7QUFDakIsQ0FBQyxFQWpCVyxlQUFlLEdBQWYsdUJBQWUsS0FBZix1QkFBZSxRQWlCMUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIOaKgOiDveezu+e7n+aOpeWPo+WumuS5iVxuICog5Z+65LqOVGltZWxpbmXnmoTmioDog73ns7vnu5/vvIzkv53nlZnljp/mnInnmoTlpI3mnYLmlYjmnpzmlK/mjIFcbiAqL1xuXG5pbXBvcnQgeyBJQ2hhcmFjdGVyIH0gZnJvbSBcIi4vSUNoYXJhY3RlclwiO1xuaW1wb3J0IHsgSUJ1ZmYgfSBmcm9tIFwiLi9JQnVmZlwiO1xuaW1wb3J0IHsgSVRpbWVsaW5lIH0gZnJvbSBcIi4vSVRpbWVsaW5lXCI7XG5cbi8qKiog5oqA6IO95o6l5Y+jKi9cbmV4cG9ydCBpbnRlcmZhY2UgSVNraWxsIHtcbiAgICAvKiog5oqA6IO9SUQgKi9cbiAgICByZWFkb25seSBpZDogc3RyaW5nO1xuICAgIC8qKiDmioDog73lkI3np7AgKi9cbiAgICByZWFkb25seSBuYW1lOiBzdHJpbmc7XG4gICAgLyoqIOaKgOiDveaPj+i/sCAqL1xuICAgIHJlYWRvbmx5IGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gICAgLyoqIOWGt+WNtOaXtumXtO+8iOenku+8iSAqL1xuICAgIHJlYWRvbmx5IGNvb2xkb3duOiBudW1iZXI7XG4gICAgLyoqIOW9k+WJjeWGt+WNtOWJqeS9meaXtumXtCAqL1xuICAgIHJlbWFpbmluZ0Nvb2xkb3duOiBudW1iZXI7XG4gICAgLyoqIOmtlOazlea2iOiAlyAqL1xuICAgIHJlYWRvbmx5IG1wQ29zdDogbnVtYmVyO1xuICAgIC8qKiDogJDlipvmtojogJcgKi9cbiAgICByZWFkb25seSBzdGFtaW5hQ29zdDogbnVtYmVyO1xuICAgIC8qKiDmioDog73nrYnnuqcgKi9cbiAgICByZWFkb25seSBsZXZlbDogbnVtYmVyO1xuICAgIC8qKiDmioDog73nsbvlnosgKi9cbiAgICByZWFkb25seSB0eXBlOiBTa2lsbFR5cGU7XG4gICAgLyoqIOebruagh+exu+WeiyAqL1xuICAgIHJlYWRvbmx5IHRhcmdldFR5cGU6IFNraWxsVGFyZ2V0VHlwZTtcbiAgICAvKiog5oqA6IO96IyD5Zu0ICovXG4gICAgcmVhZG9ubHkgcmFuZ2U6IG51bWJlcjtcbiAgICAvKiog5oqA6IO95pWI5p6cVGltZWxpbmUgKi9cbiAgICByZWFkb25seSB0aW1lbGluZTogSVRpbWVsaW5lO1xuICAgIC8qKiDlrabkuaDmioDog73ml7bojrflvpfnmoTooqvliqhCdWZmICovXG4gICAgcmVhZG9ubHkgcGFzc2l2ZUJ1ZmZzOiBJQnVmZltdO1xuICAgIC8qKiDmmK/lkKblj6/ku6Xkvb/nlKggKi9cbiAgICByZWFkb25seSBjYW5Vc2U6IGJvb2xlYW47XG4gICAgLyoqXG4gICAgICog5qOA5p+l5piv5ZCm5Y+v5Lul5a+555uu5qCH5L2/55So5oqA6IO9XG4gICAgICogQHBhcmFtIGNhc3RlciDmlr3ms5XogIVcbiAgICAgKiBAcGFyYW0gdGFyZ2V0IOebruagh1xuICAgICAqIEByZXR1cm5zIOaYr+WQpuWPr+S7peS9v+eUqFxuICAgICAqL1xuICAgIGNhbkNhc3RPbihjYXN0ZXI6IElDaGFyYWN0ZXIsIHRhcmdldD86IElDaGFyYWN0ZXIpOiBib29sZWFuO1xuICAgIC8qKlxuICAgICAqIOmHiuaUvuaKgOiDvVxuICAgICAqIEBwYXJhbSBjYXN0ZXIg5pa95rOV6ICFXG4gICAgICogQHBhcmFtIHRhcmdldCDnm67moIdcbiAgICAgKiBAcGFyYW0gdGFyZ2V0cyDlpJrkuKrnm67moIdcbiAgICAgKiBAcGFyYW0gcG9zaXRpb24g55uu5qCH5L2N572uXG4gICAgICogQHJldHVybnMg5piv5ZCm6YeK5pS+5oiQ5YqfXG4gICAgICovXG4gICAgY2FzdChjYXN0ZXI6IElDaGFyYWN0ZXIsIHRhcmdldD86IElDaGFyYWN0ZXIsIHRhcmdldHM/OiBJQ2hhcmFjdGVyW10sIHBvc2l0aW9uPzogY2MuVmVjMyk6IGJvb2xlYW47XG4gICAgLyoqXG4gICAgICog5pu05paw5oqA6IO954q25oCB77yI5Li76KaB5piv5Ya35Y205pe26Ze077yJXG4gICAgICogQHBhcmFtIGRlbHRhVGltZSDml7bpl7Tpl7TpmpRcbiAgICAgKi9cbiAgICB1cGRhdGUoZGVsdGFUaW1lOiBudW1iZXIpOiB2b2lkO1xuICAgIC8qKiAqIOmHjee9ruWGt+WNtOaXtumXtCAqL1xuICAgIHJlc2V0Q29vbGRvd24oKTogdm9pZDtcbiAgICAvKipcbiAgICAgKiDmioDog73ooqvmt7vliqDml7bnmoTlm57osIPvvIjnlKjkuo7lpJrnm67moIfmioDog73nrYnnibnmrormg4XlhrXvvIlcbiAgICAgKiBAcGFyYW0gdGFyZ2V0cyDnm67moIfliJfooahcbiAgICAgKi9cbiAgICBvbkFkZD8odGFyZ2V0czogSUNoYXJhY3RlcltdKTogdm9pZDtcbn1cblxuLyoqICog5oqA6IO95pWI5p6c5o6l5Y+jICovXG5leHBvcnQgaW50ZXJmYWNlIElTa2lsbEVmZmVjdCB7XG4gICAgLyoqIOaViOaenElEICovXG4gICAgcmVhZG9ubHkgaWQ6IHN0cmluZztcbiAgICAvKiog5pWI5p6c57G75Z6LICovXG4gICAgcmVhZG9ubHkgdHlwZTogU2tpbGxFZmZlY3RUeXBlO1xuICAgIC8qKlxuICAgICAqIOaJp+ihjOaViOaenFxuICAgICAqIEBwYXJhbSBjYXN0ZXIg5pa95rOV6ICFXG4gICAgICogQHBhcmFtIHRhcmdldCDnm67moIdcbiAgICAgKiBAcGFyYW0gc2tpbGwg5oqA6IO9XG4gICAgICogQHBhcmFtIGNvbnRleHQg5LiK5LiL5paH5L+h5oGvXG4gICAgICovXG4gICAgZXhlY3V0ZShjYXN0ZXI6IElDaGFyYWN0ZXIsIHRhcmdldDogSUNoYXJhY3Rlciwgc2tpbGw6IElTa2lsbCwgY29udGV4dD86IGFueSk6IHZvaWQ7XG59XG5cbi8qKiog5oqA6IO96YWN572u5o6l5Y+jKi9cbmV4cG9ydCBpbnRlcmZhY2UgSVNraWxsQ29uZmlnIHtcbiAgICAvKiog5oqA6IO9SUQgKi9cbiAgICBpZDogc3RyaW5nO1xuICAgIC8qKiDmioDog73lkI3np7AgKi9cbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgLyoqIOaKgOiDveaPj+i/sCAqL1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gICAgLyoqIOWGt+WNtOaXtumXtCAqL1xuICAgIGNvb2xkb3duOiBudW1iZXI7XG4gICAgLyoqIOmtlOazlea2iOiAlyAqL1xuICAgIG1wQ29zdDogbnVtYmVyO1xuICAgIC8qKiDogJDlipvmtojogJcgKi9cbiAgICBzdGFtaW5hQ29zdDogbnVtYmVyO1xuICAgIC8qKiDmioDog73nsbvlnosgKi9cbiAgICB0eXBlOiBTa2lsbFR5cGU7XG4gICAgLyoqIOebruagh+exu+WeiyAqL1xuICAgIHRhcmdldFR5cGU6IFNraWxsVGFyZ2V0VHlwZTtcbiAgICAvKiog5oqA6IO96IyD5Zu0ICovXG4gICAgcmFuZ2U6IG51bWJlcjtcbiAgICAvKiog5oqA6IO95pWI5p6c5YiX6KGoICovXG4gICAgZWZmZWN0czogSVNraWxsRWZmZWN0W107XG4gICAgLyoqIOWtpuS5oOaKgOiDveaXtuiOt+W+l+eahEJ1ZmYgKi9cbiAgICBwYXNzaXZlQnVmZnM/OiBJQnVmZltdO1xuICAgIC8qKiDliqjnlLvlkI3np7AgKi9cbiAgICBhbmltYXRpb25OYW1lPzogc3RyaW5nO1xuICAgIC8qKiDpn7PmlYhJRCAqL1xuICAgIHNvdW5kSWQ/OiBzdHJpbmc7XG4gICAgLyoqIOeJueaViOmihOWItuS9kyAqL1xuICAgIGVmZmVjdFByZWZhYj86IHN0cmluZztcbn1cblxuLyoqKiDmioDog73nsbvlnovmnprkuL4qL1xuZXhwb3J0IGVudW0gU2tpbGxUeXBlIHtcbiAgICAvKiog5Li75Yqo5oqA6IO9ICovXG4gICAgQUNUSVZFID0gXCJhY3RpdmVcIixcbiAgICAvKiog6KKr5Yqo5oqA6IO9ICovXG4gICAgUEFTU0lWRSA9IFwicGFzc2l2ZVwiLFxuICAgIC8qKiDmma7pgJrmlLvlh7sgKi9cbiAgICBCQVNJQ19BVFRBQ0sgPSBcImJhc2ljX2F0dGFja1wiLFxuICAgIC8qKiDnu4jmnoHmioDog70gKi9cbiAgICBVTFRJTUFURSA9IFwidWx0aW1hdGVcIlxufVxuXG4vKioqIOaKgOiDveebruagh+exu+Wei+aemuS4viovXG5leHBvcnQgZW51bSBTa2lsbFRhcmdldFR5cGUge1xuICAgIC8qKiDml6Dnm67moIcgKi9cbiAgICBOT05FID0gXCJub25lXCIsXG4gICAgLyoqIOWNleS4quaVjOS6uiAqL1xuICAgIFNJTkdMRV9FTkVNWSA9IFwic2luZ2xlX2VuZW15XCIsXG4gICAgLyoqIOWkmuS4quaVjOS6uiAqL1xuICAgIE1VTFRJUExFX0VORU1JRVMgPSBcIm11bHRpcGxlX2VuZW1pZXNcIixcbiAgICAvKiog5Y2V5Liq5Y+L5YabICovXG4gICAgU0lOR0xFX0FMTFkgPSBcInNpbmdsZV9hbGx5XCIsXG4gICAgLyoqIOWkmuS4quWPi+WGmyAqL1xuICAgIE1VTFRJUExFX0FMTElFUyA9IFwibXVsdGlwbGVfYWxsaWVzXCIsXG4gICAgLyoqIOiHquW3sSAqL1xuICAgIFNFTEYgPSBcInNlbGZcIixcbiAgICAvKiog5Zyw6Z2i5L2N572uICovXG4gICAgR1JPVU5EID0gXCJncm91bmRcIixcbiAgICAvKiog5omA5pyJ5pWM5Lq6ICovXG4gICAgQUxMX0VORU1JRVMgPSBcImFsbF9lbmVtaWVzXCIsXG4gICAgLyoqIOaJgOacieWPi+WGmyAqL1xuICAgIEFMTF9BTExJRVMgPSBcImFsbF9hbGxpZXNcIlxufVxuXG4vKioqIOaKgOiDveaViOaenOexu+Wei+aemuS4viovXG5leHBvcnQgZW51bSBTa2lsbEVmZmVjdFR5cGUge1xuICAgIC8qKiDkvKTlrrMgKi9cbiAgICBEQU1BR0UgPSBcImRhbWFnZVwiLFxuICAgIC8qKiDmsrvnlpcgKi9cbiAgICBIRUFMID0gXCJoZWFsXCIsXG4gICAgLyoqIOa3u+WKoEJ1ZmYgKi9cbiAgICBBRERfQlVGRiA9IFwiYWRkX2J1ZmZcIixcbiAgICAvKiog56e76ZmkQnVmZiAqL1xuICAgIFJFTU9WRV9CVUZGID0gXCJyZW1vdmVfYnVmZlwiLFxuICAgIC8qKiDlj6zllKQgKi9cbiAgICBTVU1NT04gPSBcInN1bW1vblwiLFxuICAgIC8qKiDkvKDpgIEgKi9cbiAgICBURUxFUE9SVCA9IFwidGVsZXBvcnRcIixcbiAgICAvKiog5Ye76YCAICovXG4gICAgS05PQ0tCQUNLID0gXCJrbm9ja2JhY2tcIixcbiAgICAvKiog55yp5pmVICovXG4gICAgU1RVTiA9IFwic3R1blwiXG59XG5cbi8qKiog5oqA6IO95a2m5Lmg5p2h5Lu25o6l5Y+jKi9cbmV4cG9ydCBpbnRlcmZhY2UgSVNraWxsUmVxdWlyZW1lbnQge1xuICAgIC8qKiDpnIDopoHnmoTnrYnnuqcgKi9cbiAgICByZXF1aXJlZExldmVsPzogbnVtYmVyO1xuICAgIC8qKiDliY3nva7mioDog70gKi9cbiAgICBwcmVyZXF1aXNpdGVTa2lsbHM/OiBzdHJpbmdbXTtcbiAgICAvKiog6ZyA6KaB55qE5bGe5oCnICovXG4gICAgcmVxdWlyZWRBdHRyaWJ1dGVzPzogeyBba2V5OiBzdHJpbmddOiBudW1iZXIgfTtcbiAgICAvKipcbiAgICAgKiDmo4Dmn6XmmK/lkKbmu6HotrPlrabkuaDmnaHku7ZcbiAgICAgKiBAcGFyYW0gY2hhcmFjdGVyIOinkuiJslxuICAgICAqIEByZXR1cm5zIOaYr+WQpua7oei2s+adoeS7tlxuICAgICAqL1xuICAgIGNoZWNrUmVxdWlyZW1lbnQoY2hhcmFjdGVyOiBJQ2hhcmFjdGVyKTogYm9vbGVhbjtcbn1cblxuLyoqICog5pS75Ye75Yqo5L2c5bGe5oCn5o6l5Y+jICovXG5leHBvcnQgaW50ZXJmYWNlIElBdHRhY2tBY3Rpb25Qcm9wcyB7XG4gICAgLyoqIOWJjeaRh+aXtumXtO+8jOaUu+WHu+W8gOWni+WIsOmAoOaIkOS8pOWus+eahOaXtumXtO+8iOavq+enku+8iSAqL1xuICAgIGh1cnRTdGFydFRpbWVNczogbnVtYmVyO1xuICAgIC8qKiDmlLvlh7vmjIHnu63ml7bpl7TvvIzku47lvIDlp4vliLDnu5PmnZ/nmoTmgLvml7bpl7TvvIjmr6vnp5LvvIkgKi9cbiAgICBodXJ0RW5kVGltZU1zOiBudW1iZXI7XG4gICAgLyoqIOW8gOWni+mAoOaIkOS8pOWus+aXtueahOWbnuiwgyAqL1xuICAgIG9uSHVydFN0YXJ0PzogKCkgPT4gdm9pZDtcbiAgICAvKiog5pS75Ye757uT5p2f5pe255qE5Zue6LCDICovXG4gICAgb25IdXJ0RW5kPzogKCkgPT4gdm9pZDtcbiAgICAvKiog5pS75Ye76L+H56iL5Lit55qE5Zue6LCD77yI5Y+v6YCJ77yJICovXG4gICAgb25BdHRhY2tQcm9ncmVzcz86IChwcm9ncmVzczogbnVtYmVyKSA9PiB2b2lkO1xufSJdfQ==