"use strict";
cc._RF.push(module, 'b8929RbYgdEjoHxhSzw5Jhg', 'ICharacterAttributes');
// fight/types/ICharacterAttributes.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeModifierType = void 0;
/*** 属性修改器类型*/
var AttributeModifierType;
(function (AttributeModifierType) {
    /** 加法修改 */
    AttributeModifierType["ADD"] = "add";
    /** 乘法修改 */
    AttributeModifierType["MULTIPLY"] = "multiply";
    /** 百分比修改 */
    AttributeModifierType["PERCENTAGE"] = "percentage";
    /** 覆盖修改 */
    AttributeModifierType["OVERRIDE"] = "override";
})(AttributeModifierType = exports.AttributeModifierType || (exports.AttributeModifierType = {}));

cc._RF.pop();