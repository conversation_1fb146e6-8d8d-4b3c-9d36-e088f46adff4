"use strict";
cc._RF.push(module, 'e68f8V40WZItaSUzbi9cLvM', 'Character');
// fight/characters/Character.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Character = void 0;
var AttackAction_1 = require("../actions/AttackAction");
var MoveAction_1 = require("../actions/MoveAction");
var CharacterTypes_1 = require("../types/CharacterTypes");
var FightEvent_1 = require("../types/FightEvent");
var BaseCharacter_1 = require("./BaseCharacter");
var CharacterAttributes_1 = require("./CharacterAttributes");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/*** 角色类*/
var Character = /** @class */ (function (_super) {
    __extends(Character, _super);
    function Character() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /**角色配置 */
        _this.prefabKey = "";
        _this.roleName = "";
        _this.attackSkillName = "";
        // 动画配置
        _this.idleAnimationName = "idle";
        _this.moveAnimationName = "move";
        _this.attackAnimationName = "attack";
        _this.deathAnimationName = "death";
        // 音效配置
        _this.attackSoundId = "";
        _this.hurtSoundId = "";
        _this.deathSoundId = "";
        return _this;
    }
    /*** 初始化角色*/
    Character.prototype.onCharacterStart = function () {
        this.initializeActions();
        this.initializeAnimationConfig();
        this.initializeSoundConfig();
        this.setupCollision();
    };
    /**  * 更新角色  */
    Character.prototype.onCharacterUpdate = function (deltaTime) {
        // 基类已经处理了技能和Buff的更新， 这里可以添加角色特定的更新逻辑
        this.updateActions(deltaTime);
    };
    /** * 角色死亡处理 */
    Character.prototype.onCharacterDeath = function () {
        var _this = this;
        this.playDeathAnimation();
        this.playDeathSound();
        this.disableCollision();
        // 延迟移除角色
        this.scheduleOnce(function () {
            _this.removeCharacter();
        }, 1.0);
    };
    /** * 执行移动 */
    Character.prototype.performMove = function (direction) {
        if (this._moveAction) {
            var moveVector = direction.clone().multiplyScalar(this.attributes.moveSpeed);
            this._moveAction.moveBy(moveVector);
            this.playMoveAnimation();
        }
    };
    /** * 初始化动作组件 */
    Character.prototype.initializeActions = function () {
        this._attackAction = this.node.getComponent(AttackAction_1.AttackAction) || this.node.addComponent(AttackAction_1.AttackAction);
        this._moveAction = this.node.getComponent(MoveAction_1.MoveAction) || this.node.addComponent(MoveAction_1.MoveAction);
    };
    /** * 初始化动画配置 */
    Character.prototype.initializeAnimationConfig = function () {
        this._animationConfig = {
            idle: this.idleAnimationName,
            move: this.moveAnimationName,
            attack: this.attackAnimationName,
            death: this.deathAnimationName
        };
    };
    /** * 初始化音效配置 */
    Character.prototype.initializeSoundConfig = function () {
        this._soundConfig = {
            attack: this.attackSoundId,
            hurt: this.hurtSoundId,
            death: this.deathSoundId
        };
    };
    /** * 设置碰撞 */
    Character.prototype.setupCollision = function () {
        var collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = true;
        }
    };
    /** * 禁用碰撞 */
    Character.prototype.disableCollision = function () {
        var collider = this.node.getComponent(cc.Collider);
        if (collider) {
            collider.enabled = false;
        }
    };
    /** * 更新动作组件 */
    Character.prototype.updateActions = function (_deltaTime) {
        // 动作组件会自己更新，这里可以添加额外的逻辑
    };
    /*** 播放移动动画*/
    Character.prototype.playMoveAnimation = function () {
        if (this._spine && this._animationConfig.move) {
            this._spine.setAnimation(0, this._animationConfig.move, true);
        }
    };
    /**  * 播放攻击动画  */
    Character.prototype.playAttackAnimation = function (loop) {
        if (loop === void 0) { loop = false; }
        if (this._spine && this._animationConfig.attack) {
            this._spine.setAnimation(0, this._animationConfig.attack, loop);
        }
    };
    /**  * 播放空闲动画  */
    Character.prototype.playIdleAnimation = function () {
        if (this._spine && this._animationConfig.idle) {
            this._spine.setAnimation(0, this._animationConfig.idle, true);
        }
    };
    /** * 播放死亡音效 */
    Character.prototype.playDeathSound = function () {
        if (this._soundConfig.death) {
            // 这里应该调用音效管理器播放音效
            // AudioManager.getInstance().playEffect(this._soundConfig.death);
        }
    };
    /** * 移除角色 */
    Character.prototype.removeCharacter = function () {
        this.node.emit(FightEvent_1.default.characterRemoved, this);
        this.node.destroy();
    };
    /** * 强制攻击目标 */
    Character.prototype.forceAttackTo = function (target) {
        if (!target || target.isDead) {
            console.error('forceAttackTo but target is null or dead');
            return false;
        }
        return this.attack(target);
    };
    /** * 执行攻击动作 */
    Character.prototype.performAttack = function (_target, onHitCallback) {
        var _this = this;
        if (!this._attackAction) {
            return false;
        }
        this.setState(CharacterTypes_1.CharacterState.ATTACKING);
        this.playAttackAnimation();
        // 配置攻击动作
        var attackProps = {
            hurtStartTimeMs: 300,
            hurtEndTimeMs: 600,
            onHurtStart: function () {
                onHitCallback === null || onHitCallback === void 0 ? void 0 : onHitCallback();
                // 这里可以添加伤害计算逻辑
            },
            onHurtEnd: function () {
                _this.setState(CharacterTypes_1.CharacterState.IDLE);
                _this.playIdleAnimation();
            }
        };
        return this._attackAction.doAttackOnce(attackProps);
    };
    /** * 设置角色数据 */
    Character.prototype.setCharacterData = function (data) {
        this._name = data.name;
        this._role = data.role;
        this.roleName = data.name;
        this.prefabKey = typeof data.prefabKey === 'string' ? data.prefabKey : '';
        // 设置初始属性
        if (data.initialAttributes) {
            // 重新创建属性组件以应用初始数据
            this._attributes = new CharacterAttributes_1.CharacterAttributes(data.initialAttributes);
            console.log(this._name + " \u5C5E\u6027\u5DF2\u8BBE\u7F6E:", this._attributes.getAttributeData());
        }
        // 设置位置
        if (data.worldPosition) {
            this.node.position = data.parent ? data.parent.convertToNodeSpaceAR(data.worldPosition) : data.worldPosition;
        }
        // 设置父节点
        if (data.parent) {
            this.node.parent = data.parent;
        }
    };
    /** * 获取开火点世界坐标 */
    Character.prototype.getFireWorldPosition = function () {
        if (this._fireNode) {
            return this._fireNode.convertToWorldSpaceAR(cc.Vec3.ZERO);
        }
        return this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    };
    /** * 获取开火方向 */
    Character.prototype.getFireDirection = function (target) {
        var firePos = this.getFireWorldPosition();
        return target.subtract(firePos).normalize();
    };
    /*** 检查是否在攻击范围内*/
    Character.prototype.isInAttackRange = function (target) {
        if (!target || target.isDead) {
            return false;
        }
        var distance = cc.Vec3.distance(this.node.convertToWorldSpaceAR(cc.Vec3.ZERO), target.node.convertToWorldSpaceAR(cc.Vec3.ZERO));
        return distance <= this.attributes.attackRange;
    };
    /** * 面向目标 */
    Character.prototype.faceTarget = function (target) {
        var currentPos = this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        var direction = target.subtract(currentPos);
        if (direction.x < 0) {
            this.node.scaleX = -Math.abs(this.node.scaleX);
        }
        else {
            this.node.scaleX = Math.abs(this.node.scaleX);
        }
    };
    /** * 获取角色信息 */
    Character.prototype.getCharacterInfo = function () {
        return {
            id: this.id,
            name: this.name,
            role: this.role,
            state: this.state,
            isDead: this.isDead,
            attributes: this.attributes.getAttributeData(),
            position: this.node.convertToWorldSpaceAR(cc.Vec3.ZERO),
            skillCount: this.skills.length,
            buffCount: this.buffs.length
        };
    };
    __decorate([
        property(cc.String)
    ], Character.prototype, "prefabKey", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "roleName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackSkillName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "idleAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "moveAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "deathAnimationName", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "attackSoundId", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "hurtSoundId", void 0);
    __decorate([
        property(cc.String)
    ], Character.prototype, "deathSoundId", void 0);
    Character = __decorate([
        ccclass
    ], Character);
    return Character;
}(BaseCharacter_1.BaseCharacter));
exports.Character = Character;

cc._RF.pop();