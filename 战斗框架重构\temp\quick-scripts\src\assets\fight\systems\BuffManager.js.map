{"version": 3, "sources": ["assets\\fight\\systems\\BuffManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA6C;AAC7C,wCAA+D;AAE/D,+CAA8C;AAE9C,eAAe;AACf;IAMI,qBAAY,SAAqB;;QAJzB,WAAM,GAAuB,IAAI,GAAG,EAAE,CAAC;QACvC,iBAAY,GAA+B,IAAI,GAAG,EAAE,CAAC;QAIzD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,iBAAe,SAAS,CAAC,aAAe,CAAC,CAAC;;YACxF,cAAc;YACd,KAAmB,IAAA,KAAA,SAAA,MAAM,CAAC,MAAM,CAAC,gBAAQ,CAAC,CAAA,gBAAA,4BAAE;gBAAvC,IAAM,IAAI,WAAA;gBACX,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;aAC1C;;;;;;;;;IACL,CAAC;IAED,sBAAI,8BAAK;QADT,iBAAiB;aACjB;YACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC5C,CAAC;;;OAAA;IACD;;;OAGG;IACH,6BAAO,GAAP,UAAQ,IAAW;;QACf,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,IAAI,YAAY,EAAE;YACd,mBAAmB;YACnB,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACH,UAAU;YACV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC/B,MAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,0CAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC/C,aAAa;YACb,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,SAAS,EAAE,EAAE,IAAI,MAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SACvF;IACL,CAAC;IAED;;;;OAIG;IACH,gCAAU,GAAV,UAAW,MAAc;;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,KAAK,CAAC;SAChB;QACD,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC3B,MAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,0CAAE,MAAM,CAAC,MAAM,EAAE;QACjD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,MAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;OAIG;IACH,6BAAO,GAAP,UAAQ,MAAc;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IACD;;;;OAIG;IACH,oCAAc,GAAd,UAAe,IAAc;;QACzB,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,EAAE,CAAC;SACb;QACD,IAAM,KAAK,GAAY,EAAE,CAAC;;YAC1B,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;gBAAzB,IAAM,MAAM,oBAAA;gBACb,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACrC,IAAI,IAAI,EAAE;oBACN,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACpB;aACJ;;;;;;;;;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACD;;;;OAIG;IACH,6BAAO,GAAP,UAAQ,MAAc;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IACD;;;;OAIG;IACH,mCAAa,GAAb,UAAc,IAAc;QACxB,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9C,CAAC;IACD;;;;OAIG;IACH,wCAAkB,GAAlB,UAAmB,IAAc;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IACD,iBAAiB;IACjB,mCAAa,GAAb;;QACI,IAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;;YACrD,KAAqB,IAAA,kBAAA,SAAA,aAAa,CAAA,4CAAA,uEAAE;gBAA/B,IAAM,MAAM,0BAAA;gBACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAC3B;;;;;;;;;IACL,CAAC;IACD;;;OAGG;IACH,sCAAgB,GAAhB,UAAiB,IAAc;;QAC3B,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE;YACT,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;gBACxC,KAAqB,IAAA,gBAAA,SAAA,WAAW,CAAA,wCAAA,iEAAE;oBAA7B,IAAM,MAAM,wBAAA;oBACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;iBAC3B;;;;;;;;;SACJ;IACL,CAAC;IACD;;;OAGG;IACH,iCAAW,GAAX,UAAY,MAAc;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE,EAAE,IAAI,MAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC3F;IACL,CAAC;IACD;;;;OAIG;IACH,kCAAY,GAAZ,UAAa,MAAc,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,MAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IACD;;;;OAIG;IACH,qCAAe,GAAf,UAAgB,MAAc,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,kBAAkB;YAClB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE;gBACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAC3B;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,MAAA,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;aAC9F;SACJ;IACL,CAAC;IACD;;;OAGG;IACH,4BAAM,GAAN,UAAO,SAAiB;;QACpB,IAAM,YAAY,GAAa,EAAE,CAAC;;YAClC,KAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAA/B,IAAA,KAAA,mBAAc,EAAb,MAAM,QAAA,EAAE,IAAI,QAAA;gBACpB,WAAW;gBACX,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACzC,IAAI,SAAS,EAAE;oBACX,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC7B;qBAAM;oBACH,aAAa;oBACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;iBAC1B;aACJ;;;;;;;;;;YACD,YAAY;YACZ,KAAqB,IAAA,iBAAA,SAAA,YAAY,CAAA,0CAAA,oEAAE;gBAA9B,IAAM,MAAM,yBAAA;gBACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAC3B;;;;;;;;;IACL,CAAC;IACD;;;;OAIG;IACK,qCAAe,GAAvB,UAAwB,YAAmB,EAAE,OAAc;QACvD,IAAI,YAAY,CAAC,QAAQ,GAAG,CAAC,EAAE;YAC3B,OAAO;YACP,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC1C,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS;YACjC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE;gBAC5C,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,WAAW,EAAE,OAAO,CAAC,UAAU;aAClC,CAAC,CAAC;SACN;aAAM;YACH,cAAc;YACd,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,aAAa,EAAE;gBAC9C,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,UAAU;aAC7B,CAAC,CAAC;SACN;IACL,CAAC;IACD;;;OAGG;IACH,mCAAa,GAAb;QACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,iCAAW,GAAX;QACI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD;;;OAGG;IACH,kCAAY,GAAZ;;QACI,IAAM,KAAK,GAAG;YACV,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YAC5B,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,CAAC;SACpB,CAAC;;YACF,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,gBAAA,4BAAE;gBAApC,IAAM,IAAI,WAAA;gBACX,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC/B,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACpC;gBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,KAAK,CAAC,YAAY,EAAE,CAAC;iBACxB;gBACD,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;oBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;iBAC1B;aACJ;;;;;;;;;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,oCAAc,GAAd;;QACI,IAAM,IAAI,GAAG,EAAW,CAAA;;YACxB,KAAyB,IAAA,KAAA,SAAA,IAAI,CAAC,MAAM,CAAA,gBAAA,4BAAE;gBAA3B,IAAA,KAAA,mBAAU,EAAT,EAAE,QAAA,EAAE,IAAI,QAAA;gBAChB,IAAI,CAAC,EAAE,CAAC,GAAG;oBACP,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;oBACjC,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC5B,CAAC;aACL;;;;;;;;;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAKD,sBAAI,qCAAY;QAHhB;;WAEG;aACH;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACD,kBAAkB;IAClB,6BAAO,GAAP;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;IACD,eAAe;IACf,kCAAY,GAAZ;QACI,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;YAC/B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YAC3B,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;YAC5B,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;SAC7B,CAAC;IACN,CAAC;IACL,kBAAC;AAAD,CAxSA,AAwSC,IAAA;AAxSY,kCAAW", "file": "", "sourceRoot": "/", "sourcesContent": ["import FightEvent from \"../types/FightEvent\";\nimport { IBuffManager, IBuff, BuffType } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { EventManager } from \"./EventManager\";\n\n/*** Buff管理器类*/\nexport class BuffManager implements IBuffManager {\n    private _character: ICharacter;\n    private _buffs: Map<string, IBuff> = new Map();\n    private _buffsByType: Map<BuffType, Set<string>> = new Map();\n    private _eventManager: EventManager;\n\n    constructor(character: ICharacter) {\n        this._character = character;\n        this._eventManager = EventManager.createLocal(`BuffManager_${character.characterName}`);\n        // 初始化按类型分组的映射\n        for (const type of Object.values(BuffType)) {\n            this._buffsByType.set(type, new Set());\n        }\n    }\n    /** * 获取所有Buff */\n    get buffs(): ReadonlyArray<IBuff> {\n        return Array.from(this._buffs.values());\n    }\n    /**\n     * 添加Buff\n     * @param buff Buff实例\n     */\n    addBuff(buff: IBuff): void {\n        const existingBuff = this._buffs.get(buff.id);\n        if (existingBuff) {\n            // 如果Buff已存在，处理叠加逻辑\n            this.handleBuffStack(existingBuff, buff);\n        } else {\n            // 添加新Buff\n            this._buffs.set(buff.id, buff);\n            this._buffsByType.get(buff.type)?.add(buff.id);\n            // 触发Buff应用效果\n            buff.onApply();\n            this._eventManager.emit(FightEvent.buffAdded, { buff, character: this._character });\n        }\n    }\n\n    /**\n     * 移除Buff\n     * @param buffId Buff ID\n     * @returns 是否移除成功\n     */\n    removeBuff(buffId: string): boolean {\n        const buff = this._buffs.get(buffId);\n        if (!buff) {\n            return false;\n        }\n        // 触发Buff移除效果\n        buff.onRemove();\n        // 从映射中移除\n        this._buffs.delete(buffId);\n        this._buffsByType.get(buff.type)?.delete(buffId);\n        this._eventManager.emit(FightEvent.buffRemoved, { buff, character: this._character });\n        return true;\n    }\n    /**\n     * 根据ID获取Buff\n     * @param buffId Buff ID\n     * @returns Buff实例或null\n     */\n    getBuff(buffId: string): IBuff | null {\n        return this._buffs.get(buffId) || null;\n    }\n    /**\n     * 根据类型获取Buff列表\n     * @param type Buff类型\n     * @returns Buff列表\n     */\n    getBuffsByType(type: BuffType): IBuff[] {\n        const buffIds = this._buffsByType.get(type);\n        if (!buffIds) {\n            return [];\n        }\n        const buffs: IBuff[] = [];\n        for (const buffId of buffIds) {\n            const buff = this._buffs.get(buffId);\n            if (buff) {\n                buffs.push(buff);\n            }\n        }\n        return buffs;\n    }\n    /**\n     * 检查是否拥有指定Buff\n     * @param buffId Buff ID\n     * @returns 是否拥有\n     */\n    hasBuff(buffId: string): boolean {\n        return this._buffs.has(buffId);\n    }\n    /**\n     * 检查是否拥有指定类型的Buff\n     * @param type Buff类型\n     * @returns 是否拥有\n     */\n    hasBuffOfType(type: BuffType): boolean {\n        const buffIds = this._buffsByType.get(type);\n        return buffIds ? buffIds.size > 0 : false;\n    }\n    /**\n     * 获取指定类型的Buff数量\n     * @param type Buff类型\n     * @returns Buff数量\n     */\n    getBuffCountByType(type: BuffType): number {\n        const buffIds = this._buffsByType.get(type);\n        return buffIds ? buffIds.size : 0;\n    }\n    /** * 清除所有Buff */\n    clearAllBuffs(): void {\n        const buffsToRemove = Array.from(this._buffs.keys());\n        for (const buffId of buffsToRemove) {\n            this.removeBuff(buffId);\n        }\n    }\n    /**\n     * 清除指定类型的Buff\n     * @param type Buff类型\n     */\n    clearBuffsByType(type: BuffType): void {\n        const buffIds = this._buffsByType.get(type);\n        if (buffIds) {\n            const idsToRemove = Array.from(buffIds);\n            for (const buffId of idsToRemove) {\n                this.removeBuff(buffId);\n            }\n        }\n    }\n    /**\n     * 刷新Buff持续时间\n     * @param buffId Buff ID\n     */\n    refreshBuff(buffId: string): void {\n        const buff = this._buffs.get(buffId);\n        if (buff) {\n            buff.refresh();\n            this._eventManager.emit(FightEvent.buffRefreshed, { buff, character: this._character });\n        }\n    }\n    /**\n     * 增加Buff叠加层数\n     * @param buffId Buff ID\n     * @param count 增加的层数\n     */\n    addBuffStack(buffId: string, count: number = 1): void {\n        const buff = this._buffs.get(buffId);\n        if (buff) {\n            buff.addStack(count);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff, character: this._character });\n        }\n    }\n    /**\n     * 减少Buff叠加层数\n     * @param buffId Buff ID\n     * @param count 减少的层数\n     */\n    removeBuffStack(buffId: string, count: number = 1): void {\n        const buff = this._buffs.get(buffId);\n        if (buff) {\n            buff.removeStack(count);\n            // 如果叠加层数为0，移除Buff\n            if (buff.stackCount <= 0) {\n                this.removeBuff(buffId);\n            } else {\n                this._eventManager.emit(FightEvent.buffStackChanged, { buff, character: this._character });\n            }\n        }\n    }\n    /**\n     * 更新所有Buff\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void {\n        const expiredBuffs: string[] = [];\n        for (const [buffId, buff] of this._buffs) {\n            // 更新Buff状态\n            const isExpired = buff.update(deltaTime);\n            if (isExpired) {\n                expiredBuffs.push(buffId);\n            } else {\n                // 触发Buff每帧效果\n                buff.onTick(deltaTime);\n            }\n        }\n        // 移除过期的Buff\n        for (const buffId of expiredBuffs) {\n            this.removeBuff(buffId);\n        }\n    }\n    /**\n     * 处理Buff叠加逻辑\n     * @param existingBuff 已存在的Buff\n     * @param newBuff 新的Buff\n     */\n    private handleBuffStack(existingBuff: IBuff, newBuff: IBuff): void {\n        if (existingBuff.maxStack > 1) {\n            // 可以叠加\n            existingBuff.addStack(newBuff.stackCount);\n            existingBuff.refresh(); // 刷新持续时间\n            this._eventManager.emit(FightEvent.buffStacked, {\n                buff: existingBuff,\n                character: this._character,\n                addedStacks: newBuff.stackCount\n            });\n        } else {\n            // 不能叠加，刷新持续时间\n            existingBuff.refresh();\n            this._eventManager.emit(FightEvent.buffRefreshed, {\n                buff: existingBuff,\n                character: this._character\n            });\n        }\n    }\n    /**\n     * 获取所有Buff的ID列表\n     * @returns Buff ID数组\n     */\n    getAllBuffIds(): string[] {\n        return Array.from(this._buffs.keys());\n    }\n\n    /**\n     * 获取所有Buff列表\n     * @returns Buff数组\n     */\n    getAllBuffs(): IBuff[] {\n        return Array.from(this._buffs.values());\n    }\n    /**\n     * 获取Buff统计信息\n     * @returns 统计信息\n     */\n    getBuffStats() {\n        const stats = {\n            totalBuffs: this._buffs.size,\n            buffsByType: {},\n            expiredBuffs: 0,\n            stackableBuffs: 0\n        };\n        for (const buff of this._buffs.values()) {\n            if (!stats.buffsByType[buff.type]) {\n                stats.buffsByType[buff.type] = 0;\n            }\n            stats.buffsByType[buff.type]++;\n            if (buff.isExpired) {\n                stats.expiredBuffs++;\n            }\n            if (buff.maxStack > 1) {\n                stats.stackableBuffs++;\n            }\n        }\n        return stats;\n    }\n\n    /**\n     * 导出Buff数据\n     * @returns Buff数据\n     */\n    exportBuffData() {\n        const data = {} as IBuff\n        for (const [id, buff] of this._buffs) {\n            data[id] = {\n                id: buff.id,\n                name: buff.name,\n                type: buff.type,\n                duration: buff.duration,\n                remainingTime: buff.remainingTime,\n                stackCount: buff.stackCount,\n                maxStack: buff.maxStack,\n                isExpired: buff.isExpired\n            };\n        }\n        return data;\n    }\n\n    /**\n     * 获取事件管理器（供外部直接使用，避免包装方法冗余）\n     */\n    get eventManager(): EventManager {\n        return this._eventManager;\n    }\n    /** * 清理Buff管理器 */\n    cleanup(): void {\n        this.clearAllBuffs();\n        this._buffsByType.clear();\n        this._eventManager.cleanup();\n    }\n    /** * 获取调试信息 */\n    getDebugInfo() {\n        return {\n            characterId: this._character.id,\n            buffCount: this._buffs.size,\n            buffs: this.exportBuffData(),\n            stats: this.getBuffStats()\n        };\n    }\n}\n"]}