
(function () {
var scripts = [{"deps":{"./assets/fight/buff/BuffModelBeHurtFight":1,"./assets/fight/buff/HealOverTimeBuff":50,"./assets/fight/buff/PoisonBuff":11,"./assets/fight/buff/StunBuff":13,"./assets/fight/buff/AttackBoostBuff":15,"./assets/fight/characters/BaseCharacter":12,"./assets/fight/characters/Character":3,"./assets/fight/characters/CharacterAttributes":16,"./assets/fight/characters/AttributeModifier":14,"./assets/fight/examples/SkillAndBuffExample":5,"./assets/fight/examples/BattleTestScene":17,"./assets/fight/skills/HealingLightSkill":4,"./assets/fight/skills/PlayerSkillFire1":18,"./assets/fight/skills/ThunderStormSkill":51,"./assets/fight/skills/ChargeAttackSkill":22,"./assets/fight/systems/BuffManager":6,"./assets/fight/systems/BulletManager":23,"./assets/fight/systems/BulletSystem":19,"./assets/fight/systems/DamageManager":21,"./assets/fight/systems/EventManager":20,"./assets/fight/systems/SkillManager":25,"./assets/fight/systems/TimelineManager":24,"./assets/fight/systems/BattleManager":52,"./assets/fight/timeline/TimelineEvents":7,"./assets/fight/timeline/Timeline":27,"./assets/fight/types/CharacterTypes":8,"./assets/fight/types/FightEvent":28,"./assets/fight/types/IBuff":29,"./assets/fight/types/IBullet":26,"./assets/fight/types/ICharacter":35,"./assets/fight/types/ICharacterAttributes":30,"./assets/fight/types/IDamage":45,"./assets/fight/types/ISkill":38,"./assets/fight/types/ITimeline":40,"./assets/fight/types/SkillName":36,"./assets/fight/types/Buff":39,"./assets/fight/actions/MoveAction":9,"./assets/card/cardgame/src/EquipmentManager":10,"./assets/card/cardgame/src/EventManager":43,"./assets/card/cardgame/src/FightManager":31,"./assets/card/cardgame/src/RoleManager":33,"./assets/card/cardgame/src/SkillManager":34,"./assets/card/cardgame/src/BuffManager":32,"./assets/card/cardgame/src/base/CampBase":37,"./assets/card/cardgame/src/base/CardBase":49,"./assets/card/cardgame/src/base/CardModel":2,"./assets/card/cardgame/src/base/Equipment":44,"./assets/card/cardgame/src/base/RoleBase":47,"./assets/card/cardgame/src/base/SkillBase":54,"./assets/card/cardgame/src/base/TimeModel":48,"./assets/card/cardgame/src/base/BuffBase":53,"./assets/card/cardgame/src/CampManager":46,"./assets/card/cardgame/src/CardManager":41,"./assets/fight/actions/AttackAction":42},"path":"preview-scripts/__qc_index__.js"},{"deps":{"../systems/EventManager":20,"../types/CharacterTypes":8,"../types/FightEvent":28,"../types/IBuff":29,"../types/ICharacterAttributes":30,"../types/IDamage":45,"../characters/AttributeModifier":14,"../types/Buff":39},"path":"preview-scripts/assets/fight/buff/BuffModelBeHurtFight.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/CardModel.js"},{"deps":{"../actions/AttackAction":42,"../actions/MoveAction":9,"../types/CharacterTypes":8,"../types/FightEvent":28,"./BaseCharacter":12,"./CharacterAttributes":16},"path":"preview-scripts/assets/fight/characters/Character.js"},{"deps":{"../timeline/Timeline":27,"../timeline/TimelineEvents":7,"../types/ISkill":38,"../types/SkillName":36,"../types/ITimeline":40,"../systems/BattleManager":52},"path":"preview-scripts/assets/fight/skills/HealingLightSkill.js"},{"deps":{"../systems/BattleManager":52,"../characters/Character":3,"../types/CharacterTypes":8,"../skills/PlayerSkillFire1":18,"../skills/HealingLightSkill":4,"../skills/ThunderStormSkill":51,"../skills/ChargeAttackSkill":22,"../buff/AttackBoostBuff":15,"../buff/HealOverTimeBuff":50,"../buff/StunBuff":13,"../buff/PoisonBuff":11},"path":"preview-scripts/assets/fight/examples/SkillAndBuffExample.js"},{"deps":{"../types/FightEvent":28,"../types/IBuff":29,"./EventManager":20},"path":"preview-scripts/assets/fight/systems/BuffManager.js"},{"deps":{"../types/IDamage":45,"../types/ITimeline":40,"./Timeline":27,"../buff/BuffModelBeHurtFight":1,"../systems/BattleManager":52},"path":"preview-scripts/assets/fight/timeline/TimelineEvents.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/CharacterTypes.js"},{"deps":{"../types/FightEvent":28},"path":"preview-scripts/assets/fight/actions/MoveAction.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/EquipmentManager.js"},{"deps":{"../systems/EventManager":20,"../types/FightEvent":28,"../types/IBuff":29,"../types/ICharacterAttributes":30,"../characters/AttributeModifier":14,"../types/Buff":39},"path":"preview-scripts/assets/fight/buff/PoisonBuff.js"},{"deps":{"./CharacterAttributes":16,"../systems/EventManager":20,"../systems/SkillManager":25,"../systems/BuffManager":6,"../types/FightEvent":28,"../types/IDamage":45,"../types/CharacterTypes":8},"path":"preview-scripts/assets/fight/characters/BaseCharacter.js"},{"deps":{"../systems/EventManager":20,"../types/FightEvent":28,"../types/IBuff":29,"../types/ICharacterAttributes":30,"../characters/AttributeModifier":14,"../types/Buff":39},"path":"preview-scripts/assets/fight/buff/StunBuff.js"},{"deps":{"../types/ICharacterAttributes":30},"path":"preview-scripts/assets/fight/characters/AttributeModifier.js"},{"deps":{"../systems/EventManager":20,"../types/FightEvent":28,"../types/IBuff":29,"../types/ICharacterAttributes":30,"../characters/AttributeModifier":14,"../types/Buff":39},"path":"preview-scripts/assets/fight/buff/AttackBoostBuff.js"},{"deps":{"../systems/EventManager":20,"../types/CharacterTypes":8,"../types/FightEvent":28},"path":"preview-scripts/assets/fight/characters/CharacterAttributes.js"},{"deps":{"../buff/BuffModelBeHurtFight":1,"../characters/Character":3,"../skills/PlayerSkillFire1":18,"../systems/BattleManager":52,"../timeline/Timeline":27,"../timeline/TimelineEvents":7,"../types/CharacterTypes":8,"../types/IDamage":45,"../types/SkillName":36},"path":"preview-scripts/assets/fight/examples/BattleTestScene.js"},{"deps":{"../systems/BattleManager":52,"../systems/BulletSystem":19,"../timeline/Timeline":27,"../timeline/TimelineEvents":7,"../types/IBullet":26,"../types/IDamage":45,"../types/ISkill":38,"../types/SkillName":36},"path":"preview-scripts/assets/fight/skills/PlayerSkillFire1.js"},{"deps":{"./EventManager":20,"../types/IBullet":26,"../types/FightEvent":28},"path":"preview-scripts/assets/fight/systems/BulletSystem.js"},{"deps":{},"path":"preview-scripts/assets/fight/systems/EventManager.js"},{"deps":{"./EventManager":20,"../types/FightEvent":28,"../types/IDamage":45},"path":"preview-scripts/assets/fight/systems/DamageManager.js"},{"deps":{"../timeline/TimelineEvents":7,"../timeline/Timeline":27,"../types/ISkill":38,"../types/ITimeline":40,"../types/SkillName":36,"../types/IDamage":45,"../buff/AttackBoostBuff":15,"../systems/BattleManager":52},"path":"preview-scripts/assets/fight/skills/ChargeAttackSkill.js"},{"deps":{"./EventManager":20,"../types/FightEvent":28,"./BulletSystem":19},"path":"preview-scripts/assets/fight/systems/BulletManager.js"},{"deps":{"../timeline/Timeline":27,"../types/FightEvent":28,"./EventManager":20},"path":"preview-scripts/assets/fight/systems/TimelineManager.js"},{"deps":{"../types/FightEvent":28,"../types/ISkill":38,"./EventManager":20},"path":"preview-scripts/assets/fight/systems/SkillManager.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/IBullet.js"},{"deps":{"../systems/EventManager":20,"../types/FightEvent":28},"path":"preview-scripts/assets/fight/timeline/Timeline.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/FightEvent.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/IBuff.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/ICharacterAttributes.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/FightManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/BuffManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/RoleManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/SkillManager.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/ICharacter.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/SkillName.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/CampBase.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/ISkill.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/Buff.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/ITimeline.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/CardManager.js"},{"deps":{"../types/CharacterTypes":8,"../types/FightEvent":28},"path":"preview-scripts/assets/fight/actions/AttackAction.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/EventManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/Equipment.js"},{"deps":{},"path":"preview-scripts/assets/fight/types/IDamage.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/CampManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/RoleBase.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/TimeModel.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/CardBase.js"},{"deps":{"../systems/EventManager":20,"../types/Buff":39,"../types/FightEvent":28,"../types/IBuff":29},"path":"preview-scripts/assets/fight/buff/HealOverTimeBuff.js"},{"deps":{"../timeline/Timeline":27,"../timeline/TimelineEvents":7,"../types/IDamage":45,"../types/ISkill":38,"../types/ITimeline":40,"../types/SkillName":36,"../buff/StunBuff":13,"../systems/BattleManager":52},"path":"preview-scripts/assets/fight/skills/ThunderStormSkill.js"},{"deps":{"./TimelineManager":24,"./BulletManager":23,"./EventManager":20,"../types/FightEvent":28,"../types/CharacterTypes":8,"./DamageManager":21},"path":"preview-scripts/assets/fight/systems/BattleManager.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/BuffBase.js"},{"deps":{},"path":"preview-scripts/assets/card/cardgame/src/base/SkillBase.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    