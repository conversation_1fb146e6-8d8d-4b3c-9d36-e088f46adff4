{"version": 3, "sources": ["assets\\fight\\skills\\ChargeAttackSkill.ts"], "names": [], "mappings": ";;;;;;;AACA,iDAA8D;AAC9D,6DAAyH;AAGzH,4CAA8C;AAC9C,0CAAqE;AACrE,gDAAkF;AAClF,gDAA2C;AAC3C,2DAA0D;AAC1D,0DAAyD;AAEzD;;;GAGG;AACH;IAAA;QACY,QAAG,GAAW,mBAAS,CAAC,aAAa,CAAC;QACtC,UAAK,GAAW,MAAM,CAAC;QACvB,iBAAY,GAAW,0BAA0B,CAAC;QAClD,cAAS,GAAW,GAAG,CAAC;QACxB,uBAAkB,GAAW,CAAC,CAAC;QAC/B,YAAO,GAAW,CAAC,CAAC;QACpB,iBAAY,GAAW,EAAE,CAAC;QAC1B,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAc,kBAAS,CAAC,MAAM,CAAC;QACpC,gBAAW,GAAoB,wBAAe,CAAC,YAAY,CAAC;QAC5D,WAAM,GAAW,GAAG,CAAC;QACrB,cAAS,GAAqB,IAAI,CAAC;QACnC,kBAAa,GAAY,EAAE,CAAC;QAEpC,WAAW;QACH,YAAO,GAAG;YACd,aAAa,EAAE,cAAc;YAC7B,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE,6BAA6B;YACzC,aAAa,EAAE,8BAA8B;YAC7C,MAAM,EAAE,CAAC;YACT,gBAAgB,EAAE,GAAG;YACrB,WAAW,EAAE,GAAG;YAChB,kBAAkB,EAAE,GAAG;YACvB,oBAAoB,EAAE,GAAG,CAAC,UAAU;SACvC,CAAC;IA0IN,CAAC;IAvIG,sBAAI,iCAAE;QADN,aAAa;aACb,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,uCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,gDAAiB;aAArB,cAAkC,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;aACnE,UAAsB,KAAa,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAEnE,sBAAI,qCAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7C,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,mCAAI;aAAR,cAAwB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC5C,sBAAI,yCAAU;aAAd,cAAoC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAC9D,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,uCAAQ;aAAZ,cAA4B,OAAO,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC;;;OAAA;IACrD,sBAAI,2CAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAI,qCAAM;aAAV;YACI,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QACxC,CAAC;;;OAAA;IAED,oBAAoB;IACpB,qCAAS,GAAT,UAAU,MAAkB,EAAE,MAAmB;QAC7C,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAC1B,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAChC,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC,CAAC,OAAO;QACrD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACb,6CAAiB,GAAjB,UAAkB,MAAkB;QAChC,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO;YAC9C,MAAM,CAAC,UAAU,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC;IAC9D,CAAC;IAED,WAAW;IACX,4CAAgB,GAAhB,UAAiB,MAAkB;QAC/B,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,WAAW;IACX,gCAAI,GAAJ,UAAK,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACpF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxE,6BAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,qBAAe,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAE,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,mBAAmB;IACX,0CAAc,GAAtB,UAAuB,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACtG,IAAM,UAAU,GAAM,IAAI,CAAC,GAAG,SAAI,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QAC9F,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAEnE,iBAAiB;QACjB,IAAM,WAAW,GAAG,IAAI,uBAAY,CAAI,UAAU,aAAU,EAAE,GAAG,EAAE,IAAI,2CAA0B,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,CAAC;QACjJ,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9B,eAAe;QACf,IAAM,eAAe,GAAG,IAAI,uBAAY,CAAI,UAAU,kBAAe,EAAE,GAAG,EAAE,IAAI,mBAAmB,CAAC,iBAAiB,EAAE,MAAM,EAAE,MAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1K,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAClC,oBAAoB;QACpB,IAAM,SAAS,GAAG,IAAI,uBAAY,CAAI,UAAU,WAAQ,EAAE,GAAG,EAAE,IAAI,uCAAsB,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;QACxI,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5B,IAAM,SAAS,GAAG,IAAI,uBAAY,CAAI,UAAU,WAAQ,EAAE,GAAG,EAAE,IAAI,wCAAuB,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;QACzK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5B,eAAe;QACf,IAAM,UAAU,GAAG,IAAI,uBAAY,CAAI,UAAU,YAAS,EAAE,GAAG,EAAE,IAAI,iBAAiB,CAAC,eAAe,EAAE,MAAM,EAAE,MAAO,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/J,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7B,eAAe;QACf,IAAM,aAAa,GAAG,IAAI,uBAAY,CAAI,UAAU,gBAAa,EAAE,GAAG,EAAE,IAAI,wCAAuB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC,QAAQ,KAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACnM,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAChC,oBAAoB;QACpB,IAAM,QAAQ,GAAG,IAAI,uBAAY,CAAI,UAAU,UAAO,EAAE,GAAG,EAAE,IAAI,kBAAkB,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC,CAAC;QACvL,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,WAAW;IACH,2CAAe,GAAvB,UAAwB,MAAkB;QACtC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SAC9B;QACD,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAClE,CAAC;IAED,aAAa;IACb,kCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC;SACxC;IACL,CAAC;IAED,aAAa;IACb,yCAAa,GAAb;QACI,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,WAAW;IACX,mCAAO,GAAP;QACI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,uBAAkB,IAAI,CAAC,MAAQ,CAAC,CAAC;IAC9D,CAAC;IAED,aAAa;IACb,wCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAC/C,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;SACxC,CAAC;IACN,CAAC;IACL,wBAAC;AAAD,CApKA,AAoKC,IAAA;AApKY,8CAAiB;AAsK9B,aAAa;AACb;IAOI,6BAAY,EAAU,EAAE,MAAkB,EAAE,MAAkB,EAAE,KAAa;QALrE,UAAK,GAAsB,6BAAiB,CAAC,IAAI,CAAC;QAMtD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,sBAAI,mCAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,qCAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEpD,qCAAO,GAAP,UAAQ,SAAoB,EAAE,UAAkB,EAAE,QAAc;QAC5D,YAAY;QACZ,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC5C,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC7C,IAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;QAC3D,kBAAkB;QAClB,IAAM,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW;QAC9E,IAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,uBAAkB,QAAQ,CAAC,CAAC,UAAK,QAAQ,CAAC,CAAC,cAAS,MAAM,CAAC,CAAC,UAAK,MAAM,CAAC,CAAC,MAAG,CAAC,CAAC;QACvH,4BAA4B;QAC5B,8BAA8B;QAC9B,qCAAqC;QACrC,gBAAgB;IACpB,CAAC;IACL,0BAAC;AAAD,CA/BA,AA+BC,IAAA;AAED,aAAa;AACb;IAOI,2BAAY,EAAU,EAAE,MAAkB,EAAE,MAAkB,EAAE,MAAc;QALtE,UAAK,GAAsB,6BAAiB,CAAC,MAAM,CAAC;QAMxD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,sBAAI,iCAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEpD,mCAAO,GAAP,UAAQ,SAAoB,EAAE,UAAkB,EAAE,QAAc;QAC5D,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,IAAM,UAAU,GAAG;gBACf,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,IAAI,EAAE,oBAAU,CAAC,QAAQ;gBACzB,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;gBAC/B,OAAO,EAAE,UAAU;aACtB,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,wBAAmB,IAAI,CAAC,OAAO,CAAC,aAAa,aAAQ,IAAI,CAAC,OAAO,qBAAkB,CAAC,CAAC;SACjI;IACL,CAAC;IACL,wBAAC;AAAD,CA9BA,AA8BC,IAAA;AAED,oBAAoB;AACpB;IAOI,4BAAY,EAAU,EAAE,MAAkB,EAAE,QAAgB,EAAE,UAAkB;QALxE,UAAK,GAAsB,6BAAiB,CAAC,QAAQ,CAAC;QAM1D,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED,sBAAI,kCAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,oCAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEpD,oCAAO,GAAP,UAAQ,SAAoB,EAAE,UAAkB,EAAE,QAAc;QAC5D,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,6BAAwB,IAAI,CAAC,WAAW,eAAU,IAAI,CAAC,SAAS,aAAU,CAAC,CAAC;QAErH,oBAAoB;QACpB,IAAM,UAAU,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACrG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IACL,yBAAC;AAAD,CAxBA,AAwBC,IAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TimelineManager } from \"../systems/TimelineManager\";\nimport { Timeline, TimelineNode } from \"../timeline/Timeline\";\nimport { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from \"../timeline/TimelineEvents\";\nimport { IBuff } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { DamageType } from \"../types/IDamage\";\nimport { ISkill, SkillType, SkillTargetType } from \"../types/ISkill\";\nimport { ITimeline, ITimelineEvent, TimelineEventType } from \"../types/ITimeline\";\nimport SkillName from \"../types/SkillName\";\nimport { AttackBoostBuff } from \"../buff/AttackBoostBuff\";\nimport { BattleManager } from \"../systems/BattleManager\";\n\n/**\n * 冲锋攻击技能\n * 快速冲向目标并造成物理伤害，同时获得短暂的攻击力提升\n */\nexport class ChargeAttackSkill implements ISkill {\n    private _id: string = SkillName.charge_attack;\n    private _name: string = \"冲锋攻击\";\n    private _description: string = \"快速冲向敌人并发动强力攻击，攻击后获得攻击力提升\";\n    private _cooldown: number = 5.0;\n    private _remainingCooldown: number = 0;\n    private _mpCost: number = 0;\n    private _staminaCost: number = 25;\n    private _level: number = 1;\n    private _type: SkillType = SkillType.ACTIVE;\n    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ENEMY;\n    private _range: number = 300;\n    private _timeline: ITimeline | null = null;\n    private _passiveBuffs: IBuff[] = [];\n\n    /** 技能配置 */\n    private _config = {\n        animationName: \"skill_charge\",\n        soundId: \"charge_attack\",\n        effectPath: \"prefabs/effects/ChargeTrail\",\n        hitEffectPath: \"prefabs/effects/ChargeImpact\",\n        damage: 0, // 0表示使用施法者的攻击力\n        damageMultiplier: 1.8, // 伤害倍率\n        chargeSpeed: 800, // 冲锋速度\n        attackBuffDuration: 4.0, // 攻击力提升持续时间\n        attackBuffMultiplier: 1.3 // 攻击力提升倍率\n    };\n\n    // 实现ISkill接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get cooldown(): number { return this._cooldown; }\n    get remainingCooldown(): number { return this._remainingCooldown; }\n    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }\n    get mpCost(): number { return this._mpCost; }\n    get staminaCost(): number { return this._staminaCost; }\n    get level(): number { return this._level; }\n    get type(): SkillType { return this._type; }\n    get targetType(): SkillTargetType { return this._targetType; }\n    get range(): number { return this._range; }\n    get timeline(): ITimeline { return this._timeline!; }\n    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }\n    get canUse(): boolean {\n        return this._remainingCooldown <= 0;\n    }\n\n    /** 检查是否可以对目标使用技能 */\n    canCastOn(caster: ICharacter, target?: ICharacter): boolean {\n        if (!target) return false;\n        if (target.isDead) return false;\n        if (target.role !== caster.role) return true; // 敌对阵营\n        return false;\n    }\n\n    /** 检查资源消耗 */\n    checkResourceCost(caster: ICharacter): boolean {\n        return caster.attributes.currentMp >= this._mpCost &&\n            caster.attributes.currentStamina >= this._staminaCost;\n    }\n\n    /** 消耗资源 */\n    consumeResources(caster: ICharacter): void {\n        caster.attributes.consumeMp(this._mpCost);\n        caster.attributes.consumeStamina(this._staminaCost);\n    }\n\n    /** 释放技能 */\n    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {\n        if (!this.canCastOn(caster, target)) {\n            return false;\n        }\n        if (!this.checkResourceCost(caster)) {\n            return false;\n        }\n\n        this.consumeResources(caster);\n        this._remainingCooldown = this._cooldown;\n        this._timeline = this.createTimeline(caster, target, targets, position);\n        BattleManager.instance.timelineManager.addTimeline(this._timeline);\n        console.log(`${caster.characterName} charges at ${target?.characterName}`);\n        return true;\n    }\n\n    /** 创建技能Timeline */\n    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {\n        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        const timeline = new Timeline(timelineId, this._name, 2.5, caster);\n\n        // 0.0s: 播放冲锋准备动画\n        const prepareNode = new TimelineNode(`${timelineId}_prepare`, 0.0, new PlayAnimationTimelineEvent(\"prepare_animation\", \"charge_prepare\"), false);\n        timeline.addNode(prepareNode);\n        // 0.3s: 开始冲锋移动\n        const chargeStartNode = new TimelineNode(`${timelineId}_charge_start`, 0.3, new ChargeMovementEvent(\"charge_movement\", caster, target!, this._config.chargeSpeed), false);\n        timeline.addNode(chargeStartNode);\n        // 0.4s: 播放冲锋音效和拖尾特效\n        const soundNode = new TimelineNode(`${timelineId}_sound`, 0.4, new PlaySoundTimelineEvent(\"charge_sound\", this._config.soundId), false);\n        timeline.addNode(soundNode);\n        const trailNode = new TimelineNode(`${timelineId}_trail`, 0.4, new PlayEffectTimelineEvent(\"charge_trail\", this._config.effectPath, false, caster.node.position), false);\n        timeline.addNode(trailNode);\n        // 1.0s: 冲锋攻击命中\n        const attackNode = new TimelineNode(`${timelineId}_attack`, 1.0, new ChargeAttackEvent(\"charge_attack\", caster, target!, this.calculateDamage(caster)), false);\n        timeline.addNode(attackNode);\n        // 1.1s: 播放命中特效\n        const hitEffectNode = new TimelineNode(`${timelineId}_hit_effect`, 1.1, new PlayEffectTimelineEvent(\"hit_effect\", this._config.hitEffectPath, true, target?.node.position || cc.Vec3.ZERO), false);\n        timeline.addNode(hitEffectNode);\n        // 1.2s: 添加攻击力提升buff\n        const buffNode = new TimelineNode(`${timelineId}_buff`, 1.2, new AddAttackBuffEvent(\"attack_buff\", caster, this._config.attackBuffDuration, this._config.attackBuffMultiplier), false);\n        timeline.addNode(buffNode);\n        return timeline;\n    }\n\n    /** 计算伤害 */\n    private calculateDamage(caster: ICharacter): number {\n        if (this._config.damage > 0) {\n            return this._config.damage;\n        }\n        const baseAttack = caster.attributes.attack;\n        return Math.floor(baseAttack * this._config.damageMultiplier);\n    }\n\n    /** 更新技能冷却 */\n    update(deltaTime: number): void {\n        if (this._remainingCooldown > 0) {\n            this._remainingCooldown -= deltaTime;\n        }\n    }\n\n    /** 重置冷却时间 */\n    resetCooldown(): void {\n        this._remainingCooldown = 0;\n    }\n\n    /** 升级技能 */\n    levelUp(): void {\n        this._level++;\n        this._staminaCost = Math.max(15, this._staminaCost - 2);\n        this._cooldown = Math.max(3.0, this._cooldown - 0.3);\n        this._range += 25;\n        this._config.damageMultiplier += 0.15;\n        this._config.attackBuffMultiplier += 0.05;\n        this._config.chargeSpeed += 50;\n        console.log(`${this._name} leveled up to ${this._level}`);\n    }\n\n    /** 获取技能信息 */\n    getSkillInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            description: this._description,\n            level: this._level,\n            cooldown: this._cooldown,\n            remainingCooldown: this._remainingCooldown,\n            mpCost: this._mpCost,\n            staminaCost: this._staminaCost,\n            range: this._range,\n            canUse: this.canUse,\n            type: this._type,\n            targetType: this._targetType,\n            damageMultiplier: this._config.damageMultiplier,\n            chargeSpeed: this._config.chargeSpeed\n        };\n    }\n}\n\n/*** 冲锋移动事件*/\nclass ChargeMovementEvent implements ITimelineEvent {\n    private _id: string;\n    private _type: TimelineEventType = TimelineEventType.MOVE;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _speed: number;\n\n    constructor(id: string, caster: ICharacter, target: ICharacter, speed: number) {\n        this._id = id;\n        this._caster = caster;\n        this._target = target;\n        this._speed = speed;\n    }\n\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n\n    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {\n        // 计算冲锋方向和距离\n        const startPos = this._caster.node.position;\n        const targetPos = this._target.node.position;\n        const direction = targetPos.subtract(startPos).normalize();\n        // 冲锋到目标附近（保持一定距离）\n        const chargeDistance = cc.Vec3.distance(startPos, targetPos) - 50; // 保持50单位距离\n        const endPos = startPos.add(direction.multiplyScalar(chargeDistance));\n        console.log(`${this._caster.characterName} charges from (${startPos.x}, ${startPos.y}) to (${endPos.x}, ${endPos.y})`);\n        // 这里应该实现实际的移动逻辑，比如使用Tween动画\n        // cc.tween(this._caster.node)\n        //     .to(0.7, { position: endPos })\n        //     .start();\n    }\n}\n\n/*** 冲锋攻击事件*/\nclass ChargeAttackEvent implements ITimelineEvent {\n    private _id: string;\n    private _type: TimelineEventType = TimelineEventType.DAMAGE;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _damage: number;\n\n    constructor(id: string, caster: ICharacter, target: ICharacter, damage: number) {\n        this._id = id;\n        this._caster = caster;\n        this._target = target;\n        this._damage = damage;\n    }\n\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n\n    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {\n        if (this._target && !this._target.isDead) {\n            const damageInfo = {\n                amount: this._damage,\n                type: DamageType.PHYSICAL,\n                source: this._caster,\n                isCritical: Math.random() < 0.2, // 20%暴击率\n                element: \"physical\"\n            };\n            this._target.takeDamageSimple(damageInfo.amount, this._caster);\n            console.log(`${this._caster.characterName} charge attacks ${this._target.characterName} for ${this._damage} physical damage`);\n        }\n    }\n}\n\n/*** 添加攻击力提升buff事件*/\nclass AddAttackBuffEvent implements ITimelineEvent {\n    private _id: string;\n    private _type: TimelineEventType = TimelineEventType.ADD_BUFF;\n    private _caster: ICharacter;\n    private _duration: number;\n    private _multiplier: number;\n\n    constructor(id: string, caster: ICharacter, duration: number, multiplier: number) {\n        this._id = id;\n        this._caster = caster;\n        this._duration = duration;\n        this._multiplier = multiplier;\n    }\n\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n\n    execute(_timeline: ITimeline, _nodeIndex: number, _context?: any): void {\n        console.log(`${this._caster.characterName} gains attack boost (${this._multiplier}x) for ${this._duration} seconds`);\n\n        // 实现AttackBoostBuff\n        const attackBuff = new AttackBoostBuff(this._caster, this._caster, this._duration, this._multiplier);\n        this._caster.buffManager.addBuff(attackBuff);\n    }\n}\n"]}