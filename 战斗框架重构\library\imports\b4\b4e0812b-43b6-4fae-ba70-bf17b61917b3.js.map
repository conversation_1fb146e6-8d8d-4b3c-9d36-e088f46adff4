{"version": 3, "sources": ["assets\\fight\\types\\ICharacter.ts"], "names": [], "mappings": "", "file": "", "sourceRoot": "/", "sourcesContent": ["import { CharacterRole } from \"../types/CharacterTypes\";\nimport { ISkill } from \"./ISkill\";\nimport { IBuff } from \"./IBuff\";\nimport { ICharacterAttributes } from \"./ICharacterAttributes\";\nimport { IDamageInfo } from \"./IDamage\";\nimport { SkillManager } from \"../systems/SkillManager\";\nimport { BuffManager } from \"../systems/BuffManager\";\n\n/*** 角色核心接口*/\nexport interface ICharacter {\n    /** 角色唯一ID */\n    readonly id: string;\n    /** 角色名称 */\n    readonly characterName: string;\n    /** 角色阵营 */\n    readonly role: CharacterRole;\n    /** 角色属性 */\n    readonly attributes: ICharacterAttributes;\n    /** 是否死亡 */\n    readonly isDead: boolean;\n    /** 角色节点 */\n    readonly node: cc.Node;\n    /** 技能列表 */\n    readonly skills: ReadonlyArray<ISkill>;\n    /** Buff列表 */\n    readonly buffs: ReadonlyArray<IBuff>;\n    /** 技能管理器 */\n    readonly skillManager: SkillManager;\n    /** Buff管理器 */\n    readonly buffManager: BuffManager;\n    /**\n     * 移动角色\n     * @param direction 移动方向\n     * @returns 是否移动成功\n     */\n    move(direction: cc.Vec3): boolean;\n    /**\n     * 攻击目标\n     * @param target 攻击目标\n     * @returns 是否攻击成功\n     */\n    attack(target?: ICharacter): boolean;\n    /**\n     * 释放技能\n     * @param skillName 技能名称\n     * @param target 目标\n     * @returns 是否释放成功\n     */\n    castSkill(skillName: string, target?: cc.Node): boolean;\n    /**\n     * 学习技能\n     * @param skill 技能\n     */\n    learnSkill(skill: ISkill): void;\n    /**\n     * 添加Buff\n     * @param buff Buff信息\n     */\n    addBuff(buff: IBuff): void;\n    /**\n     * 移除Buff\n     * @param buffId Buff ID\n     */\n    removeBuff(buffId: string): void;\n    /**\n     * 受到伤害\n     * @param damageInfo 伤害信息对象\n     */\n    takeDamage(damageInfo: IDamageInfo): void;\n\n    /**\n     * 受到伤害（简化版本，向后兼容）\n     * @param damage 伤害值\n     * @param attacker 攻击者\n     * @deprecated 建议使用 takeDamage(damageInfo: IDamageInfo) 版本\n     */\n    takeDamageSimple(damage: number, attacker?: ICharacter): void;\n    /**\n     * 治疗\n     * @param healAmount 治疗量\n     */\n    heal(healAmount: number): void;\n    /** * 死亡处理 */\n    die(): void;\n    /**\n     * 更新角色状态\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n}\n\n/*** 角色事件接口*/\nexport interface ICharacterEvents {\n    /** 角色死亡事件 */\n    onDeath?: (character: ICharacter) => void;\n    /** 角色受到伤害事件 */\n    onTakeDamage?: (character: ICharacter, damage: number, attacker?: ICharacter) => void;\n    /** 角色治疗事件 */\n    onHeal?: (character: ICharacter, healAmount: number) => void;\n    /** 技能释放事件 */\n    onSkillCast?: (character: ICharacter, skillName: string) => void;\n    /** Buff添加事件 */\n    onBuffAdded?: (character: ICharacter, buff: IBuff) => void;\n    /** Buff移除事件 */\n    onBuffRemoved?: (character: ICharacter, buffId: string) => void;\n}\n\n/** * 角色控制器接口 */\nexport interface ICharacterController {\n    /** 控制的角色 */\n    readonly character: ICharacter;\n    /**\n     * 初始化控制器\n     * @param character 要控制的角色\n     */\n    initialize(character: ICharacter): void;\n    /**\n     * 更新控制逻辑\n     * @param deltaTime 时间间隔\n     */\n    update(deltaTime: number): void;\n    /** * 清理控制器 */\n    cleanup(): void;\n}\n"]}