{"version": 3, "sources": ["assets\\fight\\buff\\HealOverTimeBuff.ts"], "names": [], "mappings": ";;;;;;;AAAA,wDAAuD;AACvD,sCAAgD;AAChD,kDAA6C;AAC7C,wCAA8F;AAI9F;;;GAGG;AACH;IAsBI,0BAAY,MAAkB,EAAE,MAAkB,EAAE,QAAgB,EAAE,aAAqB;QApBnF,UAAK,GAAW,MAAM,CAAC;QACvB,iBAAY,GAAW,SAAS,CAAC;QACjC,UAAK,GAAa,gBAAQ,CAAC,IAAI,CAAC;QAGhC,gBAAW,GAAW,CAAC,CAAC;QACxB,cAAS,GAAW,CAAC,CAAC;QAGtB,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAyB,EAAE,CAAC;QAI/C,kBAAa,GAAW,GAAG,CAAC,CAAC,SAAS;QACtC,kBAAa,GAAW,CAAC,CAAC;QAClC,OAAO;QACC,cAAS,GAAW,4BAA4B,CAAC;QACjD,sBAAiB,GAAW,0BAA0B,CAAC;QAG3D,IAAI,CAAC,GAAG,GAAG,oBAAkB,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QACzF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,2BAAY,CAAC,WAAW,CAAC,UAAQ,IAAI,CAAC,GAAK,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,GAAG,6BAAO,aAAa,kDAAU,QAAQ,WAAG,CAAC;IAClE,CAAC;IAGD,sBAAI,gCAAE;QADN,YAAY;aACZ,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,kCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,yCAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,kCAAI;aAAR,cAAuB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,sCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,sCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,oCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,oCAAM;aAAV,cAA2B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,uCAAS;aAAb,cAA2B,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAChF,sBAAI,gDAAkB;aAAtB,cAA8D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;;;OAAA;IAChG,sBAAI,sCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,8CAAgB;aAApB,cAAiC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;OAAA;IACjE,sBAAI,2CAAa;aAAjB,cAA8B,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;aAC3D,UAAkB,KAAa,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAE3D,sBAAI,wCAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;aACrD,UAAe,KAAa,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;;OAD7C;IAGrD,sBAAI,4CAAc;aAAlB;YACI,OAAO;gBACH,QAAQ,EAAE,IAAI,CAAC,aAAa;gBAC5B,UAAU,EAAE,8BAAsB,CAAC,cAAc;gBACjD,KAAK,EAAE,IAAI,CAAC,cAAc;gBAC1B,SAAS,EAAE,IAAI;aAClB,CAAC;QACN,CAAC;;;OAAA;IAED,iBAAiB;IACjB,kCAAO,GAAP;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,oBAAe,IAAI,CAAC,OAAO,CAAC,aAAa,iBAAY,IAAI,CAAC,WAAW,MAAG,CAAC,CAAC;QACnG,SAAS;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,kBAAkB;IAClB,iCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;QAChC,aAAa;QACb,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EAAE;YAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;SAC1B;IACL,CAAC;IAED,iBAAiB;IACjB,mCAAQ,GAAR;QACI,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACxE,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,WAAW;IACH,sCAAW,GAAnB;QACI,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,oBAAe,SAAS,iBAAY,IAAI,CAAC,KAAO,CAAC,CAAC;YAC3F,SAAS;YACT,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,SAAS;YACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;SACxI;IACL,CAAC;IAED,eAAe;IACf,iCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,cAAc,IAAI,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,iBAAiB;IACjB,kCAAO,GAAP;QACI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,sBAAiB,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;IAC5E,CAAC;IAED,aAAa;IACb,mCAAQ,GAAR,UAAS,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,aAAa;IACb,sCAAW,GAAX,UAAY,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,4BAAuB,IAAI,CAAC,WAAW,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAU,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;SAC9F;IACL,CAAC;IAED,mBAAmB;IACnB,yCAAc,GAAd,UAAe,UAAkB;QAC7B,QAAQ,UAAU,EAAE;YAChB,KAAK,sBAAe,CAAC,eAAe;gBAChC,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;YAClD,KAAK,sBAAe,CAAC,UAAU;gBAC3B,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACxE;gBACI,OAAO,CAAC,CAAC;SAChB;IACL,CAAC;IAED,yBAAyB;IACzB,wCAAa,GAAb,UAAc,UAAiB;QAC3B,uBAAuB;QACvB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACL,0CAAe,GAAvB;QACI,OAAO,CAAC,GAAG,CAAC,8BAA4B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACvF,kBAAkB;IACtB,CAAC;IAED,aAAa;IACL,yCAAc,GAAtB;QACI,OAAO,CAAC,GAAG,CAAC,6BAA2B,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QACtF,oBAAoB;IACxB,CAAC;IAED,WAAW;IACH,qCAAU,GAAlB;QACI,OAAO,CAAC,GAAG,CAAC,yBAAuB,IAAI,CAAC,KAAK,YAAO,IAAI,CAAC,OAAO,CAAC,aAAe,CAAC,CAAC;QAClF,kBAAkB;IACtB,CAAC;IAED,aAAa;IACb,uCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,sBAAe,CAAC,UAAU,CAAC;YACnE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;YAClC,SAAS,EAAE,IAAI,CAAC,UAAU;SAC7B,CAAC;IACN,CAAC;IACL,uBAAC;AAAD,CAnMA,AAmMC,IAAA;AAnMY,4CAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { EventManager } from \"../systems/EventManager\";\nimport { EBuffEffectType } from \"../types/Buff\";\nimport FightEvent from \"../types/FightEvent\";\nimport { IBuff, BuffType, IBuffPeriodicEffect, BuffPeriodicEffectType } from \"../types/IBuff\";\nimport { ICharacter } from \"../types/ICharacter\";\nimport { IAttributeModifier } from \"../types/ICharacterAttributes\";\n\n/**\n * 持续治疗Buff\n * 每秒恢复一定生命值的增益效果\n */\nexport class HealOverTimeBuff implements IBuff {\n    private _id: string;\n    private _name: string = \"持续治疗\";\n    private _description: string = \"每秒恢复生命值\";\n    private _type: BuffType = BuffType.BUFF;\n    private _duration: number;\n    private _remainingTime: number;\n    private _stackCount: number = 1;\n    private _maxStack: number = 3;\n    private _caster: ICharacter;\n    private _target: ICharacter;\n    private _isExpired: boolean = false;\n    private _attributeModifiers: IAttributeModifier[] = [];\n    private _eventManager: EventManager;\n    // 持续治疗配置\n    private _healPerSecond: number;\n    private _healInterval: number = 1.0; // 每秒触发一次\n    private _lastHealTime: number = 0;\n    // 视觉效果\n    private _iconPath: string = \"icons/buffs/heal_over_time\";\n    private _effectPrefabPath: string = \"prefabs/effects/HealAura\";\n\n    constructor(caster: ICharacter, target: ICharacter, duration: number, healPerSecond: number) {\n        this._id = `heal_over_time_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        this._caster = caster;\n        this._target = target;\n        this._duration = duration;\n        this._remainingTime = duration;\n        this._healPerSecond = healPerSecond;\n        this._eventManager = EventManager.createLocal(`buff_${this._id}`);\n        this._description = `每秒恢复${healPerSecond}点生命值，持续${duration}秒`;\n    }\n\n    // 实现IBuff接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get type(): BuffType { return this._type; }\n    get duration(): number { return this._duration; }\n    get maxStack(): number { return this._maxStack; }\n    get caster(): ICharacter { return this._caster; }\n    get target(): ICharacter { return this._target; }\n    get isExpired(): boolean { return this._isExpired || this._remainingTime <= 0; }\n    get attributeModifiers(): ReadonlyArray<IAttributeModifier> { return this._attributeModifiers; }\n    get iconPath(): string { return this._iconPath; }\n    get effectPrefabPath(): string { return this._effectPrefabPath; }\n    get remainingTime(): number { return this._remainingTime; }\n    set remainingTime(value: number) { this._remainingTime = Math.max(0, value); }\n    get stackCount(): number { return this._stackCount; }\n    set stackCount(value: number) { this._stackCount = Math.max(0, Math.min(value, this._maxStack)); }\n\n    get periodicEffect(): IBuffPeriodicEffect {\n        return {\n            interval: this._healInterval,\n            effectType: BuffPeriodicEffectType.HEAL_OVER_TIME,\n            value: this._healPerSecond,\n            stackable: true\n        };\n    }\n\n    /** Buff被添加时触发 */\n    onApply(): void {\n        console.log(`${this._name} applied to ${this._target.characterName} (Stack: ${this._stackCount})`);\n        // 播放应用特效\n        if (this._effectPrefabPath) {\n            this.playApplyEffect();\n        }\n        // 触发事件\n        this._eventManager.emit(FightEvent.buffApplied, { buff: this, target: this._target });\n    }\n\n    /** Buff每帧更新时触发 */\n    onTick(deltaTime: number): void {\n        this._lastHealTime += deltaTime;\n        // 检查是否到了治疗时间\n        if (this._lastHealTime >= this._healInterval) {\n            this.performHeal();\n            this._lastHealTime = 0;\n        }\n    }\n\n    /** Buff被移除时触发 */\n    onRemove(): void {\n        console.log(`${this._name} removed from ${this._target.characterName}`);\n        this.stopEffect();\n        this._eventManager.emit(FightEvent.buffRemoved, { buff: this, target: this._target });\n    }\n\n    /** 执行治疗 */\n    private performHeal(): void {\n        if (this._target && !this._target.isDead) {\n            const totalHeal = this._healPerSecond * this._stackCount;\n            this._target.heal(totalHeal);\n            console.log(`${this._target.characterName} healed for ${totalHeal} HP from ${this._name}`);\n            // 播放治疗特效\n            this.playHealEffect();\n            // 触发治疗事件\n            this._eventManager.emit(FightEvent.characterHealed, { target: this._target, healer: this._caster, amount: totalHeal, source: this });\n        }\n    }\n\n    /** 更新Buff状态 */\n    update(deltaTime: number): boolean {\n        if (this._isExpired) {\n            return true;\n        }\n        this._remainingTime -= deltaTime;\n        this.onTick(deltaTime);\n        if (this._remainingTime <= 0) {\n            this._isExpired = true;\n            return true;\n        }\n        return false;\n    }\n\n    /** 刷新Buff持续时间 */\n    refresh(): void {\n        this._remainingTime = this._duration;\n        this._lastHealTime = 0;\n        console.log(`${this._name} refreshed on ${this._target.characterName}`);\n    }\n\n    /** 增加叠加层数 */\n    addStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.min(this._stackCount + count, this._maxStack);\n        if (this._stackCount > oldStack) {\n            console.log(`${this._name} stack increased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 减少叠加层数 */\n    removeStack(count: number = 1): void {\n        const oldStack = this._stackCount;\n        this._stackCount = Math.max(this._stackCount - count, 0);\n        if (this._stackCount < oldStack) {\n            console.log(`${this._name} stack decreased to ${this._stackCount} on ${this._target.characterName}`);\n            this._eventManager.emit(FightEvent.buffStackChanged, { buff: this, target: this._target });\n        }\n    }\n\n    /** 获取Buff的当前效果值 */\n    getEffectValue(effectType: string): number {\n        switch (effectType) {\n            case EBuffEffectType.heal_per_second:\n                return this._healPerSecond * this._stackCount;\n            case EBuffEffectType.total_heal:\n                return this._healPerSecond * this._stackCount * this._remainingTime;\n            default:\n                return 0;\n        }\n    }\n\n    /** 检查Buff是否与另一个Buff冲突 */\n    conflictsWith(_otherBuff: IBuff): boolean {\n        // 同类型的持续治疗buff不冲突，可以叠加\n        return false;\n    }\n\n    /** 播放应用特效 */\n    private playApplyEffect(): void {\n        console.log(`Playing apply effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效播放逻辑\n    }\n\n    /** 播放治疗特效 */\n    private playHealEffect(): void {\n        console.log(`Playing heal effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的治疗特效播放逻辑\n    }\n\n    /** 停止特效 */\n    private stopEffect(): void {\n        console.log(`Stopping effect for ${this._name} on ${this._target.characterName}`);\n        // 这里应该实现实际的特效停止逻辑\n    }\n\n    /** 获取调试信息 */\n    getDebugInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            type: this._type,\n            duration: this._duration,\n            remainingTime: this._remainingTime,\n            stackCount: this._stackCount,\n            maxStack: this._maxStack,\n            healPerSecond: this._healPerSecond,\n            totalHealRemaining: this.getEffectValue(EBuffEffectType.total_heal),\n            caster: this._caster.characterName,\n            target: this._target.characterName,\n            isExpired: this._isExpired\n        };\n    }\n}\n"]}