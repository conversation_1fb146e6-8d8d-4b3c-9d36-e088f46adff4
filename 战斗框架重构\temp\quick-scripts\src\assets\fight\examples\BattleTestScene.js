"use strict";
cc._RF.push(module, '29a96ZvIcBF6pT7Lr37bHrM', 'BattleTestScene');
// fight/examples/BattleTestScene.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattleTestScene = void 0;
var BuffModelBeHurtFight_1 = require("../buff/BuffModelBeHurtFight");
var Character_1 = require("../characters/Character");
var PlayerSkillFire1_1 = require("../skills/PlayerSkillFire1");
var BattleManager_1 = require("../systems/BattleManager");
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var CharacterTypes_1 = require("../types/CharacterTypes");
var IDamage_1 = require("../types/IDamage");
var SkillName_1 = require("../types/SkillName");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 综合战斗测试场景
 * 整合了基础战斗测试、Timeline测试、AI演示等功能
 */
var BattleTestScene = /** @class */ (function (_super) {
    __extends(BattleTestScene, _super);
    function BattleTestScene() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // UI 组件
        _this.uiRoot = null;
        _this.statusLabel = null;
        // 基础战斗按钮
        _this.startBattleBtn = null;
        _this.endBattleBtn = null;
        _this.castSkillBtn = null;
        _this.addBuffBtn = null;
        // Timeline 测试按钮
        _this.testTimelineBtn = null;
        _this.testDamageTimelineBtn = null;
        _this.testBuffTimelineBtn = null;
        // AI 和多敌人测试按钮
        _this.createEnemiesBtn = null;
        _this.toggleAIBtn = null;
        _this.debugInfoBtn = null;
        // 角色和管理器
        _this.player = null;
        _this.enemy = null;
        _this.enemies = [];
        _this.battleManager = null;
        _this.timelineManager = null;
        // 状态变量
        _this.battleStarted = false;
        _this.aiEnabled = false;
        _this.testMode = "basic"; // basic, timeline, multi
        return _this;
    }
    BattleTestScene.prototype.onLoad = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        // 初始化管理器
        this.battleManager = BattleManager_1.BattleManager.getInstance();
        this.timelineManager = this.battleManager.timelineManager;
        // 设置基础战斗按钮事件
        (_a = this.startBattleBtn) === null || _a === void 0 ? void 0 : _a.node.on('click', this.onStartBattle, this);
        (_b = this.endBattleBtn) === null || _b === void 0 ? void 0 : _b.node.on('click', this.onEndBattle, this);
        (_c = this.castSkillBtn) === null || _c === void 0 ? void 0 : _c.node.on('click', this.onCastSkill, this);
        (_d = this.addBuffBtn) === null || _d === void 0 ? void 0 : _d.node.on('click', this.onAddBuff, this);
        // 设置Timeline测试按钮事件
        (_e = this.testTimelineBtn) === null || _e === void 0 ? void 0 : _e.node.on('click', this.onTestTimeline, this);
        (_f = this.testDamageTimelineBtn) === null || _f === void 0 ? void 0 : _f.node.on('click', this.onTestDamageTimeline, this);
        (_g = this.testBuffTimelineBtn) === null || _g === void 0 ? void 0 : _g.node.on('click', this.onTestBuffTimeline, this);
        // 设置AI和多敌人测试按钮事件
        (_h = this.createEnemiesBtn) === null || _h === void 0 ? void 0 : _h.node.on('click', this.onCreateEnemies, this);
        (_j = this.toggleAIBtn) === null || _j === void 0 ? void 0 : _j.node.on('click', this.onToggleAI, this);
        (_k = this.debugInfoBtn) === null || _k === void 0 ? void 0 : _k.node.on('click', this.onShowDebugInfo, this);
        console.log("综合战斗测试场景已初始化");
    };
    BattleTestScene.prototype.start = function () {
        this.createPlayer();
        this.createEnemy();
        console.log("角色创建完成");
        this.updateStatus();
    };
    BattleTestScene.prototype.onDestroy = function () {
        if (this.battleManager) {
            this.battleManager.cleanup();
        }
    };
    BattleTestScene.prototype.update = function (dt) {
        if (this.battleStarted && this.battleManager) {
            // 使用BattleManager统一更新
            this.battleManager.update(dt);
        }
        this.updateStatus();
    };
    /*** 创建玩家*/
    BattleTestScene.prototype.createPlayer = function () {
        var playerNode = new cc.Node("Player");
        playerNode.parent = this.node;
        playerNode.position = cc.v3(-200, 0, 0);
        this.player = playerNode.addComponent(Character_1.Character);
        var playerData = {
            prefabKey: "player",
            role: CharacterTypes_1.CharacterRole.HERO,
            name: "测试玩家",
            worldPosition: cc.v3(-200, 0, 0),
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.2,
                moveSpeed: 150,
                attackRange: 300,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);
        // 学习技能
        var fireSkill = new PlayerSkillFire1_1.PlayerSkillFire1();
        this.player.learnSkill(fireSkill);
        console.log("玩家创建完成:", this.player.getCharacterInfo());
    };
    /** * 创建敌人 */
    BattleTestScene.prototype.createEnemy = function () {
        var enemyNode = new cc.Node("Enemy");
        enemyNode.parent = this.node;
        enemyNode.position = cc.v3(200, 0, 0);
        this.enemy = enemyNode.addComponent(Character_1.Character);
        var enemyData = {
            prefabKey: "enemy",
            role: CharacterTypes_1.CharacterRole.ENEMY,
            name: "测试敌人",
            worldPosition: cc.v3(200, 0, 0),
            initialAttributes: {
                hp: 500,
                maxHp: 500,
                mp: 100,
                maxMp: 100,
                attack: 80,
                defense: 30,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 150,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.9,
                dodgeRate: 0.1,
                level: 5,
                experience: 0
            }
        };
        this.enemy.setCharacterData(enemyData);
        console.log("敌人创建完成:", this.enemy.getCharacterInfo());
    };
    /** * 开始战斗 */
    BattleTestScene.prototype.onStartBattle = function () {
        if (this.battleStarted) {
            console.log("战斗已经开始");
            return;
        }
        if (!this.player || !this.enemy) {
            console.log("角色未创建完成，无法开始战斗");
            return;
        }
        this.battleStarted = true;
        // 使用BattleManager开始战斗
        var participants = [this.player];
        if (this.testMode === "multi" && this.enemies.length > 0) {
            participants.push.apply(participants, __spread(this.enemies));
            console.log("=== \u591A\u654C\u4EBA\u6218\u6597\u5F00\u59CB (" + this.enemies.length + "\u4E2A\u654C\u4EBA) ===");
        }
        else if (this.enemy) {
            participants.push(this.enemy);
            console.log("=== 单敌人战斗开始 ===");
        }
        this.battleManager.startBattle("test_battle_" + Date.now(), participants);
        // 更新按钮状态
        if (this.castSkillBtn)
            this.castSkillBtn.interactable = true;
        if (this.addBuffBtn)
            this.addBuffBtn.interactable = true;
        if (this.startBattleBtn)
            this.startBattleBtn.interactable = false;
        if (this.endBattleBtn)
            this.endBattleBtn.interactable = true;
    };
    /**  * 释放技能  */
    BattleTestScene.prototype.onCastSkill = function () {
        if (!this.battleStarted || !this.player || !this.enemy) {
            console.log("战斗未开始或角色不存在");
            return;
        }
        if (this.player.isDead || this.enemy.isDead) {
            console.log("有角色已死亡，无法释放技能");
            return;
        }
        var success = this.player.castSkill(SkillName_1.default.player_skill_fire1, this.enemy.node);
        if (success) {
            console.log(this.player.name + " \u5BF9 " + this.enemy.name + " \u91CA\u653E\u4E86\u706B\u7403\u672F\uFF01");
        }
        else {
            console.log("技能释放失败");
        }
    };
    /** * 添加Buff */
    BattleTestScene.prototype.onAddBuff = function () {
        if (!this.battleStarted || !this.player) {
            console.log("战斗未开始或玩家不存在");
            return;
        }
        if (this.player.isDead) {
            console.log("玩家已死亡，无法添加Buff");
            return;
        }
        var buff = new BuffModelBeHurtFight_1.BuffModelBeHurtFight(this.player, this.player);
        this.player.addBuff(buff);
        console.log(this.player.name + " \u83B7\u5F97\u4E86\u53CD\u51FBBuff\uFF01");
    };
    /** * 更新状态显示 */
    BattleTestScene.prototype.updateStatus = function () {
        if (!this.statusLabel)
            return;
        var status = "=== 综合战斗系统测试 ===\n";
        // 玩家信息
        if (this.player) {
            var playerInfo = this.player.getCharacterInfo();
            status += "\uD83D\uDEE1\uFE0F \u73A9\u5BB6: " + playerInfo.name + "\n";
            status += "HP: " + this.player.attributes.currentHp + "/" + this.player.attributes.maxHp + "\n";
            status += "MP: " + (this.player.attributes.currentMp || 0) + "/" + (this.player.attributes.maxMp || 0) + "\n";
            status += "\u72B6\u6001: " + (playerInfo.isDead ? "💀死亡" : "✅存活") + "\n";
            status += "\u6280\u80FD\u6570: " + playerInfo.skillCount + " | Buff\u6570: " + playerInfo.buffCount + "\n\n";
        }
        // 敌人信息
        if (this.testMode === "multi" && this.enemies.length > 0) {
            status += "\uD83D\uDC79 \u654C\u4EBA (" + this.enemies.length + "\u4E2A):\n";
            this.enemies.forEach(function (enemy, index) {
                var enemyInfo = enemy.getCharacterInfo();
                status += index + 1 + ". " + enemyInfo.name + ": " + enemy.attributes.currentHp + "/" + enemy.attributes.maxHp + " " + (enemyInfo.isDead ? "💀" : "✅") + "\n";
            });
            status += "\n";
        }
        else if (this.enemy) {
            var enemyInfo = this.enemy.getCharacterInfo();
            status += "\uD83D\uDC79 \u654C\u4EBA: " + enemyInfo.name + "\n";
            status += "HP: " + this.enemy.attributes.currentHp + "/" + this.enemy.attributes.maxHp + "\n";
            status += "\u72B6\u6001: " + (enemyInfo.isDead ? "💀死亡" : "✅存活") + "\n\n";
        }
        // 测试模式和AI状态
        status += "\uD83C\uDFAE \u6D4B\u8BD5\u6A21\u5F0F: " + this.testMode + "\n";
        status += "\uD83E\uDD16 AI\u72B6\u6001: " + (this.aiEnabled ? "✅启用" : "❌禁用") + "\n\n";
        if (this.battleManager) {
            var battleStats = this.battleManager.getBattleStats();
            status += "\u6218\u6597\u72B6\u6001:\n";
            status += "\u8FDB\u884C\u4E2D: " + (battleStats.isInBattle ? "是" : "否") + "\n";
            status += "\u65F6\u957F: " + battleStats.duration.toFixed(1) + "s\n";
            status += "\u53C2\u6218\u8005: " + battleStats.participantCount + "\n\n";
            status += "Timeline\u7EDF\u8BA1:\n";
            status += "\u6D3B\u8DC3: " + battleStats.timelineStats.activeCount + "\n";
            status += "\u6682\u505C: " + battleStats.timelineStats.pausedCount + "\n";
            status += "\u603B\u6267\u884C: " + battleStats.timelineStats.totalExecutedEvents + "\n\n";
            status += "\u5B50\u5F39\u7EDF\u8BA1:\n";
            status += "\u6D3B\u8DC3: " + battleStats.bulletStats.activeCount + "\n";
            status += "\u603B\u521B\u5EFA: " + battleStats.bulletStats.totalCreated + "\n";
            status += "\u603B\u9500\u6BC1: " + battleStats.bulletStats.totalDestroyed + "\n";
        }
        this.statusLabel.string = status;
    };
    // ==================== 新增的测试方法 ====================
    /** 结束战斗 */
    BattleTestScene.prototype.onEndBattle = function () {
        if (!this.battleStarted) {
            console.log("战斗未开始");
            return;
        }
        this.battleManager.endBattle("manual_stop");
        this.battleStarted = false;
        this.aiEnabled = false;
        // 重置按钮状态
        if (this.startBattleBtn)
            this.startBattleBtn.interactable = true;
        if (this.endBattleBtn)
            this.endBattleBtn.interactable = false;
        if (this.castSkillBtn)
            this.castSkillBtn.interactable = false;
        if (this.addBuffBtn)
            this.addBuffBtn.interactable = false;
        console.log("战斗已结束");
    };
    /** 测试基础Timeline功能 */
    BattleTestScene.prototype.onTestTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试基础Timeline ===");
        // 创建一个简单的Timeline
        var timeline = new Timeline_1.Timeline("test_timeline_" + Date.now(), "基础测试Timeline", 3.0, this.player, this.enemy);
        // 添加音效事件
        var soundEvent = new TimelineEvents_1.PlaySoundTimelineEvent("sound_1", "test_sound");
        var soundNode = new Timeline_1.TimelineNode("sound_node", 0.5, soundEvent);
        timeline.addNode(soundNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("基础Timeline已创建并添加到管理器");
    };
    /** 测试伤害Timeline */
    BattleTestScene.prototype.onTestDamageTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试伤害Timeline ===");
        // 创建伤害Timeline
        var timeline = new Timeline_1.Timeline("damage_timeline_" + Date.now(), "伤害测试Timeline", 2.0, this.player, this.enemy);
        // 添加伤害事件
        var damageEvent = new TimelineEvents_1.DamageTimelineEvent("damage_1", 80, IDamage_1.DamageType.PHYSICAL);
        var damageNode = new Timeline_1.TimelineNode("damage_node", 1.0, damageEvent);
        timeline.addNode(damageNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("伤害Timeline已创建，将在1秒后造成80点物理伤害");
    };
    /** 测试Buff Timeline */
    BattleTestScene.prototype.onTestBuffTimeline = function () {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }
        console.log("=== 测试Buff Timeline ===");
        // 创建Buff Timeline
        var timeline = new Timeline_1.Timeline("buff_timeline_" + Date.now(), "Buff测试Timeline", 1.5, this.player, this.enemy);
        // 添加Buff事件
        var buffEvent = new TimelineEvents_1.AddBuffTimelineEvent("buff_1", "hurt_fight");
        var buffNode = new Timeline_1.TimelineNode("buff_node", 0.8, buffEvent);
        timeline.addNode(buffNode);
        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);
        console.log("Buff Timeline已创建，将在0.8秒后给敌人添加反击Buff");
    };
    /** 创建多个敌人 */
    BattleTestScene.prototype.onCreateEnemies = function () {
        var e_1, _a;
        console.log("=== 创建多个敌人 ===");
        try {
            // 清理现有敌人
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (enemy && enemy.node) {
                    enemy.node.destroy();
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        this.enemies = [];
        // 创建3个敌人
        for (var i = 0; i < 3; i++) {
            var enemyNode = new cc.Node("Enemy_" + (i + 1));
            enemyNode.parent = this.node;
            enemyNode.position = cc.v3(200 + i * 150, 0, 0);
            var enemy = enemyNode.addComponent(Character_1.Character);
            var enemyData = {
                prefabKey: "enemy",
                role: CharacterTypes_1.CharacterRole.ENEMY,
                name: "\u654C\u4EBA" + (i + 1),
                worldPosition: cc.v3(200 + i * 150, 0, 0),
                initialAttributes: {
                    hp: 300 + i * 100,
                    maxHp: 300 + i * 100,
                    mp: 50,
                    maxMp: 50,
                    attack: 60 + i * 10,
                    defense: 20 + i * 5,
                    attackSpeed: 1.0,
                    moveSpeed: 80,
                    attackRange: 120,
                    criticalRate: 0.05,
                    criticalDamage: 1.2,
                    hitRate: 0.9,
                    dodgeRate: 0.05,
                    level: 3 + i,
                    experience: 0
                }
            };
            enemy.setCharacterData(enemyData);
            this.setupEnemyEvents(enemy);
            this.enemies.push(enemy);
            console.log("\u521B\u5EFA\u4E86" + enemy.name + "\uFF0CHP: " + enemy.attributes.maxHp);
        }
        this.testMode = "multi";
        console.log("多敌人模式已激活");
    };
    /** 切换AI模式 */
    BattleTestScene.prototype.onToggleAI = function () {
        this.aiEnabled = !this.aiEnabled;
        if (this.aiEnabled) {
            console.log("=== AI模式已启用 ===");
            // 启动AI定时器
            this.schedule(this.updateAI, 1.0);
        }
        else {
            console.log("=== AI模式已禁用 ===");
            // 停止AI定时器
            this.unschedule(this.updateAI);
        }
        // 更新按钮文本（如果需要的话）
        if (this.toggleAIBtn && this.toggleAIBtn.getComponentInChildren(cc.Label)) {
            var label = this.toggleAIBtn.getComponentInChildren(cc.Label);
            label.string = this.aiEnabled ? "关闭AI" : "开启AI";
        }
    };
    /** 显示调试信息 */
    BattleTestScene.prototype.onShowDebugInfo = function () {
        console.log("=== 调试信息 ===");
        if (this.battleManager) {
            console.log("BattleManager状态:");
            this.battleManager.printDebugInfo();
        }
        if (this.timelineManager) {
            console.log("TimelineManager状态:");
            this.timelineManager.printDebugInfo();
        }
        if (this.player) {
            console.log("玩家信息:", this.player.getCharacterInfo());
        }
        if (this.enemy) {
            console.log("敌人信息:", this.enemy.getCharacterInfo());
        }
        if (this.enemies.length > 0) {
            console.log("多敌人信息:");
            this.enemies.forEach(function (enemy, index) {
                console.log("\u654C\u4EBA" + (index + 1) + ":", enemy.getCharacterInfo());
            });
        }
        console.log("当前测试模式:", this.testMode);
        console.log("AI状态:", this.aiEnabled ? "启用" : "禁用");
    };
    /** 设置敌人事件 */
    BattleTestScene.prototype.setupEnemyEvents = function (enemy) {
        var _this = this;
        var events = {
            onDeath: function (character) {
                console.log(character.characterName + " \u88AB\u51FB\u8D25\u4E86\uFF01");
                _this.onEnemyDeath(character);
            },
            onTakeDamage: function (character, damage, attacker) {
                console.log(character.characterName + " \u53D7\u5230\u4E86 " + damage + " \u70B9\u4F24\u5BB3");
                if (attacker) {
                    console.log("\u653B\u51FB\u8005: " + attacker.characterName);
                }
            },
            onSkillCast: function (character, skillName) {
                console.log(character.characterName + " \u91CA\u653E\u4E86\u6280\u80FD: " + skillName);
            }
        };
        enemy.setEvents(events);
    };
    /** 敌人死亡处理 */
    BattleTestScene.prototype.onEnemyDeath = function (enemy) {
        var _a;
        // 从敌人列表中移除
        var index = this.enemies.indexOf(enemy);
        if (index >= 0) {
            this.enemies.splice(index, 1);
            console.log(enemy.name + " \u5DF2\u4ECE\u654C\u4EBA\u5217\u8868\u4E2D\u79FB\u9664");
        }
        // 检查是否所有敌人都被击败
        var aliveEnemies = this.enemies.filter(function (e) { return !e.isDead; });
        if (aliveEnemies.length === 0 && !((_a = this.enemy) === null || _a === void 0 ? void 0 : _a.isDead)) {
            // 只有单个敌人的情况
        }
        else if (aliveEnemies.length === 0) {
            console.log("🎉 胜利！所有敌人都被击败了！");
            this.onEndBattle();
        }
    };
    /** AI更新逻辑 */
    BattleTestScene.prototype.updateAI = function () {
        if (!this.aiEnabled || !this.battleStarted || !this.player || this.player.isDead) {
            return;
        }
        // 玩家AI：攻击最近的敌人
        this.updatePlayerAI();
        // 敌人AI：攻击玩家
        this.updateEnemyAI();
    };
    /** 更新玩家AI */
    BattleTestScene.prototype.updatePlayerAI = function () {
        var target = this.findNearestEnemy();
        if (!target)
            return;
        // 尝试释放技能
        if (this.player.castSkill(SkillName_1.default.player_skill_fire1, target.node)) {
            console.log("[AI] " + this.player.name + " \u5BF9 " + target.name + " \u91CA\u653E\u4E86\u706B\u7403\u672F");
        }
        else if (this.player.isInAttackRange && this.player.isInAttackRange(target)) {
            // 如果技能释放失败，使用普通攻击
            this.player.attack(target);
            console.log("[AI] " + this.player.name + " \u653B\u51FB\u4E86 " + target.name);
        }
    };
    /** 更新敌人AI */
    BattleTestScene.prototype.updateEnemyAI = function () {
        var e_2, _a;
        // 单个敌人AI
        if (this.enemy && !this.enemy.isDead && this.enemy.isInAttackRange(this.player)) {
            this.enemy.attack(this.player);
            console.log("[AI] " + this.enemy.name + " \u653B\u51FB\u4E86 " + this.player.name);
        }
        try {
            // 多敌人AI
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (!enemy.isDead && enemy.isInAttackRange(this.player)) {
                    enemy.attack(this.player);
                    console.log("[AI] " + enemy.name + " \u653B\u51FB\u4E86 " + this.player.name);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
    };
    /** 寻找最近的敌人 */
    BattleTestScene.prototype.findNearestEnemy = function () {
        var e_3, _a;
        var nearestEnemy = null;
        var minDistance = Infinity;
        // 检查单个敌人
        if (this.enemy && !this.enemy.isDead) {
            var distance = cc.Vec3.distance(this.player.node.position, this.enemy.node.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = this.enemy;
            }
        }
        try {
            // 检查多个敌人
            for (var _b = __values(this.enemies), _c = _b.next(); !_c.done; _c = _b.next()) {
                var enemy = _c.value;
                if (enemy.isDead)
                    continue;
                var distance = cc.Vec3.distance(this.player.node.position, enemy.node.position);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemy;
                }
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return nearestEnemy;
    };
    __decorate([
        property(cc.Node)
    ], BattleTestScene.prototype, "uiRoot", void 0);
    __decorate([
        property(cc.Label)
    ], BattleTestScene.prototype, "statusLabel", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "startBattleBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "endBattleBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "castSkillBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "addBuffBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testDamageTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "testBuffTimelineBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "createEnemiesBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "toggleAIBtn", void 0);
    __decorate([
        property(cc.Button)
    ], BattleTestScene.prototype, "debugInfoBtn", void 0);
    BattleTestScene = __decorate([
        ccclass
    ], BattleTestScene);
    return BattleTestScene;
}(cc.Component));
exports.BattleTestScene = BattleTestScene;

cc._RF.pop();