{"version": 3, "sources": ["assets\\card\\cardgame\\src\\base\\CampBase.ts"], "names": [], "mappings": ";;;;AACA,0DAA0D;AAC1D,0CAA0C;AAC1C,4CAA4C;AAC5C,6BAA6B;AAC7B,qCAAqC;AAErC,+CAA+C;AAC/C,mBAAmB;AACnB,iBAAiB;AACjB,uCAAuC;AACvC,qBAAqB;AACrB,2BAA2B;AAC3B,QAAQ;AAER,mCAAmC;AACnC,yBAAyB;AACzB,4BAA4B;AAC5B,0BAA0B;AAC1B,QAAQ;AACR,IAAI;AAEJ,MAAM;AACN,wBAAwB;AACxB,kCAAkC;AAClC,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,WAAW;AACX,+CAA+C;AAC/C,mBAAmB;AACnB,2BAA2B;AAC3B,mBAAmB;AACnB,gCAAgC;AAChC,yBAAyB;AACzB,iCAAiC;AAEjC,gBAAgB;AAChB,uBAAuB;AACvB,uBAAuB;AAEvB,mEAAmE;AAEnE,UAAU;AACV,gBAAgB;AAChB,mCAAmC;AACnC,mCAAmC;AACnC,uCAAuC;AACvC,UAAU;AACV,6CAA6C;AAC7C,oBAAoB;AACpB,4CAA4C;AAC5C,iDAAiD;AACjD,8CAA8C;AAC9C,yDAAyD;AACzD,+BAA+B;AAC/B,4DAA4D;AAC5D,iEAAiE;AACjE,oDAAoD;AACpD,mBAAmB;AACnB,kFAAkF;AAElF,uCAAuC;AACvC,2CAA2C;AAC3C,mCAAmC;AACnC,6CAA6C;AAC7C,2DAA2D;AAC3D,2BAA2B;AAC3B,QAAQ;AAER,oBAAoB;AACpB,8DAA8D;AAC9D,qEAAqE;AACrE,YAAY;AACZ,QAAQ;AAER,qCAAqC;AACrC,mBAAmB;AACnB,gDAAgD;AAChD,sCAAsC;AACtC,wDAAwD;AACxD,mBAAmB;AACnB,+BAA+B;AAC/B,uDAAuD;AACvD,YAAY;AACZ,QAAQ;AAER,mEAAmE;AACnE,gDAAgD;AAChD,iDAAiD;AACjD,kCAAkC;AAClC,oDAAoD;AACpD,wDAAwD;AACxD,YAAY;AACZ,QAAQ;AAER,kDAAkD;AAClD,oCAAoC;AACpC,sBAAsB;AACtB,mEAAmE;AACnE,kCAAkC;AAClC,yCAAyC;AACzC,YAAY;AACZ,QAAQ;AAER,6DAA6D;AAC7D,oCAAoC;AACpC,sBAAsB;AACtB,mEAAmE;AACnE,0DAA0D;AAC1D,kCAAkC;AAClC,yCAAyC;AACzC,YAAY;AACZ,QAAQ;AAER,qDAAqD;AACrD,oCAAoC;AACpC,sBAAsB;AACtB,mEAAmE;AACnE,kCAAkC;AAClC,4CAA4C;AAC5C,YAAY;AACZ,QAAQ;AAER,gEAAgE;AAChE,oCAAoC;AACpC,sBAAsB;AACtB,mEAAmE;AACnE,0DAA0D;AAC1D,kCAAkC;AAClC,4CAA4C;AAC5C,YAAY;AACZ,QAAQ;AAER,mBAAmB;AACnB,2DAA2D;AAC3D,wCAAwC;AACxC,wCAAwC;AACxC,mEAAmE;AACnE,oDAAoD;AACpD,2BAA2B;AAC3B,gDAAgD;AAChD,kGAAkG;AAClG,2DAA2D;AAC3D,2FAA2F;AAC3F,qDAAqD;AACrD,uBAAuB;AACvB,2FAA2F;AAC3F,uCAAuC;AACvC,gBAAgB;AAChB,YAAY;AACZ,QAAQ;AAER,qBAAqB;AACrB,uFAAuF;AACvF,wCAAwC;AACxC,wCAAwC;AACxC,mEAAmE;AACnE,yEAAyE;AACzE,uCAAuC;AACvC,iDAAiD;AACjD,2FAA2F;AAC3F,kDAAkD;AAClD,gBAAgB;AAChB,YAAY;AACZ,QAAQ;AAER,wBAAwB;AAExB,mCAAmC;AACnC,8BAA8B;AAC9B,QAAQ;AAER,2BAA2B;AAC3B,0CAA0C;AAC1C,QAAQ;AACR,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n// import BuffManager from '../../Controller/BuffManager';\r\n// import { ECamp } from '../CampManager';\r\n// import RoleManager from '../RoleManager';\r\n// import Base from './Base';\r\n// import RoleBase from './RoleBase';\r\n\r\n// const { ccclass, property } = cc._decorator;\r\n// declare global {\r\n//     /**阵营数据 */\r\n//     export interface ICampDataType {\r\n//         /**阵营名字 */\r\n//         campName: ECamp;\r\n//     }\r\n\r\n//     interface IDealInteractive {\r\n//         addHp?: number\r\n//         reduceHp?: number\r\n//         attack?: number\r\n//     }\r\n// }\r\n\r\n// /**\r\n//  * @features : 阵营控制基类\r\n//  * @description: 游戏中多个角色的阵营控制基类\r\n//  * @Date : 2020-08-12 23:28:52\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 13:57:04\r\n//  * @LastEditors : judu233\r\n//  */\r\n// @ccclass\r\n// export default class CampBase extends Base {\r\n//     /**卡牌阵营信息 */\r\n//     data: ICampDataType;\r\n//     /**阵营角色管理 */\r\n//     roleMgr = new RoleManager\r\n//     /**对阵营使用Buff的管理 */\r\n//     buffMgr = new BuffManager;\r\n\r\n//     /**阵营名字*/\r\n//     @Base.ViewLinked\r\n//     campName: ECamp;\r\n\r\n//     get isAllDeath() { return this.roleMgr.checkCardAllDeath() }\r\n\r\n//     /**\r\n//      * 初始化阵营 \r\n//      * @param initData 要初始化的卡牌数据\r\n//      * @param campData 要初始化的阵营数据\r\n//      *  ! 组件查找顺序 -> 节点挂载 -> 节点的子节点查找\r\n//      */\r\n//     initCamp(initData: ICampMgrDataType) {\r\n//         //初始化卡牌组件\r\n//         // let cardList: CardBase[] = [];\r\n//         // if (this.cardComList.length != 0) {\r\n//         //     cardList = this.cardComList;\r\n//         // } else if (this.node.children.length > 0) {\r\n//         //     //尝试从节点下面或取卡牌\r\n//         //     for (let cardNode of this.node.children) {\r\n//         //         let card = cardNode.getComponent(CardBase);\r\n//         //         if (card) cardList.push(card);\r\n//         //     }\r\n//         // } else cc.error(`[${this.className}]获取卡牌组件失败，请检查是否需要初始化or检查卡牌组件列表`);\r\n\r\n//         this.initCampData = initData\r\n//         this.roleMgr.initRoles(initData)\r\n//         if (initData.campData) {\r\n//             this.data = initData.campData;\r\n//         } else cc.error(`[${this.className}]没有设置阵营数据！`);\r\n//         this.restChose()\r\n//     }\r\n\r\n//     restChose() {\r\n//         if (this.curRole == null || this.curRole.hp == 0) {\r\n//             this.curRole = this.roleMgr.roles.find(r => r.hp != 0)\r\n//         }\r\n//     }\r\n\r\n//     initCampData: ICampMgrDataType\r\n//     restCamp() {\r\n//         if (this.roleMgr.roles.length == 0) {\r\n//             this.roleMgr.roles = []\r\n//             this.roleMgr.initRoles(this.initCampData)\r\n//         } else {\r\n//             this.restChose()\r\n//             // this.curRole.name = this.curRole.name\r\n//         }\r\n//     }\r\n\r\n//     async attackCamp(attackCamp: CampBase, roles?: RoleBase[]) {\r\n//         cc.log(`阵营[${this.campName}]开始准备攻击`);\r\n//         let card = this.roleMgr.getRoleCard(1)\r\n//         if (card.length != 0) {\r\n//             await card[0].attackCamp(attackCamp);\r\n//             cc.log(`阵营[${attackCamp.campName}]攻击完成`);\r\n//         }\r\n//     }\r\n\r\n//     addHp(addValue: number): IDealInteractive {\r\n//         let role = this.getRole()\r\n//         if (role) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             role.hp += addValue\r\n//             return { addHp: addValue }\r\n//         }\r\n//     }\r\n\r\n//     addHpByPercent(addPercent: number): IDealInteractive {\r\n//         let role = this.getRole()\r\n//         if (role) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             let complete = role.hp * (addPercent / 100)\r\n//             role.hp += complete\r\n//             return { addHp: complete }\r\n//         }\r\n//     }\r\n\r\n//     reduceHp(addValue: number): IDealInteractive {\r\n//         let role = this.getRole()\r\n//         if (role) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             role.hp -= addValue\r\n//             return { reduceHp: addValue }\r\n//         }\r\n//     }\r\n\r\n//     reduceHpByPercent(addPercent: number): IDealInteractive {\r\n//         let role = this.getRole()\r\n//         if (role) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             let complete = role.hp * (addPercent / 100)\r\n//             role.hp -= complete\r\n//             return { reduceHp: complete }\r\n//         }\r\n//     }\r\n\r\n//     /**攻击指定角色 */\r\n//     attackRole(attackRole: RoleBase): IDealInteractive {\r\n//         let selfRole = this.getRole()\r\n//         if (selfRole && attackRole) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             let dodge = Boolean.randomBoolean(80)\r\n//             dodge = true\r\n//             if (dodge && attackRole.hp > 0) {\r\n//                 let v = selfRole.attack * (1 - attackRole.defence / (attackRole.defence + 150))\r\n//                 attackRole.hp -= parseInt(v.toString());\r\n//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);\r\n//                 return { attack: selfRole.attack }\r\n//             } else {\r\n//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发闪避`);\r\n//                 return { attack: 0 }\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     /** 卡片攻击指定角色*/\r\n//     attackRoleByCard(attackRole: RoleBase, aggressivity: number): IDealInteractive {\r\n//         let selfRole = this.getRole()\r\n//         if (selfRole && attackRole) {\r\n//             // cc.log(`guid: ${this.GID}, hp: ${attackRole.hp}`)\r\n//             // return role.attackRoleByCard(attackRole, aggressivity);\r\n//             if (attackRole.hp > 0) {\r\n//                 attackRole.hp -= aggressivity;\r\n//                 // cc.log(`[${selfRole.roleName}]卡牌攻击完成[${attackRole.roleName}], 触发攻击`);\r\n//                 return { attack: aggressivity }\r\n//             }\r\n//         }\r\n//     }\r\n\r\n//     curRole: RoleBase\r\n\r\n//     getRole(): RoleBase | null {\r\n//         return this.curRole\r\n//     }\r\n\r\n//     getRoleEquipment() {\r\n//         return this.getRole()?.equipMgr\r\n//     }\r\n// }"]}