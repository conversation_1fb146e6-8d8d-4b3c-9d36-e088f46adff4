
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/card/cardgame/src/CardManager.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd5d7dTDjNRC6a7VfP8NoQ/B', 'CardManager');
// card/cardgame/src/CardManager.ts

// import Base from "./Base/Base";
// import CardBase from "./Base/CardBase";
// const { ccclass, property } = cc._decorator;
// declare global {
//     /**卡牌或角色的基础数据 */
//     export interface ICardMgr extends IBaseDataType {
//     }
// }
// /**
//  * @features : 功能
//  * @description: 说明
//  * @Date : 2020-08-17 10:25:03
//  * <AUTHOR> judu233(<EMAIL>)
//  * @LastEditTime : 2023-11-14 13:58:24
//  * @LastEditors : judu233
//  */
// // @ccclass
// export default class CardManager extends Base {
//     /**存储卡牌的数据 */
//     data: ICardMgr;
//     /**当前所有卡牌的信息 */
//     cardList: CardBase[] = [];
//     /**卡片的上限 */
//     @Base.ViewLinked
//     maxCard: number;
//     /***************和卡牌相关的接口*************** */
//     /**
//      * 初始化卡牌列表 
//      * @param initData 卡牌的初始化数据
//      */
//     initCardList(data: ICampMgrDataType) {
//         let initData = data.cardData
//         let cardLis = initData.map(card => new CardBase)
//         for (let [index, card] of Object.entries(cardLis)) {
//             card.data = initData[index];
//             this.cardList.push(card);
//             card.initCard();
//         }
//     }
//     addCard(card: ICardDataType) {
//         let newCard = new CardBase;
//         newCard.data = card;
//         this.cardList.push(newCard);
//         newCard.initCard();
//         return card
//     }
//     deletCard(cardId: string) {
//         let card = this.cardList.find(card => card.data.id === cardId);
//         if (card) {
//             this.cardList.splice(this.cardList.indexOf(card), 1);
//             if (this.cardList.includes(card)) {
//                 debugger
//             }
//             card.unscheduleAllCallbacks()
//             return true;
//         }
//         return false;
//     }
//     /**更新卡牌所有数据的视图  */
//     refreshCardView(card: CardBase) {
//         // card.refreshView();
//     }
//     /**获取卡牌列表的所有数据 */
//     getCardListData() {
//         let cardData: ICampDataType[] = [];
//         // this.cardList.forEach(card => cardData.push(card.data))
//         return cardData;
//     }
//     /**************** 所有卡牌列表 相关的接口 ***********************/
//     /**
//      * 根据卡牌名字从存储列表中返回卡牌
//      * @param name 要获取卡牌的名字
//      */
//     getCardByName(name: string) {
//         const card = this.cardList.find(c => c.cardName == name);
//         if (card) return card
//         cc.warn(`[${this.className}]根据名字获取卡牌${name}失败，请检查卡牌名字和卡牌列表！`);
//     }
//     /**
//     * 根据卡牌id从存储列表中返回卡牌
//     * @param name 要获取卡牌的名字
//     */
//     getCardById(id: string) {
//         const card = this.cardList.find(c => c.id == id);
//         if (card) return card
//         cc.warn(`[${this.className}]根据id获取卡牌${id}失败，请检查卡牌id和卡牌列表！`);
//     }
//     /*************** 存活卡牌 相关的接口 *******************/
//     /**根据名字从存活列表返回卡牌 */
//     getSurviveCardByName(name: string) { return this.cardList.find(card => card.cardName == name && !card.isDeath); }
//     /**根据id从存活列表返回卡牌 */
//     getSurviveCardById(id: string) { return this.cardList.find(card => card.id == id && !card.isDeath); }
//     /**根据名字返回死亡列表的卡牌 */
//     getDeathCardByName(name: string) { return this.cardList.find(card => card.cardName == name && card.isDeath); }
//     /** 根据id从死亡列表中获取卡牌*/
//     getDeathCardById(id: string) { return this.cardList.find(card => card.id == id && card.isDeath); }
//     // /**
//     //  * 从存活列表中删除卡牌，并存入死亡卡牌，且改变卡牌的状态
//     //  * @param name 要删除存活卡牌的名字
//     //  */
//     // delSurviveCardByName(name: string) {
//     //     if (this.surviveMap.delete(name)) {
//     //         let card = this.cardMap.get(name);
//     //         this.deathMap.set(name, card);
//     //         card.death = true;
//     //         // switch (this.camp) {
//     //         //     case ECamp.Player:
//     //         //         EM.broadcast(EM.keys.PlayerCardDeath);
//     //         //         break;
//     //         //     case ECamp.Computer:
//     //         //         EM.broadcast(EM.keys.ComputerCardDeath);
//     //         //         break;
//     //         //     default:
//     //         //         cc.warn(`[${this.className}删除存活卡牌，发送事件失败，触发事件阵营：${this.camp}]`);
//     //         // }
//     //     } else cc.warn(`[${this.className}]删除存活卡牌${name}失败，请检查卡牌名字和卡牌列表`);
//     // }
//     /**
//      * 从存活列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)
//      * @param count 要随机获取的数量
//      */
//     getSurviveCardForRandom(count: number = 1) {
//         let cardList = this.cardList.filter(card => !card.isDeath)
//         let resultList: CardBase[] = [];
//         for (let i = 0; i < count && cardList.length >= 1; i++) {
//             let randomIndex = Math.floor(Math.random() * cardList.length);
//             resultList.push(cardList.splice(randomIndex, 1)[0]);
//         }
//         return resultList;
//     }
//     getSurviveOneCard() {
//         let cardList = this.getSurviveCardForRandom(1);
//         if (cardList.length != 0) {
//             return cardList[0]
//         }
//     }
//     /*************** 死亡列表 相关的接口 ***************** */
//     // /**
//     //  * 从死亡列表中删除卡牌，并存入存活列表,且改变卡牌的状态
//     //  * @param name 要删除死亡卡牌的名字
//     //  */
//     // delDeathCardByName(name: string) {
//     //     if (this.deathMap.delete(name)) {
//     //         let card = this.cardMap.get(name);
//     //         this.surviveMap.set(name, card);
//     //         card.death = false;
//     //         card.hp = card.hpUp;
//     //         // switch (this.camp) {
//     //         //     case ECamp.Player:
//     //         //         EM.broadcast(EM.keys.PlayerCardResurrection);
//     //         //         break;
//     //         //     case ECamp.Computer:
//     //         //         EM.broadcast(EM.keys.ComputerCardResurrection);
//     //         //         break;
//     //         //     default:
//     //         //         cc.warn(`[${this.className}删除死亡卡牌，发送事件失败，触发事件阵营：${this.camp}]`);
//     //         // }
//     //     } else cc.warn(`[${this.className}]删除死亡卡牌${name}失败，请检查卡牌名字和卡牌列表`);
//     // }
//     /**
//     * 从死亡列表中随机获取指定数量的卡牌(最高返回列表所有卡牌)
//     * @param count 要随机获取的数量
//     */
//     getDeathCardForRandom(count: number = 1) {
//         let cardList = this.cardList.filter(card => !card.isDeath)
//         let resultList: CardBase[] = [];
//         for (let i = 0; i < count && cardList.length >= 1; i++) {
//             let randomIndex = Math.floor(Math.random() * cardList.length);
//             resultList.push(cardList.splice(randomIndex, 1)[0]);
//         }
//         return resultList;
//     }
// }

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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