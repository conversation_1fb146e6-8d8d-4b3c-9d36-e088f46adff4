
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/fight/skills/ChargeAttackSkill.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4187eLt3SxLE6PUbl83Labp', 'ChargeAttackSkill');
// fight/skills/ChargeAttackSkill.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChargeAttackSkill = void 0;
var Timeline_1 = require("../timeline/Timeline");
var TimelineEvents_1 = require("../timeline/TimelineEvents");
var IDamage_1 = require("../types/IDamage");
var ISkill_1 = require("../types/ISkill");
var ITimeline_1 = require("../types/ITimeline");
var SkillName_1 = require("../types/SkillName");
var AttackBoostBuff_1 = require("../buff/AttackBoostBuff");
var BattleManager_1 = require("../systems/BattleManager");
/**
 * 冲锋攻击技能
 * 快速冲向目标并造成物理伤害，同时获得短暂的攻击力提升
 */
var ChargeAttackSkill = /** @class */ (function () {
    function ChargeAttackSkill() {
        this._id = SkillName_1.default.charge_attack;
        this._name = "冲锋攻击";
        this._description = "快速冲向敌人并发动强力攻击，攻击后获得攻击力提升";
        this._cooldown = 5.0;
        this._remainingCooldown = 0;
        this._mpCost = 0;
        this._staminaCost = 25;
        this._level = 1;
        this._type = ISkill_1.SkillType.ACTIVE;
        this._targetType = ISkill_1.SkillTargetType.SINGLE_ENEMY;
        this._range = 300;
        this._timeline = null;
        this._passiveBuffs = [];
        /** 技能配置 */
        this._config = {
            animationName: "skill_charge",
            soundId: "charge_attack",
            effectPath: "prefabs/effects/ChargeTrail",
            hitEffectPath: "prefabs/effects/ChargeImpact",
            damage: 0,
            damageMultiplier: 1.8,
            chargeSpeed: 800,
            attackBuffDuration: 4.0,
            attackBuffMultiplier: 1.3 // 攻击力提升倍率
        };
    }
    Object.defineProperty(ChargeAttackSkill.prototype, "id", {
        // 实现ISkill接口
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "name", {
        get: function () { return this._name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "description", {
        get: function () { return this._description; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "cooldown", {
        get: function () { return this._cooldown; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "remainingCooldown", {
        get: function () { return this._remainingCooldown; },
        set: function (value) { this._remainingCooldown = Math.max(0, value); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "mpCost", {
        get: function () { return this._mpCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "staminaCost", {
        get: function () { return this._staminaCost; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "level", {
        get: function () { return this._level; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "targetType", {
        get: function () { return this._targetType; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "range", {
        get: function () { return this._range; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "timeline", {
        get: function () { return this._timeline; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "passiveBuffs", {
        get: function () { return this._passiveBuffs; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackSkill.prototype, "canUse", {
        get: function () {
            return this._remainingCooldown <= 0;
        },
        enumerable: false,
        configurable: true
    });
    /** 检查是否可以对目标使用技能 */
    ChargeAttackSkill.prototype.canCastOn = function (caster, target) {
        if (!target)
            return false;
        if (target.isDead)
            return false;
        if (target.role !== caster.role)
            return true; // 敌对阵营
        return false;
    };
    /** 检查资源消耗 */
    ChargeAttackSkill.prototype.checkResourceCost = function (caster) {
        return caster.attributes.currentMp >= this._mpCost &&
            caster.attributes.currentStamina >= this._staminaCost;
    };
    /** 消耗资源 */
    ChargeAttackSkill.prototype.consumeResources = function (caster) {
        caster.attributes.consumeMp(this._mpCost);
        caster.attributes.consumeStamina(this._staminaCost);
    };
    /** 释放技能 */
    ChargeAttackSkill.prototype.cast = function (caster, target, targets, position) {
        if (!this.canCastOn(caster, target)) {
            return false;
        }
        if (!this.checkResourceCost(caster)) {
            return false;
        }
        this.consumeResources(caster);
        this._remainingCooldown = this._cooldown;
        this._timeline = this.createTimeline(caster, target, targets, position);
        BattleManager_1.BattleManager.instance.timelineManager.addTimeline(this._timeline);
        console.log(caster.characterName + " charges at " + (target === null || target === void 0 ? void 0 : target.characterName));
        return true;
    };
    /** 创建技能Timeline */
    ChargeAttackSkill.prototype.createTimeline = function (caster, target, targets, position) {
        var timelineId = this._id + "_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11);
        var timeline = new Timeline_1.Timeline(timelineId, this._name, 2.5, caster);
        // 0.0s: 播放冲锋准备动画
        var prepareNode = new Timeline_1.TimelineNode(timelineId + "_prepare", 0.0, new TimelineEvents_1.PlayAnimationTimelineEvent("prepare_animation", "charge_prepare"), false);
        timeline.addNode(prepareNode);
        // 0.3s: 开始冲锋移动
        var chargeStartNode = new Timeline_1.TimelineNode(timelineId + "_charge_start", 0.3, new ChargeMovementEvent("charge_movement", caster, target, this._config.chargeSpeed), false);
        timeline.addNode(chargeStartNode);
        // 0.4s: 播放冲锋音效和拖尾特效
        var soundNode = new Timeline_1.TimelineNode(timelineId + "_sound", 0.4, new TimelineEvents_1.PlaySoundTimelineEvent("charge_sound", this._config.soundId), false);
        timeline.addNode(soundNode);
        var trailNode = new Timeline_1.TimelineNode(timelineId + "_trail", 0.4, new TimelineEvents_1.PlayEffectTimelineEvent("charge_trail", this._config.effectPath, false, caster.node.position), false);
        timeline.addNode(trailNode);
        // 1.0s: 冲锋攻击命中
        var attackNode = new Timeline_1.TimelineNode(timelineId + "_attack", 1.0, new ChargeAttackEvent("charge_attack", caster, target, this.calculateDamage(caster)), false);
        timeline.addNode(attackNode);
        // 1.1s: 播放命中特效
        var hitEffectNode = new Timeline_1.TimelineNode(timelineId + "_hit_effect", 1.1, new TimelineEvents_1.PlayEffectTimelineEvent("hit_effect", this._config.hitEffectPath, true, (target === null || target === void 0 ? void 0 : target.node.position) || cc.Vec3.ZERO), false);
        timeline.addNode(hitEffectNode);
        // 1.2s: 添加攻击力提升buff
        var buffNode = new Timeline_1.TimelineNode(timelineId + "_buff", 1.2, new AddAttackBuffEvent("attack_buff", caster, this._config.attackBuffDuration, this._config.attackBuffMultiplier), false);
        timeline.addNode(buffNode);
        return timeline;
    };
    /** 计算伤害 */
    ChargeAttackSkill.prototype.calculateDamage = function (caster) {
        if (this._config.damage > 0) {
            return this._config.damage;
        }
        var baseAttack = caster.attributes.attack;
        return Math.floor(baseAttack * this._config.damageMultiplier);
    };
    /** 更新技能冷却 */
    ChargeAttackSkill.prototype.update = function (deltaTime) {
        if (this._remainingCooldown > 0) {
            this._remainingCooldown -= deltaTime;
        }
    };
    /** 重置冷却时间 */
    ChargeAttackSkill.prototype.resetCooldown = function () {
        this._remainingCooldown = 0;
    };
    /** 升级技能 */
    ChargeAttackSkill.prototype.levelUp = function () {
        this._level++;
        this._staminaCost = Math.max(15, this._staminaCost - 2);
        this._cooldown = Math.max(3.0, this._cooldown - 0.3);
        this._range += 25;
        this._config.damageMultiplier += 0.15;
        this._config.attackBuffMultiplier += 0.05;
        this._config.chargeSpeed += 50;
        console.log(this._name + " leveled up to " + this._level);
    };
    /** 获取技能信息 */
    ChargeAttackSkill.prototype.getSkillInfo = function () {
        return {
            id: this._id,
            name: this._name,
            description: this._description,
            level: this._level,
            cooldown: this._cooldown,
            remainingCooldown: this._remainingCooldown,
            mpCost: this._mpCost,
            staminaCost: this._staminaCost,
            range: this._range,
            canUse: this.canUse,
            type: this._type,
            targetType: this._targetType,
            damageMultiplier: this._config.damageMultiplier,
            chargeSpeed: this._config.chargeSpeed
        };
    };
    return ChargeAttackSkill;
}());
exports.ChargeAttackSkill = ChargeAttackSkill;
/*** 冲锋移动事件*/
var ChargeMovementEvent = /** @class */ (function () {
    function ChargeMovementEvent(id, caster, target, speed) {
        this._type = ITimeline_1.TimelineEventType.MOVE;
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._speed = speed;
    }
    Object.defineProperty(ChargeMovementEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeMovementEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    ChargeMovementEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        // 计算冲锋方向和距离
        var startPos = this._caster.node.position;
        var targetPos = this._target.node.position;
        var direction = targetPos.subtract(startPos).normalize();
        // 冲锋到目标附近（保持一定距离）
        var chargeDistance = cc.Vec3.distance(startPos, targetPos) - 50; // 保持50单位距离
        var endPos = startPos.add(direction.multiplyScalar(chargeDistance));
        console.log(this._caster.characterName + " charges from (" + startPos.x + ", " + startPos.y + ") to (" + endPos.x + ", " + endPos.y + ")");
        // 这里应该实现实际的移动逻辑，比如使用Tween动画
        // cc.tween(this._caster.node)
        //     .to(0.7, { position: endPos })
        //     .start();
    };
    return ChargeMovementEvent;
}());
/*** 冲锋攻击事件*/
var ChargeAttackEvent = /** @class */ (function () {
    function ChargeAttackEvent(id, caster, target, damage) {
        this._type = ITimeline_1.TimelineEventType.DAMAGE;
        this._id = id;
        this._caster = caster;
        this._target = target;
        this._damage = damage;
    }
    Object.defineProperty(ChargeAttackEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ChargeAttackEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    ChargeAttackEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        if (this._target && !this._target.isDead) {
            var damageInfo = {
                amount: this._damage,
                type: IDamage_1.DamageType.PHYSICAL,
                source: this._caster,
                isCritical: Math.random() < 0.2,
                element: "physical"
            };
            this._target.takeDamageSimple(damageInfo.amount, this._caster);
            console.log(this._caster.characterName + " charge attacks " + this._target.characterName + " for " + this._damage + " physical damage");
        }
    };
    return ChargeAttackEvent;
}());
/*** 添加攻击力提升buff事件*/
var AddAttackBuffEvent = /** @class */ (function () {
    function AddAttackBuffEvent(id, caster, duration, multiplier) {
        this._type = ITimeline_1.TimelineEventType.ADD_BUFF;
        this._id = id;
        this._caster = caster;
        this._duration = duration;
        this._multiplier = multiplier;
    }
    Object.defineProperty(AddAttackBuffEvent.prototype, "id", {
        get: function () { return this._id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AddAttackBuffEvent.prototype, "type", {
        get: function () { return this._type; },
        enumerable: false,
        configurable: true
    });
    AddAttackBuffEvent.prototype.execute = function (_timeline, _nodeIndex, _context) {
        console.log(this._caster.characterName + " gains attack boost (" + this._multiplier + "x) for " + this._duration + " seconds");
        // 实现AttackBoostBuff
        var attackBuff = new AttackBoostBuff_1.AttackBoostBuff(this._caster, this._caster, this._duration, this._multiplier);
        this._caster.buffManager.addBuff(attackBuff);
    };
    return AddAttackBuffEvent;
}());

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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