{"version": 3, "sources": ["assets\\fight\\skills\\HealingLightSkill.ts"], "names": [], "mappings": ";;;;;;;AACA,iDAA8D;AAC9D,6DAAyH;AAGzH,0CAAqE;AAErE,gDAA2C;AAE3C;;;GAGG;AACH;IAAA;QACY,QAAG,GAAW,mBAAS,CAAC,aAAa,CAAC;QACtC,UAAK,GAAW,MAAM,CAAC;QACvB,iBAAY,GAAW,yBAAyB,CAAC;QACjD,cAAS,GAAW,GAAG,CAAC;QACxB,uBAAkB,GAAW,CAAC,CAAC;QAC/B,YAAO,GAAW,EAAE,CAAC;QACrB,iBAAY,GAAW,CAAC,CAAC;QACzB,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAc,kBAAS,CAAC,MAAM,CAAC;QACpC,gBAAW,GAAoB,wBAAe,CAAC,WAAW,CAAC;QAC3D,WAAM,GAAW,GAAG,CAAC;QACrB,cAAS,GAAqB,IAAI,CAAC;QACnC,kBAAa,GAAY,EAAE,CAAC;QAEpC,WAAW;QACH,YAAO,GAAG;YACd,aAAa,EAAE,YAAY;YAC3B,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,8BAA8B;YAC1C,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,GAAG;YACnB,YAAY,EAAE,GAAG;YACjB,iBAAiB,EAAE,EAAE,CAAC,QAAQ;SACjC,CAAC;IAsJN,CAAC;IAnJG,sBAAI,iCAAE;QADN,aAAa;aACb,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,uCAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACjD,sBAAI,gDAAiB;aAArB,cAAkC,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;aACnE,UAAsB,KAAa,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;;OADnB;IAEnE,sBAAI,qCAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAC7C,sBAAI,0CAAW;aAAf,cAA4B,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;;OAAA;IACvD,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,mCAAI;aAAR,cAAwB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAC5C,sBAAI,yCAAU;aAAd,cAAoC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAC9D,sBAAI,oCAAK;aAAT,cAAsB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;;OAAA;IAC3C,sBAAI,uCAAQ;aAAZ,cAA4B,OAAO,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC;;;OAAA;IACrD,sBAAI,2CAAY;aAAhB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAI,qCAAM;aAAV;YACI,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QACxC,CAAC;;;OAAA;IAED,oBAAoB;IACpB,qCAAS,GAAT,UAAU,MAAkB,EAAE,MAAmB;QAC7C,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAC1B,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAChC,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC,CAAC,MAAM;QACpD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;IACb,6CAAiB,GAAjB,UAAkB,MAAkB;QAChC,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO;YAC9C,MAAM,CAAC,UAAU,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC;IAC9D,CAAC;IAED,WAAW;IACX,4CAAgB,GAAhB,UAAiB,MAAkB;QAC/B,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,WAAW;IACX,gCAAI,GAAJ,UAAK,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACpF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxE,6BAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAI,MAAM,CAAC,aAAa,eAAU,IAAI,CAAC,KAAK,aAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAE,CAAC,CAAC;QACvF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,mBAAmB;IACX,0CAAc,GAAtB,UAAuB,MAAkB,EAAE,MAAmB,EAAE,OAAsB,EAAE,QAAkB;QACtG,IAAM,UAAU,GAAM,IAAI,CAAC,GAAG,SAAI,IAAI,CAAC,GAAG,EAAE,SAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAG,CAAC;QAC9F,IAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAEnE,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,uBAAY,CAC1B,UAAU,UAAO,EACpB,GAAG,EACH,IAAI,2CAA0B,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAC5E,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3B,IAAM,SAAS,GAAG,IAAI,uBAAY,CAC3B,UAAU,WAAQ,EACrB,GAAG,EACH,IAAI,uCAAsB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAC9D,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5B,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,uBAAY,CAC1B,UAAU,UAAO,EACpB,GAAG,EACH,IAAI,iBAAiB,CAAC,aAAa,EAAE,MAAO,EAAE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,EACvF,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3B,eAAe;QACf,IAAM,UAAU,GAAG,IAAI,uBAAY,CAC5B,UAAU,YAAS,EACtB,GAAG,EACH,IAAI,wCAAuB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC,QAAQ,CAAC,EAChG,KAAK,CACR,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7B,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,YAAY;IACJ,+CAAmB,GAA3B,UAA4B,MAAkB;QAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;SAClC;QACD,IAAM,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;QACtD,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IACrE,CAAC;IAED,aAAa;IACb,kCAAM,GAAN,UAAO,SAAiB;QACpB,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC;SACxC;IACL,CAAC;IAED,aAAa;IACb,yCAAa,GAAb;QACI,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,WAAW;IACX,mCAAO,GAAP;QACI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,GAAG,CAAC;QACnC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,KAAK,uBAAkB,IAAI,CAAC,MAAQ,CAAC,CAAC;IAC9D,CAAC;IAED,aAAa;IACb,wCAAY,GAAZ;QACI,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;SAC9C,CAAC;IACN,CAAC;IACL,wBAAC;AAAD,CA9KA,AA8KC,IAAA;AA9KY,8CAAiB;AAgL9B;;GAEG;AACH;IAOI,2BAAY,EAAU,EAAE,MAAkB,EAAE,MAAkB,EAAE,UAAkB;QAL1E,UAAK,GAAsB,6BAAiB,CAAC,IAAI,CAAC;QAMtD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED,sBAAI,iCAAE;aAAN,cAAmB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACrC,sBAAI,mCAAI;aAAR,cAAgC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;OAAA;IAEpD,mCAAO,GAAP,UAAQ,QAAmB,EAAE,SAAiB,EAAE,OAAa;QACzD,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACtC,OAAO;YACP,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAI,IAAI,CAAC,OAAO,CAAC,aAAa,eAAU,IAAI,CAAC,OAAO,CAAC,aAAa,aAAQ,IAAI,CAAC,WAAW,QAAK,CAAC,CAAC;YAE5G,qCAAqC;YACrC,8EAA8E;YAC9E,8CAA8C;SACjD;IACL,CAAC;IACL,wBAAC;AAAD,CA5BA,AA4BC,IAAA;AAED,wBAAwB;AACxB,gDAAuE;AAAC,0DAAyD", "file": "", "sourceRoot": "/", "sourcesContent": ["import { TimelineManager } from \"../systems/TimelineManager\";\nimport { Timeline, TimelineNode } from \"../timeline/Timeline\";\nimport { PlayAnimationTimelineEvent, PlaySoundTimelineEvent, PlayEffectTimelineEvent } from \"../timeline/TimelineEvents\";\nimport { IBuff } from \"../types/IBuff\";\nimport { <PERSON><PERSON>haracter } from \"../types/ICharacter\";\nimport { ISkill, SkillType, SkillTargetType } from \"../types/ISkill\";\nimport { ITimeline } from \"../types/ITimeline\";\nimport SkillName from \"../types/SkillName\";\n\n/**\n * 治疗之光技能\n * 单体治疗技能，恢复目标生命值并提供短暂的生命恢复buff\n */\nexport class HealingLightSkill implements ISkill {\n    private _id: string = SkillName.healing_light;\n    private _name: string = \"治疗之光\";\n    private _description: string = \"释放治疗之光，恢复友军生命值并提供持续治疗效果\";\n    private _cooldown: number = 3.0;\n    private _remainingCooldown: number = 0;\n    private _mpCost: number = 30;\n    private _staminaCost: number = 0;\n    private _level: number = 1;\n    private _type: SkillType = SkillType.ACTIVE;\n    private _targetType: SkillTargetType = SkillTargetType.SINGLE_ALLY;\n    private _range: number = 200;\n    private _timeline: ITimeline | null = null;\n    private _passiveBuffs: IBuff[] = [];\n\n    /** 技能配置 */\n    private _config = {\n        animationName: \"skill_heal\",\n        soundId: \"heal_cast\",\n        effectPath: \"prefabs/effects/HealingLight\",\n        healAmount: 0, // 0表示使用施法者的魔法攻击力\n        healMultiplier: 1.5, // 治疗倍率\n        buffDuration: 5.0, // 持续治疗buff持续时间\n        buffHealPerSecond: 10 // 每秒治疗量\n    };\n\n    // 实现ISkill接口\n    get id(): string { return this._id; }\n    get name(): string { return this._name; }\n    get description(): string { return this._description; }\n    get cooldown(): number { return this._cooldown; }\n    get remainingCooldown(): number { return this._remainingCooldown; }\n    set remainingCooldown(value: number) { this._remainingCooldown = Math.max(0, value); }\n    get mpCost(): number { return this._mpCost; }\n    get staminaCost(): number { return this._staminaCost; }\n    get level(): number { return this._level; }\n    get type(): SkillType { return this._type; }\n    get targetType(): SkillTargetType { return this._targetType; }\n    get range(): number { return this._range; }\n    get timeline(): ITimeline { return this._timeline!; }\n    get passiveBuffs(): IBuff[] { return this._passiveBuffs; }\n    get canUse(): boolean {\n        return this._remainingCooldown <= 0;\n    }\n\n    /** 检查是否可以对目标使用技能 */\n    canCastOn(caster: ICharacter, target?: ICharacter): boolean {\n        if (!target) return false;\n        if (target.isDead) return false;\n        if (target.role === caster.role) return true; // 同阵营\n        return false;\n    }\n\n    /** 检查资源消耗 */\n    checkResourceCost(caster: ICharacter): boolean {\n        return caster.attributes.currentMp >= this._mpCost &&\n            caster.attributes.currentStamina >= this._staminaCost;\n    }\n\n    /** 消耗资源 */\n    consumeResources(caster: ICharacter): void {\n        caster.attributes.consumeMp(this._mpCost);\n        caster.attributes.consumeStamina(this._staminaCost);\n    }\n\n    /** 释放技能 */\n    cast(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): boolean {\n        if (!this.canCastOn(caster, target)) {\n            return false;\n        }\n        if (!this.checkResourceCost(caster)) {\n            return false;\n        }\n\n        this.consumeResources(caster);\n        this._remainingCooldown = this._cooldown;\n        this._timeline = this.createTimeline(caster, target, targets, position);\n        BattleManager.instance.timelineManager.addTimeline(this._timeline);\n        console.log(`${caster.characterName} casts ${this._name} on ${target?.characterName}`);\n        return true;\n    }\n\n    /** 创建技能Timeline */\n    private createTimeline(caster: ICharacter, target?: ICharacter, targets?: ICharacter[], position?: cc.Vec3): ITimeline {\n        const timelineId = `${this._id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n        const timeline = new Timeline(timelineId, this._name, 2.0, caster);\n\n        // 0.0s: 播放施法动画和音效\n        const castNode = new TimelineNode(\n            `${timelineId}_cast`,\n            0.0,\n            new PlayAnimationTimelineEvent(\"cast_animation\", this._config.animationName),\n            false\n        );\n        timeline.addNode(castNode);\n\n        const soundNode = new TimelineNode(\n            `${timelineId}_sound`,\n            0.1,\n            new PlaySoundTimelineEvent(\"cast_sound\", this._config.soundId),\n            false\n        );\n        timeline.addNode(soundNode);\n\n        // 0.8s: 执行治疗效果\n        const healNode = new TimelineNode(\n            `${timelineId}_heal`,\n            0.8,\n            new HealTimelineEvent(\"heal_effect\", target!, caster, this.calculateHealAmount(caster)),\n            false\n        );\n        timeline.addNode(healNode);\n\n        // 1.0s: 播放治疗特效\n        const effectNode = new TimelineNode(\n            `${timelineId}_effect`,\n            1.0,\n            new PlayEffectTimelineEvent(\"heal_effect\", this._config.effectPath, true, target?.node.position),\n            false\n        );\n        timeline.addNode(effectNode);\n\n        return timeline;\n    }\n\n    /** 计算治疗量 */\n    private calculateHealAmount(caster: ICharacter): number {\n        if (this._config.healAmount > 0) {\n            return this._config.healAmount;\n        }\n        const baseMagicAttack = caster.attributes.magicAttack;\n        return Math.floor(baseMagicAttack * this._config.healMultiplier);\n    }\n\n    /** 更新技能冷却 */\n    update(deltaTime: number): void {\n        if (this._remainingCooldown > 0) {\n            this._remainingCooldown -= deltaTime;\n        }\n    }\n\n    /** 重置冷却时间 */\n    resetCooldown(): void {\n        this._remainingCooldown = 0;\n    }\n\n    /** 升级技能 */\n    levelUp(): void {\n        this._level++;\n        this._mpCost = Math.max(15, this._mpCost - 2);\n        this._cooldown = Math.max(1.5, this._cooldown - 0.2);\n        this._range += 15;\n        this._config.healMultiplier += 0.1;\n        console.log(`${this._name} leveled up to ${this._level}`);\n    }\n\n    /** 获取技能信息 */\n    getSkillInfo() {\n        return {\n            id: this._id,\n            name: this._name,\n            description: this._description,\n            level: this._level,\n            cooldown: this._cooldown,\n            remainingCooldown: this._remainingCooldown,\n            mpCost: this._mpCost,\n            staminaCost: this._staminaCost,\n            range: this._range,\n            canUse: this.canUse,\n            type: this._type,\n            targetType: this._targetType,\n            healMultiplier: this._config.healMultiplier\n        };\n    }\n}\n\n/**\n * 治疗Timeline事件\n */\nclass HealTimelineEvent implements ITimelineEvent {\n    private _id: string;\n    private _type: TimelineEventType = TimelineEventType.HEAL;\n    private _target: ICharacter;\n    private _caster: ICharacter;\n    private _healAmount: number;\n\n    constructor(id: string, target: ICharacter, caster: ICharacter, healAmount: number) {\n        this._id = id;\n        this._target = target;\n        this._caster = caster;\n        this._healAmount = healAmount;\n    }\n\n    get id(): string { return this._id; }\n    get type(): TimelineEventType { return this._type; }\n\n    execute(timeline: ITimeline, nodeIndex: number, context?: any): void {\n        if (this._target && !this._target.isDead) {\n            // 执行治疗\n            this._target.heal(this._healAmount);\n            console.log(`${this._caster.characterName} heals ${this._target.characterName} for ${this._healAmount} HP`);\n\n            // 添加持续治疗buff（这里需要实现HealOverTimeBuff）\n            // const healBuff = new HealOverTimeBuff(this._caster, this._target, 5.0, 10);\n            // this._target.buffManager.addBuff(healBuff);\n        }\n    }\n}\n\n// 需要导入TimelineEventType\nimport { ITimelineEvent, TimelineEventType } from \"../types/ITimeline\"; import { BattleManager } from \"../systems/BattleManager\";\n\n"]}