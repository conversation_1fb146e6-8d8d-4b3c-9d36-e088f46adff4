{"version": 3, "sources": ["assets\\card\\cardgame\\src\\FightManager.ts"], "names": [], "mappings": ";;;;AACA,sDAAsD;AACtD,wCAAwC;AACxC,6CAA6C;AAE7C,MAAM;AACN,uBAAuB;AACvB,8BAA8B;AAC9B,iCAAiC;AACjC,yCAAyC;AACzC,yCAAyC;AACzC,4BAA4B;AAC5B,MAAM;AACN,sCAAsC;AACtC,iBAAiB;AACjB,uCAAuC;AAEvC,uBAAuB;AACvB,6BAA6B;AAC7B,yBAAyB;AACzB,mDAAmD;AACnD,qBAAqB;AACrB,iDAAiD;AAEjD,kCAAkC;AAClC,qCAAqC;AAErC,2EAA2E;AAC3E,yEAAyE;AAEzE,IAAI", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n// import CampManager, { ECamp } from \"./CampManager\";\r\n// import { BRO } from \"./EventManager\";\r\n// import SkillManager from \"./SkillManager\";\r\n\r\n// /**\r\n//  * @features : 战斗全局类\r\n//  * @description : 战斗全局静态调用类\r\n//  * @Date : 2023-11-10 16:14:27\r\n//  * <AUTHOR> judu233(<EMAIL>)\r\n//  * @LastEditTime : 2023-11-14 14:03:58\r\n//  * @LastEditors : judu233\r\n//  */\r\n// export default class FightManager {\r\n//     /**阵营管理 */\r\n//     static campManager: CampManager;\r\n\r\n//     /**战斗流程是否是自动的 */\r\n//     static isAuto = false;\r\n//     /**敌人··电脑是否轮流攻击 */\r\n//     static isComputerTurnAttack: boolean = true;\r\n//     /**玩家是否轮流攻击 */\r\n//     static isPlayerTurnAttack: boolean = true;\r\n\r\n//     static isCanUseCard = false\r\n//     static isMonsterComing = false\r\n\r\n//     static get playerCamp() { return this.campManager.getPlayerCamp(); }\r\n//     static get enemyCamp() { return this.campManager.getEnemyCamp(); }\r\n\r\n// }\r\n"]}